---
alwaysApply: false
---
# Organização de Código e Estrutura

## Estrutura de Diretórios
- Respeite a estrutura existente do projeto
- Componentes específicos de features devem estar dentro da pasta da feature
- Componentes reutilizáveis devem estar em `src/components/shared` ou `src/components/ui`
- Hooks personalizados devem estar em `src/hooks` ou dentro da pasta da feature específica

## Nomenclatura
- Componentes: PascalCase (ex: `UserCard.tsx`)
- Hooks: camelCase com prefixo 'use' (ex: `useAuth.ts`)
- Utilitários: camelCase (ex: `formatDate.ts`)
- Arquivos de contexto: PascalCase com sufixo 'Context' (ex: `AuthContext.tsx`)
- Páginas: kebab-case para rotas, index.tsx para páginas principais

## Organização de Componentes
- Divida componentes grandes em subcomponentes menores
- Mantenha a lógica de estado próxima de onde é usada
- Prefira composição ao invés de componentes muito complexos
- Use o padrão de propriedades para personalização ao invés de condicionais complexas

## Padrões de Código
- Prefira funções nomeadas em vez de funções anônimas para melhorar depuração
- Mantenha os componentes puros quando possível
- Separe lógica de UI de lógica de negócios
- Utilize o Context API para estado global apenas quando necessário
- Prefira React Query para gerenciamento de estado de servidor

