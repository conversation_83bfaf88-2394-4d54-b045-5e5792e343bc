---
alwaysApply: false
---
# Princípios Fundamentais de Código

## Simplicidade Acima de Tudo
- **Prefira soluções simples e diretas** ao invés de padrões complexos
- Evite abstrações prematuras ou excessivas
- Mantenha funções e componentes pequenos e com propósito único
- Opte por abordagens declarativas quando possível

## Consistência de Estilo
- Siga o estilo de código existente no projeto
- Mantenha convenções de nomenclatura consistentes
- Respeite a estrutura de arquivos e organização de pastas existente
- Se precisar introduzir um novo padrão, documente-o claramente

## Evite Duplicação de Código
- Antes de implementar algo novo, verifique se já existe funcionalidade semelhante no projeto
- Identifique oportunidades para refatorar código duplicado em funções/componentes reutilizáveis
- Prefira composição sobre repetição de lógica
- Utilize hooks personalizados para compartilhar lógica entre componentes

## Práticas de Desenvolvimento
- Utilize tipos do TypeScript para melhorar a segurança e autocompletar
- Mantenha o código bem testado e documentado quando necessário
- Prefira funções puras quando possível
- Mantenha as dependências de componentes explícitas e minimizadas

