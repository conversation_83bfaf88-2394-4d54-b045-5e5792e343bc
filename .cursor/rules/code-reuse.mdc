---
alwaysApply: false
---
# Reutilização de Código

## Identificação de Código Duplicado
- Antes de implementar uma nova funcionalidade, procure por soluções existentes no projeto
- Verifique componentes/hooks existentes que possam ser adaptados ou estendidos
- Se precisar reimplementar algo similar, considere refatorar o código existente para ser mais genérico

## Estratégias de Reutilização
- **Componentes UI**: extraia em componentes reutilizáveis em `src/components/ui`
- **Lógica de negócio**: crie hooks personalizados em `src/hooks`
- **Funcionalidades de feature**: considere criar factories ou serviços específicos
- **Utilitários**: funções utilitárias devem ficar em `src/utils`

## Abordagens Recomendadas
- Prefira composição sobre herança
- Utilize React Context para compartilhar estado quando necessário
- Para componentes UI, use o padrão Compound Components quando apropriado
- Prefira props explícitas em vez de prop drilling ou contexto desnecessário

## Refatoração
- Se encontrar código duplicado, planeje uma refatoração
- Extraia código similar em funções/componentes utilitários
- Mantenha a simplicidade - não crie abstrações excessivamente complexas
- Documente bem componentes reutilizáveis para facilitar o uso por outros desenvolvedores

## Localização de Código Existente
- Principais locais a verificar por componentes UI reutilizáveis:
  - `src/components/ui/`
  - `src/components/shared/`
- Principais locais a verificar por hooks reutilizáveis:
  - `src/hooks/`
  - `src/features/*/hooks/`
- Funções utilitárias comuns:
  - `src/utils/`
  - `src/lib/`

