---
description: 
globs: 
alwaysApply: true
---
# Consciência de Ambiente no Desenvolvimento

## Diferentes Ambientes
- **Desenvolvimento**: Ambiente local para desenvolvimento e testes rápidos
- **Teste**: Ambiente para testes mais extensivos antes da produção
- **Produção**: Ambiente final para usuários reais

## Variáveis de Ambiente
O projeto utiliza arquivos `.env` para configuração. Veja [.env.example](mdc:.env.example) como referência.

```typescript
// Exemplo de uso correto de variáveis de ambiente
if (process.env.NODE_ENV === 'production') {
  // Lógica específica para produção
} else if (process.env.NODE_ENV === 'test') {
  // Lógica específica para testes
} else {
  // Lógica para desenvolvimento
}
```

## Boas Práticas
1. **Sempre verifique o ambiente atual** antes de implementar lógicas condicionais
2. **Nunca hardcode credenciais** ou configurações sensíveis
3. **Use valores padrão seguros** para quando variáveis de ambiente não estiverem definidas
4. **Documente novas variáveis de ambiente** adicionando-as ao [.env.example](mdc:.env.example)

## Verificação de Ambiente
Utilize o módulo de configuração em [src/config](mdc:src/config) para acessar variáveis de ambiente de forma centralizada e tipada.
