---
alwaysApply: false
---
# Estratégias de Cache no Next.js

## Cache no Next.js App Router

O Next.js oferece um sistema robusto de cache em vários níveis que deve ser utilizado corretamente para otimizar a performance.

### React Server Components (RSC)

- Os RSCs possuem cache automático no Next.js, aproveitando:
  - **Request Memoization**: Deduplica solicitações na mesma renderização
  - **Data Cache**: Persiste dados entre renderizações
  - **Full Route Cache**: Armazena o resultado renderizado (HTML)

### Fetch Data Cache

```tsx
// Fetch com cache (padrão)
fetch('https://api.example.com/data')

// Fetch sem cache (revalidação a cada request)
fetch('https://api.example.com/data', { cache: 'no-store' })

// Fetch com revalidação periódica (em segundos)
fetch('https://api.example.com/data', { next: { revalidate: 60 } })
```

### Segmentos de Rota

```tsx
// Layout ou page.tsx
export const dynamic = 'force-dynamic'; // Desativa cache para esta rota
export const revalidate = 60; // Revalida a cada 60 segundos
```

## TanStack Query (React Query) com Next.js

### Hydration com TanStack Query

```tsx
// Em um Server Component:
async function Page() {
  const initialData = await fetchData();
  
  return (
    <HydrateClient dehydratedState={dehydrate(queryClient)}>
      <ClientComponent initialData={initialData} />
    </HydrateClient>
  );
}

// Em um Client Component:
'use client';

function ClientComponent({ initialData }) {
  const { data } = useQuery({
    queryKey: ['data'],
    queryFn: fetchData,
    initialData,
    staleTime: 60 * 1000, // 1 minuto
  });
  
  return <div>{/* rendering */}</div>;
}
```

## Estratégias de Cache Recomendadas

### Dados Públicos e Estáticos

- **Usar cache máximo** no Next.js e TanStack Query
- **Estratégia**: Cache persistente no servidor + staleTime longo no cliente

```tsx
// Server Component
async function getData() {
  const res = await fetch('https://api.example.com/public-data', {
    next: { revalidate: 3600 } // 1 hora
  });
  return res.json();
}

// Client Component com TanStack Query
const { data } = useQuery({
  queryKey: ['public-data'],
  queryFn: fetchClientData,
  staleTime: 3600 * 1000, // 1 hora
  cacheTime: 24 * 3600 * 1000 // 24 horas
});
```

### Dados Personalizados (Autenticados)

- **Usar cache seletivo** com validação por usuário
- **Estratégia**: Incluir ID do usuário na chave de cache

```tsx
// No Server Component com route handlers
export async function GET() {
  const session = await getSession();
  
  // Cache por usuário
  const data = await fetch(`https://api.example.com/user-data?userId=${session.user.id}`, {
    next: { revalidate: 60 }, // 1 minuto
    headers: { Authorization: `Bearer ${session.token}` }
  });
  
  return NextResponse.json(await data.json());
}

// No Client Component
const { data } = useQuery({
  queryKey: ['user-data', userId],
  queryFn: () => fetchUserData(userId),
  staleTime: 60 * 1000 // 1 minuto
});
```

### Dados Mutáveis Frequentemente

- **Minimizar cache** e usar estratégias de revalidação sob demanda
- **Estratégia**: Invalidação manual e revalidação

```tsx
// Com Mutations no TanStack Query
const queryClient = useQueryClient();

const mutation = useMutation({
  mutationFn: updateData,
  onSuccess: () => {
    // Invalidar cache após mutação
    queryClient.invalidateQueries({ queryKey: ['affected-data'] });
  }
});
```

## Boas Práticas de Segurança

1. **Nunca armazenar em cache**:
   - Tokens de autenticação
   - Informações financeiras sensíveis
   - Dados pessoais identificáveis (PII)

2. **Sempre incluir verificações de autorização**:
   - Mesmo com dados em cache, valide permissões
   - Use middleware de autenticação antes de servir dados em cache

3. **Separar cache por tenant/usuário**:
   - Inclua identificadores de usuário nas chaves de cache
   - Evite vazamento de dados entre usuários

## Otimizando Experiência do Usuário

1. **Loading States**:
   - Use Suspense e skeltons para melhorar UX durante carregamento
   - Implemente otimistic updates para ações frequentes

2. **Revalidação em Foco**:
   - Atualize dados quando usuário retorna à aplicação

```tsx
const { data } = useQuery({
  queryKey: ['data'],
  queryFn: fetchData,
  refetchOnWindowFocus: true,
  staleTime: 5 * 60 * 1000 // 5 minutos
});
```

3. **Prefetching Inteligente**:
   - Pré-carregue dados prováveis de serem necessários

```tsx
// Quando o mouse passa sobre um link para a página de usuário
const prefetchUserData = () => {
  queryClient.prefetchQuery({
    queryKey: ['user', userId],
    queryFn: () => fetchUserData(userId)
  });
};
```

## Troubleshooting

1. **Problema**: Cache persistindo após logout
   **Solução**: Limpar cache no logout

```tsx
const logout = () => {
  // Limpar cache do TanStack Query
  queryClient.clear();
  // Procedimento normal de logout
  signOut();
};
```

2. **Problema**: Dados desatualizados em produção
   **Solução**: Implementar sistema de versão nos dados

```tsx
// Incluir timestamp ou versão na chave de consulta
const { data } = useQuery({
  queryKey: ['data', dataVersion],
  queryFn: fetchData
});
```
