---
description: 
globs: 
alwaysApply: true
---
# 🧠 Padrões de Nomenclatura para Branches e Commits

## 🚀 Como nomear branches

Use o formato:

```
tipo/identificador-descritivo
```

### Tipos comuns:

| Tipo         | Uso                                                                 |
|--------------|----------------------------------------------------------------------|
| `feat`       | Nova funcionalidade                                                  |
| `fix`        | Correção de bug                                                      |
| `chore`      | Tarefas de manutenção (ex: atualização de dependências, configs)     |
| `refactor`   | Melhorias internas no código sem alterar comportamento               |
| `test`       | Adição ou modificação de testes                                      |
| `docs`       | Alterações em documentação                                           |
| `style`      | Formatação, identação, espaços em branco, etc                        |
| `ci`         | Mudanças na integração contínua                                      |

### Exemplos:

```
feat/login-page
fix/crash-on-submit
refactor/user-service
docs/readme-update
```

---

## ✍️ Como escrever mensagens de commit

### Estrutura básica:

```
tipo(escopo): mensagem breve no tempo presente
```

- **tipo**: mesmo usado nas branches (`feat`, `fix`, `chore`, etc.)
- **escopo** *(opcional)*: parte do sistema afetada (ex: `auth`, `api`, `ui`, etc.)
- **mensagem**: descreve a mudança de forma clara e concisa, sempre no tempo presente.

### Regras gerais:

- Use **tempo presente** (ex: _"adiciona"_, _"remove"_, _"refatora"_)
- Escreva em **português**
- Use até 72 caracteres na linha de título
- Evite ponto final no final da linha de título
- Se necessário, adicione descrição mais detalhada após uma linha em branco

### Exemplos:

```
feat(auth): adiciona fluxo de login com Google
fix(api): corrige erro ao buscar usuários inativos
refactor(ui): melhora responsividade da navbar
docs: atualiza instruções de setup no README
```

---

## ✅ Boas práticas adicionais

- **Commits atômicos**: cada commit deve conter apenas uma mudança lógica.
- **Commits pequenos**: evite commits grandes com várias responsabilidades.
- **Branches curtas e significativas**: use nomes que expressem o que está sendo feito.
- **Evite acentos e espaços** nos nomes das branches.
