---
description: 
globs: 
alwaysApply: true
---
# Implementação do Composition Pattern no React

## O que é o Composition Pattern?

O Composition Pattern (Padrão de Composição) é uma abordagem fundamental no React que favorece a composição de componentes em vez de herança. Isso permite criar interfaces flexíveis e extensíveis através da combinação de componentes menores e especializados.

## Princípios Básicos

1. **Prefira composição sobre herança**
2. **Crie componentes pequenos e com responsabilidade única**
3. **Utilize children e props para personalização**
4. **Encapsule lógica em hooks customizados**
5. **Evite prop drilling usando Context API quando apropriado**

## Implementações do Composition Pattern

### 1. Composição Através de Children

```tsx
// Componente container que aceita qualquer conteúdo como children
function Card({ children, className }: { children: React.ReactNode, className?: string }) {
  return (
    <div className={`rounded-lg shadow-md p-4 ${className || ''}`}>
      {children}
    </div>
  );
}

// Uso do componente com composição
export default function ProfileCard() {
  return (
    <Card className="bg-white">
      <h2 className="text-xl font-bold">Ana Silva</h2>
      <p className="text-gray-600">Desenvolvedora Frontend</p>
      <div className="mt-4">
        <Button>Ver perfil</Button>
      </div>
    </Card>
  );
}
```

### 2. Component Composition com Props Especializadas

```tsx
// Componentes especializados que compõem um Dialog
import { DialogRoot, DialogTrigger, DialogContent, DialogClose } from "@/components/ui/Dialog";

export function Dialog({ 
  children, 
  trigger, 
  title 
}: { 
  children: React.ReactNode, 
  trigger: React.ReactNode, 
  title: string 
}) {
  return (
    <DialogRoot>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent>
        <div className="flex flex-col gap-4">
          <h2 className="text-xl font-semibold">{title}</h2>
          <div>{children}</div>
          <DialogClose className="self-end">Fechar</DialogClose>
        </div>
      </DialogContent>
    </DialogRoot>
  );
}

// Uso do componente composto
export function ConfirmationDialog() {
  return (
    <Dialog 
      trigger={<Button variant="danger">Excluir conta</Button>}
      title="Confirmar exclusão"
    >
      <p>Tem certeza que deseja excluir sua conta? Esta ação não pode ser desfeita.</p>
      <div className="flex justify-end gap-2 mt-4">
        <DialogClose asChild>
          <Button variant="ghost">Cancelar</Button>
        </DialogClose>
        <Button variant="danger" onClick={handleDelete}>Confirmar exclusão</Button>
      </div>
    </Dialog>
  );
}
```

### 3. Compound Components Pattern

Este padrão cria uma API declarativa onde componentes relacionados são agrupados sob um namespace.

```tsx
// Definição do componente composto
import { createContext, useContext, useState } from 'react';

type TabsContextType = {
  activeTab: string;
  setActiveTab: (id: string) => void;
};

const TabsContext = createContext<TabsContextType | null>(null);

function useTabs() {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error('Tabs components must be used within a TabsProvider');
  }
  return context;
}

function Tabs({ children, defaultTab }: { children: React.ReactNode; defaultTab: string }) {
  const [activeTab, setActiveTab] = useState(defaultTab);
  return (
    <TabsContext.Provider value={{ activeTab, setActiveTab }}>
      <div className="tabs-container">{children}</div>
    </TabsContext.Provider>
  );
}

function TabList({ children }: { children: React.ReactNode }) {
  return <div className="flex border-b">{children}</div>;
}

function Tab({ id, children }: { id: string; children: React.ReactNode }) {
  const { activeTab, setActiveTab } = useTabs();
  return (
    <button
      className={`px-4 py-2 ${activeTab === id ? 'border-b-2 border-blue-500 font-medium' : ''}`}
      onClick={() => setActiveTab(id)}
    >
      {children}
    </button>
  );
}

function TabPanels({ children }: { children: React.ReactNode }) {
  return <div className="py-4">{children}</div>;
}

function TabPanel({ id, children }: { id: string; children: React.ReactNode }) {
  const { activeTab } = useTabs();
  if (id !== activeTab) return null;
  return <div>{children}</div>;
}

// Exportação do componente composto como namespace
export const TabsComponent = {
  Root: Tabs,
  List: TabList,
  Tab,
  Panels: TabPanels,
  Panel: TabPanel,
};

// Uso do componente composto
export function ProductTabs() {
  return (
    <TabsComponent.Root defaultTab="description">
      <TabsComponent.List>
        <TabsComponent.Tab id="description">Descrição</TabsComponent.Tab>
        <TabsComponent.Tab id="specifications">Especificações</TabsComponent.Tab>
        <TabsComponent.Tab id="reviews">Avaliações</TabsComponent.Tab>
      </TabsComponent.List>
      <TabsComponent.Panels>
        <TabsComponent.Panel id="description">
          <p>Descrição detalhada do produto...</p>
        </TabsComponent.Panel>
        <TabsComponent.Panel id="specifications">
          <ul>
            <li>Especificação 1</li>
            <li>Especificação 2</li>
          </ul>
        </TabsComponent.Panel>
        <TabsComponent.Panel id="reviews">
          <div>Lista de avaliações...</div>
        </TabsComponent.Panel>
      </TabsComponent.Panels>
    </TabsComponent.Root>
  );
}
```

### 4. Render Props Pattern

Este padrão permite compartilhar lógica entre componentes através de funções como props.

```tsx
// Componente que fornece lógica de contagem
function Counter({ 
  children, 
  initialCount = 0 
}: { 
  children: (value: number, handlers: { increment: () => void; decrement: () => void }) => React.ReactNode;
  initialCount?: number;
}) {
  const [count, setCount] = useState(initialCount);
  
  const increment = () => setCount(prev => prev + 1);
  const decrement = () => setCount(prev => prev - 1);
  
  return children(count, { increment, decrement });
}

// Uso do render props
export function ProductQuantity() {
  return (
    <Counter initialCount={1}>
      {(count, { increment, decrement }) => (
        <div className="flex items-center gap-2">
          <Button onClick={decrement} disabled={count <= 1}>-</Button>
          <span className="w-8 text-center">{count}</span>
          <Button onClick={increment}>+</Button>
        </div>
      )}
    </Counter>
  );
}
```

## Estrutura de Pastas e Nomenclatura

### Estrutura de Pastas Recomendada

```
src/
├── components/
│   ├── Form/
│   │   ├── index.ts           # Exporta todos os componentes
│   │   ├── Form.tsx           # Componente principal
│   │   ├── FormField.tsx      # Subcomponente
│   │   ├── FormLabel.tsx      # Subcomponente
│   │   ├── FormMessage.tsx    # Subcomponente
│   │   └── types.ts           # Tipos compartilhados
│   │
│   ├── Card/
│   │   ├── index.ts
│   │   ├── Card.tsx
│   │   ├── CardHeader.tsx
│   │   ├── CardBody.tsx
│   │   └── CardFooter.tsx
│   │
│   └── ui/                    # Componentes base reutilizáveis
│       ├── Button/
│       ├── Input/
│       ├── Select/
│       └── Dialog/
│
├── features/                  # Componentes específicos de funcionalidades
│   ├── Authentication/
│   ├── Dashboard/
│   └── UserProfile/
│
└── layouts/                   # Componentes de layout
    ├── MainLayout/
    ├── AuthLayout/
    └── DashboardLayout/
```

### Convenções de Nomenclatura

1. **Nomes de componentes**:
   - Use PascalCase para nomes de componentes (ex: `FormField.tsx`)
   - Nomes devem ser descritivos e significativos
   - Prefira nomes compostos que indicam o propósito (ex: `ProductCard` em vez de apenas `Card`)

2. **Nomes de pastas**:
   - Use PascalCase para pastas que contêm componentes React
   - A pasta deve ter o mesmo nome do componente principal
   - A estrutura aninhada deve refletir a relação dos componentes

3. **Exports**:
   - Use o arquivo `index.ts` para exportar todos os componentes de uma pasta
   - Isso permite importações mais limpas: `import { Form, FormField } from '@/components/Form'`

### Exemplo de Estrutura de um Componente Composto

```tsx
// src/components/Form/index.ts
export * from './Form';
export * from './FormField';
export * from './FormLabel';
export * from './FormMessage';

// Ou exportar como namespace para uso de dot notation
import { Form } from './Form';
import { FormField } from './FormField';
import { FormLabel } from './FormLabel';
import { FormMessage } from './FormMessage';

export const FormComponent = {
  Root: Form,
  Field: FormField,
  Label: FormLabel,
  Message: FormMessage,
};
```

## Benefícios do Composition Pattern

1. **Reusabilidade**: Componentes pequenos e especializados podem ser combinados de diferentes maneiras
2. **Manutenção**: Mais fácil de manter e testar componentes com responsabilidade única
3. **Flexibilidade**: Adapta-se melhor a mudanças de requisitos do que componentes monolíticos
4. **Legibilidade**: Torna o JSX mais declarativo e fácil de entender
5. **Previsibilidade**: Comportamento mais previsível através de composição explícita

## Práticas a Evitar

1. ❌ **Componentes monolíticos**: Evite criar componentes grandes que fazem muitas coisas
2. ❌ **Herança de classes**: Evite usar herança para compartilhar código entre componentes
3. ❌ **Prop drilling excessivo**: Se estiver passando props por muitos níveis, considere Context API
4. ❌ **Nomes genéricos**: Evite nomes como `Container` ou `Wrapper` sem contexto claro
5. ❌ **Lógica misturada com UI**: Separe lógica complexa em hooks customizados

## Resumo das Recomendações

- **Componentes**: Mantenha-os pequenos, focados e reutilizáveis
- **Nomenclatura**: Use PascalCase para componentes e pastas de componentes
- **Estrutura**: Organize por funcionalidade ou domínio
- **Composição**: Prefira composição (children, render props) sobre herança
- **Declaratividade**: Crie APIs declarativas e intuitivas para seus componentes
