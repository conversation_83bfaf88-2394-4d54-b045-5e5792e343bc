---
alwaysApply: false
---
# Guia de Integração do React Hook Form com Zod

## Princípios Básicos

O React Hook Form combinado com Zod oferece uma solução robusta para validação de formulários com tipagem estática. Esta combinação permite:

- Validação de formulários com schemas declarativos
- Inferência de tipos automática
- Mensagens de erro personalizadas
- Melhor performance ao evitar re-renderizações desnecessárias

## Configuração Inicial

### Instalação das Dependências

```bash
npm install react-hook-form zod @hookform/resolvers/zod
```

### Estrutura Básica

```tsx
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// 1. Definir o schema de validação com Zod
const formSchema = z.object({
  nome: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: z.string().email('Email inválido'),
  idade: z.number().min(18, 'Deve ser maior de 18 anos').optional(),
  senha: z.string().min(8, 'Senha deve ter pelo menos 8 caracteres'),
  confirmarSenha: z.string()
}).refine(data => data.senha === data.confirmarSenha, {
  message: 'As senhas não conferem',
  path: ['confirmarSenha']
});

// 2. Inferir o tipo a partir do schema
type FormValues = z.infer<typeof formSchema>;

export default function FormularioRegistro() {
  // 3. Inicializar o formulário com o resolver Zod
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      nome: '',
      email: '',
      senha: '',
      confirmarSenha: ''
    }
  });

  // 4. Função para processar o envio do formulário
  const onSubmit = async (data: FormValues) => {
    try {
      // Chamar API, processar dados, etc.
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulação
      console.log('Dados enviados:', data);
      reset();
    } catch (erro) {
      console.error('Erro ao enviar:', erro);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <label htmlFor="nome" className="block text-sm font-medium">
          Nome
        </label>
        <input
          id="nome"
          {...register('nome')}
          className="mt-1 block w-full px-3 py-2 border rounded-md"
        />
        {errors.nome && (
          <p className="mt-1 text-sm text-red-600">{errors.nome.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="email" className="block text-sm font-medium">
          Email
        </label>
        <input
          id="email"
          type="email"
          {...register('email')}
          className="mt-1 block w-full px-3 py-2 border rounded-md"
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="idade" className="block text-sm font-medium">
          Idade (opcional)
        </label>
        <input
          id="idade"
          type="number"
          {...register('idade', { valueAsNumber: true })}
          className="mt-1 block w-full px-3 py-2 border rounded-md"
        />
        {errors.idade && (
          <p className="mt-1 text-sm text-red-600">{errors.idade.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="senha" className="block text-sm font-medium">
          Senha
        </label>
        <input
          id="senha"
          type="password"
          {...register('senha')}
          className="mt-1 block w-full px-3 py-2 border rounded-md"
        />
        {errors.senha && (
          <p className="mt-1 text-sm text-red-600">{errors.senha.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="confirmarSenha" className="block text-sm font-medium">
          Confirmar Senha
        </label>
        <input
          id="confirmarSenha"
          type="password"
          {...register('confirmarSenha')}
          className="mt-1 block w-full px-3 py-2 border rounded-md"
        />
        {errors.confirmarSenha && (
          <p className="mt-1 text-sm text-red-600">{errors.confirmarSenha.message}</p>
        )}
      </div>

      <button
        type="submit"
        disabled={isSubmitting}
        className="w-full py-2 px-4 bg-blue-600 text-white rounded-md"
      >
        {isSubmitting ? 'Enviando...' : 'Cadastrar'}
      </button>
    </form>
  );
}
```

## Melhores Práticas para Tipagem

### 1. Sempre Infira Tipos do Schema Zod

```tsx
// Correto: Inferir tipos do schema Zod
const userSchema = z.object({
  nome: z.string(),
  email: z.string().email(),
  perfil: z.object({
    bio: z.string().optional(),
    idade: z.number().optional()
  })
});

type UserFormValues = z.infer<typeof userSchema>;

// Uso com useForm
const { register } = useForm<UserFormValues>({
  resolver: zodResolver(userSchema)
});
```

### 2. Reutilize Schemas e Tipos

Definir schemas em arquivos separados permite reutilização:

```tsx
// schemas/user.ts
import { z } from 'zod';

export const userSchema = z.object({
  nome: z.string().min(2),
  email: z.string().email(),
  // ... outros campos
});

export type UserFormValues = z.infer<typeof userSchema>;
```

### 3. Validações Complexas

Para validações que dependem de múltiplos campos, use `.refine()`:

```tsx
const formSchema = z.object({
  senha: z.string().min(8),
  confirmarSenha: z.string()
})
.refine(data => data.senha === data.confirmarSenha, {
  message: 'As senhas não conferem',
  path: ['confirmarSenha'] // aponta qual campo mostrará o erro
});
```

### 4. Lide Corretamente com Campos Numéricos

```tsx
const schema = z.object({
  idade: z.number().min(18).optional(),
  preco: z.number().positive()
});

// No register, use valueAsNumber
<input {...register('idade', { valueAsNumber: true })} type="number" />
<input {...register('preco', { valueAsNumber: true })} type="number" step="0.01" />
```

### 5. Schemas para Select, Radio e Checkbox

```tsx
// Select com opções específicas
const estadoCivilSchema = z.object({
  estadoCivil: z.enum(['solteiro', 'casado', 'divorciado', 'viuvo'])
});

// Checkbox único
const termsSchema = z.object({
  aceitouTermos: z.literal(true, {
    errorMap: () => ({ message: 'Você deve aceitar os termos' })
  })
});

// Grupo de checkboxes
const interessesSchema = z.object({
  interesses: z.array(z.string()).min(1, 'Selecione pelo menos um interesse')
});
```

## Integração com Server Actions

```tsx
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { criarUsuario } from '@/actions/user-actions';

const userSchema = z.object({
  nome: z.string().min(2),
  email: z.string().email()
});

type UserFormValues = z.infer<typeof userSchema>;

export default function UserForm() {
  const { register, handleSubmit, formState: { errors }, setError } = useForm<UserFormValues>({
    resolver: zodResolver(userSchema)
  });

  const onSubmit = async (data: UserFormValues) => {
    const result = await criarUsuario(data);

    if (!result.success) {
      // Definir erros do servidor nos campos do formulário
      if (result.errors.email) {
        setError('email', { 
          type: 'server', 
          message: result.errors.email 
        });
      }
      
      if (result.errors._form) {
        setError('root.serverError', {
          type: 'server',
          message: result.errors._form
        });
      }
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* Campos do formulário */}
      
      {/* Erro geral do servidor */}
      {errors.root?.serverError && (
        <div className="error">{errors.root.serverError.message}</div>
      )}
    </form>
  );
}
```

## Formulários Dinâmicos com Tipagem Segura

### Arrays de Campos com `useFieldArray`

```tsx
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const telefoneSchema = z.object({
  numero: z.string().min(8, 'Telefone inválido'),
  tipo: z.enum(['celular', 'residencial', 'comercial'])
});

const contatoSchema = z.object({
  nome: z.string().min(2),
  telefones: z.array(telefoneSchema).min(1, 'Adicione pelo menos um telefone')
});

type ContatoFormValues = z.infer<typeof contatoSchema>;

export default function ContatoForm() {
  const { register, control, handleSubmit, formState: { errors } } = useForm<ContatoFormValues>({
    resolver: zodResolver(contatoSchema),
    defaultValues: {
      nome: '',
      telefones: [{ numero: '', tipo: 'celular' }]
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'telefones'
  });

  const onSubmit = (data: ContatoFormValues) => {
    console.log(data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div>
        <label>Nome</label>
        <input {...register('nome')} />
        {errors.nome && <p>{errors.nome.message}</p>}
      </div>

      <div>
        <h3>Telefones</h3>
        {fields.map((field, index) => (
          <div key={field.id} className="flex gap-2">
            <input 
              {...register(`telefones.${index}.numero`)} 
              placeholder="Número" 
            />
            
            <select {...register(`telefones.${index}.tipo`)}>
              <option value="celular">Celular</option>
              <option value="residencial">Residencial</option>
              <option value="comercial">Comercial</option>
            </select>
            
            <button type="button" onClick={() => remove(index)}>
              Remover
            </button>
            
            {errors.telefones?.[index]?.numero && (
              <p>{errors.telefones[index]?.numero?.message}</p>
            )}
          </div>
        ))}
        
        <button
          type="button"
          onClick={() => append({ numero: '', tipo: 'celular' })}
        >
          Adicionar Telefone
        </button>
        
        {errors.telefones && errors.telefones.message && (
          <p>{errors.telefones.message}</p>
        )}
      </div>

      <button type="submit">Salvar</button>
    </form>
  );
}
```

## Formulários com Steps e Wizard

Para formulários complexos com múltiplos steps, você pode combinar React Hook Form, Zod e gerenciamento de estado:

```tsx
'use client';

import { useState } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// Schema para cada step
const step1Schema = z.object({
  nome: z.string().min(2),
  email: z.string().email()
});

const step2Schema = z.object({
  endereco: z.string().min(5),
  cidade: z.string().min(2)
});

const step3Schema = z.object({
  senha: z.string().min(8),
  confirmarSenha: z.string()
}).refine(data => data.senha === data.confirmarSenha, {
  message: 'As senhas não conferem',
  path: ['confirmarSenha']
});

// Schema completo (união de todos os steps)
const formSchema = step1Schema.merge(step2Schema).merge(step3Schema);

// Tipo do formulário completo
type FormValues = z.infer<typeof formSchema>;

// Componente de wizard
export default function FormWizard() {
  const [step, setStep] = useState(1);
  
  // Inicializar o form com o schema completo
  const methods = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    mode: 'onChange',
    defaultValues: {
      nome: '',
      email: '',
      endereco: '',
      cidade: '',
      senha: '',
      confirmarSenha: ''
    }
  });
  
  // Função para avançar ao próximo step
  const nextStep = async () => {
    let schemaToValidate: z.ZodType<any>;
    
    // Selecionar o schema adequado para o step atual
    switch (step) {
      case 1:
        schemaToValidate = step1Schema;
        break;
      case 2:
        schemaToValidate = step2Schema;
        break;
      default:
        schemaToValidate = step3Schema;
    }
    
    // Validar apenas os campos do step atual
    const currentStepData = methods.getValues();
    const validationResult = schemaToValidate.safeParse(currentStepData);
    
    if (validationResult.success) {
      setStep(prev => prev + 1);
    } else {
      // Trigger validation to show errors
      await methods.trigger();
    }
  };
  
  // Voltar para o step anterior
  const prevStep = () => {
    setStep(prev => prev - 1);
  };
  
  // Enviar o formulário completo
  const onSubmit = (data: FormValues) => {
    console.log('Form completo:', data);
    // Processar o envio...
  };
  
  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6">
        {/* Step 1: Informações Pessoais */}
        {step === 1 && (
          <div className="space-y-4">
            <h2>Informações Pessoais</h2>
            
            <div>
              <label htmlFor="nome">Nome</label>
              <input
                id="nome"
                {...methods.register('nome')}
                className="w-full p-2 border rounded"
              />
              {methods.formState.errors.nome && (
                <p className="text-red-500">{methods.formState.errors.nome.message}</p>
              )}
            </div>
            
            <div>
              <label htmlFor="email">Email</label>
              <input
                id="email"
                type="email"
                {...methods.register('email')}
                className="w-full p-2 border rounded"
              />
              {methods.formState.errors.email && (
                <p className="text-red-500">{methods.formState.errors.email.message}</p>
              )}
            </div>
            
            <button
              type="button"
              onClick={nextStep}
              className="px-4 py-2 bg-blue-500 text-white rounded"
            >
              Próximo
            </button>
          </div>
        )}
        
        {/* Step 2: Endereço */}
        {step === 2 && (
          <div className="space-y-4">
            <h2>Endereço</h2>
            
            <div>
              <label htmlFor="endereco">Endereço</label>
              <input
                id="endereco"
                {...methods.register('endereco')}
                className="w-full p-2 border rounded"
              />
              {methods.formState.errors.endereco && (
                <p className="text-red-500">{methods.formState.errors.endereco.message}</p>
              )}
            </div>
            
            <div>
              <label htmlFor="cidade">Cidade</label>
              <input
                id="cidade"
                {...methods.register('cidade')}
                className="w-full p-2 border rounded"
              />
              {methods.formState.errors.cidade && (
                <p className="text-red-500">{methods.formState.errors.cidade.message}</p>
              )}
            </div>
            
            <div className="flex gap-2">
              <button
                type="button"
                onClick={prevStep}
                className="px-4 py-2 bg-gray-500 text-white rounded"
              >
                Voltar
              </button>
              <button
                type="button"
                onClick={nextStep}
                className="px-4 py-2 bg-blue-500 text-white rounded"
              >
                Próximo
              </button>
            </div>
          </div>
        )}
        
        {/* Step 3: Senha */}
        {step === 3 && (
          <div className="space-y-4">
            <h2>Definir Senha</h2>
            
            <div>
              <label htmlFor="senha">Senha</label>
              <input
                id="senha"
                type="password"
                {...methods.register('senha')}
                className="w-full p-2 border rounded"
              />
              {methods.formState.errors.senha && (
                <p className="text-red-500">{methods.formState.errors.senha.message}</p>
              )}
            </div>
            
            <div>
              <label htmlFor="confirmarSenha">Confirmar Senha</label>
              <input
                id="confirmarSenha"
                type="password"
                {...methods.register('confirmarSenha')}
                className="w-full p-2 border rounded"
              />
              {methods.formState.errors.confirmarSenha && (
                <p className="text-red-500">{methods.formState.errors.confirmarSenha.message}</p>
              )}
            </div>
            
            <div className="flex gap-2">
              <button
                type="button"
                onClick={prevStep}
                className="px-4 py-2 bg-gray-500 text-white rounded"
              >
                Voltar
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-green-500 text-white rounded"
              >
                Enviar
              </button>
            </div>
          </div>
        )}
      </form>
    </FormProvider>
  );
}
```

## Considerações Finais

1. **Separação de Schemas e Tipos**:
   - Mantenha schemas Zod em arquivos separados para reutilização
   - Exporte também os tipos inferidos para uso consistente

2. **Mensagens de Erro Consistentes**:
   - Defina mensagens de erro claras e consistentes nos schemas
   - Considere criar um arquivo de mensagens para internacionalização

3. **Transformações de Dados**:
   - Use `.transform()` do Zod para modificar dados antes da validação
   - Útil para formatação de strings, conversão de tipos, etc.

4. **Performance**:
   - Para formulários grandes, considere usar modo de validação `onChange` ou `onBlur`
   - Use `shouldUnregister: true` para campos que são removidos do DOM

5. **Segurança de Tipos**:
   - Sempre mantenha sincronia entre os schemas e os tipos TypeScript
   - Evite usar `as` ou outras asserções de tipo para contornar o sistema de tipos
