---
alwaysApply: false
---
# Guia de Utilização do Hook `use()` no React 19

## O que é o Hook `use()`?

O hook `use()` é uma nova adição ao React 19 que permite consumir recursos de forma dinâmica dentro de componentes React, incluindo Promises e Context, diretamente no corpo do componente. Ele foi projetado para substituir muitos casos de uso do `useEffect` e `useState`, tornando o código mais limpo e direto.

## Vantagens do Hook `use()`

- **Código mais declarativo**: Elimina boilerplate de gerenciamento de estado
- **Melhor leitura sequencial**: O código se parece mais com sua execução lógica
- **Menos estados intermediários**: Reduz significativamente a necessidade de useState para armazenar resultados
- **Suporte nativo a Suspense**: Integra-se naturalmente com o modelo de Suspense do React

## Substituindo `useState` e `useEffect` com `use()`

### Antes (com useState e useEffect):

```tsx
'use client';

import { useState, useEffect } from 'react';

function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    async function fetchUser() {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/users/${userId}`);
        if (!response.ok) throw new Error('Falha ao carregar usuário');
        const data = await response.json();
        setUser(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchUser();
  }, [userId]);
  
  if (isLoading) return <div>Carregando...</div>;
  if (error) return <div>Erro: {error}</div>;
  if (!user) return null;
  
  return (
    <div>
      <h1>{user.name}</h1>
      <p>Email: {user.email}</p>
    </div>
  );
}
```

### Depois (com o hook use()):

```tsx
import { use, Suspense } from 'react';

// Esta função retorna uma Promise que será consumida pelo hook use()
function fetchUser(userId) {
  return fetch(`/api/users/${userId}`)
    .then(response => {
      if (!response.ok) throw new Error('Falha ao carregar usuário');
      return response.json();
    });
}

// Componente que utiliza use() para consumir a Promise
function UserProfile({ userId }) {
  const user = use(fetchUser(userId));
  
  return (
    <div>
      <h1>{user.name}</h1>
      <p>Email: {user.email}</p>
    </div>
  );
}

// Componente pai que encapsula com Suspense
export default function UserProfilePage({ userId }) {
  return (
    <Suspense fallback={<div>Carregando...</div>}>
      <UserProfile userId={userId} />
    </Suspense>
  );
}
```

## Regras para Utilização do Hook `use()`

1. **Utilize `use()` no lugar de `useEffect` para:**
   - Buscar dados de APIs
   - Consumir Promises
   - Acessar valores de Context

2. **Encapsule com `<Suspense>`:**
   - Sempre envolva componentes que usam `use()` com Promises em um componente `<Suspense>`
   - Defina um fallback adequado para estados de carregamento

3. **Trate erros adequadamente:**
   - Use `<ErrorBoundary>` para capturar erros em componentes que utilizam `use()`
   - Ou implemente um mecanismo de try/catch quando necessário

4. **Posicionamento correto:**
   - Chame `use()` apenas no nível superior do componente
   - Não utilize dentro de condições, loops ou funções aninhadas

5. **Memoize recursos pesados:**
   - Para recursos que exigem cálculos intensivos, considere usar `useMemo` junto com `use()`

## Exemplos de Uso com Context

### Antes (com useContext):

```tsx
import { useContext } from 'react';
import { ThemeContext } from './ThemeContext';

function ThemedButton() {
  const theme = useContext(ThemeContext);
  
  return (
    <button style={{ background: theme.background, color: theme.foreground }}>
      Botão Temático
    </button>
  );
}
```

### Depois (com use()):

```tsx
import { use } from 'react';
import { ThemeContext } from './ThemeContext';

function ThemedButton() {
  const theme = use(ThemeContext);
  
  return (
    <button style={{ background: theme.background, color: theme.foreground }}>
      Botão Temático
    </button>
  );
}
```

## Gerenciamento de Estado Complexo

Para casos de gerenciamento de estado mais complexos, combine `use()` com patterns como o de recursos:

```tsx
// userResource.js
export function createUserResource(userId) {
  let status = 'pending';
  let result;
  let error;
  
  const promise = fetch(`/api/users/${userId}`)
    .then(response => {
      if (!response.ok) throw new Error('Falha ao carregar');
      return response.json();
    })
    .then(data => {
      status = 'success';
      result = data;
      return data;
    })
    .catch(e => {
      status = 'error';
      error = e;
      throw e;
    });
  
  return {
    read() {
      if (status === 'pending') throw promise;
      if (status === 'error') throw error;
      return result;
    }
  };
}

// UserProfile.jsx
function UserProfile({ userId }) {
  const userResource = useMemo(() => createUserResource(userId), [userId]);
  const user = use(userResource);
  
  return (
    <div>
      <h1>{user.name}</h1>
      <p>Email: {user.email}</p>
    </div>
  );
}
```

## Considerações Importantes

1. **Compatibilidade de Versão:**
   - O hook `use()` está disponível apenas no React 19 e superior
   - Verifique a compatibilidade antes de implementar

2. **Server Components:**
   - `use()` funciona de forma diferente em componentes Server vs Client
   - Em Server Components, as Promises são resolvidas no servidor
   - Em Client Components, ocorre suspensão até a Promise resolver

3. **Cache e Revalidação:**
   - Considere implementar um mecanismo de cache para evitar requisições repetidas
   - Use bibliotecas como SWR ou React Query junto com `use()` para controle mais refinado

4. **Quando ainda usar useState e useEffect:**
   - Para efeitos colaterais não relacionados a dados (ex: analytics, scroll)
   - Para integração com bibliotecas de terceiros que não suportam Suspense
   - Quando você precisa controlar manualmente o ciclo de vida de um recurso

## Exemplo Prático: Formulário com Validação

```tsx
'use client';

import { use, useMemo, useState, useTransition } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const schema = z.object({
  email: z.string().email('Email inválido'),
  password: z.string().min(8, 'Senha deve ter pelo menos 8 caracteres')
});

// Função que retorna um resource para ser usado com use()
function createAuthResource() {
  return {
    login: async (data) => {
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Falha ao fazer login');
      }
      
      return response.json();
    }
  };
}

export default function LoginForm() {
  const authResource = useMemo(() => createAuthResource(), []);
  const [formState, setFormState] = useState({ email: '', password: '' });
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState(null);
  const [result, setResult] = useState(null);
  
  function handleSubmit(e) {
    e.preventDefault();
    
    // Validação com Zod
    const validation = schema.safeParse(formState);
    if (!validation.success) {
      setError(validation.error.format());
      return;
    }
    
    setError(null);
    
    // Usar startTransition para não bloquear a UI
    startTransition(async () => {
      try {
        // Usar o hook use com o resource
        const response = await authResource.login(formState);
        setResult(response);
      } catch (err) {
        setError({ form: err.message });
      }
    });
  }
  
  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label htmlFor="email">Email</label>
        <input
          id="email"
          type="email"
          value={formState.email}
          onChange={(e) => setFormState({ ...formState, email: e.target.value })}
        />
        {error?.email && <p className="error">{error.email._errors[0]}</p>}
      </div>
      
      <div>
        <label htmlFor="password">Senha</label>
        <input
          id="password"
          type="password"
          value={formState.password}
          onChange={(e) => setFormState({ ...formState, password: e.target.value })}
        />
        {error?.password && <p className="error">{error.password._errors[0]}</p>}
      </div>
      
      {error?.form && <p className="error">{error.form}</p>}
      
      <button type="submit" disabled={isPending}>
        {isPending ? 'Entrando...' : 'Entrar'}
      </button>
      
      {result && <p className="success">Login bem-sucedido!</p>}
    </form>
  );
}
```

## Conclusão

O hook `use()` representa uma evolução significativa na forma como gerenciamos efeitos e estado no React. Seguindo as práticas recomendadas neste guia, você poderá:

1. Escrever componentes mais concisos e declarativos
2. Reduzir significativamente a quantidade de código boilerplate
3. Aproveitar melhor o modelo de Suspense do React
4. Criar interfaces de usuário mais responsivas

Lembre-se de sempre considerar o contexto da sua aplicação ao decidir entre `use()`, `useState` e `useEffect` para garantir a melhor experiência de desenvolvimento e de usuário.
