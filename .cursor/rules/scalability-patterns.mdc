---
alwaysApply: false
---
# Padrões de Escalabilidade

## Arquitetura Escalável

- **Separe responsabilidades** em componentes, hooks e serviços bem definidos
- **Prefira estruturas modulares** que possam evoluir independentemente
- **Mantenha contratos de interface estáveis** entre módulos
- **Implemente recursos pensando em multi-tenancy** para suportar vários inquilinos

## Gestão de Estado e Cache

- **Implemente estratégias de cache** para dados frequentemente acessados
- **Utilize os mecanismos de cache do Next.js** em [src/services/cache](mdc:src/services/cache)
- **Prefira Server Components** para evitar estado desnecessário no cliente
- **Use React Query para caching eficiente** de dados no lado do cliente
- **Implemente revalidação estratégica** apenas quando necessário

## Otimização de Performance

- **Minimize chamadas à API** através de batching quando apropriado
- **Pagine resultados grandes** para evitar sobrecarga de memória
- **Utilize lazy loading para componentes pesados** e rotas raramente acessadas
- **Implemente virtualização para listas longas** (react-virtualized ou similar)
- **Prefira componentes memoizados** para props estáveis

## Arquitetura de Dados

- **Projete consultas ao banco com índices adequados**
- **Prefira consultas leves e específicas** ao invés de grandes junções
- **Implemente transações para operações complexas**
- **Considere estratégias de particionamento** para grandes volumes de dados
- **Utilize schemas validados** para garantir consistência dos dados

## Práticas para Escala

- **Implemente operações em background** para processos demorados
- **Monitore e registre erros** para identificar problemas cedo
- **Prefira mudanças graduais e reversíveis** ao invés de refatorações completas
- **Considere edge functions** para lógica que precisa estar próxima aos usuários
- **Projete para falhas parciais** ao invés de falhas catastróficas

## Estratégias de Migração e Evolução

- **Mantenha esquemas de migração versionados** em [migrations](mdc:migrations)
- **Implemente feature flags** para lançamentos graduais
- **Suporte versões compatíveis com versões anteriores** para APIs públicas
- **Documente alterações que quebram compatibilidade** e planeje migrações

## Exemplos de Implementação Escalável

### Paginação Eficiente
```typescript
// Server Component ou API Route
async function getUsers(page = 1, pageSize = 10) {
  const start = (page - 1) * pageSize;
  
  const { data, count } = await supabase
    .from('users')
    .select('*', { count: 'exact' })
    .range(start, start + pageSize - 1);
    
  return {
    data,
    pagination: {
      totalCount: count,
      totalPages: Math.ceil(count / pageSize),
      currentPage: page,
      pageSize
    }
  };
}
```

### Cache com Revalidação
```typescript
// Route Handler com cache controlado
export async function GET(request: Request) {
  const tenant = getTenantFromRequest(request);
  
  // Usar cache com revalidação condicional
  const cacheControl = process.env.NODE_ENV === 'production'
    ? 'public, s-maxage=60, stale-while-revalidate=300'
    : 'no-cache';
    
  const data = await fetchTenantSettings(tenant.id);
  
  return NextResponse.json(data, {
    headers: {
      'Cache-Control': cacheControl
    }
  });
}
```

### Carregamento Condicional de Componentes
```typescript
// Cliente Component com carregamento condicional
import { Suspense, lazy } from 'react';

// Importação lazy de componente pesado
const HeavyDataTable = lazy(() => import('@/components/HeavyDataTable'));

function DashboardPage({ shouldShowTable }: { shouldShowTable: boolean }) {
  return (
    <div>
      <DashboardSummary />
      
      {shouldShowTable && (
        <Suspense fallback={<TableSkeleton />}>
          <HeavyDataTable />
        </Suspense>
      )}
    </div>
  );
}
```

