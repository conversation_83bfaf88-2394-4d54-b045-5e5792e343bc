---
description: 
globs: 
alwaysApply: true
---
# Guia de Server Actions e Client Actions com Zod no Next.js

## Server Actions

Server Actions são funções executadas no servidor que podem ser chamadas diretamente de componentes client-side. Com <PERSON>, você pode validar os dados enviados para essas ações.

### Estrutura básica de um Server Action com Zod

```tsx
'use server';

import { z } from 'zod';

// 1. Definir o schema para validação
const formSchema = z.object({
  nome: z.string().min(2, "Nome precisa ter pelo menos 2 caracteres"),
  email: z.string().email("Email inválido"),
  idade: z.number().min(18, "Precisa ser maior de 18 anos")
});

// 2. Criar o Server Action com validação
export async function enviarFormulario(dados: unknown) {
  // 3. Validar os dados com Zod
  const resultado = formSchema.safeParse(dados);
  
  // 4. <PERSON><PERSON><PERSON> erros se a validação falhar
  if (!resultado.success) {
    return { 
      success: false, 
      errors: resultado.error.format() 
    };
  }
  
  // 5. Processar dados validados
  const dadosValidados = resultado.data;
  
  try {
    // Lógica de negócio (salvar no banco, etc.)
    await salvarNoBanco(dadosValidados);
    
    return { 
      success: true 
    };
  } catch (error) {
    return { 
      success: false, 
      errors: { _form: "Falha ao processar o formulário" } 
    };
  }
}
```

### Boas Práticas para Server Actions

1. **Use 'use server'** no início do arquivo ou na função específica
2. **Sempre valide entrada de dados** com Zod antes de processá-los
3. **Retorne objetos estruturados** contendo status e erros/dados
4. **Trate erros adequadamente** e retorne mensagens amigáveis
5. **Não exponha informações sensíveis** nos retornos de erro
6. **Mantenha ações atômicas** - cada action deve fazer uma coisa específica

## Client Components e Actions

### Exemplo de Client Component com Server Action

```tsx
'use client';

import { useState } from 'react';
import { enviarFormulario } from '@/actions/form-actions';
import { useFormStatus } from 'react-dom';

// Componente de botão de submissão com estado de loading
function SubmitButton() {
  const { pending } = useFormStatus();
  
  return (
    <button 
      type="submit" 
      disabled={pending}
      className="btn btn-primary"
    >
      {pending ? 'Enviando...' : 'Enviar'}
    </button>
  );
}

export default function FormularioCliente() {
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Função para lidar com a submissão do formulário
  async function handleSubmit(formData: FormData) {
    // Preparar dados para o server action
    const dados = {
      nome: formData.get('nome'),
      email: formData.get('email'),
      idade: Number(formData.get('idade'))
    };
    
    // Chamar server action
    const resultado = await enviarFormulario(dados);
    
    if (!resultado.success) {
      // Formatando erros para exibição
      const formattedErrors: Record<string, string> = {};
      
      // Convertendo os erros do Zod para formato simples
      Object.entries(resultado.errors).forEach(([key, value]) => {
        if (key === '_form') {
          formattedErrors._form = value as string;
        } else if (value && typeof value === 'object' && 'message' in value) {
          formattedErrors[key] = value.message as string;
        }
      });
      
      setErrors(formattedErrors);
    } else {
      setErrors({});
      // Resetar formulário ou redirecionar
    }
  }
  
  return (
    <form action={handleSubmit}>
      {errors._form && (
        <div className="error-message">{errors._form}</div>
      )}
      
      <div className="form-group">
        <label htmlFor="nome">Nome</label>
        <input 
          id="nome"
          name="nome" 
          type="text" 
        />
        {errors.nome && <span className="error">{errors.nome}</span>}
      </div>
      
      <div className="form-group">
        <label htmlFor="email">Email</label>
        <input 
          id="email"
          name="email" 
          type="email" 
        />
        {errors.email && <span className="error">{errors.email}</span>}
      </div>
      
      <div className="form-group">
        <label htmlFor="idade">Idade</label>
        <input 
          id="idade"
          name="idade" 
          type="number" 
        />
        {errors.idade && <span className="error">{errors.idade}</span>}
      </div>
      
      <SubmitButton />
    </form>
  );
}
```

## Padrões Avançados

### Pattern: Criando um wrapper de validação

```tsx
'use server';

import { z } from 'zod';

// Função genérica para validar input com Zod
export function withValidation<T extends z.ZodType>(
  schema: T,
  action: (data: z.infer<T>) => Promise<any>
) {
  return async (data: unknown) => {
    const result = schema.safeParse(data);
    
    if (!result.success) {
      return { 
        success: false, 
        errors: result.error.format() 
      };
    }
    
    try {
      const actionResult = await action(result.data);
      return { success: true, data: actionResult };
    } catch (error) {
      console.error(error);
      return { 
        success: false, 
        errors: { _form: "Ocorreu um erro ao processar sua solicitação" } 
      };
    }
  };
}

// Exemplo de uso do wrapper
const userSchema = z.object({
  nome: z.string().min(2),
  email: z.string().email()
});

export const criarUsuario = withValidation(userSchema, async (dados) => {
  // Implementação para criar usuário com dados já validados
  return { id: "123", ...dados };
});
```

### Pattern: Form Action Hooks com TypeScript

```tsx
'use client';

import { useState } from 'react';

// Hook para gestão de forms com server actions
export function useFormAction<T, E = Record<string, string>>(
  serverAction: (data: T) => Promise<{ success: boolean; errors?: E; data?: any }>
) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<E | null>(null);
  const [data, setData] = useState<any>(null);
  
  const execute = async (formData: T) => {
    setIsSubmitting(true);
    setErrors(null);
    
    try {
      const result = await serverAction(formData);
      
      if (result.success) {
        setData(result.data);
        return { success: true, data: result.data };
      } else {
        setErrors(result.errors || null);
        return { success: false, errors: result.errors };
      }
    } catch (error) {
      setErrors({ _form: "Erro inesperado" } as unknown as E);
      return { success: false, errors: { _form: "Erro inesperado" } as unknown as E };
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return {
    execute,
    isSubmitting,
    errors,
    data,
    setErrors
  };
}
```

## Considerações de Segurança

1. **Nunca confie em dados do cliente** - sempre valide no servidor
2. **Use rate limiting** para prevenir ataques de força bruta
3. **Implemente CSRF protection** (o Next.js já inclui por padrão)
4. **Valide permissões do usuário** antes de executar ações sensíveis
5. **Sanitize dados** antes de usar em consultas ao banco de dados

## Estrutura de Diretórios Recomendada

```
app/
├── actions/               # Server actions
│   ├── auth-actions.ts    # Ações relacionadas à autenticação
│   ├── form-actions.ts    # Ações de formulários
│   └── utils/
│       └── validation.ts  # Helpers de validação
├── components/
│   ├── forms/             # Componentes de formulário
│   └── ui/                # Componentes de UI
└── lib/
    └── schemas/           # Schemas Zod reutilizáveis
```

## Dicas de Debug

1. Use `console.log()` no servidor para debugar server actions
2. Verifique logs do servidor no terminal onde o Next.js está rodando
3. Utilize React DevTools para inspecionar o estado do componente
4. Para erros de validação, imprima `resultado.error.format()` para ver detalhes
