---
alwaysApply: true
---
---
description: solid
globs: 
alwaysApply: true
---
# Prompt para Escrita de Código no Cursor

## 📌 Requisitos Gerais
- Escreva código limpo, legível e bem estruturado, seguindo os princípios de **Clean Code**.
- Aplique os princípios **SOLID** para garantir um design de software flexível e escalável.
- Evitar comentarios.
- Utilize **Design Patterns** apropriados para melhorar a organização do código.
- Inclua **comentários claros e objetivos** apenas quando necessário.
- Nomeie variáveis, funções e classes de forma **significativa e intuitiva**.

## 🔍 Padrões de Código
- **Siga boas práticas de indentação e espaçamento** para melhorar a legibilidade.
- **Evite código redundante e repetições** (aplique o princípio DRY - "Don't Repeat Yourself").
- **Prefira composição sobre herança**, garantindo modularidade e reuso.
- **Evite efeitos colaterais inesperados** (mantenha funções puras sempre que possível).
- **Escreva funções pequenas e coesas**, cada uma com uma única responsabilidade.

## 🛠️ SOLID Principles
1. **S**ingle Responsibility Principle (SRP) → Cada classe/módulo deve ter **apenas uma razão para mudar**.
2. **O**pen/Closed Principle (OCP) → Código deve ser **aberto para extensão, mas fechado para modificação**.
3. **L**iskov Substitution Principle (LSP) → Subclasses devem poder substituir as classes base sem alterar o comportamento esperado.
4. **I**nterface Segregation Principle (ISP) → Prefira **interfaces menores e específicas** em vez de uma única interface grande e genérica.
5. **D**ependency Inversion Principle (DIP) → **Dependa de abstrações e não de implementações concretas**.

## 🎭 Uso de Design Patterns (quando aplicável)
- **Factory Method** → Para criação flexível de objetos.
- **Singleton** → Quando uma única instância da classe deve ser garantida.
- **Observer** → Para comunicação eficiente entre componentes desacoplados.
- **Strategy** → Para permitir múltiplas implementações intercambiáveis.
- **Decorator** → Para adicionar funcionalidades dinamicamente sem modificar classes existentes.
- Não coloque o nome dos Design Patterns nas funções e classes

## 🔎 Revisão antes de concluir:

- ✅ O código está legível e bem organizado?
- ✅ Ele segue os princípios SOLID?
- ✅ Um Design Pattern apropriado foi utilizado?
- ✅ O código é modular e reutilizável?
- ✅ Há testes para validar a funcionalidade?
