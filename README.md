<h1 align="center">ApexSaaS</h1>

<p align="center">
 Sistema SaaS para academias de artes marciais (e outros no futuro)
</p>

<p align="center">
  <a href="#funcionalidades"><strong>Funcionalidades</strong></a> ·
  <a href="#arquitetura"><strong>Arquitetura</strong></a> ·
  <a href="#multi-tenancy"><strong>Multi-Tenancy</strong></a> ·
  <a href="#tecnologias"><strong>Tecnologias</strong></a>
</p>
<br/>

## Funcionalidades

- **Sistema Multi-Tenant** completo para academias de artes marciais
  - Isolamento de dados por academia através de subdomínios
  - Gerenciamento de múltiplas filiais por academia
  - Controle de acesso baseado em papéis (admin, instrutor, aluno)
- **Gestão de Alunos**
  - Cadastro e acompanhamento de alunos
  - Histórico de graduações e frequência
  - Gerenciamento de mensalidades
- **Gestão de Turmas e Aulas**
  - Agendamento de aulas
  - Controle de frequência
  - Avaliações e observações
- **Gestão Financeira**
  - Controle de mensalidades e pagamentos
  - Relatórios financeiros por filial
- **Painel Administrativo Completo**
  - Dashboard com métricas importantes
  - Gestão centralizada de todas as academias

## Arquitetura

O ApexSaaS utiliza uma arquitetura moderna e escalável:

- **Frontend**: Next.js com App Router, React Server Components e TypeScript
- **Backend**: Supabase para autenticação, banco de dados e armazenamento
- **Multi-Tenancy**: Implementação robusta com isolamento no nível do banco de dados
- **UI/UX**: Interface responsiva com Shadcn UI, Radix e Tailwind CSS
- **Optimizações**: Server Components, React Query para dados dinâmicos e caching

## Multi-Tenancy

O sistema implementa multi-tenancy através de:

- **Subdomínios Personalizados**: Cada academia possui seu próprio subdomínio (ex: `academia.apexsaas.com`)
- **Row-Level Security (RLS)**: Políticas avançadas de segurança no Supabase garantem isolamento de dados
- **Middleware**: Identificação e validação automática do tenant via subdomínio
- **Metadados Seguros**: O tenant_id é armazenado nos metadados do usuário autenticado

## Tecnologias

O projeto utiliza um stack moderno e poderoso:

- **Next.js 15**: Framework React com suporte a App Router
- **TypeScript**: Tipagem estática para código mais seguro
- **Supabase**: Backend as a Service para autenticação, banco de dados PostgreSQL e armazenamento
- **Shadcn UI / Radix UI**: Componentes de UI acessíveis e customizáveis
- **Tailwind CSS**: Framework CSS utility-first para estilização
- **React Query**: Gerenciamento de estado para dados do servidor
- **React Hook Form**: Gerenciamento de formulários com validação
- **Zod**: Validação de esquemas e tipagem em tempo de execução

## Estrutura do Projeto

```
/src
  /app             # Rotas da aplicação (Next.js App Router)
    /(auth-pages)  # Páginas de autenticação
    /protected     # Páginas protegidas por autenticação  
    /tenant        # Páginas específicas de cada tenant
  /components      # Componentes React reutilizáveis
  /hooks           # Hooks personalizados
  /lib             # Bibliotecas e utilitários
  /utils           # Funções utilitárias
```
