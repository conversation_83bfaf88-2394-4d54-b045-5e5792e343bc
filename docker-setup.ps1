# Cores para output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

Write-ColorOutput Blue "==== Configurando ambiente Docker para ApexSaaS ===="

# Verificar se o arquivo .env existe
if (-not (Test-Path .env)) {
    Write-ColorOutput Yellow "Arquivo .env não encontrado. Criando..."
    if (Test-Path .env.example) {
        Copy-Item .env.example .env
        Write-ColorOutput Green "Arquivo .env criado com sucesso!"
        Write-ColorOutput Yellow "Por favor, edite o arquivo .env com suas configurações antes de continuar."
        Write-ColorOutput Green "Para editar: notepad .env"
        exit
    }
    else {
        Write-ColorOutput Red "Arquivo .env.example não encontrado. Criando arquivo .env básico..."
        @"
# Configurações do Supabase
NEXT_PUBLIC_SUPABASE_URL=https://seu-projeto.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=sua-chave-anonima-do-supabase

# Configurações da aplicação
NEXT_PUBLIC_BASE_DOMAIN=localhost:3000
NODE_ENV=development
"@ | Out-File -FilePath .env -Encoding utf8
        Write-ColorOutput Green "Arquivo .env básico criado. Por favor, edite-o com suas configurações."
        Write-ColorOutput Green "Para editar: notepad .env"
        exit
    }
}

# Criar diretório para o Nginx se não existir
if (-not (Test-Path nginx)) {
    Write-ColorOutput Yellow "Criando diretório nginx..."
    New-Item -ItemType Directory -Path nginx | Out-Null
    Write-ColorOutput Green "Diretório nginx criado com sucesso!"
}

# Verificar se o arquivo de configuração do Nginx existe
if (-not (Test-Path nginx/default.conf)) {
    Write-ColorOutput Yellow "Arquivo de configuração do Nginx não encontrado. Criando..."
    @"
server {
    listen 80;
    server_name ~^(?<subdomain>.+)\.localhost$;

    location / {
        proxy_pass http://apexsaas-dev:3000;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        proxy_http_version 1.1;
        proxy_set_header Upgrade `$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass `$http_upgrade;
    }
}

server {
    listen 80;
    server_name localhost;

    location / {
        proxy_pass http://apexsaas-dev:3000;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        proxy_http_version 1.1;
        proxy_set_header Upgrade `$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass `$http_upgrade;
    }
}
"@ | Out-File -FilePath nginx/default.conf -Encoding utf8
    Write-ColorOutput Green "Arquivo de configuração do Nginx criado com sucesso!"
}

Write-ColorOutput Blue "Ambiente Docker configurado com sucesso!"
Write-ColorOutput Yellow "Para iniciar o ambiente de desenvolvimento:"
Write-ColorOutput Green "docker compose up apexsaas-dev nginx"
Write-ColorOutput Yellow "Para iniciar o ambiente de produção:"
Write-ColorOutput Green "docker compose up apexsaas-prod nginx"

# Adicione uma solução para configurar subdomínios no Windows
$hostsPath = "$env:SystemRoot\System32\drivers\etc\hosts"
$hasPermission = $false

try {
    $testWrite = [io.file]::OpenWrite($hostsPath)
    $testWrite.Close()
    $hasPermission = $true
} catch {
    $hasPermission = $false
}

if ($hasPermission) {
    Write-ColorOutput Yellow "Verificando configuração de subdomínios no arquivo hosts..."
    $hostsContent = Get-Content -Path $hostsPath
    
    $subdomains = @("academia1", "academia2")
    $needsUpdate = $false
    
    foreach ($subdomain in $subdomains) {
        $entry = "127.0.0.1 $subdomain.localhost"
        if ($hostsContent -notcontains $entry) {
            $needsUpdate = $true
        }
    }
    
    if ($needsUpdate) {
        Write-ColorOutput Yellow "Deseja adicionar entradas de subdomínios ao arquivo hosts? (S/N)"
        $response = Read-Host
        
        if ($response -eq "S" -or $response -eq "s") {
            try {
                foreach ($subdomain in $subdomains) {
                    $entry = "127.0.0.1 $subdomain.localhost"
                    if ($hostsContent -notcontains $entry) {
                        Add-Content -Path $hostsPath -Value $entry
                    }
                }
                Write-ColorOutput Green "Subdomínios adicionados com sucesso!"
            }
            catch {
                Write-ColorOutput Red "Erro ao atualizar o arquivo hosts. Você pode precisar executar como administrador."
            }
        }
    }
} else {
    Write-ColorOutput Yellow "IMPORTANTE: Para usar subdomínios locais, execute o PowerShell como administrador e adicione estas linhas ao arquivo hosts ($hostsPath):"
    Write-ColorOutput White "127.0.0.1 academia1.localhost"
    Write-ColorOutput White "127.0.0.1 academia2.localhost"
}

Write-ColorOutput Green "Tudo pronto! Execute 'docker compose up apexsaas-dev nginx' para iniciar." 