#!/bin/bash

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}==== Configurando ambiente Docker para ApexSaaS ====${NC}"

# Verificar se o arquivo .env existe
if [ ! -f .env ]; then
  echo -e "${YELLOW}Arquivo .env não encontrado. Criando a partir de .env.example...${NC}"
  if [ -f .env.example ]; then
    cp .env.example .env
    echo -e "${GREEN}Arquivo .env criado com sucesso!${NC}"
    echo -e "${YELLOW}Por favor, edite o arquivo .env com suas configurações antes de continuar.${NC}"
    echo -e "Para editar: ${GREEN}nano .env${NC}"
    exit 0
  else
    echo -e "${RED}Arquivo .env.example não encontrado. Criando arquivo .env básico...${NC}"
    cat > .env << EOF
# Configurações do Supabase
NEXT_PUBLIC_SUPABASE_URL=https://seu-projeto.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=sua-chave-anonima-do-supabase

# Configurações da aplicação
NEXT_PUBLIC_BASE_DOMAIN=localhost:3000
NODE_ENV=development
EOF
    echo -e "${GREEN}Arquivo .env básico criado. Por favor, edite-o com suas configurações.${NC}"
    echo -e "Para editar: ${GREEN}nano .env${NC}"
    exit 0
  fi
fi

# Criar diretório para o Nginx se não existir
if [ ! -d "nginx" ]; then
  echo -e "${YELLOW}Criando diretório nginx...${NC}"
  mkdir -p nginx
  echo -e "${GREEN}Diretório nginx criado com sucesso!${NC}"
fi

# Verificar se o arquivo de configuração do Nginx existe
if [ ! -f "nginx/default.conf" ]; then
  echo -e "${YELLOW}Arquivo de configuração do Nginx não encontrado. Criando...${NC}"
  cat > nginx/default.conf << EOF
server {
    listen 80;
    server_name ~^(?<subdomain>.+)\.localhost$;

    location / {
        proxy_pass http://apexsaas-dev:3000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass \$http_upgrade;
    }
}

server {
    listen 80;
    server_name localhost;

    location / {
        proxy_pass http://apexsaas-dev:3000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF
  echo -e "${GREEN}Arquivo de configuração do Nginx criado com sucesso!${NC}"
fi

echo -e "${BLUE}Ambiente Docker configurado com sucesso!${NC}"
echo -e "${YELLOW}Para iniciar o ambiente de desenvolvimento:${NC}"
echo -e "${GREEN}docker-compose up apexsaas-dev nginx${NC}"
echo -e "${YELLOW}Para iniciar o ambiente de produção:${NC}"
echo -e "${GREEN}docker-compose up apexsaas-prod nginx${NC}"

# Tornar o script executável
chmod +x docker-setup.sh

echo -e "${GREEN}Tudo pronto! Execute ./docker-setup.sh para configurar o ambiente.${NC}" 