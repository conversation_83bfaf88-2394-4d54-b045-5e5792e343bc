# Atualização: Trigger de Status Financeiro com Ciclo de Recorrência

## 📋 Problema Identificado

O trigger que atualiza o status financeiro dos estudantes na tabela `students` não estava considerando adequadamente o ciclo de recorrência dos pagamentos, resultando em status incorretos para estudantes com pagamentos futuros.

### Exemplo do Problema:
- **Estudante**: <PERSON>
- **Próximo pagamento**: 25/08/2025 (muito futuro)
- **Status atual**: `pending` (incorreto)
- **Status correto**: `up_to_date` (não há pagamentos urgentes)

## 🔧 Solução Implementada

### 1. Função Atualizada: `update_student_financial_status`

**Arquivos**: 
- `migrations/20250125_003_update_financial_status_trigger_recurrence.sql`
- `migrations/20250125_004_add_financial_status_recurrence_helpers.sql`

**Nova Lógica**:
```sql
-- Calcular apenas pagamentos URGENTES (próximos 7 dias)
SELECT 
    COALESCE(SUM(p.amount), 0) as urgent_amt
FROM public.payments p
WHERE p.student_id = student_id_param 
AND p.membership_id = membership_record.id
AND p.status = 'pending'
AND p.overdue_date IS NULL
AND p.due_date IS NOT NULL
AND p.due_date <= (today_date + INTERVAL '7 days')::date;
```

**Comportamento Corrigido**:
- ✅ **Pagamentos vencidos**: Status `overdue`
- ✅ **Pagamentos urgentes** (próximos 7 dias): Status `pending`
- ✅ **Pagamentos futuros** (mais de 7 dias): Status `up_to_date`
- ✅ **Sem matrícula ativa**: Status `no_data`

### 2. Funções Auxiliares Criadas

#### `check_students_financial_status_with_recurrence()`
- Verifica quais estudantes precisam de atualização de status
- Considera o ciclo de recorrência na análise
- Retorna comparação entre status atual e calculado

#### `process_financial_status_updates_with_recurrence()`
- Processa atualizações em lote
- Deve ser executada periodicamente
- Retorna estatísticas do processamento

## 🔄 Fluxo Corrigido

### Cenário 1: Pagamento Futuro (Mais de 7 dias)
1. **Estudante tem pagamento** para 25/08/2025
2. **Data atual**: 25/07/2025
3. **Trigger executa** → `update_student_financial_status()`
4. **Função verifica**: Pagamento não é urgente (> 7 dias)
5. **Status definido**: `up_to_date`

### Cenário 2: Pagamento Urgente (Próximos 7 dias)
1. **Estudante tem pagamento** para 30/07/2025
2. **Data atual**: 25/07/2025
3. **Trigger executa** → `update_student_financial_status()`
4. **Função verifica**: Pagamento é urgente (≤ 7 dias)
5. **Status definido**: `pending`

### Cenário 3: Pagamento Vencido
1. **Estudante tem pagamento** vencido em 20/07/2025
2. **Data atual**: 25/07/2025
3. **Trigger executa** → `update_student_financial_status()`
4. **Função verifica**: Pagamento está vencido
5. **Status definido**: `overdue`

## 📊 Monitoramento

### Verificar Estudantes que Precisam de Atualização
```sql
SELECT 
    student_id,
    current_status,
    calculated_status,
    needs_update,
    overdue_amount,
    urgent_amount,
    next_payment_due
FROM public.check_students_financial_status_with_recurrence()
WHERE needs_update = true;
```

### Processar Atualizações Manualmente
```sql
SELECT public.process_financial_status_updates_with_recurrence();
```

### Verificar Status de um Estudante Específico
```sql
SELECT public.update_student_financial_status('student-uuid-here');
```

## 🚀 Implementação de Processamento Automático

### Recomendação: Cron Job Diário

Para manter os status sempre atualizados, configure um cron job que execute diariamente:

```sql
-- Executar diariamente às 01:00
SELECT public.process_financial_status_updates_with_recurrence();
```

### Alternativa: Edge Function

Criar uma Edge Function que execute periodicamente:

```typescript
// edge-functions/update-financial-status/index.ts
import { createClient } from '@supabase/supabase-js'

Deno.serve(async (req) => {
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
  )

  const { data, error } = await supabase.rpc('process_financial_status_updates_with_recurrence')

  if (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }

  return new Response(JSON.stringify(data), {
    headers: { 'Content-Type': 'application/json' }
  })
})
```

## ✅ Benefícios da Atualização

1. **Precisão no Status**: Apenas pagamentos realmente urgentes afetam o status
2. **Alinhamento com Correção**: Segue a mesma lógica da correção de pagamentos antecipados
3. **Performance**: Mantém a lógica no banco de dados para melhor performance
4. **Flexibilidade**: Estudantes com pagamentos futuros não são marcados incorretamente
5. **Monitoramento**: Funções auxiliares permitem acompanhar e processar atualizações

## 🔍 Testes Recomendados

1. **Teste Pagamento Futuro**:
   - Criar estudante com pagamento para mais de 7 dias
   - Verificar que status é `up_to_date`

2. **Teste Pagamento Urgente**:
   - Criar estudante com pagamento para próximos 7 dias
   - Verificar que status é `pending`

3. **Teste Pagamento Vencido**:
   - Criar estudante com pagamento vencido
   - Verificar que status é `overdue`

4. **Teste Processamento em Lote**:
   - Executar `process_financial_status_updates_with_recurrence()`
   - Verificar estatísticas de processamento

## 📝 Notas Importantes

- A atualização é **retrocompatível** - não quebra funcionalidades existentes
- O trigger continua sendo executado automaticamente em mudanças de payments/memberships
- As funções auxiliares permitem processamento manual quando necessário
- A lógica alinha com o código TypeScript em `financial-status.ts`
- Recomenda-se executar processamento periódico para manter dados atualizados

## 🔗 Integração com Código TypeScript

A nova lógica do banco está alinhada com a implementação TypeScript:

- **Arquivo**: `src/app/(dashboard)/alunos/server/financial-status.ts`
- **Função**: `calculateStudentFinancialStatus()`
- **Lógica**: Pagamentos urgentes (próximos 7 dias) = status `pending`

Esta integração garante consistência entre cálculos no banco e na aplicação.
