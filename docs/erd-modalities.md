# ERD – Modalidades

```mermaid
erDiagram
    tenants ||--o{ tenant_modalities : "possui"
    modalities ||--o{ tenant_modalities : "associada"

    tenants {
      uuid id PK
      text name
      text slug
      timestamptz created_at
    }

    modalities {
      uuid id PK
      text slug
      text name
      timestamptz created_at
    }

    tenant_modalities {
      uuid tenant_id FK
      uuid modality_id FK
      boolean enabled
      timestamptz created_at
    }
```

## Explicação

1. **modalities** armazena todas as modalidades possíveis (ex.: Jiu-Jitsu Adulto, Infantil).  
2. **tenant_modalities** liga cada tenant às modalidades e controla se estão habilitadas (`enabled`).  
3. Ao adicionar novas modalidades, basta inserir na tabela `modalities`; as regras de UI e RLS permanecem intactas.

### Escalabilidade

* O design many-to-many permite múltiplas modalidades por academia hoje e futuras modalidades sem migrações estruturais.  
* <PERSON>ndice de chave composta `(tenant_id, modality_id)` na `tenant_modalities` garante lookup rápido.  
* Políticas RLS limitam escrita a admins do tenant, mantendo isolamento multi-tenant no próprio banco. 