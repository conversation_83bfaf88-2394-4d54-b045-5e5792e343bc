# Integração Evolution API - Sistema WhatsApp

## Visão Geral

Este documento detalha a integração com a Evolution API para o sistema de notificações via WhatsApp. A Evolution API é uma solução open-source para integração com WhatsApp Business.

## Configuração Inicial

### 1. Instalação e Setup da Evolution API

```bash
# Via Docker (Recomendado)
docker run -d \
  --name evolution-api \
  -p 8080:8080 \
  -e AUTHENTICATION_API_KEY=your_api_key \
  -e WEBHOOK_GLOBAL_URL=https://yourdomain.com/webhook/whatsapp \
  atendai/evolution-api:v2.1.1
```

### 2. Variáveis de Ambiente

```env
# Evolution API Configuration (Centralizadas no SaaS)
EVOLUTION_API_URL=https://your-evolution-api.com
EVOLUTION_API_KEY=your_global_api_key
EVOLUTION_WEBHOOK_SECRET=your_webhook_secret
EVOLUTION_INSTANCE_NAME=apexdojo-whatsapp

# WhatsApp Configuration
WHATSAPP_ENABLED=true
NOTIFICATIONS_WHATSAPP_RATE_LIMIT=100 # Mensagens por hora
```

### 3. Configuração Centralizada

O SaaS utilizará uma única instância WhatsApp compartilhada:
- `apexsaas-whatsapp` (instância única para todo o SaaS)
- Cada academia será identificada por prefixos nas mensagens
- Gerenciamento centralizado de contatos e opt-in/opt-out

## Arquitetura do Sistema

### 1. Cliente Evolution API

```typescript
// src/services/notifications/channels/whatsapp/evolution-api-client.ts
export class EvolutionApiClient {
  private baseUrl: string;
  private apiKey: string;

  constructor(baseUrl: string, apiKey: string) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  async createInstance(instanceData: CreateInstanceData): Promise<InstanceResult> {
    const response = await fetch(`${this.baseUrl}/instance/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': this.apiKey
      },
      body: JSON.stringify(instanceData)
    });

    if (!response.ok) {
      throw new Error(`Failed to create instance: ${response.statusText}`);
    }

    return response.json();
  }

  async sendMessage(instanceId: string, message: WhatsAppMessage): Promise<MessageResult> {
    const response = await fetch(`${this.baseUrl}/message/sendText/${instanceId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': this.apiKey
      },
      body: JSON.stringify(message)
    });

    if (!response.ok) {
      throw new Error(`Failed to send message: ${response.statusText}`);
    }

    return response.json();
  }

  async getInstanceStatus(instanceId: string): Promise<InstanceStatus> {
    const response = await fetch(`${this.baseUrl}/instance/connectionState/${instanceId}`, {
      headers: {
        'apikey': this.apiKey
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get instance status: ${response.statusText}`);
    }

    return response.json();
  }

  async validateNumber(instanceId: string, number: string): Promise<NumberValidation> {
    const response = await fetch(`${this.baseUrl}/chat/whatsappNumbers/${instanceId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': this.apiKey
      },
      body: JSON.stringify({ numbers: [number] })
    });

    if (!response.ok) {
      throw new Error(`Failed to validate number: ${response.statusText}`);
    }

    const result = await response.json();
    return {
      number,
      valid: result.length > 0 && result[0].exists,
      jid: result[0]?.jid
    };
  }

  async setupWebhook(instanceId: string, webhookConfig: WebhookConfig): Promise<void> {
    const response = await fetch(`${this.baseUrl}/webhook/set/${instanceId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': this.apiKey
      },
      body: JSON.stringify(webhookConfig)
    });

    if (!response.ok) {
      throw new Error(`Failed to setup webhook: ${response.statusText}`);
    }
  }

  async getQRCode(instanceId: string): Promise<QRCodeResult> {
    const response = await fetch(`${this.baseUrl}/instance/connect/${instanceId}`, {
      headers: {
        'apikey': this.apiKey
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get QR code: ${response.statusText}`);
    }

    return response.json();
  }
}

// Interfaces
export interface CreateInstanceData {
  instanceName: string;
  qrcode: boolean;
  integration: 'WHATSAPP-BAILEYS';
  webhookUrl?: string;
  webhookByEvents?: boolean;
  events?: string[];
}

export interface WhatsAppMessage {
  number: string;
  text: string;
  delay?: number;
}

export interface MessageResult {
  key: {
    remoteJid: string;
    fromMe: boolean;
    id: string;
  };
  message: any;
  messageTimestamp: number;
  status: string;
}

export interface InstanceStatus {
  instance: {
    instanceName: string;
    status: 'open' | 'close' | 'connecting';
  };
}

export interface NumberValidation {
  number: string;
  valid: boolean;
  jid?: string;
}

export interface WebhookConfig {
  url: string;
  webhook_by_events: boolean;
  webhook_base64: boolean;
  events: string[];
}

export interface QRCodeResult {
  base64: string;
  code: string;
}

export interface InstanceResult {
  instance: {
    instanceName: string;
    status: string;
  };
  hash: {
    apikey: string;
  };
  webhook: {
    webhook: string;
    events: string[];
  };
}
```

### 2. Serviço WhatsApp

```typescript
// src/services/notifications/channels/whatsapp/whatsapp-service.ts
export class WhatsAppService {
  private evolutionClient: EvolutionApiClient;

  constructor(apiUrl: string, apiKey: string) {
    this.evolutionClient = new EvolutionApiClient(apiUrl, apiKey);
  }

  async initializeSaaSInstance(): Promise<SaaSWhatsAppInstance> {
    const instanceName = 'apexsaas-whatsapp';

    // Verificar se instância já existe
    const supabase = await createClient();
    const { data: existingInstance } = await supabase
      .from('whatsapp_saas_instance')
      .select('*')
      .single();

    if (existingInstance && existingInstance.status === 'active') {
      return {
        instanceName: existingInstance.instance_name,
        instanceKey: existingInstance.instance_key,
        status: existingInstance.status,
        phoneNumber: existingInstance.phone_number
      };
    }

    const instanceData: CreateInstanceData = {
      instanceName,
      qrcode: true,
      integration: 'WHATSAPP-BAILEYS',
      webhookUrl: `${process.env.APP_URL}/api/webhooks/whatsapp`,
      webhookByEvents: false,
      events: [
        'QRCODE_UPDATED',
        'CONNECTION_UPDATE',
        'MESSAGES_UPSERT',
        'SEND_MESSAGE'
      ]
    };

    const result = await this.evolutionClient.createInstance(instanceData);

    // Salvar configuração no banco
    await supabase
      .from('whatsapp_saas_instance')
      .upsert({
        instance_name: instanceName,
        instance_key: result.hash.apikey,
        status: 'created',
        webhook_url: instanceData.webhookUrl,
        created_at: new Date().toISOString()
      });

    return {
      instanceName,
      instanceKey: result.hash.apikey,
      status: 'created',
      qrCode: await this.getQRCode(instanceName)
    };
  }

  async sendNotification(
    tenantId: string,
    phoneNumber: string,
    message: string
  ): Promise<WhatsAppNotificationResult> {
    try {
      // Buscar instância do SaaS
      const instance = await this.getSaaSInstance();
      if (!instance || instance.status !== 'active') {
        throw new Error('WhatsApp SaaS instance not available');
      }

      // Validar número
      const validation = await this.evolutionClient.validateNumber(
        instance.instance_name,
        phoneNumber
      );

      if (!validation.valid) {
        return {
          success: false,
          error: 'Invalid WhatsApp number',
          phoneNumber
        };
      }

      // Buscar dados da academia para personalizar mensagem
      const academyData = await this.getAcademyData(tenantId);
      const personalizedMessage = this.addAcademySignature(message, academyData);

      // Enviar mensagem
      const result = await this.evolutionClient.sendMessage(instance.instance_name, {
        number: phoneNumber,
        text: personalizedMessage,
        delay: 1000
      });

      // Registrar envio
      await this.logMessage(tenantId, phoneNumber, personalizedMessage, result);

      return {
        success: true,
        messageId: result.key.id,
        phoneNumber,
        timestamp: new Date(result.messageTimestamp * 1000)
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        phoneNumber
      };
    }
  }

  async getQRCode(): Promise<string> {
    const instance = await this.getSaaSInstance();
    if (!instance) {
      throw new Error('SaaS WhatsApp instance not found');
    }

    const result = await this.evolutionClient.getQRCode(instance.instance_name);
    return result.base64;
  }

  async getInstanceStatus(): Promise<WhatsAppInstanceStatus> {
    const instance = await this.getSaaSInstance();
    if (!instance) {
      return { status: 'not_found' };
    }

    const status = await this.evolutionClient.getInstanceStatus(instance.instance_name);
    return {
      status: status.instance.status,
      instanceName: instance.instance_name,
      lastUpdate: new Date()
    };
  }

  private async getSaaSInstance(): Promise<SaaSWhatsAppInstanceDB | null> {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from('whatsapp_saas_instance')
      .select('*')
      .single();

    if (error || !data) {
      return null;
    }

    return data;
  }

  private async getAcademyData(tenantId: string): Promise<AcademyData> {
    const supabase = await createClient();
    const { data } = await supabase
      .from('tenants')
      .select('name, slug')
      .eq('id', tenantId)
      .single();

    return {
      name: data?.name || 'Academia',
      slug: data?.slug || 'academia'
    };
  }

  private addAcademySignature(message: string, academyData: AcademyData): string {
    // Adicionar assinatura da academia se não estiver presente
    if (!message.includes(academyData.name)) {
      return `🥋 *${academyData.name}*\n\n${message}`;
    }
    return message;
  }

  private async logMessage(
    tenantId: string,
    phoneNumber: string,
    message: string,
    result: MessageResult
  ): Promise<void> {
    const supabase = await createClient();
    await supabase
      .from('whatsapp_message_logs')
      .insert({
        tenant_id: tenantId,
        phone_number: phoneNumber,
        message_content: message,
        message_id: result.key.id,
        status: 'sent',
        timestamp: new Date(result.messageTimestamp * 1000).toISOString(),
        created_at: new Date().toISOString()
      });
  }
}

// Interfaces
export interface SaaSWhatsAppInstance {
  instanceName: string;
  instanceKey: string;
  status: string;
  phoneNumber?: string;
  qrCode?: string;
}

export interface SaaSWhatsAppInstanceDB {
  instance_name: string;
  instance_key: string;
  status: string;
  phone_number?: string;
  webhook_url: string;
  connected_at?: string;
  last_seen?: string;
  created_at: string;
  updated_at?: string;
}

export interface AcademyData {
  name: string;
  slug: string;
}

export interface WhatsAppNotificationResult {
  success: boolean;
  messageId?: string;
  phoneNumber: string;
  timestamp?: Date;
  error?: string;
}

export interface WhatsAppInstanceStatus {
  status: 'open' | 'close' | 'connecting' | 'not_found';
  instanceName?: string;
  lastUpdate?: Date;
}
```

### 3. Templates de Mensagem

```typescript
// src/services/notifications/channels/whatsapp/templates/whatsapp-templates.ts
export class WhatsAppTemplateEngine {
  static renderPaymentReminder(data: PaymentReminderData): string {
    const { studentName, amount, dueDate, academyName, daysUntilDue } = data;
    
    if (daysUntilDue <= 0) {
      return `🚨 *${academyName}*

Olá, ${studentName}!

Seu pagamento está *${Math.abs(daysUntilDue)} dia(s) em atraso*.

💰 Valor: R$ ${amount.toFixed(2).replace('.', ',')}
📅 Vencimento: ${new Date(dueDate).toLocaleDateString('pt-BR')}

Por favor, regularize sua situação o quanto antes.

Em caso de dúvidas, entre em contato conosco.`;
    }

    if (daysUntilDue <= 3) {
      return `⚠️ *${academyName}*

Olá, ${studentName}!

Lembramos que seu pagamento vence em *${daysUntilDue} dia(s)*.

💰 Valor: R$ ${amount.toFixed(2).replace('.', ',')}
📅 Vencimento: ${new Date(dueDate).toLocaleDateString('pt-BR')}

Não esqueça de efetuar o pagamento!`;
    }

    return `📋 *${academyName}*

Olá, ${studentName}!

Lembramos que seu pagamento vence em ${daysUntilDue} dias.

💰 Valor: R$ ${amount.toFixed(2).replace('.', ',')}
📅 Vencimento: ${new Date(dueDate).toLocaleDateString('pt-BR')}

Obrigado!`;
  }

  static renderClassReminder(data: ClassReminderData): string {
    const { studentName, className, classDate, classTime, academyName } = data;
    
    return `🥋 *${academyName}*

Olá, ${studentName}!

Lembrete da sua aula:

📚 Aula: ${className}
📅 Data: ${new Date(classDate).toLocaleDateString('pt-BR')}
🕐 Horário: ${classTime}

Nos vemos lá!`;
  }

  static renderClassCancellation(data: ClassCancellationData): string {
    const { studentName, className, classDate, reason, academyName } = data;
    
    return `❌ *${academyName}*

Olá, ${studentName}!

Infelizmente precisamos cancelar a aula:

📚 Aula: ${className}
📅 Data: ${new Date(classDate).toLocaleDateString('pt-BR')}
❓ Motivo: ${reason}

Entraremos em contato para reagendar.

Desculpe o transtorno!`;
  }

  static renderWelcomeMessage(data: WelcomeMessageData): string {
    const { studentName, academyName } = data;
    
    return `🎉 *Bem-vindo(a) à ${academyName}!*

Olá, ${studentName}!

É um prazer tê-lo(a) conosco! 

Você receberá notificações importantes sobre:
• Lembretes de aulas
• Avisos de pagamento
• Eventos especiais
• Informações gerais

Para parar de receber mensagens, responda *SAIR*.

Vamos começar essa jornada juntos! 🥋`;
  }
}

// Interfaces para templates
export interface PaymentReminderData {
  studentName: string;
  amount: number;
  dueDate: string;
  academyName: string;
  daysUntilDue: number;
}

export interface ClassReminderData {
  studentName: string;
  className: string;
  classDate: string;
  classTime: string;
  academyName: string;
}

export interface ClassCancellationData {
  studentName: string;
  className: string;
  classDate: string;
  reason: string;
  academyName: string;
}

export interface WelcomeMessageData {
  studentName: string;
  academyName: string;
}
```

## Estrutura do Banco de Dados

### 1. Tabelas WhatsApp

```sql
-- Instância única do SaaS para WhatsApp
CREATE TABLE whatsapp_saas_instance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  instance_name VARCHAR(100) NOT NULL UNIQUE DEFAULT 'apexsaas-whatsapp',
  instance_key VARCHAR(255) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'created', -- 'created', 'active', 'disconnected', 'error'
  webhook_url TEXT,
  qr_code TEXT, -- Base64 do QR Code
  phone_number VARCHAR(20), -- Número conectado
  connected_at TIMESTAMP WITH TIME ZONE,
  last_seen TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE
);

-- Contatos WhatsApp com opt-in/opt-out (agora com identificação da academia)
CREATE TABLE whatsapp_contacts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  phone_number VARCHAR(20) NOT NULL,
  whatsapp_jid VARCHAR(100), -- JID do WhatsApp
  opted_in BOOLEAN DEFAULT false,
  opted_in_at TIMESTAMP WITH TIME ZONE,
  opted_out_at TIMESTAMP WITH TIME ZONE,
  opt_out_reason VARCHAR(100),
  last_message_at TIMESTAMP WITH TIME ZONE,
  is_valid BOOLEAN DEFAULT true, -- Número válido no WhatsApp
  academy_name VARCHAR(100), -- Nome da academia para identificação
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE,

  UNIQUE(tenant_id, user_id),
  UNIQUE(phone_number) -- Número único globalmente (uma pessoa pode estar em apenas uma academia)
);

-- Log de mensagens enviadas
CREATE TABLE whatsapp_message_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  contact_id UUID REFERENCES whatsapp_contacts(id) ON DELETE SET NULL,
  phone_number VARCHAR(20) NOT NULL,
  message_content TEXT NOT NULL,
  message_id VARCHAR(100), -- ID da mensagem na Evolution API
  message_type VARCHAR(20) DEFAULT 'text', -- 'text', 'image', 'document'
  status VARCHAR(20) NOT NULL, -- 'sent', 'delivered', 'read', 'failed'
  error_message TEXT,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  delivered_at TIMESTAMP WITH TIME ZONE,
  read_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  INDEX idx_whatsapp_logs_tenant_phone (tenant_id, phone_number),
  INDEX idx_whatsapp_logs_status (status),
  INDEX idx_whatsapp_logs_timestamp (timestamp DESC)
);

-- Configurações de horário permitido
CREATE TABLE whatsapp_sending_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  allowed_start_time TIME NOT NULL DEFAULT '08:00:00',
  allowed_end_time TIME NOT NULL DEFAULT '22:00:00',
  allowed_days INTEGER[] DEFAULT ARRAY[1,2,3,4,5,6,7], -- 1=Segunda, 7=Domingo
  timezone VARCHAR(50) DEFAULT 'America/Sao_Paulo',
  max_messages_per_day INTEGER DEFAULT 5,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE,
  
  UNIQUE(tenant_id)
);
```

## Webhook Handler

### 1. Processamento de Webhooks

```typescript
// src/app/api/webhooks/whatsapp/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/services/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Verificar origem do webhook
    const apiKey = request.headers.get('apikey');
    if (!apiKey || apiKey !== process.env.EVOLUTION_API_KEY) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Processar evento baseado no tipo
    await processWhatsAppEvent(body);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('WhatsApp webhook error:', error);
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}

async function processWhatsAppEvent(event: any) {
  const { event: eventType, instance, data } = event;
  
  switch (eventType) {
    case 'qrcode.updated':
      await handleQRCodeUpdate(instance, data);
      break;
      
    case 'connection.update':
      await handleConnectionUpdate(instance, data);
      break;
      
    case 'messages.upsert':
      await handleMessageReceived(instance, data);
      break;
      
    case 'send.message':
      await handleMessageSent(instance, data);
      break;
      
    default:
      console.log(`Unhandled event type: ${eventType}`);
  }
}

async function handleQRCodeUpdate(instance: string, data: any) {
  const supabase = await createClient();

  await supabase
    .from('whatsapp_saas_instance')
    .update({
      qr_code: data.qrcode,
      updated_at: new Date().toISOString()
    })
    .eq('instance_name', instance);
}

async function handleConnectionUpdate(instance: string, data: any) {
  const supabase = await createClient();

  const updates: any = {
    status: data.state === 'open' ? 'active' : 'disconnected',
    updated_at: new Date().toISOString()
  };

  if (data.state === 'open') {
    updates.connected_at = new Date().toISOString();
    updates.phone_number = data.instance?.wuid?.split('@')[0];
    updates.last_seen = new Date().toISOString();
  }

  await supabase
    .from('whatsapp_saas_instance')
    .update(updates)
    .eq('instance_name', instance);
}

async function handleMessageReceived(instance: string, data: any) {
  // Processar mensagens recebidas (opt-out, comandos, etc.)
  const message = data.messages?.[0];
  if (!message || message.key.fromMe) return;
  
  const phoneNumber = message.key.remoteJid.split('@')[0];
  const messageText = message.message?.conversation || 
                     message.message?.extendedTextMessage?.text || '';
  
  // Verificar comandos de opt-out
  if (messageText.toLowerCase().includes('sair') || 
      messageText.toLowerCase().includes('parar')) {
    await handleOptOut(instance, phoneNumber);
  }
}

async function handleMessageSent(instance: string, data: any) {
  // Atualizar status de mensagens enviadas
  const supabase = await createClient();
  
  await supabase
    .from('whatsapp_message_logs')
    .update({
      status: 'delivered',
      delivered_at: new Date().toISOString()
    })
    .eq('message_id', data.key.id);
}

async function handleOptOut(instance: string, phoneNumber: string) {
  const supabase = await createClient();

  // Verificar se a instância é a do SaaS
  if (instance !== 'apexsaas-whatsapp') return;

  // Marcar contato como opt-out (globalmente, para todas as academias)
  await supabase
    .from('whatsapp_contacts')
    .update({
      opted_in: false,
      opted_out_at: new Date().toISOString(),
      opt_out_reason: 'user_request',
      updated_at: new Date().toISOString()
    })
    .eq('phone_number', phoneNumber);

  // Enviar mensagem de confirmação
  const evolutionClient = new EvolutionApiClient(
    process.env.EVOLUTION_API_URL!,
    process.env.EVOLUTION_API_KEY!
  );

  await evolutionClient.sendMessage(instance, {
    number: phoneNumber,
    text: '✅ Você foi removido da lista de notificações WhatsApp do Apex SaaS. Para voltar a receber mensagens, entre em contato com sua academia.',
    delay: 1000
  });
}
```

## Compliance e Regulamentações

### 1. Sistema de Opt-in/Opt-out

```typescript
// src/services/notifications/channels/whatsapp/compliance-service.ts
export class WhatsAppComplianceService {
  async requestOptIn(tenantId: string, userId: string, phoneNumber: string): Promise<OptInResult> {
    const supabase = await createClient();
    
    // Verificar se já existe contato
    const { data: existingContact } = await supabase
      .from('whatsapp_contacts')
      .select('*')
      .eq('tenant_id', tenantId)
      .eq('user_id', userId)
      .single();
    
    if (existingContact?.opted_in) {
      return { success: true, alreadyOptedIn: true };
    }
    
    // Criar/atualizar contato
    await supabase
      .from('whatsapp_contacts')
      .upsert({
        tenant_id: tenantId,
        user_id: userId,
        phone_number: phoneNumber,
        opted_in: true,
        opted_in_at: new Date().toISOString(),
        opted_out_at: null,
        opt_out_reason: null,
        updated_at: new Date().toISOString()
      });
    
    // Enviar mensagem de boas-vindas
    const whatsappService = new WhatsAppService(
      process.env.EVOLUTION_API_URL!,
      process.env.EVOLUTION_API_KEY!
    );
    
    const { data: user } = await supabase
      .from('users')
      .select('first_name')
      .eq('id', userId)
      .single();
    
    const { data: tenant } = await supabase
      .from('tenants')
      .select('name')
      .eq('id', tenantId)
      .single();
    
    const welcomeMessage = WhatsAppTemplateEngine.renderWelcomeMessage({
      studentName: user?.first_name || 'Aluno',
      academyName: tenant?.name || 'Academia'
    });
    
    await whatsappService.sendNotification(tenantId, phoneNumber, welcomeMessage);
    
    return { success: true, alreadyOptedIn: false };
  }

  async checkOptInStatus(tenantId: string, userId: string): Promise<OptInStatus> {
    const supabase = await createClient();
    
    const { data: contact } = await supabase
      .from('whatsapp_contacts')
      .select('opted_in, opted_in_at, opted_out_at')
      .eq('tenant_id', tenantId)
      .eq('user_id', userId)
      .single();
    
    if (!contact) {
      return { optedIn: false, status: 'not_registered' };
    }
    
    return {
      optedIn: contact.opted_in,
      status: contact.opted_in ? 'opted_in' : 'opted_out',
      optedInAt: contact.opted_in_at,
      optedOutAt: contact.opted_out_at
    };
  }

  async isAllowedToSend(tenantId: string, phoneNumber: string): Promise<boolean> {
    const supabase = await createClient();
    
    // Verificar opt-in
    const { data: contact } = await supabase
      .from('whatsapp_contacts')
      .select('opted_in')
      .eq('tenant_id', tenantId)
      .eq('phone_number', phoneNumber)
      .single();
    
    if (!contact?.opted_in) {
      return false;
    }
    
    // Verificar horário permitido
    const { data: rules } = await supabase
      .from('whatsapp_sending_rules')
      .select('*')
      .eq('tenant_id', tenantId)
      .single();
    
    if (rules) {
      const now = new Date();
      const currentTime = now.toTimeString().slice(0, 8);
      const currentDay = now.getDay() || 7; // Domingo = 7
      
      if (!rules.allowed_days.includes(currentDay)) {
        return false;
      }
      
      if (currentTime < rules.allowed_start_time || currentTime > rules.allowed_end_time) {
        return false;
      }
      
      // Verificar limite diário
      const today = now.toISOString().split('T')[0];
      const { count } = await supabase
        .from('whatsapp_message_logs')
        .select('*', { count: 'exact', head: true })
        .eq('tenant_id', tenantId)
        .eq('phone_number', phoneNumber)
        .gte('timestamp', `${today}T00:00:00Z`)
        .lt('timestamp', `${today}T23:59:59Z`);
      
      if (count && count >= rules.max_messages_per_day) {
        return false;
      }
    }
    
    return true;
  }
}

// Interfaces
export interface OptInResult {
  success: boolean;
  alreadyOptedIn: boolean;
  error?: string;
}

export interface OptInStatus {
  optedIn: boolean;
  status: 'not_registered' | 'opted_in' | 'opted_out';
  optedInAt?: string;
  optedOutAt?: string;
}
```

## Vantagens da Arquitetura Centralizada

### ✅ **Benefícios Operacionais**
- **Custo Reduzido**: Uma única instância ao invés de N instâncias
- **Gerenciamento Simplificado**: Um único QR Code para conectar
- **Manutenção Centralizada**: Updates e configurações em um só lugar
- **Monitoramento Unificado**: Logs e métricas centralizados

### ✅ **Benefícios Técnicos**
- **Escalabilidade**: Suporta milhares de academias em uma instância
- **Confiabilidade**: Menos pontos de falha
- **Performance**: Melhor utilização de recursos
- **Compliance**: Controle centralizado de opt-in/opt-out

### ✅ **Benefícios para o Usuário**
- **Experiência Consistente**: Mesmo número WhatsApp para todas as academias
- **Opt-out Global**: Usuário sai de todas as academias de uma vez
- **Identificação Clara**: Mensagens sempre identificam a academia

### ⚠️ **Considerações Importantes**
- **Identificação Obrigatória**: Todas as mensagens devem identificar a academia
- **Opt-out Global**: Usuário que sair não recebe de nenhuma academia
- **Rate Limiting**: Controle de envio para evitar bloqueios
- **Backup**: Plano de contingência se a instância cair

## Próximos Passos

1. **Setup Evolution API** - Configurar instância única da Evolution API
2. **Implementar Cliente** - Criar EvolutionApiClient centralizado
3. **Configurar Webhooks** - Setup de recebimento de eventos
4. **Criar Templates** - Implementar templates com identificação da academia
5. **Sistema de Compliance** - Opt-in/opt-out global obrigatório
6. **Testes** - Validar envio e recebimento com múltiplas academias
7. **Monitoramento** - Logs e analytics centralizados

---

**Documento criado em:** 2025-01-26  
**Versão:** 1.0  
**Status:** Pronto para Implementação
