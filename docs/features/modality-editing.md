# Edição de Modalidades e Ranks

## Visão Geral

A funcionalidade de edição de modalidades permite que administradores configurem detalhes específicos de cada modalidade e definam requisitos para cada rank/faixa dentro da modalidade.

## Funcionalidades Implementadas

### 1. Configurações da Modalidade

- **Program Name**: Nome da modalidade
- **Type**: <PERSON><PERSON><PERSON> da modalidade (Martial Arts, Fitness, Dance, Other)
- **Level/Rank Style**: Estilo do sistema de graduação (Brazilian Jiu-Jitsu, Judo, Karate, etc.)
- **Secondary Color**: Configuração da cor secundária da faixa
- **Auto Assign Initial Rank**: Atribuição automática do rank inicial
- **Promotion Setting**: Configuração de promoção (Manual/Automática)
- **Promotion Fee**: Taxa de promoção

### 2. Configurações dos Ranks

Para cada rank/faixa, é possível configurar:

- **Sessions**: Número de sessões necessárias
- **Hours**: Horas de treino necessárias
- **Days in Rank**: Dias mínimos na faixa atual
- **Days Attended**: Dias de frequência necessários
- **Skill Requirements**: Se há requisitos de habilidade
- **Minimum Age**: Idade mínima para o rank
- **Promotion Fee**: Taxa específica para promoção neste rank

## Estrutura do Banco de Dados

### Tabelas Criadas

1. **`tenant_modality_settings`**
   - Armazena configurações específicas de cada modalidade por tenant
   - Relaciona tenant_id + modality_id
   - Campos: type, level_rank_style, secondary_color, auto_assign_initial_rank, promotion_setting, promotion_fee

2. **`tenant_belt_level_requirements`**
   - Armazena requisitos específicos de cada rank por tenant
   - Relaciona tenant_id + belt_level_id
   - Campos: sessions, hours, days_in_rank, days_attended, skill_requirements, minimum_age, promotion_fee

## Arquitetura da Solução

### Services

- **`/services/modalities/settings.ts`**: Gerencia configurações de modalidades
- **`/services/belts/requirements.ts`**: Gerencia requisitos de ranks

### Server Actions

- **`update-modality-settings.ts`**: Atualiza configurações da modalidade
- **`upsert-rank.ts`**: Cria/atualiza requisitos de ranks

### Componentes

- **`ModalityEditForm`**: Formulário principal de edição
- **`RanksList`**: Lista de ranks com suas configurações
- **`RankEditDialog`**: Dialog para editar requisitos de um rank específico

## Como Usar

### Acessar a Edição

1. Vá para **Academia > Configurações > Modalidades**
2. Clique no ícone de engrenagem (⚙️) ao lado da modalidade desejada
3. Você será redirecionado para `/academia/configuracoes/modalidades/[slug]/editar`

### Editar Configurações da Modalidade

1. Preencha os campos na seção "Program Details"
2. Configure as opções de promoção
3. Clique em "Save Program Details"

### Editar Requisitos dos Ranks

1. Na seção "Levels", clique no botão "Edit" ao lado do rank desejado
2. Configure os requisitos necessários
3. Clique em "Save Requirements"

## Considerações Técnicas

### Multitenancy

- Todas as configurações são específicas por tenant
- Cada academia pode ter configurações diferentes para a mesma modalidade
- As configurações não afetam outras academias

### Validação

- Todos os campos numéricos devem ser >= 0
- Campos obrigatórios são validados no cliente e servidor
- Uso do Zod para validação de schemas

### Performance

- Carregamento otimizado com Server Components
- Estados de loading e erro tratados adequadamente
- Revalidação automática após atualizações

## Próximos Passos

1. **Testes de Integração**: Verificar funcionamento completo
2. **Validação de Regras de Negócio**: Implementar validações específicas
3. **Histórico de Mudanças**: Registrar alterações para auditoria
4. **Importação/Exportação**: Permitir backup e restauração de configurações 