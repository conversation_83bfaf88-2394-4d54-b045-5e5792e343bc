# Implementação do Módulo de Instrutores

## Visão Geral

Este documento descreve a implementação do módulo de gestão de instrutores/professores da academia. O módulo seguirá uma estrutura semelhante ao módulo de alunos já existente, adaptado para as necessidades específicas do gerenciamento de instrutores.

## Requisitos Funcionais

1. **Listagem de Instrutores**
   - Visualização de todos os instrutores cadastrados
   - Filtros por nome, status, especialidade, etc.
   - Paginação para navegação eficiente

2. **Cadastro de Instrutores**
   - Formulário para inserção de novos instrutores
   - Validação de dados utilizando Zod
   - Upload de documentos e fotos

3. **Edição de Instrutores**
   - Alteração de dados cadastrais
   - Atualização de status e permissões
   - Gestão de especialidades e graduações

4. **Visualização Detalhada**
   - Perfil completo do instrutor
   - Histórico de aulas ministradas
   - Alunos associados

5. **Gerenciamento de Permissões**
   - Definição de níveis de acesso
   - Utilização do sistema de permissões existente

6. **Gestão de Contratos e Pagamentos**
   - Registro do tipo de vínculo (CLT, PJ, Autônomo, Parceria)
   - Controle de remuneração (por hora, percentual, fixo)
   - Histórico de pagamentos realizados

## Estrutura de Diretórios

```
src/app/(dashboard)/instrutores/
├── actions/
│   ├── import-export/      # Ações para importação/exportação
│   └── schemas/            # Schemas de validação Zod
├── components/
│   ├── actions/            # Componentes de ações na UI
│   ├── errors/             # Componentes de tratamento de erros
│   ├── filters/            # Filtros da listagem
│   ├── form/               # Componentes de formulário
│   ├── list/               # Componentes da listagem
│   └── pagination/         # Paginação
├── hooks/                  # Hooks personalizados
├── novo/                   # Subpágina para criação
│   ├── actions/            # Server actions específicas
│   │   └── schemas/        # Schemas de validação
│   └── components/         # Componentes do formulário
│       └── form-sections/  # Seções do formulário
├── server/                 # Lógica do servidor
└── types/                  # Definições de tipos
```

## Arquivos Base

1. **page.tsx** - Página principal de listagem de instrutores
2. **layout.tsx** - Layout específico para o módulo
3. **loading.tsx** - Estado de carregamento
4. **error.tsx** - Tratamento de erros
5. **types.ts** - Tipos relacionados a instrutores

## Modelo de Dados

### Contexto de Negócio

De acordo com a pesquisa, a contratação de instrutores no Brasil é flexível, com diferentes modelos:

1. **CLT**: Emprego formal com carteira assinada (comum em redes grandes/franquias)
2. **PJ/Autônomo**: Contrato de prestação de serviço sem subordinação CLT
3. **Parceria/Associação**: Instrutor como sócio ou parceiro da academia

Os modelos de remuneração variam conforme o contrato:
- Pagamento por aula ministrada (valor fixo por hora ou por turma)
- Comissão (percentual) sobre as mensalidades dos alunos
- Salário mensal fixo (geralmente em contratos CLT)
- Isenção da própria mensalidade como forma de compensação

### Tabela `instructors`

```sql
CREATE TABLE instructors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  tenant_id UUID REFERENCES tenants(id) NOT NULL,
  branch_id UUID REFERENCES branches(id) NOT NULL,
  
  -- Dados básicos do instrutor
  specialties TEXT[],
  certification_level TEXT,
  federation_registration TEXT,
  experience_years INTEGER,
  bio TEXT,
  
  -- Dados de contratação
  contract_type TEXT NOT NULL, -- 'clt', 'pj', 'autonomo', 'parceria'
  payment_model TEXT, -- 'hourly', 'percentage', 'fixed', 'exempt'
  payment_value NUMERIC,
  payment_percentage NUMERIC,
  
  -- Certificações e qualificações
  has_first_aid_certification BOOLEAN DEFAULT false,
  has_cpr_certification BOOLEAN DEFAULT false,
  has_rules_course BOOLEAN DEFAULT false,
  
  -- Datas e status
  hire_date TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  deleted_at TIMESTAMP WITH TIME ZONE,
  
  -- Campos para busca
  search_vector TSVECTOR,
  
  -- Referência à faixa atual
  current_belt_id UUID REFERENCES instructor_belts(id),
  
  -- Informações pessoais adicionais
  birth_date DATE, -- Data de nascimento do instrutor
  gender TEXT, -- Gênero do instrutor
  
  -- Endereço
  street TEXT, -- Rua do endereço do instrutor
  street_number TEXT, -- Número do endereço do instrutor
  complement TEXT, -- Complemento do endereço do instrutor
  neighborhood TEXT, -- Bairro do endereço do instrutor
  city TEXT, -- Cidade do endereço do instrutor
  state TEXT, -- Estado do endereço do instrutor
  postal_code TEXT, -- CEP do endereço do instrutor
  
  -- Contato de emergência
  emergency_contact_name TEXT, -- Nome do contato de emergência
  emergency_contact_phone TEXT, -- Telefone do contato de emergência
  emergency_contact_relationship TEXT -- Relação do contato de emergência com o instrutor
);

-- Índices
CREATE INDEX idx_instructors_user_id ON instructors(user_id);
CREATE INDEX idx_instructors_tenant_id ON instructors(tenant_id);
CREATE INDEX idx_instructors_branch_id ON instructors(branch_id);
CREATE INDEX idx_instructors_status ON instructors(status);
CREATE INDEX idx_instructors_contract_type ON instructors(contract_type);
CREATE INDEX idx_instructors_search_vector ON instructors USING gin(search_vector);
```

### Tabela `instructor_belts`

Esta tabela armazena o histórico de faixas/graduações dos instrutores, seguindo uma estrutura similar à tabela `student_belts` existente:

```sql
CREATE TABLE instructor_belts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) NOT NULL,
  instructor_id UUID REFERENCES instructors(id) NOT NULL,
  belt_color TEXT NOT NULL, -- 'white', 'blue', 'purple', 'brown', 'black'
  degree INTEGER NOT NULL DEFAULT 0,
  awarded_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  awarded_by UUID REFERENCES users(id),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Índices
CREATE INDEX idx_instructor_belts_instructor_id ON instructor_belts(instructor_id);
CREATE INDEX idx_instructor_belts_tenant_id ON instructor_belts(tenant_id);
CREATE INDEX idx_instructor_belts_belt_color ON instructor_belts(belt_color);
```

### Tabela `instructor_classes`

Esta tabela associa instrutores às turmas/aulas que ministram:

```sql
CREATE TABLE instructor_classes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  instructor_id UUID REFERENCES instructors(id) NOT NULL,
  class_id UUID REFERENCES classes(id) NOT NULL,
  is_main_instructor BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  UNIQUE(instructor_id, class_id)
);

CREATE INDEX idx_instructor_classes_instructor_id ON instructor_classes(instructor_id);
CREATE INDEX idx_instructor_classes_class_id ON instructor_classes(class_id);
```

### Tabela `instructor_payments`

Registro de pagamentos realizados aos instrutores:

```sql
CREATE TABLE instructor_payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  instructor_id UUID REFERENCES instructors(id) NOT NULL,
  tenant_id UUID REFERENCES tenants(id) NOT NULL,
  amount NUMERIC NOT NULL,
  payment_date TIMESTAMP WITH TIME ZONE NOT NULL,
  payment_period_start TIMESTAMP WITH TIME ZONE,
  payment_period_end TIMESTAMP WITH TIME ZONE,
  description TEXT,
  status TEXT DEFAULT 'paid',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE INDEX idx_instructor_payments_instructor_id ON instructor_payments(instructor_id);
CREATE INDEX idx_instructor_payments_tenant_id ON instructor_payments(tenant_id);
CREATE INDEX idx_instructor_payments_payment_date ON instructor_payments(payment_date);
```

### Tabela `instructor_documents`

Documentos relacionados aos instrutores (contratos, certificados, etc.):

```sql
CREATE TABLE instructor_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  instructor_id UUID REFERENCES instructors(id) NOT NULL,
  tenant_id UUID REFERENCES tenants(id) NOT NULL,
  document_type TEXT NOT NULL,
  document_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  upload_date TIMESTAMP WITH TIME ZONE DEFAULT now(),
  expiry_date TIMESTAMP WITH TIME ZONE,
  status TEXT DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE INDEX idx_instructor_documents_instructor_id ON instructor_documents(instructor_id);
CREATE INDEX idx_instructor_documents_tenant_id ON instructor_documents(tenant_id);
CREATE INDEX idx_instructor_documents_document_type ON instructor_documents(document_type);
```

> **Nota Importante**: As tabelas `instructor_classes`, `instructor_payments` e `instructor_documents` descritas acima não serão implementadas na fase inicial do projeto. Elas estão documentadas como parte do planejamento de longo prazo e poderão ser criadas em iterações futuras conforme a necessidade do sistema evoluir.

### Relações com Tabelas Existentes

#### Tabela `users`

Os instrutores são usuários do sistema, portanto têm um registro na tabela `users`. Na tabela existente, já temos o campo `role` que permite o valor 'instructor':

```
-- Na tabela users existente temos:
role UserRole NOT NULL -- Enum com valores: 'admin', 'instructor', 'student'
```

#### Tabela `classes`

A tabela `classes` existente contém o campo `instructor_id` que se refere ao instrutor principal da turma. Para suportar múltiplos instrutores por turma, utilizaremos a tabela `instructor_classes`.

### Políticas de RLS (Row Level Security)

```sql
-- Política para visualização de instrutores
CREATE POLICY "Instrutores visíveis para usuários do mesmo tenant" 
ON instructors FOR SELECT 
TO authenticated 
USING (tenant_id = auth.jwt() -> 'tenant_id');

-- Política para inserção de instrutores
CREATE POLICY "Apenas administradores podem adicionar instrutores" 
ON instructors FOR INSERT 
TO authenticated 
WITH CHECK (
  tenant_id = auth.jwt() -> 'tenant_id' AND 
  EXISTS (
    SELECT 1 FROM user_permissions up 
    WHERE up.user_id = auth.uid() 
    AND up.permission = 'instructor:create'
  )
);

-- Política para atualização de instrutores
CREATE POLICY "Apenas administradores podem atualizar instrutores" 
ON instructors FOR UPDATE 
TO authenticated 
USING (
  tenant_id = auth.jwt() -> 'tenant_id' AND 
  EXISTS (
    SELECT 1 FROM user_permissions up 
    WHERE up.user_id = auth.uid() 
    AND up.permission = 'instructor:update'
  )
);

-- Política para exclusão de instrutores
CREATE POLICY "Apenas administradores podem excluir instrutores" 
ON instructors FOR DELETE 
TO authenticated 
USING (
  tenant_id = auth.jwt() -> 'tenant_id' AND 
  EXISTS (
    SELECT 1 FROM user_permissions up 
    WHERE up.user_id = auth.uid() 
    AND up.permission = 'instructor:delete'
  )
);
```

## Fluxo de Implementação

1. **Fase 1: Estrutura Básica**
   - Criar estrutura de diretórios
   - Implementar arquivos base (page, layout, loading, error)
   - Criar tipos básicos

2. **Fase 2: Banco de Dados**
   - Criar tabela `instructors` no Supabase
   - Criar tabelas relacionadas (`instructor_belts`, `instructor_classes`, `instructor_payments`, `instructor_documents`)
   - Configurar políticas de segurança
   - Implementar migrations

3. **Fase 3: Listagem de Instrutores**
   - Implementar componentes de listagem
   - Desenvolver filtros e paginação
   - Conectar com a API

4. **Fase 4: Cadastro e Edição**
   - Desenvolver formulários de cadastro/edição
   - Implementar validação com Zod
   - Criar server actions para processamento

5. **Fase 5: Permissões e Integração**
   - Configurar permissões específicas
   - Integrar com o sistema existente
   - Testar fluxos completos

6. **Fase 6: Funcionalidades Avançadas**
   - Implementar gestão de contratos
   - Desenvolver controle de pagamentos
   - Adicionar relatórios e estatísticas

## Permissões

Utilizar o sistema de permissões existente, adaptando para o contexto de instrutores:

- `instructor:create` - Criar novos instrutores
- `instructor:read` - Visualizar instrutores
- `instructor:update` - Editar instrutores
- `instructor:delete` - Desativar/remover instrutores
- `instructor:manage_payments` - Gerenciar pagamentos de instrutores
- `instructor:view_payments` - Visualizar pagamentos de instrutores
- `instructor:manage_classes` - Atribuir/remover instrutores de turmas

## Componentes Reutilizáveis

Identificar componentes do módulo de alunos que podem ser reutilizados:

1. Componentes de paginação
2. Filtros de busca
3. Formulários de endereço
4. Upload de documentos e fotos
5. Seletores de filial
6. Componentes de visualização de faixas/graduações

## Schemas de Validação

Exemplo de schema Zod para validação de instrutores:

```typescript
import { z } from 'zod';

export const instructorSchema = z.object({
  userId: z.string().uuid(),
  tenantId: z.string().uuid(),
  branchId: z.string().uuid(),
  
  // Dados básicos
  specialties: z.array(z.string()).min(1, "Informe pelo menos uma especialidade"),
  certificationLevel: z.string().optional(),
  federationRegistration: z.string().optional(),
  experienceYears: z.number().int().min(0).optional(),
  bio: z.string().max(1000).optional(),
  
  // Dados de contratação
  contractType: z.enum(['clt', 'pj', 'autonomo', 'parceria']),
  paymentModel: z.enum(['hourly', 'percentage', 'fixed', 'exempt']).optional(),
  paymentValue: z.number().min(0).optional(),
  paymentPercentage: z.number().min(0).max(100).optional(),
  
  // Certificações
  hasFirstAidCertification: z.boolean().default(false),
  hasCprCertification: z.boolean().default(false),
  hasRulesCourse: z.boolean().default(false),
  
  // Datas e status
  hireDate: z.date().optional(),
  status: z.enum(['active', 'inactive', 'suspended']).default('active'),
  
  // Informações pessoais adicionais
  birthDate: z.date().optional(),
  gender: z.string().optional(),
  
  // Endereço
  street: z.string().optional(),
  streetNumber: z.string().optional(),
  complement: z.string().optional(),
  neighborhood: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  
  // Contato de emergência
  emergencyContactName: z.string().optional(),
  emergencyContactPhone: z.string().optional(),
  emergencyContactRelationship: z.string().optional(),
});

export type InstructorFormValues = z.infer<typeof instructorSchema>;
```

Exemplo de schema para faixas de instrutores:

```typescript
import { z } from 'zod';

export const instructorBeltSchema = z.object({
  tenantId: z.string().uuid(),
  instructorId: z.string().uuid(),
  beltColor: z.string(), // 'white', 'blue', 'purple', 'brown', 'black'
  degree: z.number().int().min(0).max(10),
  awardedAt: z.date(),
  awardedBy: z.string().uuid().optional(),
  notes: z.string().optional()
});

export type InstructorBeltFormValues = z.infer<typeof instructorBeltSchema>;
```

## Considerações Técnicas

1. **Performance**
   - Implementar cache adequado para listagens
   - Otimizar consultas SQL
   - Utilizar React Query para gerenciamento de estado do servidor

2. **UX/UI**
   - Manter consistência com o módulo de alunos
   - Implementar feedback visual para ações
   - Garantir responsividade

3. **Segurança**
   - Validar permissões em server actions
   - Sanitizar dados de entrada
   - Aplicar políticas de RLS no Supabase
   - Implementar logs de auditoria para ações sensíveis

4. **Multi-tenancy**
   - Garantir isolamento de dados entre academias
   - Considerar especificidades de cada academia na modelagem

## Funções e Triggers do Banco de Dados

Para garantir o funcionamento adequado do módulo de instrutores e manter a consistência com o módulo de alunos existente, foram implementadas as seguintes funções e triggers no banco de dados:

### Criação de Registros Automáticos para Instrutores

#### 1. Função para Criação de Instrutor ao Inserir Usuário

Esta função cria automaticamente um registro na tabela `instructors` quando um novo usuário com `role='instructor'` é criado:

```sql
CREATE OR REPLACE FUNCTION public.handle_new_instructor()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
  instructor_id UUID;
  admin_user_id UUID;
  new_belt_id UUID;
BEGIN
  -- Só prosseguir se o novo registro tiver role='instructor'
  IF NEW.role = 'instructor' THEN
    -- Gerar um UUID para o novo instrutor
    instructor_id := gen_random_uuid();
    
    -- Inserir o novo instrutor na tabela instructors
    INSERT INTO public.instructors (
      id,
      tenant_id,
      user_id,
      branch_id,
      contract_type,
      status,
      created_at,
      updated_at
    ) VALUES (
      instructor_id,
      NEW.tenant_id,
      NEW.id,
      NEW.branch_id,
      'autonomo', -- Tipo de contrato padrão
      'active', -- Status padrão
      NOW(),
      NOW()
    );
    
    -- Buscar um usuário administrador para registrar como responsável pela faixa
    SELECT id INTO admin_user_id 
    FROM public.users 
    WHERE tenant_id = NEW.tenant_id AND role = 'admin' 
    LIMIT 1;
    
    -- Se não encontrar um admin, usar o próprio ID do usuário
    IF admin_user_id IS NULL THEN
      admin_user_id := NEW.id;
    END IF;
    
    -- Inserir a faixa inicial (assumindo faixa preta para instrutores, mas isso pode variar)
    new_belt_id := gen_random_uuid();
    
    INSERT INTO public.instructor_belts (
      id,
      tenant_id,
      instructor_id,
      belt_color,
      degree,
      awarded_at,
      awarded_by,
      created_at
    ) VALUES (
      new_belt_id,
      NEW.tenant_id,
      instructor_id,
      'black', -- Faixa preta como inicial para instrutores (pode ser alterado conforme necessário)
      1, -- Grau 1
      NOW(), -- Data de premiação
      admin_user_id, -- Usuário que concedeu a faixa
      NOW()
    );

    -- Atualizar o current_belt_id do instrutor com a faixa recém-criada
    UPDATE public.instructors
    SET current_belt_id = new_belt_id
    WHERE id = instructor_id;
    
  END IF;
  
  RETURN NULL; -- Para triggers AFTER não importa o retorno
END;
$$;
```

#### 2. Trigger para Ativar a Função na Inserção de Usuário

```sql
CREATE TRIGGER create_instructor_on_user_insert
AFTER INSERT ON public.users
FOR EACH ROW
EXECUTE FUNCTION handle_new_instructor();
```

#### 3. Função para Criação de Instrutor ao Atualizar Role de Usuário

Esta função cria um registro na tabela `instructors` quando o `role` de um usuário existente é alterado para `'instructor'`:

```sql
CREATE OR REPLACE FUNCTION public.handle_user_role_update_for_instructor()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  instructor_id UUID;
  admin_user_id UUID;
  existing_instructor_id UUID;
  new_belt_id UUID;
BEGIN
  -- Verificar se o role foi alterado para 'instructor'
  IF NEW.role = 'instructor' AND (OLD.role IS NULL OR OLD.role <> 'instructor') THEN
    -- Verificar se já existe um registro de instrutor para este usuário
    SELECT id INTO existing_instructor_id FROM public.instructors WHERE user_id = NEW.id AND deleted_at IS NULL;
    
    -- Só prosseguir se não existir um instrutor
    IF existing_instructor_id IS NULL THEN
      -- Gerar um UUID para o novo instrutor
      instructor_id := gen_random_uuid();
      
      -- Inserir o novo instrutor na tabela instructors
      INSERT INTO public.instructors (
        id,
        tenant_id,
        user_id,
        branch_id,
        contract_type,
        status,
        created_at,
        updated_at
      ) VALUES (
        instructor_id,
        NEW.tenant_id,
        NEW.id,
        NEW.branch_id,
        'autonomo', -- Tipo de contrato padrão
        'active', -- Status padrão
        NOW(),
        NOW()
      );
      
      -- Buscar um usuário administrador para registrar como responsável pela faixa
      SELECT id INTO admin_user_id 
      FROM public.users 
      WHERE tenant_id = NEW.tenant_id AND role = 'admin' 
      LIMIT 1;
      
      -- Se não encontrar um admin, usar o próprio ID do usuário
      IF admin_user_id IS NULL THEN
        admin_user_id := NEW.id;
      END IF;
      
      -- Inserir a faixa inicial (assumindo faixa preta para instrutores)
      new_belt_id := gen_random_uuid();
      
      INSERT INTO public.instructor_belts (
        id,
        tenant_id,
        instructor_id,
        belt_color,
        degree,
        awarded_at,
        awarded_by,
        created_at
      ) VALUES (
        new_belt_id,
        NEW.tenant_id,
        instructor_id,
        'black', -- Faixa preta como inicial para instrutores
        1, -- Grau 1
        NOW(), -- Data de premiação
        admin_user_id, -- Usuário que concedeu a faixa
        NOW()
      );
      
      -- Atualizar o current_belt_id do instrutor com a faixa recém-criada
      UPDATE public.instructors
      SET current_belt_id = new_belt_id
      WHERE id = instructor_id;
    END IF;
  END IF;
  
  RETURN NULL; -- Para triggers AFTER não importa o retorno
END;
$$;
```

#### 4. Trigger para Ativar a Função na Atualização de Usuário

```sql
CREATE TRIGGER create_instructor_on_user_update
AFTER UPDATE ON public.users
FOR EACH ROW
WHEN (NEW.role = 'instructor' AND (OLD.role IS NULL OR OLD.role <> 'instructor'))
EXECUTE FUNCTION handle_user_role_update_for_instructor();
```

Estas funções e triggers garantem que:

1. Quando um novo usuário com role 'instructor' é criado, um registro correspondente é automaticamente adicionado à tabela `instructors`
2. Quando um usuário existente tem seu role alterado para 'instructor', um registro correspondente é criado na tabela `instructors` (se não existir)
3. Em ambos os casos, uma faixa inicial (faixa preta grau 1) é atribuída ao instrutor
4. O campo `current_belt_id` é atualizado para referenciar a faixa inicial

Este comportamento é similar ao já implementado para os estudantes, garantindo consistência no sistema.

## Próximos Passos

1. ✅ Criar estrutura de diretórios inicial
2. ✅ Implementar tabelas no banco de dados
3. ✅ Implementar funções e triggers para processamento automático
4. ✅ Desenvolver componentes básicos de listagem
5. 🔄 Criar formulário de cadastro
6. ⏱️ Configurar permissões específicas para instrutores
7. ⏱️ Implementar gestão de contratos e pagamentos
8. ⏱️ Desenvolver relatórios e estatísticas de instrutores

**Legenda:**
- ✅ Concluído
- 🔄 Em andamento
- ⏱️ Pendente

## Referências

- Módulo de alunos existente
- Documentação do Supabase
- Sistema de permissões da aplicação
- Pesquisa sobre contratação de instrutores no Brasil
- Requisitos específicos de federações (CBJJ/IBJJF, USJJF, etc.) 