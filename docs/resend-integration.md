# Integração Resend - Sistema de E-mail

## Visão Geral

Este documento detalha a integração com o Resend para o sistema de notificações por e-mail. O Resend será o provedor inicial, com arquitetura preparada para migração futura para AWS SES.

## Configuração Inicial

### 1. Instalação de Dependências

```bash
npm install resend react-email @react-email/components
npm install -D @types/react-email
```

### 2. Variáveis de Ambiente

```env
# Resend Configuration
RESEND_API_KEY=re_xxxxxxxxx
RESEND_FROM_DOMAIN=meusaas.com
RESEND_WEBHOOK_SECRET=whsec_xxxxxxxxx

# Email Configuration
EMAIL_PROVIDER=resend
EMAIL_FROM_NAME="Apex SaaS"
EMAIL_REPLY_TO=<EMAIL>
```

### 3. Configuração de Domínio

Cada academia terá seu subdomínio:
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

## Arquitetura do Sistema

### 1. Interface Base do Provedor

```typescript
// src/services/notifications/channels/email/providers/email-provider.ts
export interface EmailProvider {
  send(email: EmailData): Promise<EmailResult>;
  sendBatch(emails: EmailData[]): Promise<BatchEmailResult>;
  getDeliveryStatus(messageId: string): Promise<DeliveryStatus>;
  verifyDomain(domain: string): Promise<DomainVerification>;
  createDomain(domain: string): Promise<DomainCreation>;
  getDomainStatus(domain: string): Promise<DomainStatus>;
}

export interface EmailData {
  from: string;
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject: string;
  html?: string;
  text?: string;
  attachments?: EmailAttachment[];
  tags?: EmailTag[];
  headers?: Record<string, string>;
  replyTo?: string;
  scheduledAt?: Date;
}

export interface EmailResult {
  id: string;
  success: boolean;
  error?: string;
  messageId?: string;
}

export interface EmailAttachment {
  filename: string;
  content: Buffer | string;
  contentType?: string;
  path?: string;
}

export interface EmailTag {
  name: string;
  value: string;
}

export interface BatchEmailResult {
  total: number;
  successful: number;
  failed: number;
  results: EmailResult[];
}

export interface DeliveryStatus {
  messageId: string;
  status: DeliveryStatusType;
  deliveredAt?: Date;
  openedAt?: Date;
  clickedAt?: Date;
  bouncedAt?: Date;
}

export type DeliveryStatusType = 
  | 'sent' 
  | 'delivered' 
  | 'opened' 
  | 'clicked' 
  | 'bounced' 
  | 'complained' 
  | 'unsubscribed' 
  | 'unknown';

export interface DomainVerification {
  domain: string;
  verified: boolean;
  records: DnsRecord[];
}

export interface DomainCreation {
  domain: string;
  id: string;
  status: string;
  records: DnsRecord[];
}

export interface DomainStatus {
  domain: string;
  status: string;
  verified: boolean;
  records: DnsRecord[];
  createdAt: Date;
}

export interface DnsRecord {
  type: string;
  name: string;
  value: string;
  priority?: number;
}
```

### 2. Implementação Resend

```typescript
// src/services/notifications/channels/email/providers/resend-provider.ts
import { Resend } from 'resend';
import type { EmailProvider, EmailData, EmailResult } from './email-provider';

export class ResendProvider implements EmailProvider {
  private resend: Resend;

  constructor(apiKey: string) {
    this.resend = new Resend(apiKey);
  }

  async send(email: EmailData): Promise<EmailResult> {
    try {
      const { data, error } = await this.resend.emails.send({
        from: email.from,
        to: Array.isArray(email.to) ? email.to : [email.to],
        cc: email.cc ? (Array.isArray(email.cc) ? email.cc : [email.cc]) : undefined,
        bcc: email.bcc ? (Array.isArray(email.bcc) ? email.bcc : [email.bcc]) : undefined,
        subject: email.subject,
        html: email.html,
        text: email.text,
        attachments: email.attachments?.map(att => ({
          filename: att.filename,
          content: att.content,
          content_type: att.contentType,
          path: att.path
        })),
        tags: email.tags?.map(tag => ({
          name: tag.name,
          value: tag.value
        })),
        headers: email.headers,
        reply_to: email.replyTo,
        scheduled_at: email.scheduledAt?.toISOString()
      });

      if (error) {
        return {
          id: '',
          success: false,
          error: error.message
        };
      }

      return {
        id: data?.id || '',
        success: true,
        messageId: data?.id
      };
    } catch (error) {
      return {
        id: '',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async sendBatch(emails: EmailData[]): Promise<BatchEmailResult> {
    const results = await Promise.allSettled(
      emails.map(email => this.send(email))
    );

    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    const failed = results.length - successful;

    return {
      total: emails.length,
      successful,
      failed,
      results: results.map(r => 
        r.status === 'fulfilled' ? r.value : { id: '', success: false, error: 'Promise rejected' }
      )
    };
  }

  async getDeliveryStatus(messageId: string): Promise<DeliveryStatus> {
    try {
      const { data, error } = await this.resend.emails.get(messageId);
      
      if (error) {
        throw new Error(error.message);
      }

      return {
        messageId,
        status: this.mapResendStatus(data?.last_event || 'unknown'),
        deliveredAt: data?.last_event === 'delivered' ? new Date() : undefined,
        openedAt: data?.last_event === 'opened' ? new Date() : undefined,
        clickedAt: data?.last_event === 'clicked' ? new Date() : undefined,
        bouncedAt: data?.last_event === 'bounced' ? new Date() : undefined
      };
    } catch (error) {
      throw new Error(`Failed to get delivery status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async verifyDomain(domain: string): Promise<DomainVerification> {
    try {
      const { data, error } = await this.resend.domains.verify(domain);
      
      if (error) {
        throw new Error(error.message);
      }

      return {
        domain,
        verified: data?.status === 'verified',
        records: data?.records || []
      };
    } catch (error) {
      throw new Error(`Failed to verify domain: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async createDomain(domain: string): Promise<DomainCreation> {
    try {
      const { data, error } = await this.resend.domains.create({
        name: domain,
        region: 'us-east-1' // Pode ser configurável
      });

      if (error) {
        throw new Error(error.message);
      }

      return {
        domain,
        id: data?.id || '',
        status: data?.status || 'pending',
        records: data?.records || []
      };
    } catch (error) {
      throw new Error(`Failed to create domain: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getDomainStatus(domain: string): Promise<DomainStatus> {
    try {
      const { data, error } = await this.resend.domains.get(domain);
      
      if (error) {
        throw new Error(error.message);
      }

      return {
        domain,
        status: data?.status || 'unknown',
        verified: data?.status === 'verified',
        records: data?.records || [],
        createdAt: data?.created_at ? new Date(data.created_at) : new Date()
      };
    } catch (error) {
      throw new Error(`Failed to get domain status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private mapResendStatus(resendStatus: string): DeliveryStatusType {
    const statusMap: Record<string, DeliveryStatusType> = {
      'sent': 'sent',
      'delivered': 'delivered',
      'opened': 'opened',
      'clicked': 'clicked',
      'bounced': 'bounced',
      'complained': 'complained',
      'unsubscribed': 'unsubscribed'
    };

    return statusMap[resendStatus] || 'unknown';
  }
}
```

### 3. Factory de Provedores Centralizada

```typescript
// src/services/notifications/channels/email/email-provider-factory.ts
import { ResendProvider } from './providers/resend-provider';
import { AWSProvider } from './providers/aws-provider';
import type { EmailProvider } from './providers/email-provider';

export type EmailProviderType = 'resend' | 'aws_ses';

export class EmailProviderFactory {
  // Factory usa configurações do ambiente do servidor
  static create(): EmailProvider {
    const providerType = process.env.EMAIL_PROVIDER as EmailProviderType;

    switch (providerType) {
      case 'resend':
        return new ResendProvider(process.env.RESEND_API_KEY!);
      case 'aws_ses':
        return new AWSProvider({
          accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
          region: process.env.AWS_REGION || 'us-east-1'
        });
      default:
        throw new Error(`Unsupported email provider: ${providerType}`);
    }
  }

  // Método para obter configurações específicas do tenant
  static getTenantEmailConfig(tenantSlug: string, tenantName: string): TenantEmailConfig {
    const baseDomain = process.env.RESEND_FROM_DOMAIN || 'meusaas.com';

    return {
      fromDomain: `${tenantSlug}@${baseDomain}`,
      fromName: tenantName,
      replyTo: process.env.EMAIL_REPLY_TO || '<EMAIL>'
    };
  }
}

export interface TenantEmailConfig {
  fromDomain: string;
  fromName: string;
  replyTo: string;
}
```

## Próximos Passos

1. **Configurar Conta Resend** - Criar conta e obter API key
2. **Configurar Domínio** - Adicionar meusaas.com no Resend
3. **Implementar Provider** - Criar ResendProvider
4. **Criar Templates** - Implementar templates React Email
5. **Configurar Webhooks** - Setup de tracking de entrega
6. **Testes** - Validar envio e recebimento

---

**Documento criado em:** 2025-01-26  
**Versão:** 1.0  
**Status:** Pronto para Implementação
