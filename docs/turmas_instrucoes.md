## Gestão de turmas em academias de Jiu-Jitsu (Brasil)

As academias de Jiu-Jitsu no Brasil costumam dividir os alunos em turmas segundo critérios claros de idade e nível técnico, além de organizar professor, horários e presença com sistemas específicos. Em geral, as turmas são separadas por:

- Faixa etária: especialmente no jiu-jitsu infantil, há subdivisões estritas por idade. Por exemplo, a Academia Armazém do Jiu-Jitsu define turmas de 'Mini Campeão' (3-4 anos), 'Pequeno Campeão #1' (5-7 anos), 'Pequeno Campeão #2' (8-10 anos), '<PERSON><PERSON><PERSON>' (11-13 anos) e 'Juvenil' (14-15 anos) . Esse modelo ilustra como várias escolinhas de luta formatam grupos etários nos treinos infantis (evitando misturar crianças muito novas com mais velhas). · 1
- Faixa de graduação (nível técnico): para adultos e adolescentes, as turmas geralmente são criadas com base na cor da faixa (Branca, Azul, Roxa etc.), seguindo o sistema padronizado de graduação do BJJ . Assim, iniciantes (faixa-branca) treinam separadamente dos avançados (faixa-preta), respeitando os tempos mínimos entre exames de faixa . · 2 2
- Gênero (quando aplicável): algumas academias oferecem aulas exclusivas para mulheres ou misturam gêneros, mas esse critério varia. Em muitos casos formam-se turmas mistas para adultos, enquanto turmas exclusivas ocorrem apenas onde há demanda específica (por exemplo, aula só de mulheres). ·
- Nível geral ou foco da turma: além de faixa, algumas academias criam turmas de 'iniciantes' ou de treinamento competitivo independentemente da faixa, orientando o conteúdo (lutas livres, defesa pessoal etc.) conforme o público. ·

## Alocação de professores por turma

Os professores são escalados a turmas fixas de acordo com agenda e especialidade. Em geral cada professor/técnico titular fica responsável por certas turmas (por exemplo, todas as turmas infantis, ou os treinos avançados de adultos). Algumas academias vinculam parte da remuneração do professor ao desempenho de sua turma: por exemplo, no sistema DojoWeb 'o comissionamento para professor se baseia na quantidade de alunos da turma' .   Ou   seja,   para   calcular   pagamento é preciso manter atualizado o número de alunos em cada classe e suas presenças, reforçando que 'controlar o quanto pagar ao seu instrutor' exige ter lista de alunos e presenças sempre atualizada . 3 3

## Organização dos horários e aulas

Aulas de jiu-jitsu ocorrem em vários dias da semana (normalmente de segunda a sábado) e horários diversos para atender rotina dos alunos. Por exemplo, o Armazém do Jiu-Jitsu oferece aulas infantis de segunda a sábado em horários flexíveis . Em muitas academias, há turmas diurnas (por exemplo, turmas de crianças no turno vespertino) e noturnas (aulas de adultos após o trabalho). A duração típica de cada aula varia de cerca de 60 a 90 minutos ; turmas de iniciantes costumam durar em torno de 1 hora . Em geral cada faixa etária ou nível tem seu próprio calendário semanal: por exemplo, crianças podem treinar 2-3 vezes por semana, enquanto adultos treinam 3-5 vezes por semana, de acordo com o plano. Não há um padrão único público, mas a maioria das academias visa disponibilizar várias aulas por semana para cada grupo (algo como 2-4 treinos/semana para crianças e 3-6 para adultos, em 4 5 5

média). Em síntese, criam-se grades de horários onde turmas de faixa etária/nivel definidos são repetidas nos dias determinados.

## Dados e campos de cadastro de turmas e alunos

Para gerenciar tudo isso, as academias usam sistemas ou cadastros com campos específicos. No cadastro de alunos , incluem-se dados pessoais (nome completo, endereço, contato) e técnicos - por exemplo, data de nascimento (faixa etária), faixa atual de jiu-jitsu e histórico de graduações . Também é comum registrar informações médicas e de emergência (tipo sanguíneo, convênio de saúde, histórico   de   lesões)   e   até   anexar   uma   foto   de   identificação   do   aluno .   Esses   detalhes   são importantes para identificação e segurança (ex.: saber contato em caso de acidente). No cadastro de turmas , registram-se o nome/categoria da turma, o professor responsável, os dias e horários em que a turma acontece, o público-alvo (faixa etária ou de graduação) e a lotação máxima. Em sistemas de gestão essa estrutura costuma ser padronizada, permitindo criar e editar turmas e horários (por exemplo, 'Jiu-Jitsu Kids 5-7 anos - Seg/Qui 17h') e vincular a cada turma sua faixa e professor . Como ilustra o DojoWeb, basta 'criar e gerenciar turmas, horários e professores' para ter uma agenda completa . 6 2 6 7

A seguir , um quadro exemplificando alguns campos típicos nos cadastros de alunos e turmas:

| Campo / Dado               | Descrição / Exemplos                                                               |
|----------------------------|------------------------------------------------------------------------------------|
| Nome completo e contato    | Nome, telefone, e-mail e endereço do aluno (dados básicos de identificação) 8      |
| Dados médicos e emergência | Tipo sanguíneo, doenças pré-existentes, convênio saúde e contatos de emergência 9  |
| Foto do aluno              | Foto de identificação associada ao cadastro 9                                      |
| Faixa atual de graduação   | Faixa de jiu-jitsu (branca, azul, roxa, etc.) e histórico de promoções de faixa 2  |
| Nome da turma              | Ex.: 'Jiu-Jitsu Infantil 8-10 anos' ou 'Adulto Iniciante', conforme categoria-alvo |
| Horários da turma          | Dias da semana e horário (ex.: Seg/Qua/Sex 18h), conforme grade de aulas 10        |
| Professor responsável      | Nome do instrutor principal da turma (útil para calcular comissionamento) 3        |
| Capacidade da turma        | Número máximo de alunos na turma (para controle de vaga e pagamento de instrutor)  |

Note que muitos sistemas de gestão (SaaS) centralizam todos esses cadastros e permitem acessá-los pela internet , eliminando a necessidade de planilhas manuscritas. 11 7

## Registro de presenças e desempenho

- O acompanhamento diário   do   treino   também   é   formalizado.   A   maioria   das   academias   registra presenças em cada aula - seja manualmente (lista de chamada) ou eletronicamente (scan de crachá, app de check-in, etc.). Muitos softwares permitem ao aluno 'dar check-in' no treino via aplicativo móvel . 12

Por exemplo, o sistema iLUTAS oferece app em que o aluno faz check-in dos treinos e pode até dar feedback sobre as aulas .   O DojoWeb e similares mantêm um diário de classe digital: 'Tenha um registro do que aconteceu e de quem veio em cada aula' , simplificando a verificação de frequência. 12 7

O desempenho técnico do aluno costuma ser avaliado nos exames de faixa: as academias registram datas e resultados dos gradings (promoções) em cada cadastro de aluno. Alguns sistemas permitem gerar notificações quando o aluno atinge o tempo mínimo para próximo exame (como sugerido nas regras da IBJJF) . Outras avaliações informais (participação em competições, evolução de nível nas aulas, peso, notas de professores) geralmente não têm registro padronizado, mas algumas plataformas já oferecem recursos de 'histórico de graduação' e monitoramento de evolução. Por exemplo, o MyBelt disponibiliza controle de graduação mostrando 'todas as informações e o histórico de faixa dos seus alunos em um clique!' , facilitando ver como cada aluno evolui ao longo do tempo. 2 13

## Ferramentas tecnológicas (SaaS) usadas na gestão

Nos últimos anos surgiram várias soluções SaaS (software como serviço) focadas em academias de artes marciais, incluindo jiu-jitsu. Esses sistemas centralizam cadastros de alunos, controle financeiro e acadêmico, facilitando muito a gestão. Exemplos populares no mercado brasileiro incluem:

- DojoWeb: plataforma online brasileira que permite 'cadastro de alunos, professores e turmas' com diário de aula e controle de presença . Já vem com módulos para cobrança de mensalidades e geração de relatórios financeiros. · 11 7
- Next Fit CT: oferece grade de aulas, agendamento de treinos, envio de mensagens automáticas, relatórios de desempenho e até site próprio para vendas. Tem aplicativo exclusivo para alunos (agendamento, pagamento, treino) . · 14
- iLUTAS: inclui um módulo 'secretaria' para controlar matrículas e fluxo de alunos, além de comunicação automática (SMS/WhatsApp) com a base de alunos . Também possui app para aluno fazer check-in e avaliar as aulas . · 15 12
- Fight System: sistema completo com gestão de agenda, CRM e marketing. Possui CRM e rede social própria, e app do aluno para check-in nos treinos e acompanhamento de avaliações físicas e evolução pessoal . · 16
- IBlackBelt: foca no básico do financeiro e cadastro. O plano básico inclui registro de alunos, controle de pagamentos e alertas de vencimentos, além de relatórios de receitas . · 17
- TatamePRO: sistema nacional para artes marciais que oferece controle de contratos, presença, gestão de graduações e cobrança automática . A versão gratuita já permite cadastro ilimitado de alunos e gestão de presenças e graduações. · 18
- MyBelt: plataforma que disponibiliza app tanto para alunos quanto para professores. No app do aluno há agenda de aulas, histórico de graduação e pagamentos; no app do professor há controle de agenda e registro de presenças . Também inclui módulo de comunicação interna (microblog) e integração de vídeos. · 19

Cada uma dessas plataformas agrega funções semelhantes: acompanhamento financeiro (contratos e inadimplência), controle de acesso (check-in), comunicação (SMS/app), indicadores e até e-commerce. A tabela a seguir compara alguns recursos típicos desses sistemas:

| Sistema   | Funcionalidades principais (exemplos)                                                                                    |
|-----------|--------------------------------------------------------------------------------------------------------------------------|
| DojoWeb   | Cadastro de alunos/professores, agendamento de turmas, diário de presença . Relatórios financeiros e de desempenho. 11 7 |

| Sistema                                                                                                                                       | Funcionalidades principais (exemplos)              |
|-----------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------|
| Next Fit CT Grade de aulas, site integrado para planos, envio de mensagens automáticas, relatórios e gráficos , app exclusivo para alunos. 14 |                                                    |
|                                                                                                                                               | iLUTAS Controle de matrículas, sorteios, app aluno |
| Gestão de agenda, in e visualização de                                                                                                        | FightSystem                                        |
| Cadastro relatórios                                                                                                                           | IBlackBelt de alunos, de receita                   |
| Gestão de contratos, fluxo de caixa, presenças, automáticas via app . Suporte 24h. 18                                                         | TatamePRO                                          |
|                                                                                                                                               | MyBelt alunos e professores                        |

Além desses, existem outros softwares voltados a academias em geral (e não só de BJJ), como iDojo, Luiz Paulo, Bento, entre outros. Muitos academias também usam sistemas de gestão de academias multimodalidade (ex.: Gympass, Zen Planner etc.) ou desenvolvem sistemas próprios. A vantagem de usar uma plataforma SaaS é automatizar processos (financeiro, frequência e comunicação) e gerar indicadores como ocupação de turmas e fluxo de alunos . 20 16

## Desafios comuns na gestão das academias

Na prática, as academias de Jiu-Jitsu enfrentam desafios administrativos frequentes. Entre os mais reportados estão:

- Dependência de planilhas e burocracia manual: muitas academias ainda mantêm cadastros e controles em planilhas ou anotações, o que consome muito tempo. Conforme enfatiza o DojoWeb, é importante 'eliminar as planilhas e a burocracia do seu dojo' centralizando informações no sistema . Essa transição para digital reduz erros e retrabalho. · 21
- Controle de presença e inadimplência: acompanhar quem frequentou cada aula e quem pagou em dia costuma ser complicado sem ferramenta automatizada. Proprietários valorizam recursos que 'acompanham a frequência, automatizam os pagamentos e enviam alertas' aos alunos . A falta de um registro preciso de presenças e inadimplentes pode causar perda de receita e de alunos. · 22
- Comunicação e engajamento dos alunos: manter os alunos informados sobre horários, promoções de faixa e eventos exige esforço extra. Ferramentas de envio automático de SMS/ WhatsApp e aplicativos são usadas para contornar isso. Por exemplo, o iLUTAS destaca o envio de mensagens automáticas aos alunos . Academias relatam que sem essa comunicação digital (notificações, redes sociais, newsletter) há maior evasão de alunos. · 15
- Escalonamento da equipe: à medida que a academia cresce, distribuir tarefas administrativas (cobrança, matriculas, controle) entre professores e secretária se torna complexo. Alguns sistemas permitem liberação de acesso para outros colaboradores, mas nas pequenas academias o próprio mestre costuma acumular funções gerenciais, o que é desgastante. ·
- Adequação às normas: manter regularização fiscal (CNPJ, notas fiscais) e a segurança dos dados dos alunos (LGPD) também são demandas novas que exigem atenção e podem exigir adaptações dos sistemas usados. ·

Em suma, os principais desafios giram em torno da gestão de pessoas e processos - como sintetiza o relato   de   gestores, 'o   que   os   [proprietários   de   academia]   mais   valorizam   em   um   software   é   poder automatizar frequência, cobranças e ter relatórios claros sobre alunos e finanças' . 22

## Exemplo ilustrativo de métricas

Embora não existam dados públicos consolidados sobre métricas específicas (distribuição etária ou média de alunos por turma) nas academias de BJJ, podemos apresentar um exemplo hipotético para ilustrar a dimensão. Por exemplo, considerando que há cerca de 34 mil academias oficiais de jiu-jitsu no Brasil e uma média de ~30 alunos por academia (sugestão de Paula Canton), chegamos a cerca de 1 milhão de praticantes no país. Em termos de perfil, é razoável supor que uma parte significativa desses alunos seja de adultos jovens (70-80%) e o restante crianças/adolescentes (20-30%). Uma ilustração simplificada poderia ser: 23 24

| Faixa etária   | %estimado de alunos (ex.)   | Carga horária semanal por faixa (ex.)   |
|----------------|-----------------------------|-----------------------------------------|
| 4-10 anos      | 20%                         | ~3 horas (2-3 aulas)                    |
| 11-17 anos     | 15%                         | ~3-4 horas                              |
| 18-30 anos     | 40%                         | ~4-6 horas                              |
| 31-50 anos     | 20%                         | ~3-4 horas                              |
| >50 anos       | 5%                          | ~2 horas                                |

Dados acima são meramente ilustrativos, não baseados em pesquisa real. Servem para indicar que turmas infantis tendem a ser menores (em torno de 10-15 alunos) do que turmas adultas (muitas vezes 15-30 alunos). Em geral, classes mais jovens têm menor carga horária semanal (por restrições de idade/ horários escolares), enquanto adultos treinam com maior frequência. Como metrificar esses valores varia muito conforme cada academia e região, recomenda-se que gestores monitorem suas próprias estatísticas (número de alunos ativos, % de frequentes, etc.) usando os sistemas de gestão para extrair gráficos   de   presença,   distribuição   por   faixa   etária/faixa ,   carga   horária   por   plano   etc.   Muitos sistemas acima (e soluções genéricas de BI) permitem exportar essas informações em gráficos e relatórios visuais.

Fontes: As   informações   acima   foram   compiladas   de   blogs,   sites   institucionais   e   materiais   de provedores de software para academias de artes marciais . Não foram encontradas estatísticas oficiais abrangentes sobre demografia de praticantes, portanto as análises numéricas são indicativas. Em cada seção, as citações referenciam os principais achados das fontes consultadas. 1 6 7 25 22

Infantil - Armazém do Jiu-Jitsu 1 4

https://armazemjj.com/infantil/

Software na academia de lutas: organização de alunos e turmas - Dojoweb https://dojoweb.com.br/software-na-academia-de-lutas-organizacao-de-alunos-e-turmas/ 2 3 6 8 9

- how long are jiu jitsu classes?: Mastering the Clock - My Jiu Jitsu Academia 5

https://myjiujitsuacademia.com/how-long-are-jiu-jitsu-classes/

Sistema de Gestão para Academias de Jiu-Jitsu - Dojoweb 7 11 20 21

https://dojoweb.com.br/sistema-de-gestao-para-academias-de-jiu-jitsu/

## Confira os 5 melhores sistemas para academia de artes marciais 10 12 14 15 16 17 25

https://nfe.io/blog/gestao-empresarial/melhores-sistemas-academia-artes-marciais/

## MyBelt Sistemas - Sistema de gestão de academias de artes marciais. 13 19

https://mybelt.com.br/wp/

## TatamePRO | Boas vindas 18

https://tatamepro.com/

## Software de gestão de academia : r/bjj 22

https://www.reddit.com/r/bjj/comments/1akh730/gym\_management\_software/?tl=pt-br

## SMARTDOJO. Aplicativo para gestão de presenças dos… | by Paula Canton | Product Designer | 23 24

## Medium

https://medium.com/@paula.aa.canton/smartdojo-174ffa654ff7