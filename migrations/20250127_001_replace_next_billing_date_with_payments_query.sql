-- Migration: Replace next_billing_date usage with payments table queries
-- Purpose: Change billing logic to use payments table as source of truth instead of next_billing_date column
-- Date: 2025-01-27

-- Step 1: Create helper function to get next payment for a membership
CREATE OR REPLACE FUNCTION public.get_next_payment_for_membership(p_membership_id uuid)
RETURNS TABLE(
  payment_id uuid, 
  due_date date, 
  amount numeric,
  status text,
  payment_type text
)
LANGUAGE plpgsql
SET search_path TO ''
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id as payment_id,
    p.due_date,
    p.amount,
    p.status,
    p.payment_type
  FROM public.payments p
  WHERE p.membership_id = p_membership_id
    AND p.status IN ('pending', 'awaiting_confirmation')
    AND p.payment_type = 'recurring'
  ORDER BY p.due_date ASC
  LIMIT 1;
END;
$$;

-- Step 2: Create helper function to get last paid payment for a membership
CREATE OR REPLACE FUNCTION public.get_last_paid_payment_for_membership(p_membership_id uuid)
RETURNS TABLE(
  payment_id uuid, 
  due_date date, 
  paid_at timestamptz,
  amount numeric
)
LANGUAGE plpgsql
SET search_path TO ''
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id as payment_id,
    p.due_date,
    p.paid_at,
    p.amount
  FROM public.payments p
  WHERE p.membership_id = p_membership_id
    AND p.status = 'paid'
    AND p.payment_type = 'recurring'
  ORDER BY p.due_date DESC, p.paid_at DESC
  LIMIT 1;
END;
$$;

-- Step 3: Update process_membership_billing function to use payments table instead of next_billing_date
CREATE OR REPLACE FUNCTION public.process_membership_billing(p_membership_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SET search_path TO ''
AS $$
DECLARE
  v_tenant_id uuid;
  v_membership_record record;
  v_payment_id uuid;
  v_amount numeric;
  v_next_billing_date date;
  v_billing_cycle text;
  v_due_date date;
  v_result jsonb;
  v_existing_payment record;
  v_last_payment record;
BEGIN
  -- Get tenant_id from JWT
  v_tenant_id := (select auth.jwt() ->> 'tenant_id')::uuid;
  
  if v_tenant_id is null then
    raise exception 'Acesso negado: tenant_id não encontrado';
  end if;

  -- Get membership and plan data
  select 
    m.*,
    p.pricing_config,
    p.title as plan_title
  into v_membership_record
  from public.memberships m
  inner join public.plans p on p.id = m.plan_id
  where m.id = p_membership_id and m.tenant_id = v_tenant_id;

  if not found then
    raise exception 'Matrícula não encontrada ou acesso negado';
  end if;

  -- Check if membership is active and has recurring billing
  if v_membership_record.status != 'active'::public.membership_status then
    raise exception 'Matrícula não está ativa';
  end if;

  if v_membership_record.pricing_config ->> 'tipo' != 'recurring' then
    raise exception 'Matrícula não possui cobrança recorrente';
  end if;

  -- Check if there's already a pending payment for this membership
  SELECT * INTO v_existing_payment 
  FROM public.get_next_payment_for_membership(p_membership_id);
  
  if v_existing_payment.payment_id IS NOT NULL then
    raise exception 'Já existe uma cobrança pendente para esta matrícula';
  end if;

  -- Calculate amount
  v_amount := coalesce((v_membership_record.pricing_config ->> 'valor')::numeric, 0);
  
  -- Get billing cycle
  v_billing_cycle := v_membership_record.pricing_config ->> 'frequencia';

  -- Get last paid payment to calculate next billing date
  SELECT * INTO v_last_payment 
  FROM public.get_last_paid_payment_for_membership(p_membership_id);

  -- Calculate next billing date based on last payment or membership start date
  if v_last_payment.payment_id IS NOT NULL then
    -- Use last payment due date as base
    if v_billing_cycle = 'month' then
      v_next_billing_date := v_last_payment.due_date + interval '1 month';
    elsif v_billing_cycle = 'week' then
      v_next_billing_date := v_last_payment.due_date + interval '1 week';
    elsif v_billing_cycle = 'year' then
      v_next_billing_date := v_last_payment.due_date + interval '1 year';
    elsif v_billing_cycle = 'day' then
      v_next_billing_date := v_last_payment.due_date + interval '1 day';
    else
      -- Default to monthly if not specified
      v_next_billing_date := v_last_payment.due_date + interval '1 month';
      v_billing_cycle := 'month';
    end if;
  else
    -- No previous payments, use membership start date or current next_billing_date as fallback
    if v_membership_record.next_billing_date IS NOT NULL then
      v_next_billing_date := v_membership_record.next_billing_date;
    else
      -- Calculate from start date
      if v_billing_cycle = 'month' then
        v_next_billing_date := v_membership_record.start_date + interval '1 month';
      elsif v_billing_cycle = 'week' then
        v_next_billing_date := v_membership_record.start_date + interval '1 week';
      elsif v_billing_cycle = 'year' then
        v_next_billing_date := v_membership_record.start_date + interval '1 year';
      elsif v_billing_cycle = 'day' then
        v_next_billing_date := v_membership_record.start_date + interval '1 day';
      else
        -- Default to monthly if not specified
        v_next_billing_date := v_membership_record.start_date + interval '1 month';
        v_billing_cycle := 'month';
      end if;
    end if;
  end if;

  -- Calculate due date using tenant configuration
  v_due_date := public.calculate_due_date_for_tenant(v_tenant_id, v_next_billing_date);

  -- Create payment record
  insert into public.payments (
    id,
    tenant_id,
    student_id,
    membership_id,
    amount,
    currency,
    status,
    payment_type,
    description,
    due_date,
    billing_cycle,
    metadata,
    created_at
  ) values (
    gen_random_uuid(),
    v_tenant_id,
    v_membership_record.student_id,
    p_membership_id,
    v_amount,
    coalesce(v_membership_record.pricing_config ->> 'currency', 'BRL'),
    'pending',
    'recurring',
    'Mensalidade - ' || v_membership_record.plan_title,
    v_due_date,
    v_billing_cycle,
    jsonb_build_object(
      'membership_id', p_membership_id,
      'plan_title', v_membership_record.plan_title,
      'billing_cycle', v_billing_cycle,
      'processed_at', now(),
      'calculated_from', case 
        when v_last_payment.payment_id IS NOT NULL then 'last_payment'
        else 'membership_start_date'
      end
    ),
    now()
  ) returning id into v_payment_id;

  -- NOTE: We no longer update next_billing_date as we now use payments table as source of truth

  select jsonb_build_object(
    'success', true,
    'payment_id', v_payment_id,
    'amount', v_amount,
    'due_date', v_due_date,
    'next_billing_date', v_next_billing_date,
    'billing_cycle', v_billing_cycle,
    'message', 'Cobrança recorrente processada com sucesso'
  ) into v_result;

  return v_result;

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Erro interno: ' || SQLERRM
    );
END;
$$;

-- Step 4: Update create_next_recurring_payment function to not update next_billing_date
CREATE OR REPLACE FUNCTION public.create_next_recurring_payment(p_paid_payment_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SET search_path TO ''
AS $$
DECLARE
  v_membership_id UUID;
  v_tenant_id UUID;
  v_student_id UUID;
  v_plan_title TEXT;
  v_plan_amount NUMERIC;
  v_pricing_type TEXT;
  v_frequency TEXT;
  v_currency TEXT;
  v_paid_due_date DATE;
  v_paid_at TIMESTAMP;
  v_current_date DATE;
  v_next_due_date DATE;
  v_calculated_due_date DATE;
  v_payment_id UUID;
  v_result JSONB;
  v_existing_payment record;
BEGIN
  -- Validações básicas
  IF p_paid_payment_id IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'ID do pagamento é obrigatório'
    );
  END IF;

  -- Buscar informações do pagamento pago
  SELECT 
    p.membership_id,
    p.tenant_id,
    p.student_id,
    p.due_date,
    p.paid_at,
    pl.title,
    pl.pricing_config->>'amount',
    pl.pricing_config->>'type',
    pl.pricing_config->>'frequency',
    COALESCE(pl.pricing_config->>'currency', 'BRL')
  INTO 
    v_membership_id,
    v_tenant_id,
    v_student_id,
    v_paid_due_date,
    v_paid_at,
    v_plan_title,
    v_plan_amount,
    v_pricing_type,
    v_frequency,
    v_currency
  FROM public.payments p
  INNER JOIN public.memberships m ON m.id = p.membership_id
  INNER JOIN public.plans pl ON pl.id = m.plan_id
  WHERE p.id = p_paid_payment_id
    AND p.status = 'paid'
    AND p.payment_type = 'recurring';

  -- Verificar se o pagamento foi encontrado
  IF v_membership_id IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Pagamento não encontrado ou não é um pagamento recorrente pago'
    );
  END IF;

  -- Verificar se já existe um próximo pagamento pendente
  SELECT * INTO v_existing_payment 
  FROM public.get_next_payment_for_membership(v_membership_id);
  
  IF v_existing_payment.payment_id IS NOT NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Já existe um próximo pagamento pendente para esta matrícula'
    );
  END IF;

  -- Obter data atual
  v_current_date := CURRENT_DATE;

  -- Verificar se o pagamento foi feito no prazo ou em atraso (não antecipado)
  IF v_paid_due_date > v_current_date THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Pagamento antecipado detectado. Próximo pagamento será criado automaticamente na data correta.',
      'payment_timing', 'early',
      'paid_due_date', v_paid_due_date,
      'current_date', v_current_date
    );
  END IF;

  -- Calcular próxima data de vencimento baseada na frequência
  CASE v_frequency
    WHEN 'weekly' THEN
      v_next_due_date := v_paid_due_date + INTERVAL '1 week';
    WHEN 'monthly' THEN
      v_next_due_date := v_paid_due_date + INTERVAL '1 month';
    WHEN 'yearly' THEN
      v_next_due_date := v_paid_due_date + INTERVAL '1 year';
    ELSE
      -- Default para mensal se não especificado
      v_next_due_date := v_paid_due_date + INTERVAL '1 month';
  END CASE;

  -- Calcular data de vencimento usando configuração do tenant
  v_calculated_due_date := public.calculate_due_date_for_tenant(v_tenant_id, v_next_due_date);

  -- Criar próximo pagamento
  INSERT INTO public.payments (
    id,
    tenant_id,
    student_id,
    membership_id,
    amount,
    currency,
    status,
    payment_type,
    description,
    due_date,
    billing_cycle,
    metadata,
    created_at
  ) VALUES (
    gen_random_uuid(),
    v_tenant_id,
    v_student_id,
    v_membership_id,
    v_plan_amount::NUMERIC,
    v_currency,
    'pending',
    'recurring',
    'Mensalidade - ' || v_plan_title,
    v_calculated_due_date,
    v_frequency,
    jsonb_build_object(
      'membership_id', v_membership_id,
      'plan_title', v_plan_title,
      'billing_cycle', v_frequency,
      'previous_payment_id', p_paid_payment_id,
      'auto_created', true,
      'created_from_payment', v_paid_due_date
    ),
    NOW()
  ) RETURNING id INTO v_payment_id;

  -- NOTE: We no longer update next_billing_date as we now use payments table as source of truth

  -- Retornar resultado
  RETURN jsonb_build_object(
    'success', true,
    'data', jsonb_build_object(
      'payment_id', v_payment_id,
      'membership_id', v_membership_id,
      'amount', v_plan_amount,
      'due_date', v_calculated_due_date,
      'frequency', v_frequency,
      'plan_title', v_plan_title,
      'previous_payment_id', p_paid_payment_id,
      'payment_timing', 'on_time_or_late',
      'created_at', NOW()
    )
  );

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Erro interno: ' || SQLERRM
    );
END;
$$;

-- Add comments to document the changes
COMMENT ON FUNCTION public.get_next_payment_for_membership(uuid) IS 
'Helper function to get the next pending payment for a membership from payments table';

COMMENT ON FUNCTION public.get_last_paid_payment_for_membership(uuid) IS 
'Helper function to get the last paid payment for a membership from payments table';

COMMENT ON FUNCTION public.process_membership_billing(uuid) IS 
'Updated function to process membership billing using payments table as source of truth instead of next_billing_date column';

COMMENT ON FUNCTION public.create_next_recurring_payment(uuid) IS 
'Updated function to create next recurring payment without updating next_billing_date column';
