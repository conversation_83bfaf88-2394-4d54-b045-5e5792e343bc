'use client'

import { Suspense } from 'react'
import { ResetPassword } from '@/components/auth/reset-password'
import { SplashScreen } from '@/components/splash-screen'
import { AuthLoading } from '@/components/auth'

export default function ResetPasswordPage() {
  return (
    <SplashScreen>
      <div className="relative flex min-h-screen items-center justify-center overflow-hidden">
        {/* Padrão de fundo */}
        <div className="absolute inset-0 -z-10">
          <div className="relative h-full w-full bg-white dark:bg-gray-950">
            <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_110%)]" />
          </div>
        </div>

        {/* Formulário */}
        <div className="relative flex w-full items-center justify-center bg-white/[0.96] px-4 dark:bg-gray-950/[0.96] sm:px-6">
          <div className="absolute inset-0 -z-10 bg-gradient-to-b from-white via-white/80 to-white/40 dark:from-gray-950 dark:via-gray-950/80 dark:to-gray-950/40" />
          <Suspense fallback={<AuthLoading />}>
            <ResetPassword />
          </Suspense>
        </div>
      </div>
    </SplashScreen>
  )
} 