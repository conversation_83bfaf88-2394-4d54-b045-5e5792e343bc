'use server';

import { requireAuth } from '@/services/auth/actions/auth-actions';
import { listModalities, getModalitySettings } from '@/services/modalities';
import { listGraduationLevels } from '@/services/belts/levels';
import { getRankRequirements } from '@/services/belts/requirements';

interface ActionResult {
  success: boolean;
  modality?: any;
  modalitySettings?: any;
  levelsWithRequirements?: any[];
  errors?: any;
}

/**
 * Server Action: busca uma modalidade completa com configurações e ranks
 */
export async function getModalityWithSettingsAction(modalitySlug: string): Promise<ActionResult> {
  try {
    const { user } = await requireAuth();
    const tenantId: string | undefined = (user.app_metadata as any)?.tenant_id;
    const role: string | undefined = (user.app_metadata as any)?.role;

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não identificado' },
      };
    }

    if (role !== 'admin') {
      return {
        success: false,
        errors: { _form: '<PERSON>sso negado' },
      };
    }

    // Buscar todas as modalidades para encontrar a correta pelo slug
    const modalities = await listModalities(tenantId);
    const modality = modalities.find(m => m.slug === modalitySlug);

    if (!modality) {
      return {
        success: false,
        errors: { _form: 'Modalidade não encontrada' },
      };
    }

    // Buscar configurações da modalidade para este tenant
    const modalitySettings = await getModalitySettings(tenantId, modality.id);

    // Buscar níveis de graduação da modalidade
    const graduationLevels = await listGraduationLevels(tenantId, modality.slug);

    // Buscar requisitos dos níveis
    const beltLevelIds = graduationLevels.map(level => level.id);
    const rankRequirements = await getRankRequirements(tenantId, beltLevelIds);

    // Combinar dados
    const levelsWithRequirements = graduationLevels.map(level => ({
      ...level,
      requirements: rankRequirements.find(req => req.belt_level_id === level.id),
    }));

    return {
      success: true,
      modality,
      modalitySettings,
      levelsWithRequirements,
    };
  } catch (error: any) {
    console.error('[getModalityWithSettingsAction] erro:', error);
    return {
      success: false,
      errors: { _form: 'Falha ao buscar dados da modalidade' },
    };
  }
} 