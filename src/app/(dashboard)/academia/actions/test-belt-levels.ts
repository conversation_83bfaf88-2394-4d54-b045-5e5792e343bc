'use server';

import { requireAuth } from '@/services/auth/actions/auth-actions';
import { testBeltLevelsUpsert, checkBeltLevelsState } from '@/services/belts/test-belt-levels';

/**
 * Action para testar o upsert de belt levels
 */
export async function testBeltLevelsAction(modalityId: string) {
  console.log('🧪 Action de teste chamada para modalityId:', modalityId);
  
  const { user } = await requireAuth();
  const tenantId: string | undefined = (user.app_metadata as any)?.tenant_id;
  const role: string | undefined = (user.app_metadata as any)?.role;

  if (!tenantId) {
    return {
      success: false,
      message: 'Tenant não identificado',
    };
  }

  if (role !== 'admin') {
    return {
      success: false,
      message: 'Acesso negado',
    };
  }

  try {
    // 1. Verificar estado atual
    console.log('🔍 Verificando estado atual...');
    const currentState = await checkBeltLevelsState(tenantId, modalityId);
    console.log('📊 Estado atual:', currentState);

    // 2. Executar teste
    console.log('🧪 Executando teste...');
    const testResult = await testBeltLevelsUpsert(tenantId, modalityId);
    console.log('📊 Resultado do teste:', testResult);

    // 3. Verificar estado final
    console.log('🔍 Verificando estado final...');
    const finalState = await checkBeltLevelsState(tenantId, modalityId);
    console.log('📊 Estado final:', finalState);

    return {
      success: testResult.success,
      message: testResult.message,
      data: {
        currentState,
        testResult,
        finalState
      }
    };

  } catch (error) {
    console.error('💥 Erro na action de teste:', error);
    return {
      success: false,
      message: `Erro na action: ${error}`,
      data: { error }
    };
  }
}
