"use client"

import React, { useEffect } from 'react';
import { usePageTitle } from '@/contexts/PageTitleContext';
import { FaCcVisa } from "react-icons/fa6";
import { CreditCard, Calendar, DollarSign, CheckCircle, AlertCircle, Settings, Clock, Wallet } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Mock data - será substituído por dados reais no futuro
const mockSubscriptionData = {
  plan: {
    name: 'Plano Professional',
    price: 89.90,
    currency: 'BRL',
    billing_cycle: 'monthly',
    features: ['Até 500 alunos', 'Relatórios avançados', 'Suporte prioritário', 'API completa']
  },
  subscription: {
    status: 'active', // active, past_due, canceled, expired
    current_period_start: '2024-01-15',
    current_period_end: '2024-02-15',
    next_billing_date: '2024-02-15',
    days_until_renewal: 7,
    auto_renew: true
  },
  payment: {
    method: 'credit_card',
    last_four: '4532',
    brand: 'visa',
    status: 'paid', // paid, pending, failed
    last_payment_date: '2024-01-15',
    next_payment_amount: 89.90
  },
  billing_history: [
    {
      id: '1',
      date: '2024-01-15',
      amount: 89.90,
      status: 'paid',
      invoice_url: '#'
    },
    {
      id: '2', 
      date: '2023-12-15',
      amount: 89.90,
      status: 'paid',
      invoice_url: '#'
    }
  ]
};

const getStatusBadge = (status: string) => {
  const statusConfig = {
    active: { label: 'Ativo', variant: 'default' as const, className: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' },
    past_due: { label: 'Em atraso', variant: 'destructive' as const, className: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' },
    canceled: { label: 'Cancelado', variant: 'secondary' as const, className: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400' },
    expired: { label: 'Expirado', variant: 'secondary' as const, className: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400' }
  };
  
  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
  return <Badge className={config.className}>{config.label}</Badge>;
};

const getPaymentStatusIcon = (status: string) => {
  switch (status) {
    case 'paid':
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case 'pending':
      return <Clock className="h-4 w-4 text-yellow-600" />;
    case 'failed':
      return <AlertCircle className="h-4 w-4 text-red-600" />;
    default:
      return <Clock className="h-4 w-4 text-gray-600" />;
  }
};

const formatCurrency = (amount: number, currency: string = 'BRL') => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: currency
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(new Date(dateString));
};

export default function AssinaturaPage() {
  const { setPageTitle, setPageSubtitle, setPageIcon } = usePageTitle();

  useEffect(() => {
    const icon = <CreditCard className="h-6 w-6 text-primary" />;
    
    setPageTitle('Gerenciar Assinatura');
    setPageSubtitle('Gerencie sua assinatura, pagamentos e configurações de cobrança.');
    setPageIcon(icon);
    
    return () => {
      setPageTitle(null);
      setPageSubtitle(null);
      setPageIcon(null);
    };
  }, [setPageTitle, setPageSubtitle, setPageIcon]);

  const { plan, subscription, payment, billing_history } = mockSubscriptionData;

  return (
    <div className="space-y-6">
      {/* Status da Assinatura */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">Status da Assinatura</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                {getStatusBadge(subscription.status)}
                <p className="text-sm text-muted-foreground">
                  {subscription.status === 'active' ? 'Renovação automática ativa' : 'Verifique sua assinatura'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">Próximo Vencimento</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <p className="text-2xl font-bold">{subscription.days_until_renewal} dias</p>
              <p className="text-sm text-muted-foreground">
                {formatDate(subscription.next_billing_date)}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">Valor Mensal</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <p className="text-2xl font-bold">{formatCurrency(plan.price)}</p>
              <p className="text-sm text-muted-foreground">Cobrado mensalmente</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Informações do Plano */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wallet className="h-5 w-5" />
            Plano Atual
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold">{plan.name}</h3>
              <p className="text-sm text-muted-foreground">
                {formatCurrency(plan.price)}/mês
              </p>
            </div>
            <Button variant="outline" size="sm">
              Alterar Plano
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
            {plan.features.map((feature, index) => (
              <div key={index} className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm">{feature}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Método de Pagamento */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Método de Pagamento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-6 bg-gradient-to-r from-blue-600 to-blue-800 rounded flex items-center justify-center">
                <FaCcVisa className="h-4 w-4 text-white" />
              </div>
              <div>
                <p className="font-medium">•••• •••• •••• {payment.last_four}</p>
                <p className="text-sm text-muted-foreground">
                  Último pagamento: {formatDate(payment.last_payment_date)}
                </p>
              </div>
            </div>
            <Button variant="outline" size="sm">
              Alterar
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Histórico de Pagamentos */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Histórico de Pagamentos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {billing_history.map((payment) => (
              <div key={payment.id} className="flex items-center justify-between py-3 border-b last:border-b-0">
                <div className="flex items-center gap-3">
                  {getPaymentStatusIcon(payment.status)}
                  <div>
                    <p className="font-medium">{formatCurrency(payment.amount)}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(payment.date)}
                    </p>
                  </div>
                </div>
                <Button variant="ghost" size="sm">
                  Ver Fatura
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Ações Rápidas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configurações
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="text-left">
                <p className="font-medium">Pausar Assinatura</p>
                <p className="text-sm text-muted-foreground">Suspender temporariamente</p>
              </div>
            </Button>
            
            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="text-left">
                <p className="font-medium">Cancelar Assinatura</p>
                <p className="text-sm text-muted-foreground">Encerrar permanentemente</p>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Alertas */}
      {subscription.status === 'past_due' && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Sua assinatura está em atraso. Atualize seu método de pagamento para continuar usando todos os recursos.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
