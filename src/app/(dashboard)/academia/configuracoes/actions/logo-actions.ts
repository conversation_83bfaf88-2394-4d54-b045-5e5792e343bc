'use server';

import { z } from 'zod';
import { createClient } from '@/services/supabase/server';
import { requireAuth } from '@/services/auth/server';
import { getTenantSlug } from '@/services/tenant';
import { revalidatePath } from 'next/cache';

/**
 * Schema de validação para upload de logo
 */
const logoUploadSchema = z.object({
  file: z.instanceof(File, { message: 'Arquivo é obrigatório' }),
}).refine((data) => {
  const file = data.file;
  const validTypes = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg'];
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  if (!validTypes.includes(file.type)) {
    return false;
  }
  
  if (file.size > maxSize) {
    return false;
  }
  
  return true;
}, {
  message: 'Arquivo deve ser SVG, PNG ou JPG e ter no máximo 10MB',
  path: ['file']
});

/**
 * Busca o logo atual da academia
 */
export async function getCurrentLogo() {
  try {
    // Verificar autenticação
    const { user } = await requireAuth();
    
    // Obter slug do tenant atual
    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      return {
        success: false,
        errors: { _form: 'Tenant não encontrado' }
      };
    }

    const supabase = await createClient();

    // Buscar dados do tenant
    const { data: tenantData, error: tenantError } = await supabase
      .from('tenants')
      .select('logo_url, logo_storage_path, favicon_storage_path')
      .eq('slug', tenantSlug)
      .single();

    if (tenantError) {
      console.error('Erro ao buscar logo:', tenantError);
      return {
        success: false,
        errors: { _form: 'Erro ao buscar logo da academia' }
      };
    }

    return {
      success: true,
      data: {
        logoUrl: tenantData?.logo_url || null,
        logoStoragePath: tenantData?.logo_storage_path || null,
        faviconStoragePath: tenantData?.favicon_storage_path || null
      }
    };

  } catch (error) {
    console.error('Erro ao buscar logo:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' }
    };
  }
}

/**
 * Faz upload de um novo logo para a academia
 */
export async function uploadLogo(formData: FormData) {
  try {
    // Verificar autenticação
    const { user } = await requireAuth();
    
    // Obter slug do tenant atual
    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      return {
        success: false,
        errors: { _form: 'Tenant não encontrado' }
      };
    }

    const file = formData.get('file') as File;
    
    // Validar arquivo
    const result = logoUploadSchema.safeParse({ file });
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format()
      };
    }

    const supabase = await createClient();

    // Buscar dados do tenant
    const { data: tenantData, error: tenantError } = await supabase
      .from('tenants')
      .select('id, logo_storage_path, favicon_storage_path')
      .eq('slug', tenantSlug)
      .single();

    if (tenantError || !tenantData) {
      console.error('Erro ao buscar tenant:', tenantError);
      return {
        success: false,
        errors: { _form: 'Erro ao buscar dados da academia' }
      };
    }

    // Remover logo antigo se existir
    if (tenantData.logo_storage_path) {
      const { error: deleteError } = await supabase.storage
        .from('logos')
        .remove([tenantData.logo_storage_path]);
      
      if (deleteError) {
        console.error('Erro ao remover logo antigo:', deleteError);
        // Não retornar erro, apenas continuar com o upload
      }
    }

    // Gerar nome único para o arquivo
    const fileExtension = file.name.split('.').pop();
    const fileName = `${tenantData.id}/${Date.now()}.${fileExtension}`;

    // Fazer upload do novo arquivo
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('logos')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      console.error('Erro ao fazer upload:', uploadError);
      return {
        success: false,
        errors: { _form: 'Erro ao fazer upload do arquivo' }
      };
    }

    // Obter URL pública do arquivo
    const { data: publicUrlData } = supabase.storage
      .from('logos')
      .getPublicUrl(fileName);

    if (!publicUrlData?.publicUrl) {
      // Tentar remover arquivo que foi enviado já que não conseguimos gerar URL
      await supabase.storage.from('logos').remove([fileName]);
      
      return {
        success: false,
        errors: { _form: 'Erro ao gerar URL pública do logo' }
      };
    }

    // Atualizar dados do tenant
    const { error: updateError } = await supabase
      .from('tenants')
      .update({
        logo_url: publicUrlData.publicUrl,
        logo_storage_path: fileName,
        favicon_url: publicUrlData.publicUrl,
        favicon_storage_path: fileName,
        updated_at: new Date().toISOString()
      })
      .eq('id', tenantData.id);

    if (updateError) {
      console.error('Erro ao atualizar tenant:', updateError);
      
      // Tentar remover arquivo que foi enviado já que não conseguimos atualizar o banco
      await supabase.storage.from('logos').remove([fileName]);
      
      return {
        success: false,
        errors: { _form: 'Erro ao salvar informações do logo' }
      };
    }

    // Revalidar cache
    revalidatePath('/academia/configuracoes');

    return {
      success: true,
      data: {
        logoUrl: publicUrlData.publicUrl,
        logoStoragePath: fileName
      }
    };

  } catch (error) {
    console.error('Erro ao fazer upload do logo:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' }
    };
  }
}

/**
 * Remove o logo atual da academia
 */
export async function removeLogo() {
  try {
    // Verificar autenticação
    const { user } = await requireAuth();
    
    // Obter slug do tenant atual
    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      return {
        success: false,
        errors: { _form: 'Tenant não encontrado' }
      };
    }

    const supabase = await createClient();

    // Buscar dados do tenant
    const { data: tenantData, error: tenantError } = await supabase
      .from('tenants')
      .select('id, logo_storage_path, favicon_storage_path')
      .eq('slug', tenantSlug)
      .single();

    if (tenantError || !tenantData) {
      console.error('Erro ao buscar tenant:', tenantError);
      return {
        success: false,
        errors: { _form: 'Erro ao buscar dados da academia' }
      };
    }

    // Remover arquivo do storage se existir
    if (tenantData.logo_storage_path) {
      const { error: deleteError } = await supabase.storage
        .from('logos')
        .remove([tenantData.logo_storage_path]);
      
      if (deleteError) {
        console.error('Erro ao remover arquivo:', deleteError);
        // Continuar mesmo com erro no storage
      }
    }

    // Atualizar dados do tenant para remover logo
    const { error: updateError } = await supabase
      .from('tenants')
      .update({
        logo_url: null,
        logo_storage_path: null,
        favicon_url: null,
        favicon_storage_path: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', tenantData.id);

    if (updateError) {
      console.error('Erro ao atualizar tenant:', updateError);
      return {
        success: false,
        errors: { _form: 'Erro ao remover informações do logo' }
      };
    }

    // Revalidar cache
    revalidatePath('/academia/configuracoes');

    return {
      success: true,
      data: null
    };

  } catch (error) {
    console.error('Erro ao remover logo:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' }
    };
  }
} 