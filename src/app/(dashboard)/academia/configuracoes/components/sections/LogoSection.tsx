'use client';

import { But<PERSON> } from '@/components/ui/button';
import { UploadCloud, Image as ImageIcon, Trash2, Loader2, Edit } from 'lucide-react';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';
import { useLogoManagement } from '../../hooks/use-logo-management';
import { LogoUploadProvider, useLogoUpload } from '../../hooks/use-logo-upload';
import { LogoUploadModal } from '../LogoUploadModal';

function LogoSectionContent() {
  const {
    logoData,
    isLoading,
    loadCurrentLogo,
    handleUpload,
    handleRemove,
    isRemoving,
  } = useLogoManagement();

  const { 
    setFile, 
    setLogoUrl, 
    temporaryLogoUrl, 
    hasPendingChanges, 
    file,
    clearTemporary 
  } = useLogoUpload();
  const [dragActive, setDragActive] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Carregar logo ao montar o componente
  useEffect(() => {
    loadCurrentLogo();
  }, [loadCurrentLogo]);

  // Sincronizar logoUrl com o contexto
  useEffect(() => {
    if (logoData.logoUrl) {
      setLogoUrl(logoData.logoUrl);
    }
  }, [logoData.logoUrl, setLogoUrl]);

  // Handlers para drag and drop
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      // Validar arquivo
      const validTypes = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg'];
      const maxSize = 10 * 1024 * 1024; // 10MB
      
      if (!validTypes.includes(file.type)) {
        alert('Arquivo deve ser SVG, PNG ou JPG');
        return;
      }
      
      if (file.size > maxSize) {
        alert('Arquivo deve ter no máximo 10MB');
        return;
      }

      // Limpar estado temporário primeiro
      clearTemporary();
      
      // Aguardar um pequeno tempo para garantir que o estado foi limpo
      setTimeout(() => {
        setFile(file);
        setModalOpen(true);
      }, 50);
    }
  }, [setFile, clearTemporary]);

  // Handler para input file
  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      // Validar arquivo
      const validTypes = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg'];
      const maxSize = 10 * 1024 * 1024; // 10MB
      
      if (!validTypes.includes(file.type)) {
        alert('Arquivo deve ser SVG, PNG ou JPG');
        return;
      }
      
      if (file.size > maxSize) {
        alert('Arquivo deve ter no máximo 10MB');
        return;
      }

      // Limpar estado temporário primeiro
      clearTemporary();
      
      // Aguardar um pequeno tempo para garantir que o estado foi limpo
      setTimeout(() => {
        setFile(file);
        setModalOpen(true);
      }, 50);
    }
  };

  // Handler para atualização do logo
  const handleLogoUpdated = (url: string | null) => {
    // Recarregar dados do logo
    loadCurrentLogo();
  };

  // Handler para salvar o logo temporário
  const handleSaveLogo = async () => {
    if (!file || !hasPendingChanges) return;

    try {
      setIsSaving(true);
      await handleUpload(file);
      // Limpar o estado temporário após salvar
      clearTemporary();
      // Recarregar o logo atual
      loadCurrentLogo();
    } catch (error) {
      console.error('Erro ao salvar logo:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Handler para cancelar mudanças
  const handleCancelChanges = () => {
    clearTemporary();
  };

  // Handler para deletar logo
  const handleDeleteLogo = async () => {
    if (window.confirm('Tem certeza que deseja excluir o logo da academia?')) {
      await handleRemove();
      clearTemporary();
    }
  };

  // Determinar qual logo mostrar (temporário ou atual)
  const displayLogoUrl = temporaryLogoUrl || logoData.logoUrl;

  return (
    <>
      <section className="space-y-8 rounded-2xl border border-border bg-card p-6 shadow-sm">
        <div>
          <h2 className="text-lg font-semibold">Logo da Academia</h2>
          <p className="text-sm text-muted-foreground">
            Faça upload do logo da sua academia. O logo será exibido na página de check-in e nos e-mails.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-x-8 gap-y-6 md:grid-cols-3">
          <div className="md:col-span-1">
            <h3 className="font-medium">Preview</h3>
            <p className="text-xs text-muted-foreground">
              É assim que seu logo irá aparecer.
            </p>
          </div>
          <div className="md:col-span-2">
            <div className="flex h-40 items-center justify-center rounded-lg border border-border bg-background/50 p-8 relative">
              {isLoading ? (
                <div className="text-center text-muted-foreground">
                  <Loader2 className="mx-auto h-10 w-10 animate-spin" />
                  <p className="mt-2 text-sm">Carregando...</p>
                </div>
              ) : displayLogoUrl ? (
                <div className="relative">
                  {/* Padrão xadrez transparente como fundo */}
                  <div 
                    className="absolute inset-0 rounded-full opacity-20"
                    style={{
                      backgroundImage: `
                        linear-gradient(45deg, #ccc 25%, transparent 25%), 
                        linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #ccc 75%), 
                        linear-gradient(-45deg, transparent 75%, #ccc 75%)
                      `,
                      backgroundSize: '8px 8px',
                      backgroundPosition: '0 0, 0 4px, 4px -4px, -4px 0px'
                    }}
                  />
                  <Image 
                    src={displayLogoUrl} 
                    alt="Logo da academia" 
                    width={128} 
                    height={128} 
                    className="max-h-24 w-auto object-contain rounded-full relative z-10"
                    unoptimized // Para URLs do Supabase Storage
                  />
                  {temporaryLogoUrl && (
                    <div className="absolute -top-2 -right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full z-20">
                      Não salvo
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center text-muted-foreground">
                  <ImageIcon className="mx-auto h-10 w-10" />
                  <p className="mt-2 text-sm">Sem logo</p>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 gap-x-8 gap-y-6 md:grid-cols-3">
          <div className="md:col-span-1">
            <h3 className="font-medium">Arquivo do Logo</h3>
            <p className="text-xs text-muted-foreground">
              Clique para carregar ou arraste e solte.
            </p>
          </div>
          <div className="md:col-span-2">
            <div 
              className={`flex flex-col items-center justify-center gap-4 rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
                dragActive 
                  ? 'border-primary bg-primary/5' 
                  : 'border-border hover:border-primary/50'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                <UploadCloud className="h-6 w-6 text-muted-foreground" />
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">
                  <label 
                    htmlFor="logo-upload" 
                    className="cursor-pointer font-semibold text-primary hover:text-primary/90"
                  >
                    Clique para fazer upload
                  </label>
                  {' ou arraste e solte'}
                </p>
                <p className="text-xs text-muted-foreground">SVG, PNG, JPG (max. 10MB)</p>
              </div>
              <input 
                type="file" 
                className="sr-only" 
                id="logo-upload" 
                accept="image/*"
                onChange={handleFileInput}
              />
            </div>
            
            {/* Botões de ação */}
            <div className="mt-4 flex justify-end gap-2">
              {hasPendingChanges ? (
                <>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={handleCancelChanges}
                    disabled={isSaving}
                  >
                    Cancelar
                  </Button>
                  <Button 
                    size="sm" 
                    onClick={handleSaveLogo}
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Salvando...
                      </>
                    ) : (
                      'Salvar Logo'
                    )}
                  </Button>
                </>
              ) : displayLogoUrl ? (
                <>
                  <Button 
                    variant="destructive" 
                    size="sm" 
                    onClick={handleDeleteLogo}
                    disabled={isRemoving}
                  >
                    {isRemoving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Excluindo...
                      </>
                    ) : (
                      <>
                        <Trash2 className="mr-2 h-4 w-4" />
                        Excluir Logo
                      </>
                    )}
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => {
                      // Limpar estado temporário primeiro
                      clearTemporary();
                      
                      // Aguardar um pequeno tempo para garantir que o estado foi limpo
                      setTimeout(() => {
                        setModalOpen(true);
                      }, 50);
                    }}
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    Editar Logo
                  </Button>
                </>
              ) : null}
            </div>
          </div>
        </div>
      </section>

      <LogoUploadModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        onLogoUpdated={handleLogoUpdated}
      />
    </>
  );
}

export default function LogoSection() {
  return (
    <LogoUploadProvider>
      <LogoSectionContent />
    </LogoUploadProvider>
  );
} 