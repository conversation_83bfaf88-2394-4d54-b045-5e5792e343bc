'use client';

import { useMemo } from 'react';
import { GraduationLevel } from '@/services/belts/levels';
import { BeltDisplay, beltColorTranslation } from '@/components/belt';

interface Props {
  initialLevels: GraduationLevel[];
}

export default function GraduationLevelsClient({ initialLevels }: Props) {
  const levels = useMemo(() => initialLevels, [initialLevels]);

  return (
    <div className="space-y-4">
      {levels.map((lvl) => (
        <div
          key={lvl.id}
          className="flex items-center justify-between p-4 border rounded-md"
        >
          <div className="flex items-center gap-3">
            <BeltDisplay belt={lvl.belt_color} stripes={lvl.degree} size="md" />
            <span className="text-sm text-gray-700">
              {beltColorTranslation[lvl.belt_color]} {lvl.degree > 0 && `- ${lvl.degree}º grau`}
            </span>
          </div>
          <span className="text-sm text-gray-600">{lvl.members_count} alunos</span>
        </div>
      ))}
    </div>
  );
} 