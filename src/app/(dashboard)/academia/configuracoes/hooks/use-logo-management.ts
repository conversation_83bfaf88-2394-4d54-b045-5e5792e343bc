'use client';

import { useCallback, useState } from 'react';
import { getCurrentLogo, uploadLogo, removeLogo } from '../actions/logo-actions';
import { useToast } from '@/hooks/ui/use-toast';

interface LogoData {
  logoUrl: string | null;
  logoStoragePath: string | null;
}

export function useLogoManagement() {
  const [logoData, setLogoData] = useState<LogoData>({ logoUrl: null, logoStoragePath: null });
  const [isLoading, setIsLoading] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const { toast } = useToast();

  // Carregar logo atual
  const loadCurrentLogo = useCallback(async () => {
    try {
      setIsLoading(true);
      const result = await getCurrentLogo();
      
      if (result.success && result.data) {
        setLogoData(result.data);
      } else {
        toast({
          title: 'Erro ao carregar logo',
          description: result.errors?._form || 'Erro desconhecido',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Erro ao carregar logo',
        description: 'Erro interno do servidor',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Validar arquivo antes do upload
  const validateFile = (file: File): string | null => {
    const validTypes = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!validTypes.includes(file.type)) {
      return 'Arquivo deve ser SVG, PNG ou JPG';
    }
    
    if (file.size > maxSize) {
      return 'Arquivo deve ter no máximo 10MB';
    }
    
    return null;
  };

  // Fazer upload do logo
  const handleUpload = async (file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      toast({
        title: 'Arquivo inválido',
        description: validationError,
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsUploading(true);
      const formData = new FormData();
      formData.append('file', file);
      
      const result = await uploadLogo(formData);
      
      if (result.success && result.data) {
        setLogoData(result.data);
        toast({
          title: 'Logo atualizado com sucesso',
          description: 'O logo da academia foi atualizado.',
        });
      } else {
        let errorMessage = 'Erro desconhecido';
        if (result.errors) {
          if ('_form' in result.errors) {
            errorMessage = result.errors._form;
          } else {
            errorMessage = 'Arquivo inválido';
          }
        }
        toast({
          title: 'Erro ao fazer upload',
          description: errorMessage,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Erro ao fazer upload',
        description: 'Erro interno do servidor',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Remover logo
  const handleRemove = async () => {
    try {
      setIsRemoving(true);
      const result = await removeLogo();
      
      if (result.success) {
        setLogoData({ logoUrl: null, logoStoragePath: null });
        toast({
          title: 'Logo removido com sucesso',
          description: 'O logo da academia foi removido.',
        });
      } else {
        toast({
          title: 'Erro ao remover logo',
          description: result.errors?._form || 'Erro desconhecido',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Erro ao remover logo',
        description: 'Erro interno do servidor',
        variant: 'destructive',
      });
    } finally {
      setIsRemoving(false);
    }
  };

  return {
    logoData,
    isLoading,
    isUploading,
    isRemoving,
    loadCurrentLogo,
    handleUpload,
    handleRemove,
  };
} 