'use client';

import { UploadCloud } from 'lucide-react';
import { useState, useRef, ChangeEvent, DragEvent } from 'react';

interface FileDropzoneProps {
  onFileChange: (file: File | null) => void;
}

export default function FileDropzone({ onFileChange }: FileDropzoneProps) {
  const [isDragActive, setIsDragActive] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0] || null;
    if (selectedFile && selectedFile.name.endsWith('.csv')) {
        setFile(selectedFile);
        onFileChange(selectedFile);
    } else {
        setFile(null);
        onFileChange(null);
    }
  };

  const handleDragEnter = (event: DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragActive(true);
  };

  const handleDragLeave = (event: DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragActive(false);
  };

  const handleDragOver = (event: DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDrop = (event: DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragActive(false);

    const droppedFile = event.dataTransfer.files?.[0] || null;
    if (droppedFile && droppedFile.name.endsWith('.csv')) {
      setFile(droppedFile);
      onFileChange(droppedFile);
    } else {
        setFile(null);
        onFileChange(null);
    }
  };

  const openFileDialog = () => {
    inputRef.current?.click();
  };

  return (
    <div
      onClick={openFileDialog}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      className={`flex flex-col items-center justify-center w-full min-h-[200px] border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
        isDragActive ? 'border-primary bg-primary/10' : 'border-input hover:border-input/80'
      }`}
    >
      <input
        ref={inputRef}
        type="file"
        className="hidden"
        onChange={handleFileChange}
        accept=".csv"
      />
      <div className="flex flex-col items-center justify-center pt-5 pb-6 text-center">
        <UploadCloud className="w-10 h-10 mb-4 text-muted-foreground" />
        {file ? (
            <p className="font-semibold">{file.name}</p>
        ) : (
            <>
                <p className="mb-2 text-sm text-muted-foreground">
                    Arraste seus arquivos aqui ou <span className="font-semibold text-primary">procure</span>
                </p>
                <p className="text-xs text-muted-foreground">Suporta arquivos .csv</p>
            </>
        )}
      </div>
    </div>
  );
} 