'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ReactNode, useState } from 'react';
import ImportCsvModal from './ImportCsvModal';
import { useRouter } from 'next/navigation';
import <PERSON> from 'papaparse';

interface ImportCardProps {
  icon: ReactNode;
  title: string;
  description: string;
}

export default function ImportCard({ icon, title, description }: ImportCardProps) {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleImportClick = () => {
    setIsModalOpen(true);
  };

  const handleContinue = (file: File) => {
    if (!file) {
      setIsModalOpen(false);
      return;
    }

    const processAndRedirect = () => {
      Papa.parse(file, {
        complete: result => {
          const data = result.data as string[][];
          if (data.length > 0) {
            const headers = data[0];
            const rows = data
              .slice(1)
              .filter(row => row.some(cell => cell.trim() !== ''));

            sessionStorage.setItem('csv_headers', JSON.stringify(headers));
            sessionStorage.setItem('csv_data', JSON.stringify(rows));
            router.push('/alunos/importar?from=/academia/configuracoes/migracao');
          }
        },
        header: false,
      });
    };

    if (title.toLowerCase().includes('alunos')) {
      processAndRedirect();
    } else {
      console.log('Arquivo selecionado para importação:', file.name);
      // A lógica para outros tipos de importação será implementada aqui.
    }

    setIsModalOpen(false);
  };

  const modalDescription = `Você pode importar seus dados de ${title
    .toLowerCase()
    .replace(
      'importar ',
      '',
    )} do seu provedor anterior usando arquivos CSV. Arquivos CSV contêm dados tabulares, semelhantes a planilhas do Excel, e são usados para armazenar e transferir dados entre diferentes aplicativos.`;

  return (
    <>
      <Card className="flex h-full flex-col p-6 text-left">
        <div className="flex-grow space-y-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-accent text-primary">
            {icon}
          </div>
          <h3 className="text-lg font-semibold">{title}</h3>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
        <Button className="mt-6 w-full" onClick={handleImportClick}>
          IMPORTAR
        </Button>
      </Card>
      <ImportCsvModal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        title={title}
        description={modalDescription}
        onContinue={handleContinue}
      />
    </>
  );
} 