'use client';

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import FileDropzone from './FileDropzone';
import { useState } from 'react';

interface ImportCsvModalProps {
  title: string;
  description: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onContinue: (file: File) => void;
}

export default function ImportCsvModal({
  title,
  description,
  open,
  onOpenChange,
  onContinue,
}: ImportCsvModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleContinue = () => {
    if (selectedFile) {
      onContinue(selectedFile);
    }
  };

  const handleFileChange = (file: File | null) => {
    setSelectedFile(file);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader className="text-left">
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <div className="py-4">
            <h3 className="mb-2 text-sm font-medium text-left">Escolha o arquivo para importar</h3>
            <FileDropzone onFileChange={handleFileChange} />
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Cancelar</Button>
          </DialogClose>
          <Button onClick={handleContinue} disabled={!selectedFile}>
            Continuar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 