'use client';

import { usePageTitle } from '@/contexts/PageTitleContext';
import { useEffect } from 'react';
import { DownloadCloud } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import ImportSection from './components/ImportSection';
import MigrationFlowDiagram from './components/MigrationFlowDiagram';
import Link from 'next/link';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

export default function MigrationPage() {
  const { setPageTitle, setPageSubtitle, setPageIcon } = usePageTitle();

  useEffect(() => {
    setPageTitle('Central de Migração');
    setPageSubtitle('Importe seus dados do seu provedor de software anterior para o ApexDojo.');
    setPageIcon(<DownloadCloud className="h-6 w-6 text-primary" />);
  }, [setPageTitle, setPageSubtitle, setPageIcon]);


  return (
    <div className="space-y-6">
      <div className="mb-4">
        <Link
          href="/academia/configuracoes"
          className="flex items-center gap-2 text-sm font-semibold text-foreground hover:text-primary transition-colors"
        >
          <ArrowLeftIcon className="h-4 w-4" />
          <span>Voltar para Configurações</span>
        </Link>
      </div>
      <Card>
        <CardContent className="space-y-8 p-6">
          <h2 className="text-left text-2xl font-semibold">
            Importe seus dados para o ApexDojo
          </h2>
          <div className="flex justify-center">
            <MigrationFlowDiagram />
          </div>
          <ImportSection />
        </CardContent>
      </Card>
    </div>
  );
} 