'use client';

import { useState, startTransition } from 'react';
import { Switch } from '@/components/ui/switch';
import { toggleModalityAction } from '../../actions/toggle-modality';
import { toast } from '@/hooks/ui/use-toast';

type Modality = {
  id: string;
  slug: string;
  name: string;
  enabled: boolean;
};

export default function ModalitiesClient({ initialModalities }: { initialModalities: Modality[] }) {
  const [modalities, setModalities] = useState<Modality[]>(initialModalities);

  const handleToggle = (modalityId: string) => (checked: boolean) => {
    // Optimistic update
    setModalities((prev) => prev.map((m) => (m.id === modalityId ? { ...m, enabled: checked } : m)));

    startTransition(async () => {
      const result = await toggleModalityAction({ modalityId, enabled: checked });
      if (!result.success) {
        // revert
        setModalities((prev) => prev.map((m) => (m.id === modalityId ? { ...m, enabled: !checked } : m)));
        toast({ title: 'Erro', description: result.errors?._form || 'Falha ao atualizar modalidade', variant: 'destructive' });
      }
    });
  };

  return (
    <div className="space-y-4">
      {modalities.map((modality) => (
        <div key={modality.id} className="flex items-center justify-between p-4 rounded-md border">
          <span>{modality.name}</span>
          <Switch checked={modality.enabled} onCheckedChange={handleToggle(modality.id)} />
        </div>
      ))}
    </div>
  );
} 