'use client';

import React, { useState, startTransition, useCallback } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { type ModalitySettings } from '@/services/modalities/settings';
import { type GraduationLevelWithRequirements } from '../hooks/use-modality-data';
import RanksFieldArray from './RanksFieldArray';
import { beltColorTranslation, BeltColor } from '@/components/belt';
import { HelpCircle, Download, RefreshCw, Check } from 'lucide-react';
import {
  TooltipContent,
  TooltipPortal,
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { presetLabels, getRanksByPreset } from '@/store/rankPresets';
import { toast } from 'sonner';
import { updateModalitySettingsAction } from '@/app/(dashboard)/academia/actions/update-modality-settings';
import { SuccessFeedback } from './SuccessFeedback';
import { DebugInfo } from './DebugInfo';

const rankSchema = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  degree: z.number().min(0),
  belt_color: z.string(),
  stripe_color: z.string(),
  show_center_line: z.boolean().optional(),
  center_line_color: z.string().optional(),
  sessions: z.number().min(0).optional(),
  minimum_age: z.number().min(0).optional(),
});

const formSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  type: z.string().optional(),
  level_rank_style: z.string().optional(),
  secondary_color: z.string().optional(),
  auto_assign_initial_rank: z.boolean(),
  promotion_setting: z.enum(['manual', 'automatic']),
  require_sessions: z.boolean(),
  require_minimum_age: z.boolean(),
  promotion_fee: z.number().min(0).optional(),
  levels: z.array(rankSchema),
});

type FormData = z.infer<typeof formSchema>;

interface Props {
  modality: {
    id: string;
    slug: string;
    name: string;
    enabled: boolean;
  };
  modalitySettings: ModalitySettings | null;
  levelsWithRequirements: GraduationLevelWithRequirements[];
  onDataUpdate?: () => Promise<void>;
  isRefetching?: boolean;
}



export default function ModalityEditForm({
  modality,
  modalitySettings,
  levelsWithRequirements,
  onDataUpdate,
  isRefetching = false,
}: Props) {
  console.log('🔍 ModalityEditForm props:', {
    modality: modality.name,
    modalitySettings,
    promotion_setting: modalitySettings?.promotion_setting,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedPreset, setSelectedPreset] = useState<string>('');
  const [showSuccessState, setShowSuccessState] = useState(false);

  // Função para tocar som de sucesso (opcional)
  // const playSuccessSound = useCallback(() => {
  //   try {
  //     // Criar um som sintético usando Web Audio API
  //     const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
  //     const oscillator = audioContext.createOscillator();
  //     const gainNode = audioContext.createGain();

  //     oscillator.connect(gainNode);
  //     gainNode.connect(audioContext.destination);

  //     oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
  //     oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);

  //     gainNode.gain.setValueAtTime(0, audioContext.currentTime);
  //     gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
  //     gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

  //     oscillator.start(audioContext.currentTime);
  //     oscillator.stop(audioContext.currentTime + 0.2);
  //   } catch (error) {
  //     // Falha silenciosa se o áudio não estiver disponível
  //     console.debug('Audio not available:', error);
  //   }
  // }, []);

  // Log para debug dos defaultValues
  const promotionSettingValue = (modalitySettings?.promotion_setting as 'manual' | 'automatic') || 'manual';
  console.log('🔍 Promotion setting calculation:', {
    modalitySettings_promotion_setting: modalitySettings?.promotion_setting,
    cast_result: modalitySettings?.promotion_setting as 'manual' | 'automatic',
    final_value: promotionSettingValue,
  });

  // Detectar automaticamente se há dados de requirements para marcar os checkboxes
  const hasExistingSessions = levelsWithRequirements.some(level => 
    level.requirements?.sessions && level.requirements.sessions > 0
  );
  const hasExistingMinAge = levelsWithRequirements.some(level => 
    level.requirements?.minimum_age && level.requirements.minimum_age > 0
  );

  // Determinar valores dos checkboxes: preferir dados salvos, ou detectar automaticamente
  const shouldRequireSessions = modalitySettings?.require_sessions ?? hasExistingSessions;
  const shouldRequireMinAge = modalitySettings?.require_minimum_age ?? hasExistingMinAge;

  console.log('🔍 Checkbox calculation:', {
    modalitySettings_require_sessions: modalitySettings?.require_sessions,
    hasExistingSessions,
    shouldRequireSessions,
    modalitySettings_require_minimum_age: modalitySettings?.require_minimum_age,
    hasExistingMinAge,
    shouldRequireMinAge,
  });

  const methods = useForm<FormData>({
    resolver: zodResolver(formSchema),
    mode: 'onChange',
    defaultValues: {
      name: modality.name,
      type: modalitySettings?.type || 'Artes Marciais',
      level_rank_style:
        modalitySettings?.level_rank_style || '',
      secondary_color: modalitySettings?.secondary_color || '',
      auto_assign_initial_rank:
        modalitySettings?.auto_assign_initial_rank || false,
      promotion_setting: promotionSettingValue,
      require_sessions: shouldRequireSessions,
      require_minimum_age: shouldRequireMinAge,
      promotion_fee: modalitySettings?.promotion_fee || undefined,
      levels: levelsWithRequirements.map(l => {
        const beltColorName = l.belt_color as BeltColor;

        return {
          id: l.id,
          name: l.label || `${beltColorTranslation[beltColorName]}${l.degree > 0 ? ` - ${l.degree}º grau` : ''}`,
          degree: l.degree,
          belt_color: beltColorName,
          stripe_color: l.stripe_color || '#FFFFFF',
          show_center_line: l.show_center_line ?? false,
          center_line_color: l.center_line_color || '',
          sessions: l.requirements?.sessions || 0,
          minimum_age: l.requirements?.minimum_age || undefined,
        };
      }),
    },
  });

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = methods;

  const watchedValues = watch();

  // Log de erros de validação
  React.useEffect(() => {
    if (Object.keys(errors).length > 0) {
      console.log('❌ Erros de validação do formulário:', errors);
    }
  }, [errors]);

  // Função para aplicar preset selecionado
  const applyPreset = () => {
    if (!selectedPreset) return;

    const presetRanks = getRanksByPreset(selectedPreset);
    if (presetRanks) {
      const formattedLevels = presetRanks.map(rank => ({
        id: undefined,
        name: rank.name,
        degree: rank.degree,
        belt_color: rank.belt_color,
        stripe_color: rank.stripe_color,
        show_center_line: rank.show_center_line ?? false,
        center_line_color: rank.center_line_color ?? '',
        sessions: rank.sessions ?? 0,
        minimum_age: rank.minimum_age,
      }));
      
      setValue('levels', formattedLevels, { shouldValidate: true });
      setValue('level_rank_style', selectedPreset, { shouldValidate: true });
      setSelectedPreset('');
      
      toast.success('Preset aplicado', {
        description: `Graduações do preset "${selectedPreset}" foram aplicadas.`,
        position: 'bottom-right',
      });
    }
  };

  const onSubmit = async (data: FormData) => {
    console.log('🔄 Formulário sendo submetido com dados:', data);
    console.log('📊 Níveis sendo enviados:', data.levels);
    setIsSubmitting(true);

    startTransition(async () => {
      try {
        const result = await updateModalitySettingsAction({
          modalityId: modality.id,
          name: data.name,
          type: data.type,
          level_rank_style: data.level_rank_style,
          secondary_color: data.secondary_color,
          auto_assign_initial_rank: data.auto_assign_initial_rank,
          promotion_setting: data.promotion_setting,
          promotion_fee: data.promotion_fee,
          levels: data.levels,
          require_sessions: data.require_sessions,
          require_minimum_age: data.require_minimum_age,
        });

        if (result.success) {
          // Mostrar estado de sucesso no botão
          setShowSuccessState(true);

          // Tocar som de sucesso
          // playSuccessSound();

          toast.success('Configurações salvas!', {
            description: 'Todas as alterações foram aplicadas com sucesso. Os dados foram atualizados em tempo real.',
            duration: 4000,
            position: 'bottom-right',
          });

          // Atualizar dados em tempo real sem reload
          if (onDataUpdate) {
            await onDataUpdate();
          }

          // O estado será removido pelo callback do SuccessFeedback
        } else {
          toast.error(result.errors?._form || 'Falha ao salvar as configurações.', { position: 'bottom-right' });
        }
      } catch (error) {
        toast.error('Falha ao salvar as configurações.', { position: 'bottom-right' });
      } finally {
        setIsSubmitting(false);
      }
    });
  };

  return (
    <FormProvider {...methods}>
      <div className="space-y-8">
        <DebugInfo 
          modalitySettings={modalitySettings}
          levelsWithRequirements={levelsWithRequirements}
        />
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold">Detalhes da Modalidade</h3>
              {isRefetching && (
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <RefreshCw className="h-3 w-3 animate-spin" />
                  <span>Atualizando...</span>
                </div>
              )}
              {showSuccessState && (
                <div className="flex items-center gap-1 text-sm text-green-600 animate-in fade-in-50 duration-300">
                  <Check className="h-3 w-3 animate-bounce" />
                  <span>Salvo</span>
                </div>
              )}
            </div>
            <Button variant="outline" size="sm" disabled>
              DUPLICAR
            </Button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Nome da Modalidade */}
              <div className="space-y-2">
                <Label htmlFor="name">NOME DA MODALIDADE</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Jiu-Jitsu Brasileiro"
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name.message}</p>
                )}
              </div>

              {/* Tipo */}
              <div className="space-y-2">
                <Label htmlFor="type">TIPO</Label>
                <Select
                  value={watchedValues.type}
                  onValueChange={value => setValue('type', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Artes Marciais">
                      Artes Marciais
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Estilo de Nível/Graduação - Agora apenas informativo */}
              {/* <div className="space-y-2">
                <Label htmlFor="level_rank_style">
                  ESTILO DE NÍVEL/GRADUAÇÃO
                </Label>
                <Input
                  id="level_rank_style"
                  {...register('level_rank_style')}
                  placeholder="Ex: Jiu-Jitsu Brasileiro Adulto"
                />
                <p className="text-xs text-muted-foreground">
                  Campo informativo. Use os presets abaixo para aplicar graduações predefinidas.
                </p>
              </div> */}
            </div>

            {/* Seção de Presets */}
            <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">Aplicar Preset de Graduações</Label>
                  <p className="text-xs text-muted-foreground">
                    Selecione um preset para aplicar graduações predefinidas (substituirá as graduações atuais)
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Select
                  value={selectedPreset}
                  onValueChange={setSelectedPreset}
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="Selecione um preset" />
                  </SelectTrigger>
                  <SelectContent>
                    {presetLabels.map(label => (
                      <SelectItem key={label} value={label}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button 
                      type="button" 
                      variant="outline" 
                      disabled={!selectedPreset}
                      className="flex items-center gap-2"
                    >
                      <Download className="h-4 w-4" />
                      Aplicar Preset
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Aplicar Preset de Graduações</AlertDialogTitle>
                      <AlertDialogDescription>
                        Tem certeza que deseja aplicar o preset "{selectedPreset}"? 
                        Isso irá substituir todas as graduações atuais pelas graduações do preset selecionado.
                        Esta ação não pode ser desfeita.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancelar</AlertDialogCancel>
                      <AlertDialogAction onClick={applyPreset}>
                        Aplicar Preset
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>

            {/* Atribuir Graduação Inicial Automaticamente */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5 flex items-center gap-2">
                <Label htmlFor="auto_assign_initial_rank">
                  ATRIBUIR GRADUAÇÃO INICIAL AUTOMATICAMENTE
                </Label>
                <TooltipProvider delayDuration={150}>
                  <TooltipRoot>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 text-muted-foreground cursor-pointer" />
                    </TooltipTrigger>
                    <TooltipPortal>
                      <TooltipContent side="right" sideOffset={4}>
                        Atribui automaticamente a primeira graduação aos novos alunos com base na sua matrícula.
                      </TooltipContent>
                    </TooltipPortal>
                  </TooltipRoot>
                </TooltipProvider>
              </div>
              <Switch
                id="auto_assign_initial_rank"
                checked={watchedValues.auto_assign_initial_rank}
                onCheckedChange={checked =>
                  setValue('auto_assign_initial_rank', checked)
                }
              />
            </div>

            {/* Configuração de Promoção */}
            <div className="space-y-2">
              <Label htmlFor="promotion_setting">
                CONFIGURAÇÃO DE PROMOÇÃO
              </Label>
              <Select
                value={watchedValues.promotion_setting}
                onValueChange={value =>
                  setValue('promotion_setting', value as 'manual' | 'automatic')
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="manual">Promoções Manuais</SelectItem>
                  <SelectItem value="automatic">
                    Promoções Automáticas
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Requisitos para Promoção */}
            <div className="space-y-2">
              <Label>REQUISITOS PARA PROMOÇÃO</Label>
              <div className="grid grid-cols-2 gap-4">
                {/* Aulas */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="require_sessions"
                    checked={watchedValues.require_sessions}
                    onCheckedChange={checked =>
                      setValue('require_sessions', !!checked)
                    }
                  />
                  <Label htmlFor="require_sessions" className="cursor-pointer">
                    Quantidade de Aulas
                  </Label>
                </div>

                {/* Idade Mínima */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="require_minimum_age"
                    checked={watchedValues.require_minimum_age}
                    onCheckedChange={checked =>
                      setValue('require_minimum_age', !!checked)
                    }
                  />
                  <Label htmlFor="require_minimum_age" className="cursor-pointer">
                    Idade Mínima
                  </Label>
                </div>
              </div>
            </div>

            <Button
              type="submit"
              className={`w-full transition-all duration-300 ${
                showSuccessState
                  ? 'bg-green-600 hover:bg-green-700 border-green-600'
                  : ''
              }`}
              disabled={isSubmitting || isRefetching}
            >
              <div className="flex items-center justify-center gap-2">
                {showSuccessState && (
                  <Check className="h-4 w-4" />
                )}
                <span>
                  {isSubmitting
                    ? 'Salvando...'
                    : isRefetching
                      ? 'Atualizando...'
                      : showSuccessState
                        ? 'Salvo com sucesso!'
                        : 'SALVAR MODALIDADE'
                  }
                </span>
              </div>
            </Button>
          </form>
        </div>

        {/* Níveis / Graduações */}
        <RanksFieldArray />
      </div>

      {/* Feedback visual de sucesso */}
      <SuccessFeedback
        show={showSuccessState}
        onComplete={() => setShowSuccessState(false)}
      />
    </FormProvider>
  );
}