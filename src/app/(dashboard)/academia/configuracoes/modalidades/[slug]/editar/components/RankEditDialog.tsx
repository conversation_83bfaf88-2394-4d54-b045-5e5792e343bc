'use client';

import { useState, startTransition } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/hooks/ui/use-toast';
import { BeltDisplay, beltColorTranslation } from '@/components/belt';
import { type GraduationLevel } from '@/services/belts/levels';
import { type BeltLevelRequirements } from '@/services/belts/requirements';
import { upsertRankAction } from '@/app/(dashboard)/academia/actions/upsert-rank';

const formSchema = z.object({
  sessions: z.number().min(0).optional(),
  hours: z.number().min(0).optional(),
  days_in_rank: z.number().min(0).optional(),
  days_attended: z.number().min(0).optional(),
  skill_requirements: z.boolean(),
  minimum_age: z.number().min(0).optional(),
  promotion_fee: z.number().min(0).optional(),
});

type FormData = z.infer<typeof formSchema>;

interface GraduationLevelWithRequirements extends GraduationLevel {
  requirements?: BeltLevelRequirements;
}

interface Props {
  rank: GraduationLevelWithRequirements;
  modality: {
    id: string;
    slug: string;
    name: string;
    enabled: boolean;
  };
  open: boolean;
  onClose: () => void;
}

export default function RankEditDialog({ rank, modality, open, onClose }: Props) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset,
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      sessions: rank.requirements?.sessions || undefined,
      hours: rank.requirements?.hours || undefined,
      days_in_rank: rank.requirements?.days_in_rank || undefined,
      days_attended: rank.requirements?.days_attended || undefined,
      skill_requirements: rank.requirements?.skill_requirements || false,
      minimum_age: rank.requirements?.minimum_age || undefined,
      promotion_fee: rank.requirements?.promotion_fee || undefined,
    },
  });

  const watchedValues = watch();

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);

    startTransition(async () => {
      try {
        const result = await upsertRankAction({
          beltLevelId: rank.id,
          ...data,
        });

        if (result.success) {
          toast({
            title: 'Sucesso',
            description: 'Requisitos da graduação atualizados com sucesso',
          });
          onClose();
          // Optionally refresh the page or update state
          window.location.reload();
        } else {
          toast({
            title: 'Erro',
            description: result.errors?._form || 'Falha ao atualizar os requisitos',
            variant: 'destructive',
          });
        }
      } catch (error) {
        toast({
          title: 'Erro',
          description: 'Falha ao atualizar os requisitos',
          variant: 'destructive',
        });
      } finally {
        setIsSubmitting(false);
      }
    });
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <BeltDisplay 
              belt={rank.belt_color} 
              stripes={rank.degree} 
              size="md" 
              showTranslation={false}
            />
            <span>
              {rank.label || beltColorTranslation[rank.belt_color] || rank.belt_color}
            </span>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            {/* Aulas */}
            <div className="space-y-2">
              <Label htmlFor="sessions">AULAS</Label>
              <Input
                id="sessions"
                type="number"
                min="0"
                {...register('sessions', { valueAsNumber: true })}
                placeholder="0"
              />
              {errors.sessions && (
                <p className="text-sm text-red-500">{errors.sessions.message}</p>
              )}
            </div>

            {/* Horas */}
            <div className="space-y-2">
              <Label htmlFor="hours">HORAS</Label>
              <Input
                id="hours"
                type="number"
                min="0"
                {...register('hours', { valueAsNumber: true })}
                placeholder="0"
              />
              {errors.hours && (
                <p className="text-sm text-red-500">{errors.hours.message}</p>
              )}
            </div>

            {/* Dias na Faixa */}
            <div className="space-y-2">
              <Label htmlFor="days_in_rank">DIAS NA FAIXA</Label>
              <Input
                id="days_in_rank"
                type="number"
                min="0"
                {...register('days_in_rank', { valueAsNumber: true })}
                placeholder="0"
              />
              {errors.days_in_rank && (
                <p className="text-sm text-red-500">{errors.days_in_rank.message}</p>
              )}
            </div>

            {/* Dias de Presença */}
            <div className="space-y-2">
              <Label htmlFor="days_attended">DIAS DE PRESENÇA</Label>
              <Input
                id="days_attended"
                type="number"
                min="0"
                {...register('days_attended', { valueAsNumber: true })}
                placeholder="0"
              />
              {errors.days_attended && (
                <p className="text-sm text-red-500">{errors.days_attended.message}</p>
              )}
            </div>

            {/* Idade Mínima */}
            <div className="space-y-2">
              <Label htmlFor="minimum_age">IDADE MÍNIMA</Label>
              <Input
                id="minimum_age"
                type="number"
                min="0"
                {...register('minimum_age', { valueAsNumber: true })}
                placeholder="0"
              />
              {errors.minimum_age && (
                <p className="text-sm text-red-500">{errors.minimum_age.message}</p>
              )}
            </div>

            {/* Taxa de Promoção */}
            <div className="space-y-2">
              <Label htmlFor="promotion_fee">TAXA DE PROMOÇÃO</Label>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">R$</span>
                <Input
                  id="promotion_fee"
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('promotion_fee', { valueAsNumber: true })}
                  placeholder="0.00"
                />
              </div>
              {errors.promotion_fee && (
                <p className="text-sm text-red-500">{errors.promotion_fee.message}</p>
              )}
            </div>
          </div>

          {/* Requisitos de Habilidade */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="skill_requirements">REQUISITOS DE HABILIDADE</Label>
              <p className="text-sm text-muted-foreground">
                Exigir habilidades específicas para promoção
              </p>
            </div>
            <Switch
              id="skill_requirements"
              checked={watchedValues.skill_requirements}
              onCheckedChange={(checked) => setValue('skill_requirements', checked)}
            />
          </div>

          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Salvando...' : 'Salvar Alterações'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
} 