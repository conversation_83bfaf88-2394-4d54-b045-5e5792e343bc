import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/academia/configuracoes/modalidades">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-foreground">Modalidade não encontrada</h1>
          <p className="text-muted-foreground">
            A modalidade que você está procurando não existe ou foi removida.
          </p>
        </div>
      </div>

      <Card>
        <CardContent className="text-center py-12">
          <div className="space-y-4">
            <div className="text-6xl">🥋</div>
            <h2 className="text-xl font-semibold">Modalidade não encontrada</h2>
            <p className="text-muted-foreground max-w-md mx-auto">
              Verifique se o link está correto ou se a modalidade ainda está disponível 
              para configuração.
            </p>
            <div className="pt-4">
              <Link href="/academia/configuracoes/modalidades">
                <Button>Ver todas as modalidades</Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 