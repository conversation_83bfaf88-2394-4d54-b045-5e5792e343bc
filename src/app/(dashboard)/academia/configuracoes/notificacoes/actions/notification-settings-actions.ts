'use server';

import { z } from 'zod';
import { 
  NotificationConfigService, 
  TenantNotificationConfig,
  UpdateTenantNotificationSettingsData 
} from '@/services/notifications/channels/email/notification-config-service';

// Schema para validação dos dados de atualização
const updateNotificationSettingsSchema = z.object({
  tenantId: z.string().min(1, 'ID do tenant é obrigatório'),
  settings: z.object({
    emailFromName: z.string().optional(),
    emailEnabled: z.boolean().optional(),
    whatsappEnabled: z.boolean().optional(),
    whatsappOptInMessage: z.string().optional(),
    notificationTypes: z.object({
      payment: z.object({
        email: z.boolean(),
        whatsapp: z.boolean(),
        in_app: z.boolean()
      }).optional(),
      class: z.object({
        email: z.boolean(),
        whatsapp: z.boolean(),
        in_app: z.boolean()
      }).optional(),
      system: z.object({
        email: z.boolean(),
        whatsapp: z.boolean(),
        in_app: z.boolean()
      }).optional(),
      enrollment: z.object({
        email: z.boolean(),
        whatsapp: z.boolean(),
        in_app: z.boolean()
      }).optional(),
      event: z.object({
        email: z.boolean(),
        whatsapp: z.boolean(),
        in_app: z.boolean()
      }).optional()
    }).optional(),
    quietHoursStart: z.string().optional(),
    quietHoursEnd: z.string().optional(),
    timezone: z.string().optional()
  })
});

/**
 * Server Action para buscar configurações de notificação do tenant
 */
export async function getNotificationSettings(tenantId: string) {
  try {
    if (!tenantId) {
      return {
        success: false,
        error: 'ID do tenant é obrigatório'
      };
    }

    const configService = new NotificationConfigService();
    const settings = await configService.getTenantConfig(tenantId);

    // Retornar apenas dados serializáveis
    return {
      success: true,
      data: settings
    };

  } catch (error) {
    console.error('Erro ao buscar configurações de notificação:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro interno do servidor'
    };
  }
}

/**
 * Server Action para atualizar configurações de notificação do tenant
 */
export async function updateNotificationSettings(data: unknown) {
  try {
    // Validar dados de entrada
    const validationResult = updateNotificationSettingsSchema.safeParse(data);
    
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Dados inválidos',
        details: validationResult.error.format()
      };
    }

    const { tenantId, settings } = validationResult.data;

    const configService = new NotificationConfigService();
    
    // Atualizar configurações
    await configService.updateTenantSettings(tenantId, settings as UpdateTenantNotificationSettingsData);

    // Buscar configurações atualizadas
    const updatedSettings = await configService.getTenantConfig(tenantId);

    return {
      success: true,
      data: updatedSettings
    };

  } catch (error) {
    console.error('Erro ao atualizar configurações de notificação:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro interno do servidor'
    };
  }
}

/**
 * Server Action para criar configurações padrão para um tenant
 */
export async function createDefaultNotificationSettings(tenantId: string) {
  try {
    if (!tenantId) {
      return {
        success: false,
        error: 'ID do tenant é obrigatório'
      };
    }

    const configService = new NotificationConfigService();
    await configService.createDefaultSettings(tenantId);

    // Buscar configurações criadas
    const settings = await configService.getTenantConfig(tenantId);

    return {
      success: true,
      data: settings
    };

  } catch (error) {
    console.error('Erro ao criar configurações padrão:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro interno do servidor'
    };
  }
}

/**
 * Server Action para validar configurações globais
 */
export async function validateGlobalNotificationConfig() {
  try {
    const validation = NotificationConfigService.validateGlobalConfig();
    
    return {
      success: true,
      data: validation
    };

  } catch (error) {
    console.error('Erro ao validar configurações globais:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro interno do servidor'
    };
  }
}
