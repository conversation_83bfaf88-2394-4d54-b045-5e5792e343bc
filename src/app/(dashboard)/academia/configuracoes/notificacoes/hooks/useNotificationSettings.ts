'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  TenantNotificationConfig,
  UpdateTenantNotificationSettingsData
} from '@/services/notifications/channels/email/notification-config-service';
import { useTenant } from '@/hooks/tenant/use-tenant';
import { CACHE_KEYS } from '@/constants/cache-keys';
import {
  getNotificationSettings,
  updateNotificationSettings,
  createDefaultNotificationSettings
} from '../actions/notification-settings-actions';

export function useNotificationSettings() {
  const { tenantInfo } = useTenant();
  const queryClient = useQueryClient();
  const [isInitialized, setIsInitialized] = useState(false);

  // Query para buscar as configurações usando Server Action
  const {
    data: settings,
    isLoading,
    error,
  } = useQuery({
    queryKey: CACHE_KEYS.NOTIFICATION_SETTINGS.TENANT_CONFIG(tenantInfo?.id || ''),
    queryFn: async (): Promise<TenantNotificationConfig> => {
      if (!tenantInfo?.id) {
        throw new Error('Tenant ID não encontrado');
      }

      const result = await getNotificationSettings(tenantInfo.id);

      if (!result.success) {
        throw new Error(result.error || 'Erro ao buscar configurações');
      }

      return result.data!;
    },
    enabled: !!tenantInfo?.id,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 2,
  });

  // Mutation para atualizar as configurações
  const updateMutation = useMutation({
    mutationFn: async (data: UpdateTenantNotificationSettingsData) => {
      if (!tenantInfo?.id) {
        throw new Error('Tenant ID não encontrado');
      }

      const result = await updateNotificationSettings({
        tenantId: tenantInfo.id,
        settings: data
      });

      if (!result.success) {
        throw new Error(result.error || 'Erro ao atualizar configurações');
      }

      return result.data!;
    },
    onSuccess: (data) => {
      // Invalidar e refetch das configurações
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.NOTIFICATION_SETTINGS.TENANT_CONFIG(tenantInfo?.id || '')
      });

      // Atualizar o cache com os novos dados
      queryClient.setQueryData(
        CACHE_KEYS.NOTIFICATION_SETTINGS.TENANT_CONFIG(tenantInfo?.id || ''),
        data
      );

      toast.success('Configurações salvas com sucesso!', {
        description: 'As configurações de notificação foram atualizadas.',
      });
    },
    onError: (error: any) => {
      console.error('Erro ao salvar configurações:', error);

      toast.error('Erro ao salvar configurações', {
        description: error?.message || 'Ocorreu um erro inesperado. Tente novamente.',
      });
    },
  });

  // Função para atualizar as configurações
  const updateSettings = async (data: UpdateTenantNotificationSettingsData) => {
    return updateMutation.mutateAsync(data);
  };

  // Mutation para criar configurações padrão
  const createDefaultMutation = useMutation({
    mutationFn: async () => {
      if (!tenantInfo?.id) {
        throw new Error('Tenant ID não encontrado');
      }

      const result = await createDefaultNotificationSettings(tenantInfo.id);

      if (!result.success) {
        throw new Error(result.error || 'Erro ao criar configurações padrão');
      }

      return result.data!;
    },
    onSuccess: (data) => {
      // Atualizar o cache com os novos dados
      queryClient.setQueryData(
        CACHE_KEYS.NOTIFICATION_SETTINGS.TENANT_CONFIG(tenantInfo?.id || ''),
        data
      );

      toast.success('Configurações padrão criadas!', {
        description: 'As configurações padrão foram aplicadas.',
      });
    },
    onError: (error: any) => {
      console.error('Erro ao criar configurações padrão:', error);

      toast.error('Erro ao criar configurações padrão', {
        description: error?.message || 'Ocorreu um erro inesperado.',
      });
    },
  });

  // Função para resetar para configurações padrão
  const resetToDefaults = async () => {
    return createDefaultMutation.mutateAsync();
  };

  // Função para testar configurações de e-mail
  const testEmailSettings = async () => {
    if (!tenantInfo?.id || !settings) {
      throw new Error('Configurações não carregadas');
    }

    try {
      // Aqui você pode implementar uma função para testar o envio de e-mail
      // Por exemplo, enviar um e-mail de teste para o administrador
      toast.success('E-mail de teste enviado!', {
        description: 'Verifique sua caixa de entrada.',
      });
    } catch (error) {
      toast.error('Erro ao enviar e-mail de teste', {
        description: 'Verifique as configurações de e-mail.',
      });
    }
  };

  // Função para testar configurações de WhatsApp
  const testWhatsAppSettings = async () => {
    if (!tenantInfo?.id || !settings) {
      throw new Error('Configurações não carregadas');
    }

    try {
      // Aqui você pode implementar uma função para testar o WhatsApp
      toast.success('Teste de WhatsApp realizado!', {
        description: 'Configurações estão funcionando.',
      });
    } catch (error) {
      toast.error('Erro ao testar WhatsApp', {
        description: 'Verifique as configurações de WhatsApp.',
      });
    }
  };

  // Marcar como inicializado quando os dados carregarem
  useEffect(() => {
    if (settings && !isInitialized) {
      setIsInitialized(true);
    }
  }, [settings, isInitialized]);

  return {
    // Dados
    settings,
    isLoading,
    error,
    isInitialized,

    // Estados das mutations
    isSaving: updateMutation.isPending,
    isCreatingDefaults: createDefaultMutation.isPending,
    
    // Funções
    updateSettings,
    resetToDefaults,
    testEmailSettings,
    testWhatsAppSettings,
    
    // Utilitários
    refetch: () => queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.NOTIFICATION_SETTINGS.TENANT_CONFIG(tenantInfo?.id || '')
    }),
  };
}
