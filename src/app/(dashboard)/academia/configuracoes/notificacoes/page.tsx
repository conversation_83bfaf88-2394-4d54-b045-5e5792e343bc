'use client';

import React, { useEffect } from 'react';
import { usePageTitle } from '@/contexts/PageTitleContext';
import { BellIcon } from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { NotificationSettingsForm } from './components/NotificationSettingsForm';

export default function NotificacoesPage() {
  const { setPageTitle, setPageSubtitle, setPageIcon } = usePageTitle();

  useEffect(() => {
    const icon = <BellIcon className="h-6 w-6 text-primary" />;
    
    setPageTitle('Configurações de Notificações');
    setPageSubtitle('Gerencie como e quando sua academia enviará notificações para alunos e responsáveis.');
    setPageIcon(icon);
  }, [setPageTitle, setPageSubtitle, setPageIcon]);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Configurações de Notificações</CardTitle>
        </CardHeader>
        <CardContent>
          <NotificationSettingsForm />
        </CardContent>
      </Card>
    </div>
  );
}
