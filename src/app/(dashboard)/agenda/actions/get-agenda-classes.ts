'use server'

import { z } from 'zod'
import { createClient } from '@/services/supabase/server'
import { getCurrentUser } from '@/services/auth/actions/auth-actions'
import type { AgendaClass, AgendaResponse } from '../types/agenda'

const agendaFiltersSchema = z.object({
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  branchId: z.string().uuid().optional(),
  instructorId: z.string().uuid().optional(),
  classGroupId: z.string().uuid().optional(),
  status: z.array(z.string()).optional()
})

export async function getAgendaClasses(
  filters: z.infer<typeof agendaFiltersSchema>
): Promise<AgendaResponse> {
  try {
    const validatedFilters = agendaFiltersSchema.parse(filters)
    const user = await getCurrentUser()
    
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    // Obter tenant_id dos metadados do usuário
    const tenantId = user.app_metadata?.tenant_id
    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não encontrado' }
      }
    }

    const supabase = await createClient()

    // Construir query base
    let query = supabase
      .from('classes')
      .select(`
        id,
        name,
        description,
        start_time,
        end_time,
        status,
        instructor_id,
        class_group_id,
        branch_id,
        max_capacity,
        attendance_recorded,
        notes,
        users!classes_instructor_id_fkey(
          first_name,
          last_name
        ),
        class_groups(
          name
        ),
        branches(
          name
        )
      `)
      .eq('tenant_id', tenantId)
      .gte('start_time', validatedFilters.startDate)
      .lte('end_time', validatedFilters.endDate)
      .is('deleted_at', null)
      .order('start_time', { ascending: true })

    // Aplicar filtros baseados no role do usuário
    const userRole = user.app_metadata?.role || 'student'
    switch (userRole) {
      case 'admin':
        // Administradores podem ver todas as aulas
        // Aplicar apenas filtros opcionais
        if (validatedFilters.branchId) {
          query = query.eq('branch_id', validatedFilters.branchId)
        }
        if (validatedFilters.instructorId) {
          query = query.eq('instructor_id', validatedFilters.instructorId)
        }
        if (validatedFilters.classGroupId) {
          query = query.eq('class_group_id', validatedFilters.classGroupId)
        }
        break

      case 'instructor':
        // Instrutores podem ver aulas vinculadas a eles e aulas das turmas que instruem
        const instructorQuery = supabase
          .from('instructors')
          .select('id')
          .eq('user_id', user.id)
          .single()

        const { data: instructorData } = await instructorQuery

        if (!instructorData) {
          return {
            success: false,
            errors: { _form: 'Instrutor não encontrado' }
          }
        }

        // Buscar turmas que o instrutor leciona
        const classGroupsQuery = supabase
          .from('class_groups')
          .select('id')
          .eq('instructor_id', user.id)
          .eq('is_active', true)

        const { data: classGroups } = await classGroupsQuery
        const classGroupIds = classGroups?.map(cg => cg.id) || []

        // Filtrar por aulas do instrutor OU aulas das turmas que ele leciona
        query = query.or(
          `instructor_id.eq.${user.id},class_group_id.in.(${classGroupIds.join(',')})`
        )

        // Aplicar filtros opcionais
        if (validatedFilters.branchId) {
          query = query.eq('branch_id', validatedFilters.branchId)
        }
        if (validatedFilters.classGroupId) {
          query = query.eq('class_group_id', validatedFilters.classGroupId)
        }
        break

      case 'student':
        // Alunos podem ver aulas das turmas em que estão inscritos
        const studentQuery = supabase
          .from('students')
          .select('id')
          .eq('user_id', user.id)
          .single()

        const { data: studentData } = await studentQuery

        if (!studentData) {
          return {
            success: false,
            errors: { _form: 'Estudante não encontrado' }
          }
        }

        // Buscar inscrições ativas do aluno
        const enrollmentsQuery = supabase
          .from('class_group_enrollments')
          .select('class_group_id')
          .eq('student_id', studentData.id)
          .eq('status', 'active')

        const { data: enrollments } = await enrollmentsQuery
        const enrolledClassGroupIds = enrollments?.map(e => e.class_group_id) || []

        if (enrolledClassGroupIds.length === 0) {
          return {
            success: true,
            data: []
          }
        }

        // Filtrar por aulas das turmas em que o aluno está inscrito
        query = query.in('class_group_id', enrolledClassGroupIds)

        // Aplicar filtros opcionais
        if (validatedFilters.branchId) {
          query = query.eq('branch_id', validatedFilters.branchId)
        }
        if (validatedFilters.classGroupId && enrolledClassGroupIds.includes(validatedFilters.classGroupId)) {
          query = query.eq('class_group_id', validatedFilters.classGroupId)
        }
        break

      default:
        return {
          success: false,
          errors: { _form: 'Role de usuário não reconhecido' }
        }
    }

    // Aplicar filtro de status se fornecido
    if (validatedFilters.status && validatedFilters.status.length > 0) {
      query = query.in('status', validatedFilters.status)
    }

    const { data: classes, error } = await query

    if (error) {
      console.error('Erro ao buscar aulas da agenda:', error)
      return {
        success: false,
        errors: { _form: 'Erro ao buscar aulas da agenda' }
      }
    }

    // Transformar dados para o formato esperado
    const agendaClasses: AgendaClass[] = classes?.map((classItem: any) => ({
      id: classItem.id,
      name: classItem.name,
      description: classItem.description,
      start_time: classItem.start_time,
      end_time: classItem.end_time,
      status: classItem.status,
      instructor_id: classItem.instructor_id,
      instructor_name: `${classItem.users?.first_name || ''} ${classItem.users?.last_name || ''}`.trim(),
      class_group_id: classItem.class_group_id,
      class_group_name: classItem.class_groups?.name,
      branch_id: classItem.branch_id,
      branch_name: classItem.branches?.name || '',
      max_capacity: classItem.max_capacity,
      attendance_recorded: classItem.attendance_recorded,
      notes: classItem.notes
    })) || []

    return {
      success: true,
      data: agendaClasses
    }

  } catch (error) {
    console.error('Erro na validação ou processamento:', error)
    
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string> = {}
      Object.entries(error.flatten().fieldErrors).forEach(([key, value]) => {
        if (value && Array.isArray(value)) {
          fieldErrors[key] = value[0]
        }
      })
      return {
        success: false,
        errors: fieldErrors
      }
    }

    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' }
    }
  }
} 