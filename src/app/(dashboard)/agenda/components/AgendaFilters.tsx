'use client'

import React, { useState } from 'react'
import { Filter, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useCalendar } from '../contexts/CalendarContext'
import { cn } from '@/lib/utils'

const statusOptions = [
  { value: 'scheduled', label: 'Agendado' },
  { value: 'ongoing', label: 'Em andamento' },
  { value: 'completed', label: 'Concluído' },
  { value: 'cancelled', label: 'Cancelado' },
  { value: 'rescheduled', label: 'Reagendado' }
]

export function AgendaFilters() {
  const { filters, updateFilters, clearFilters, loadEvents } = useCalendar()
  const [isOpen, setIsOpen] = useState(false)
  const [tempFilters, setTempFilters] = useState(filters)

  const hasActiveFilters = Object.keys(filters).length > 0

  const handleStatusChange = (value: string) => {
    const currentStatus = tempFilters.status || []
    const newStatus = currentStatus.includes(value)
      ? currentStatus.filter(s => s !== value)
      : [...currentStatus, value]
    
    setTempFilters(prev => ({
      ...prev,
      status: newStatus.length > 0 ? newStatus : undefined
    }))
  }

  const applyFilters = async () => {
    updateFilters(tempFilters)
    await loadEvents(tempFilters)
    setIsOpen(false)
  }

  const resetFilters = async () => {
    setTempFilters({})
    clearFilters()
    await loadEvents({})
    setIsOpen(false)
  }

  const removeFilter = async (filterKey: string) => {
    const newFilters = { ...filters }
    delete newFilters[filterKey as keyof typeof newFilters]
    updateFilters(newFilters)
    await loadEvents(newFilters)
  }

  return (
    <div className="flex items-center gap-2">
      {/* Filtros ativos */}
      {hasActiveFilters && (
        <div className="flex items-center gap-1">
          {filters.status && filters.status.length > 0 && (
            <Badge variant="secondary" className="gap-1">
              Status: {filters.status.length}
              <button
                onClick={() => removeFilter('status')}
                className="ml-1 hover:bg-muted rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
        </div>
      )}

      {/* Botão de filtros */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={cn(
              "gap-2",
              hasActiveFilters && "border-primary text-primary"
            )}
          >
            <Filter className="h-4 w-4" />
            Filtros
            {hasActiveFilters && (
              <Badge variant="secondary" className="ml-1 px-1.5 py-0.5 text-xs">
                {Object.keys(filters).length}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="end">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Filtros da Agenda</h4>
              {hasActiveFilters && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={resetFilters}
                  className="h-auto p-1 text-xs"
                >
                  Limpar tudo
                </Button>
              )}
            </div>

            {/* Filtro por Status */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Status das Aulas</label>
              <div className="grid grid-cols-1 gap-2">
                {statusOptions.map((status) => (
                  <label
                    key={status.value}
                    className="flex items-center space-x-2 cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={tempFilters.status?.includes(status.value) || false}
                      onChange={() => handleStatusChange(status.value)}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">{status.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Botões de ação */}
            <div className="flex justify-end gap-2 pt-2 border-t">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setTempFilters(filters)
                  setIsOpen(false)
                }}
              >
                Cancelar
              </Button>
              <Button size="sm" onClick={applyFilters}>
                Aplicar Filtros
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
} 