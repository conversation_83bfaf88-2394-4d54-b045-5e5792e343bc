'use client'

import { useState, useCallback } from 'react'
import type { AgendaFilters } from '../types/agenda'

export function useAgendaFilters() {
  const [filters, setFilters] = useState<Partial<AgendaFilters>>({})

  const updateFilter = useCallback(<K extends keyof AgendaFilters>(
    key: K,
    value: AgendaFilters[K]
  ) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }, [])

  const removeFilter = useCallback((key: keyof AgendaFilters) => {
    setFilters(prev => {
      const newFilters = { ...prev }
      delete newFilters[key]
      return newFilters
    })
  }, [])

  const clearFilters = useCallback(() => {
    setFilters({})
  }, [])

  const hasFilters = Object.keys(filters).length > 0

  return {
    filters,
    updateFilter,
    removeFilter,
    clearFilters,
    hasFilters,
    setFilters
  }
} 