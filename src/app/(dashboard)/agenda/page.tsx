'use client'

import { CalendarDays } from 'lucide-react'
import { WeeklyCalendar } from './components/WeeklyCalendar'
import { CalendarProvider } from './contexts/CalendarContext'
import { PageWrapper } from '@/components/layout/page-wrapper'

export default function AgendaPage() {
  return (
    <CalendarProvider>
      <PageWrapper
        title="Agenda"
        subtitle="Visualize todos os seus eventos e aulas agendadas"
        icon={<CalendarDays className="h-6 w-6 text-primary" />}
      >
        <WeeklyCalendar />
      </PageWrapper>
    </CalendarProvider>
  )
} 