import { Button } from "@/components/ui/button"
import { UserPlus, Download, Upload, FileSpreadsheet } from "lucide-react"
import { motion } from "framer-motion"
import {
  TooltipContent,
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
  TooltipPortal
} from "@/components/ui/tooltip"

export function StudentActions() {
  return (
    <motion.div 
      className="flex items-center gap-2"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.2 }}
    >
      <TooltipProvider>
        <TooltipRoot>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              className="h-9 w-9 border-dashed hover:border-primary hover:bg-primary/5 hover:text-primary transition-colors"
            >
              <FileSpreadsheet className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipPortal>
            <TooltipContent>
              <p>Exportar planilha</p>
            </TooltipContent>
          </TooltipPortal>
        </TooltipRoot>

        <TooltipRoot>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              className="h-9 w-9 border-dashed hover:border-primary hover:bg-primary/5 hover:text-primary transition-colors"
            >
              <Upload className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipPortal>
            <TooltipContent>
              <p>Importar alunos</p>
            </TooltipContent>
          </TooltipPortal>
        </TooltipRoot>
      </TooltipProvider>
    </motion.div>
  )
} 