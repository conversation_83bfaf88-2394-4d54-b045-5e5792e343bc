'use server';

import { createClient } from '@/services/supabase/server';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import { TenantExtractorServer } from '@/services/tenant/tenant-extractor-server';

/**
 * Action para obter informações de debug para diagnóstico de problemas
 */
export async function getDebugInfo() {
  try {
    // Informações do usuário
    const user = await getCurrentUser();
    
    if (!user) {
      return {
        success: false,
        error: 'Usuário não autenticado',
        tenant: null,
        user: null
      };
    }
    
    // Informações do tenant
    const tenantExtractor = new TenantExtractorServer();
    const tenantInfo = await tenantExtractor.getTenantIdentifier();
    
    // Buscar informações adicionais do usuário
    const supabase = await createClient();
    
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('tenant_id, email, role')
      .eq('id', user.id)
      .single();
      
    // Verificar filiais
    const { data: branches, error: branchError } = await supabase
      .from('branches')
      .select('id, name, tenant_id')
      .order('name');
      
    return {
      success: true,
      tenant: tenantInfo,
      user: {
        id: user.id,
        email: user.email,
        ...userData
      },
      metadata: {
        tenant_query: userData?.tenant_id ? {
          branches_count: branches?.filter(b => b.tenant_id === userData.tenant_id).length || 0,
          branches: branches?.filter(b => b.tenant_id === userData.tenant_id) || []
        } : null,
        all_branches_count: branches?.length || 0,
        tenant_ids_in_branches: Array.from(new Set(branches?.map(b => b.tenant_id) || []))
      }
    };
  } catch (error) {
    return {
      success: false,
      error: 'Erro ao obter informações de debug: ' + (error instanceof Error ? error.message : String(error))
    };
  }
} 