'use server'

import { createClient } from '@/services/supabase/server'
import { revalidatePath } from 'next/cache'

export interface StudentStats {
  total: number
  active: number
  inactive: number
  pending: number
}

export async function getStudentStats(): Promise<StudentStats> {
  try {
    const supabase = await createClient()
    
    // Buscar número total de alunos usando join com users
    const { count: total } = await supabase
      .from('students')
      .select('*, users!students_user_id_fkey!inner(role)', { count: 'exact', head: true })
      .eq('users.role', 'student')
      .is('deleted_at', null)
      .is('users.deleted_at', null)
    
    // Buscar número de alunos ativos usando status da tabela users
    const { count: active } = await supabase
      .from('students')
      .select('*, users!students_user_id_fkey!inner(role, status)', { count: 'exact', head: true })
      .eq('users.role', 'student')
      .eq('users.status', 'active')
      .is('deleted_at', null)
      .is('users.deleted_at', null)
    
    // Buscar número de alunos inativos usando status da tabela users
    const { count: inactive } = await supabase
      .from('students')
      .select('*, users!students_user_id_fkey!inner(role, status)', { count: 'exact', head: true })
      .eq('users.role', 'student')
      .eq('users.status', 'inactive')
      .is('deleted_at', null)
      .is('users.deleted_at', null)
    
    // Buscar número de alunos com pendências financeiras
    const { count: pending } = await supabase
      .from('students')
      .select('*, users!students_user_id_fkey!inner(role)', { count: 'exact', head: true })
      .eq('users.role', 'student')
      .eq('financial_status', 'overdue')
      .is('deleted_at', null)
      .is('users.deleted_at', null)
    
    return {
      total: total || 0,
      active: active || 0,
      inactive: inactive || 0,
      pending: pending || 0
    }
  } catch (error) {
    console.error('Erro ao buscar estatísticas de alunos:', error)
    // Retornar valores padrão em caso de erro
    return {
      total: 0,
      active: 0,
      inactive: 0,
      pending: 0
    }
  }
}

export async function refreshStudentStats() {
  revalidatePath('/alunos')
} 