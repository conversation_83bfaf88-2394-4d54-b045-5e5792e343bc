"use client";

import { useState, useEffect } from "react";
import { Branch, FilterState } from "../../types";
import { useAlunosFilter } from "../../hooks/use-alunos-filter";
import { FilterBar } from "./filter-bar";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { StudentStats } from "../../actions/student-stats";

interface ClientFiltersProps {
  branches?: Branch[];
  initialStats?: StudentStats;
}

export function ClientFilters({ branches = [], initialStats }: ClientFiltersProps) {
  const { filters, updateFilters } = useAlunosFilter();
  
  // Estado local para a busca com debounce
  const [searchValue, setSearchValue] = useState(filters.search || '');
  
  // Mapeamento do estado dos filtros para o formato esperado pelo FilterBar
  const [filterBarState, setFilterBarState] = useState<FilterState>({
    belt: filters.belt || [],
    status: filters.status || [],
    financialStatus: filters.financialStatus || [],
    branch: filters.branch || [],
    enrollmentStatus: filters.enrollmentStatus || [],
    startDate: filters.startDate,
    endDate: filters.endDate,
  });
  
  // Sincronizar os filtros do hook com o estado local do FilterBar
  useEffect(() => {
    setFilterBarState({
      belt: filters.belt || [],
      status: filters.status || [],
      financialStatus: filters.financialStatus || [],
      branch: filters.branch || [],
      enrollmentStatus: filters.enrollmentStatus || [],
      startDate: filters.startDate,
      endDate: filters.endDate,
    });
  }, [filters]);
  
  // Atualizar searchValue quando filters.search mudar
  useEffect(() => {
    setSearchValue(filters.search || '');
  }, [filters.search]);
  
  // Debounce para pesquisa
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (searchValue !== filters.search) {
        updateFilters({ search: searchValue });
      }
    }, 300);
    
    return () => clearTimeout(timeout);
  }, [searchValue, updateFilters, filters.search]);
  
  // Função para atualizar os filtros quando o FilterBar mudar
  const handleFilterChange = (newFilters: FilterState) => {
    setFilterBarState(newFilters);
    
    // Atualizando os filtros no hook - a lógica de reset de página
    // será gerenciada automaticamente pelo hook useAlunosFilterState
    updateFilters({
      belt: newFilters.belt,
      status: newFilters.status,
      financialStatus: newFilters.financialStatus,
      branch: newFilters.branch,
      enrollmentStatus: newFilters.enrollmentStatus,
      startDate: newFilters.startDate,
      endDate: newFilters.endDate,
      // Manter o campo search atual
      search: filters.search,
    });
  };
  
  return (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Buscar alunos..."
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          className="pl-9 bg-background w-full"
        />
      </div>
      
      <FilterBar 
        filters={filterBarState}
        onFilterChange={handleFilterChange}
        branches={branches}
        initialStats={initialStats}
      />
    </div>
  );
} 