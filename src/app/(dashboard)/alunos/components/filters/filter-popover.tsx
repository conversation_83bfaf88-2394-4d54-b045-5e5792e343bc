"use client";

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Filter, CalendarIcon } from "lucide-react"
import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Belt, BeltColor } from "@/components/belt"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { ptBR } from "date-fns/locale"
import { DateRange } from "react-day-picker"
import { Branch, FilterState } from "../../types"

interface FilterPopoverProps {
  filters: FilterState;
  onFilterChange: (filters: FilterState) => void;
  branches: Branch[];
}

const filterLabels = {
  belt: {
    white: "Branca",
    blue: "Azul",
    purple: "Roxa",
    brown: "Marrom",
    black: "Preta",
  },
  status: {
    active: "Ativo",
    inactive: "Inativo",
  },
  financialStatus: {
    up_to_date: "Em dia",
    pending: "Pendente",
    overdue: "Atrasado",
  },
  enrollmentStatus: {
    active: "Ativo",
    inactive: "Inativo",
    paused: "Pausado",
  },
  branch: {} as Record<string, string>,
};

export function FilterPopover({ filters, onFilterChange, branches }: FilterPopoverProps) {
  const [open, setOpen] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(
    filters.startDate && filters.endDate
      ? {
          from: filters.startDate,
          to: filters.endDate,
        }
      : undefined
  );

  // Gerar as opções de filiais a partir dos branches passados como prop
  const branchOptions = branches.reduce((acc: Record<string, string>, branch: Branch) => {
    acc[branch.id] = branch.name;
    return acc;
  }, {} as Record<string, string>);

  // Atualizar o dateRange quando os filtros mudarem
  useEffect(() => {
    setDateRange(
      filters.startDate && filters.endDate
        ? {
            from: filters.startDate,
            to: filters.endDate,
          }
        : undefined
    );
  }, [filters]);

  const activeFiltersCount = Object.values(filters).reduce((count: number, values) => {
    if (Array.isArray(values)) {
      return count + values.length;
    }
    return count + (values ? 1 : 0);
  }, 0);

  const handleClearFilters = () => {
    onFilterChange({});
    setDateRange(undefined);
  };

  const handleToggleValue = (type: keyof Omit<FilterState, 'startDate' | 'endDate'>, value: string) => {
    const currentValues = filters[type] || [];
    // Garantir que estamos tratando um array
    const currentArray = Array.isArray(currentValues) ? currentValues : [];
    
    const newValues = currentArray.includes(value)
      ? currentArray.filter((v: string) => v !== value)
      : [...currentArray, value];
    
    onFilterChange({
      ...filters,
      [type]: newValues.length ? newValues : undefined,
    });
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "h-9 gap-2 border-dashed",
            activeFiltersCount > 0 && "border-primary"
          )}
        >
          <Filter className="h-4 w-4" />
          Filtros
          {activeFiltersCount > 0 && (
            <Badge
              variant="secondary"
              className="h-5 rounded-sm px-1.5 font-normal"
            >
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0 max-h-[min(60vh,500px)] overflow-hidden" align="start">
        {/* Cabeçalho */}
        <div className="p-4 border-b flex-shrink-0">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Filtros</h4>
            {activeFiltersCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 text-muted-foreground outline-none hover:text-foreground"
                onClick={handleClearFilters}
              >
                Limpar filtros
              </Button>
            )}
          </div>
        </div>
        {/* Conteúdo com scroll */}
        <div className="overflow-y-auto flex-1 max-h-[400px]">
          <div className="p-4 space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Faixa</label>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(filterLabels.belt).map(([value, label]) => (
                  <Button
                    key={value}
                    variant="outline"
                    size="sm"
                    className={cn(
                      "justify-start gap-2",
                      filters.belt?.includes(value) && "border-primary bg-primary/5 text-primary"
                    )}
                    onClick={() => handleToggleValue('belt', value)}
                  >
                    <Belt color={value as BeltColor} size="xs" />
                    {label}
                  </Button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(filterLabels.status).map(([value, label]) => (
                  <Button
                    key={value}
                    variant="outline"
                    size="sm"
                    className={cn(
                      "justify-start gap-2",
                      filters.status?.includes(value) && "border-primary bg-primary/5 text-primary"
                    )}
                    onClick={() => handleToggleValue('status', value)}
                  >
                    <div className="h-2 w-2 rounded-full bg-current" />
                    {label}
                  </Button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Situação Financeira</label>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(filterLabels.financialStatus).map(([value, label]) => (
                  <Button
                    key={value}
                    variant="outline"
                    size="sm"
                    className={cn(
                      "justify-start gap-2",
                      filters.financialStatus?.includes(value) && "border-primary bg-primary/5 text-primary"
                    )}
                    onClick={() => handleToggleValue('financialStatus', value)}
                  >
                    <div className="h-2 w-2 rounded-full bg-current" />
                    {label}
                  </Button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status de Matrícula</label>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(filterLabels.enrollmentStatus).map(([value, label]) => (
                  <Button
                    key={value}
                    variant="outline"
                    size="sm"
                    className={cn(
                      "justify-start gap-2",
                      filters.enrollmentStatus?.includes(value) && "border-primary bg-primary/5 text-primary"
                    )}
                    onClick={() => handleToggleValue('enrollmentStatus', value)}
                  >
                    <div className="h-2 w-2 rounded-full bg-current" />
                    {label}
                  </Button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Filial</label>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(branchOptions).map(([value, label]) => (
                  <Button
                    key={value}
                    variant="outline"
                    size="sm"
                    className={cn(
                      "justify-start gap-2",
                      filters.branch?.includes(value) && "border-primary bg-primary/5 text-primary"
                    )}
                    onClick={() => handleToggleValue('branch', value)}
                  >
                    <div className="h-2 w-2 rounded-full bg-current" />
                    {label}
                  </Button>
                ))}
              </div>
            </div>

            {/* <div className="space-y-2">
              <label className="text-sm font-medium">Período</label>
              <div className="grid gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !dateRange && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange?.from ? (
                        dateRange.to ? (
                          <>
                            {format(dateRange.from, "dd/MM/yyyy", { locale: ptBR })} -{" "}
                            {format(dateRange.to, "dd/MM/yyyy", { locale: ptBR })}
                          </>
                        ) : (
                          format(dateRange.from, "dd/MM/yyyy", { locale: ptBR })
                        )
                      ) : (
                        "Selecione um período"
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <div className="p-3 border-b">
                      <Calendar
                        initialFocus
                        mode="range"
                        defaultMonth={dateRange?.from}
                        selected={dateRange}
                        onSelect={(range) => {
                          setDateRange(range);
                          onFilterChange({
                            ...filters,
                            startDate: range?.from,
                            endDate: range?.to,
                          });
                        }}
                        numberOfMonths={2}
                        locale={ptBR}
                      />
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div> */}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
} 