"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { createAluno } from "../../novo/actions/create-aluno";
import { useRouter } from "next/navigation";
import { novoAlunoSchema, NovoAlunoFormValues } from "../../novo/actions/schemas/aluno-schema";

export function AlunoForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const router = useRouter();

  const form = useForm<NovoAlunoFormValues>({
    resolver: zodResolver(novoAlunoSchema),
  });

  async function onSubmit(values: NovoAlunoFormValues) {
    setIsSubmitting(true);
    
    try {
      await createAluno(values);
      setSuccess(true);
      router.refresh();
    } catch (error) {
      console.error("Erro ao criar aluno:", error);
      form.setError("root", { 
        message: "Ocorreu um erro ao criar o aluno. Tente novamente." 
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  // Componentes de UI serão implementados posteriormente
  return null;
} 