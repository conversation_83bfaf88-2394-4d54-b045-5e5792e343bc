'use client';

import { useState } from "react";
import { EmptyState } from "./empty-state";
import { AlunosTable } from "./alunos-table";
import { AlunosCard } from "./alunos-card";
import { Pagination } from "../pagination";
import { useStudentsQuery } from "../../hooks/use-students-query";
import { Student } from "../../types";
import { useAlunosFilter } from "../../hooks/use-alunos-filter";
import { AlertCircle, MousePointerClick, Settings2 } from "lucide-react";
import { SelectUsers } from "../selectusers";
import { User } from "../selectusers/types";
import { 
  deleteMultipleStudents, 
  updateStudentsBelt, 
  updateStudentsBranch, 
  updateStudentsStatus,
  deleteStudentCompletely,
  deleteMultipleStudentsPermanently 
} from "../../actions/bulk-actions";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { useRowActionAnimation, ActionType } from "../../hooks/use-row-action-animation";

interface AlunosListProps {
  isSelecting: boolean;
  selectedUsers: User[];
  setSelectedUsers: React.Dispatch<React.SetStateAction<User[]>>;
  onCloseSelectionMode: () => void;
  onActivateSelectionMode: () => void;
}

export function AlunosList({ 
  isSelecting = false, 
  selectedUsers = [], 
  setSelectedUsers, 
  onCloseSelectionMode,
  onActivateSelectionMode
}: AlunosListProps) {
  // Usar o hook personalizado de consulta
  const { data, isError, error, refetch } = useStudentsQuery();
  const { filters, updatePage } = useAlunosFilter();
  const [showActionsDialog, setShowActionsDialog] = useState(false);
  
  // Hook para animações de ação nas linhas
  const { executeBulkActionWithAnimation } = useRowActionAnimation();
  
  const handleBulkAction = async (action: string, ids: string[]) => {
    try {
      // Mapear ação para tipo de animação
      const actionTypeMap: Record<string, ActionType> = {
        'delete': 'delete',
        'delete-permanently': 'delete-permanently',
        'inactivate': 'inactivate',
        'activate': 'activate',
        'change-belt': 'change-belt',
        'change-branch': 'change-branch'
      };

      const animationAction = actionTypeMap[action] || 'generic';

      const result = await executeBulkActionWithAnimation(
        ids,
        animationAction,
        async () => {
          switch (action) {
            case 'delete':
              return await deleteMultipleStudents(ids);
            case 'delete-permanently':
              // Usar função otimizada para múltiplos estudantes
              if (ids.length > 1) {
                return await deleteMultipleStudentsPermanently(ids);
              } else {
                // Para um único estudante, usar a função individual
                return await deleteStudentCompletely(ids[0]);
              }
            case 'inactivate':
              return await updateStudentsStatus(ids, false);
            case 'activate':
              return await updateStudentsStatus(ids, true);
            case 'change-belt':
              // Para simplificar, definindo uma faixa padrão, mas isso poderia abrir um diálogo para seleção
              return await updateStudentsBelt(ids, 'white');
            case 'change-branch':
              // Para simplificar, definindo uma filial padrão, mas isso poderia abrir um diálogo para seleção
              return await updateStudentsBranch(ids, 'default-branch-id');
            default:
              throw new Error('Ação não suportada');
          }
        }
      );
      
      if (result.success) {
        toast.success(result.message);
        onCloseSelectionMode();
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "Ocorreu um erro ao processar a ação");
    }
  };
  
  // Mostrar mensagem de erro
  if (isError) {
    return (
      <div className="rounded-md bg-destructive/15 p-4 flex items-center gap-3">
        <AlertCircle className="h-5 w-5 text-destructive" />
        <div className="text-sm text-destructive">
          Erro ao carregar dados: {error instanceof Error ? error.message : 'Erro desconhecido'}
        </div>
      </div>
    );
  }
  
  if (!data || !data.students) {
    return (
      <div className="rounded-md bg-yellow-100 p-4 flex items-center gap-3">
        <AlertCircle className="h-5 w-5 text-yellow-600" />
        <div className="text-sm text-yellow-700">Nenhum dado encontrado</div>
      </div>
    );
  }
  
  const { students, totalCount } = data;

  // Converter estudantes para o formato esperado pelo SelectUsers
  const usersForSelection: User[] = students.map((student: Student) => ({
    id: student.id,
    name: student.name,
    email: student.email,
    avatar: student.avatar,
    belt: student.belt,
    isActive: (student as any).isActive,
    degree: student.degree,
    branch: student.branch,
    phone: student.phone
  }));
  
  // Calcular o total de páginas
  const pageSize = filters.limit || 10;
  const totalPages = Math.ceil(totalCount / pageSize);
  const currentPage = filters.page || 1;
  
  // Se não houver resultados, mostrar estado vazio
  if (students.length === 0) {
    return <EmptyState search={filters.search || ''} />;
  }
  
  return (
    <div className="space-y-4">
      {/* Componente de seleção de usuários */}
      {isSelecting && (
        <SelectUsers
          students={usersForSelection}
          onClose={onCloseSelectionMode}
          onAction={handleBulkAction}
          isSelecting={isSelecting}
          selectedUsers={selectedUsers}
          setSelectedUsers={setSelectedUsers}
          showActionsDialog={showActionsDialog}
          setShowActionsDialog={setShowActionsDialog}
        />
      )}

      {/* View para desktop */}
      <div className="hidden md:block">
        <AlunosTable 
          students={students} 
          isSelecting={isSelecting} 
          selectedUsers={selectedUsers} 
          onSelectUser={(user) => {
            if (isSelecting) {
              setSelectedUsers(prev => {
                const isSelected = prev.find(u => u.id === user.id);
                if (isSelected) {
                  return prev.filter(u => u.id !== user.id);
                }
                return [...prev, user];
              });
            }
          }}
        />
      </div>
      
      {/* View para mobile */}
      <div className="md:hidden space-y-4">
        {students.map((student: Student) => (
          <AlunosCard 
            key={student.id} 
            student={student} 
            students={students} 
            isSelecting={isSelecting}
            isSelected={selectedUsers.some(u => u.id === student.id)}
            onSelect={() => {
              if (isSelecting) {
                setSelectedUsers(prev => {
                  const isSelected = prev.find(u => u.id === student.id);
                  if (isSelected) {
                    return prev.filter(u => u.id !== student.id);
                  }
                  return [...prev, {
                    id: student.id,
                    name: student.name,
                    email: student.email,
                    avatar: student.avatar,
                    belt: student.belt,
                    isActive: (student as any).isActive,
                    degree: student.degree,
                    branch: student.branch,
                    phone: student.phone
                  }];
                });
              }
            }}
          />
        ))}
      </div>
      
      <div className="flex items-center justify-between">
        {/* Área de botões na parte inferior esquerda */}
        <div className="flex items-center gap-2">
          <Button 
            variant={isSelecting ? "secondary" : "secondary"}
            size="sm"
            className="flex items-center gap-2"
            onClick={isSelecting ? onCloseSelectionMode : onActivateSelectionMode}
          >
            <MousePointerClick className="h-4 w-4" />
            {isSelecting ? "Cancelar seleção" : "Modo de seleção"}
          </Button>

          {/* Botão de Ações - Exibido apenas quando há alunos selecionados */}
          {isSelecting && selectedUsers.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowActionsDialog(true)}
              className="flex items-center gap-2"
            >
              <Settings2 className="h-4 w-4" />
              Ações ({selectedUsers.length})
            </Button>
          )}
        </div>

        {/* Paginação */}
        {totalPages > 1 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalCount}
            itemsPerPage={pageSize}
            onPageChange={updatePage}
          />
        )}
      </div>
    </div>
  );
} 