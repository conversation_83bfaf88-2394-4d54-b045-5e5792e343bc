"use client";

import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { BeltWithDetails } from "@/components/belt/BeltWithDetails";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Student } from "../../types";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Trash2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDateFormatter } from "../../hooks/use-date-formatter";
import { formatPhoneNumberBRWithDDI } from "@/utils/phone-utils";
import { Checkbox } from "@/components/ui/checkbox";
import { User } from "../selectusers/types";
import { useAdminStatus } from "@/hooks/user/Permissions/useAdminStatus";
import { deleteStudentCompletely } from "../../actions/bulk-actions";
import { toast } from "sonner";
import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { RowActionAnimation, RowRemovalAnimation } from "./row-action-animation";
import { useRowActionAnimation } from "../../hooks/use-row-action-animation";
import { getFinancialStatusLabel, getFinancialStatusBadgeVariant } from "../../utils/financial-status-utils";

interface AlunosTableProps {
  students: Student[];
  isSelecting?: boolean;
  selectedUsers?: User[];
  onSelectUser?: (user: User) => void;
  onRefresh?: () => void;
}

export function AlunosTable({
  students,
  isSelecting = false,
  selectedUsers = [],
  onSelectUser,
  onRefresh
}: AlunosTableProps) {
  const { formatBirthDate, calculateAge } = useDateFormatter();
  const { isAdmin } = useAdminStatus();
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    student?: Student;
  }>({ isOpen: false });

  // Hook para animações de ação nas linhas
  const {
    executeActionWithAnimation,
    removeFromList,
    isUserAnimating,
    isUserRemoving,
    getUserAction
  } = useRowActionAnimation();

  const [lastSelectedIndex, setLastSelectedIndex] = useState<number | null>(null);

  // Função para gerar as iniciais do nome
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Formatar data relativa
  const formatRelativeDate = (dateStr: string) => {
    try {
      return formatDistanceToNow(new Date(dateStr), {
        addSuffix: true,
        locale: ptBR
      });
    } catch (e) {
      return "Data inválida";
    }
  };

  const renderBelt = (student: Student) => {
    return (
      <BeltWithDetails
        color={student.belt}
        degree={student.degree}
        label={student.beltDetails?.label}
        stripeColor={student.beltDetails?.stripe_color}
        showCenterLine={student.beltDetails?.show_center_line}
        centerLineColor={student.beltDetails?.center_line_color}
        size="md"
      />
    );
  };

  // Função para obter o label do status
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Ativo';
      case 'inactive':
        return 'Inativo';
      case 'suspended':
        return 'Suspenso';
      case 'completed':
        return 'Concluído';
      case 'paused':
        return 'Pausado';
      default:
        return 'Desconhecido';
    }
  };

  // Função para obter a variante do badge baseada no status
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'statusActive';
      case 'inactive':
        return 'statusInactive';
      case 'suspended':
        return 'statusSuspended';
      case 'paused':
        return 'statusSuspended';
      case 'completed':
        return 'statusCompleted';
      default:
        return 'outline';
    }
  };

  // Converter estudante para o formato do seletor de usuários
  const mapStudentToUser = (student: Student): User => {
    return {
      id: student.id,
      name: student.name,
      email: student.email,
      avatar: student.avatar,
      belt: student.belt,
      isActive: (student as any).isActive, // Mantém compatibilidade
      degree: student.degree,
      branch: student.branch,
      phone: student.phone,
      birthDate: student.birthDate,
      lastVisit: student.lastVisit,
      financialStatus: student.financialStatus
    };
  };

  // Verificar se todos os alunos estão selecionados
  const areAllSelected =
    students.length > 0 &&
    selectedUsers.length === students.length &&
    students.every(student =>
      selectedUsers.some(user => user.id === student.id)
    );

  // Selecionar ou desselecionar todos os alunos
  const toggleSelectAll = () => {
    if (!onSelectUser) return;

    if (areAllSelected) {
      // Desselecionar todos
      students.forEach(student => {
        const user = mapStudentToUser(student);
        onSelectUser(user);
      });
    } else {
      // Selecionar todos que não estão selecionados
      students.forEach(student => {
        const isSelected = selectedUsers.some(user => user.id === student.id);
        if (!isSelected) {
          const user = mapStudentToUser(student);
          onSelectUser(user);
        }
      });
    }
  };

  // Função para deletar permanentemente um estudante individual
  const handleDeletePermanently = async (student: Student) => {
    setDeleteConfirmation({ isOpen: true, student });
  };

  const confirmDelete = async () => {
    if (!deleteConfirmation.student) return;

    const student = deleteConfirmation.student;
    setDeleteConfirmation({ isOpen: false });

    try {
      const result = await executeActionWithAnimation(
        student.id,
        'delete-permanently',
        () => deleteStudentCompletely(student.id)
      );

      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error("Erro ao deletar aluno: " + (error instanceof Error ? error.message : "Erro desconhecido"));
    }
  };

  // Função para lidar com seleção de linhas, incluindo seleção em faixa com Shift
  const handleRowSelection = (
    student: Student,
    rowIndex: number,
    event: React.MouseEvent
  ) => {
    if (!onSelectUser || isUserAnimating(student.id)) return;

    const user = mapStudentToUser(student);

    // Se a tecla Shift estiver pressionada e houver uma seleção anterior, selecionar intervalo
    if (event.shiftKey && lastSelectedIndex !== null) {
      const start = Math.min(lastSelectedIndex, rowIndex);
      const end = Math.max(lastSelectedIndex, rowIndex);

      // Determinar se devemos selecionar ou desselecionar com base no estado do item alvo
      const targetShouldSelect = !selectedUsers.some(u => u.id === student.id);

      for (let i = start; i <= end; i++) {
        const currentStudent = students[i];
        const isCurrentlySelected = selectedUsers.some(u => u.id === currentStudent.id);

        if (targetShouldSelect && !isCurrentlySelected) {
          onSelectUser(mapStudentToUser(currentStudent));
        }
        if (!targetShouldSelect && isCurrentlySelected) {
          onSelectUser(mapStudentToUser(currentStudent));
        }
      }
    } else {
      // Seleção individual (sem Shift)
      onSelectUser(user);
    }

    // Atualizar o último índice selecionado
    setLastSelectedIndex(rowIndex);
  };

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {isSelecting && (
                <TableHead className="w-12">
                  <Checkbox
                    checked={areAllSelected}
                    onCheckedChange={() => toggleSelectAll()}
                    className="opacity-90"
                  />
                </TableHead>
              )}
              <TableHead>Foto</TableHead>
              <TableHead>Nome</TableHead>
              <TableHead>Contato</TableHead>
              <TableHead>Idade</TableHead>
              <TableHead>Faixa / Grau</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Matrícula</TableHead>
              <TableHead>Plano</TableHead>
              <TableHead>Última Visita</TableHead>
              <TableHead>Situação Financeira</TableHead>
              <TableHead>Filial</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <AnimatePresence mode="popLayout">
              {students.map((student, index) => {
                const isSelected = selectedUsers.some(user => user.id === student.id);
                // Usar o status centralizado se disponível, senão usar isActive como fallback
                const status = (student as any).status || ((student as any).isActive ? 'active' : 'inactive');
                // Obter status de matrícula diretamente (já normalizado no servidor)
                const enrollmentStatus = (student as any).enrollmentStatus || 'inactive';

                // Verificar se este estudante está sendo removido
                if (isUserRemoving(student.id)) {
                  const columnCount = isSelecting ? 13 : 12; // Updated to account for the new Plan column
                  return (
                    <RowRemovalAnimation
                      key={`removing-${student.id}`}
                      onComplete={() => removeFromList(student.id)}
                      colSpan={columnCount}
                    />
                  );
                }

                return (
                  <motion.tr
                    key={student.id}
                    layout
                    initial={{ opacity: 1 }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                    className="relative"
                  >
                    {isSelecting && (
                      <TableCell className="w-12">
                        <Checkbox
                          checked={isSelected}
                          disabled={isUserAnimating(student.id)}
                          onClick={(e) => handleRowSelection(student, index, e)}
                          className="border-gray-300 dark:border-gray-500"
                        />
                      </TableCell>
                    )}
                    <TableCell>
                      {/* Animação de ação sobrepondo a linha */}
                      <AnimatePresence>
                        {isUserAnimating(student.id) && (
                          <RowActionAnimation
                            action={getUserAction(student.id) || 'generic'}
                            isVisible={isUserAnimating(student.id)}
                          />
                        )}
                      </AnimatePresence>

                      <Avatar className="h-8 w-8">
                        <AvatarImage src={student.avatar || ""} alt={student.name} />
                        <AvatarFallback>{getInitials(student.name)}</AvatarFallback>
                      </Avatar>
                    </TableCell>
                    <TableCell className="font-medium">
                      <Link href={`/perfil/${student.id}?from=alunos`} className="flex flex-col hover:underline">
                        <span>{student.name}</span>
                        <span className="text-xs text-muted-foreground capitalize">
                          {getStatusLabel(status)}
                        </span>
                      </Link>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span>{student.email}</span>
                        {student.phone && (
                          <span className="text-xs text-muted-foreground">{formatPhoneNumberBRWithDDI(student.phone)}</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span>{calculateAge(student.birthDate)}</span>
                        <span className="text-xs text-muted-foreground">{formatBirthDate(student.birthDate)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {renderBelt(student)}
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(status)}>
                        {getStatusLabel(status)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(enrollmentStatus)}>
                        {getStatusLabel(enrollmentStatus)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        {student.enrollmentPlan ? (
                          <>
                            <span className="text-sm font-medium">{student.enrollmentPlan.title}</span>
                            <span className="text-xs text-muted-foreground capitalize">
                              {student.enrollmentPlan.type === 'individual' ? 'Individual' :
                                student.enrollmentPlan.type === 'family' ? 'Familiar' :
                                  student.enrollmentPlan.type === 'corporate' ? 'Corporativo' :
                                    student.enrollmentPlan.type}
                            </span>
                          </>
                        ) : (
                          <span className="text-sm text-muted-foreground">Sem plano</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="text-sm">
                          {student.lastVisit ? formatRelativeDate(student.lastVisit) : "Nunca"}
                        </span>
                        {student.lastVisit && (
                          <span className="text-xs text-muted-foreground">
                            {new Date(student.lastVisit).toLocaleDateString('pt-BR')}
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getFinancialStatusBadgeVariant(student.financialStatus)}>
                        {getFinancialStatusLabel(student.financialStatus)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="text-sm">{student.branch || "Não informado"}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            disabled={isUserAnimating(student.id)}
                          >
                            <span className="sr-only">Abrir menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/perfil/${student.id}?from=alunos`}>
                              Ver perfil
                            </Link>
                          </DropdownMenuItem>
                          {/* Opção de deletar permanentemente - apenas para admins */}
                          {isAdmin && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDeletePermanently(student)}
                                disabled={isUserAnimating(student.id)}
                                className="text-red-600 focus:text-red-600 focus:bg-red-50 cursor-pointer"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Deletar Permanentemente
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </motion.tr>
                );
              })}
            </AnimatePresence>
          </TableBody>
        </Table>
      </div>

      {/* Modal de confirmação para deletar permanentemente */}
      <Dialog
        open={deleteConfirmation.isOpen}
        onOpenChange={(open) => setDeleteConfirmation({ isOpen: open })}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <DialogTitle className="text-red-600 dark:text-red-400">Ação Perigosa</DialogTitle>
            </div>
            <DialogDescription>
              Esta ação não pode ser desfeita e removerá permanentemente todos os dados.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <Alert className="border-red-200 bg-red-50 dark:border-red-600 dark:bg-red-900/30">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-700 dark:text-red-300">
                <strong>ATENÇÃO:</strong> Você está prestes a deletar permanentemente o aluno <strong>{deleteConfirmation.student?.name}</strong> do sistema.
              </AlertDescription>
            </Alert>

            <div className="space-y-2 text-sm">
              <p className="font-medium">Os seguintes dados serão removidos:</p>
              <ul className="list-disc list-inside space-y-1 text-gray-600">
                <li>Perfil do aluno e informações pessoais</li>
                <li>Histórico de faixas e graduações</li>
                <li>Registros de presença em aulas</li>
                <li>Histórico de pagamentos</li>
                <li>Matrículas em turmas</li>
                <li>Conta de usuário do sistema</li>
              </ul>
            </div>

            <div className="p-3 bg-yellow-50 border border-yellow-200 dark:bg-yellow-900/30 dark:border-yellow-600 rounded-md">
              <p className="text-sm text-yellow-800 dark:text-yellow-300">
                <strong>Recomendação:</strong> Considere inativar o aluno ao invés de deletar,
                para preservar dados históricos importantes.
              </p>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setDeleteConfirmation({ isOpen: false })}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              className="flex-1"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Deletar Permanentemente
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
