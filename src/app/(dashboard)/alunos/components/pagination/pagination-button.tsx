"use client";

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import type { PaginationButtonProps } from "./types";

export function PaginationButton({
  page,
  isActive,
  isDisabled,
  onClick,
  children,
  "aria-label": ariaLabel,
}: PaginationButtonProps) {
  return (
    <Button
      variant={isActive ? "default" : "outline"}
      size="icon"
      className={cn(
        "h-8 w-8 text-sm transition-all",
        isActive &&
          "font-semibold ring-2 ring-ring ring-offset-2 ring-offset-background",
        !isActive && "hover:bg-muted",
        isDisabled && "pointer-events-none opacity-50"
      )}
      onClick={onClick}
      disabled={isDisabled}
      aria-current={isActive ? "page" : undefined}
      aria-label={ariaLabel || `Go to page ${page}`}
    >
      {children}
    </Button>
  );
}