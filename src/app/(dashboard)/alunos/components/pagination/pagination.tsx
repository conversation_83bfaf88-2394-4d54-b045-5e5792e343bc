"use client";

import { ChevronLeft, ChevronRight } from "lucide-react";
import { generatePaginationRange } from "./utils";
import { PaginationButton } from "./pagination-button";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  siblingCount?: number;
  onPageChange?: (page: number) => void;
}

export function Pagination({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  siblingCount = 1,
  onPageChange,
}: PaginationProps) {
  // Não renderizar paginação se houver apenas uma página
  if (totalPages <= 1) return null;

  // Função para navegar para a página
  const handlePageChange = (pageNumber: number) => {
    if (onPageChange) {
      onPageChange(pageNumber);
    }
  };

  const paginationRange = generatePaginationRange(
    currentPage,
    totalPages,
    siblingCount
  );

  const onNext = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  const onPrevious = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  return (
    <div className="flex flex-col md:flex-row gap-4 items-center justify-between border-t pt-4">
      {/* Informações de paginação */}
      <div className="text-sm text-muted-foreground">
        <p className="font-medium">
          Mostrando{" "}
          <span className="font-medium text-foreground">
            {Math.min((currentPage - 1) * itemsPerPage + 1, totalItems)}
          </span>{" "}
          até{" "}
          <span className="font-medium text-foreground">
            {Math.min(currentPage * itemsPerPage, totalItems)}
          </span>{" "}
          de{" "}
          <span className="font-medium text-foreground">{totalItems}</span>{" "}
          resultados
        </p>
      </div>

      {/* Botões de paginação */}
      <div className="flex items-center justify-center gap-2">
        <PaginationButton
          onClick={onPrevious}
          isDisabled={currentPage === 1}
          page="previous"
          aria-label="Página anterior"
        >
          <ChevronLeft className="h-4 w-4" />
        </PaginationButton>

        {paginationRange.map((pageNumber, index) => {
          if (pageNumber === "...") {
            return (
              <span
                key={`dots-${index}`}
                className="px-2 text-sm text-muted-foreground"
              >
                ...
              </span>
            );
          }

          const page = pageNumber as number;
          return (
            <PaginationButton
              key={page}
              page={page}
              isActive={page === currentPage}
              onClick={() => handlePageChange(page)}
            >
              {page}
            </PaginationButton>
          );
        })}

        <PaginationButton
          onClick={onNext}
          isDisabled={currentPage === totalPages}
          page="next"
          aria-label="Próxima página"
        >
          <ChevronRight className="h-4 w-4" />
        </PaginationButton>
      </div>
    </div>
  );
}