"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertTriangle, Trash2 } from "lucide-react"
import { useAdminStatus } from "@/hooks/user/Permissions/useAdminStatus"
import { useRowActionAnimation } from "../../hooks/use-row-action-animation"
import { User, SelectUsersProps } from "./types"

export function SelectUsers({ 
  students, 
  onClose, 
  onAction, 
  isSelecting, 
  selectedUsers, 
  setSelectedUsers,
  showActionsDialog = false,
  setShowActionsDialog
}: SelectUsersProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [pendingAction, setPendingAction] = useState<string | null>(null)
  const { isAdmin } = useAdminStatus()
  
  // Integração com o sistema de animações
  const { executeBulkActionWithAnimation } = useRowActionAnimation()

  // Check if all users are selected
  const areAllSelected = students.length > 0 && students.every(user => 
    selectedUsers.some(selectedUser => selectedUser.id === user.id)
  )

  const handleSelectAll = () => {
    if (areAllSelected) {
      setSelectedUsers([])
    } else {
      setSelectedUsers([...students])
    }
  }

  const handleSelectUser = (user: User) => {
    setSelectedUsers((prev: User[]) => {
      const isSelected = prev.find(u => u.id === user.id)
      if (isSelected) {
        return prev.filter(u => u.id !== user.id)
      }
      return [...prev, user]
    })
  }

  const handleActionSelect = (action: string) => {
    if (action === 'delete-permanently') {
      setPendingAction(action)
      setShowDeleteConfirmation(true)
      return
    }

    executeAction(action)
  }

  const executeAction = async (action: string) => {
    const selectedIds = selectedUsers.map(user => user.id)
    
    // Mapear ações para tipos de animação
    const actionTypeMap: Record<string, 'delete' | 'delete-permanently' | 'inactivate' | 'activate' | 'change-belt' | 'change-branch' | 'generic'> = {
      'delete': 'delete',
      'delete-permanently': 'delete-permanently',
      'inactivate': 'inactivate',
      'activate': 'activate',
      'change-belt': 'change-belt',
      'change-branch': 'change-branch'
    }
    
    const animationType = actionTypeMap[action] || 'generic'
    
    // Executar ação com animação
    const result = await executeBulkActionWithAnimation(
      selectedIds,
      animationType,
      async (ids: string[]) => {
        // Chamar a função original de ação
        await onAction(action, ids)
        return { success: true }
      }
    )
    
    // Fechar diálogos
    setIsDialogOpen(false)
    if (setShowActionsDialog) {
      setShowActionsDialog(false)
    }
    
    // Limpar seleção após ação bem-sucedida
    if (result.success) {
      setSelectedUsers([])
    }
  }

  const handleConfirmDelete = () => {
    if (pendingAction) {
      executeAction(pendingAction)
      setShowDeleteConfirmation(false)
      setPendingAction(null)
    }
  }

  const handleCancelDelete = () => {
    setShowDeleteConfirmation(false)
    setPendingAction(null)
  }

  const selectedCount = selectedUsers.length

  if (!isSelecting) {
    return null;
  }

  return (
    <>
      {/* Modal principal de ações */}
      <Dialog open={isDialogOpen || showActionsDialog} onOpenChange={(open) => {
        setIsDialogOpen(open);
        if (setShowActionsDialog) {
          setShowActionsDialog(open);
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Alteração em Massa</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <p className="text-sm text-gray-500">
              {selectedCount} membro{selectedCount > 1 ? 's' : ''} selecionado{selectedCount > 1 ? 's' : ''}
            </p>

            <Select onValueChange={handleActionSelect}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione uma ação..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="delete">Excluir</SelectItem>
                <SelectItem value="inactivate">Inativar</SelectItem>
                <SelectItem value="activate">Ativar</SelectItem>
                <SelectItem value="change-belt">Trocar Faixa</SelectItem>
                <SelectItem value="change-branch">Trocar Filial</SelectItem>
                {/* Opção de deletar permanentemente - apenas para admins */}
                {isAdmin && (
                  <SelectItem 
                    value="delete-permanently" 
                    className="text-red-600 focus:text-red-600 focus:bg-red-50"
                  >
                    <div className="flex items-center gap-2">
                      <Trash2 className="h-4 w-4" />
                      <span>Deletar Permanentemente</span>
                    </div>
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
        </DialogContent>
      </Dialog>

      {/* Modal de confirmação para deletar permanentemente */}
      <Dialog open={showDeleteConfirmation} onOpenChange={setShowDeleteConfirmation}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <DialogTitle className="text-red-600 dark:text-red-400">Ação Perigosa</DialogTitle>
            </div>
            <DialogDescription>
              Esta ação não pode ser desfeita e removerá permanentemente todos os dados.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <Alert className="border-red-200 bg-red-50 dark:border-red-600 dark:bg-red-900/30">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-700 dark:text-red-300">
                <strong>ATENÇÃO:</strong> Você está prestes a deletar <strong>{selectedCount}</strong> aluno{selectedCount > 1 ? 's' : ''} permanentemente do sistema.
              </AlertDescription>
            </Alert>

            <div className="space-y-2 text-sm">
              <p className="font-medium">Os seguintes dados serão removidos:</p>
              <ul className="list-disc list-inside space-y-1 text-gray-600 dark:text-gray-400">
                <li>Perfil do aluno e informações pessoais</li>
                <li>Histórico de faixas e graduações</li>
                <li>Registros de presença em aulas</li>
                <li>Histórico de pagamentos</li>
                <li>Matrículas em turmas</li>
                <li>Conta de usuário do sistema</li>
              </ul>
            </div>

            <div className="p-3 bg-yellow-50 border border-yellow-200 dark:bg-yellow-900/30 dark:border-yellow-600 rounded-md">
              <p className="text-sm text-yellow-800 dark:text-yellow-300">
                <strong>Recomendação:</strong> Considere inativar o aluno ao invés de deletar, 
                para preservar dados históricos importantes.
              </p>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button 
              variant="outline" 
              onClick={handleCancelDelete}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleConfirmDelete}
              className="flex-1"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Deletar Permanentemente
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
} 