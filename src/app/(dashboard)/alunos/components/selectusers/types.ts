import React from 'react'

export interface User {
  id: string
  name: string
  email: string
  avatar?: string | null
  belt?: string
  isActive?: boolean
  degree?: number
  branch?: string
  phone?: string
  birthDate?: string
  lastVisit?: string
  financialStatus?: string
  selected?: boolean
}

export interface SelectUsersProps {
  students: User[]
  onClose: () => void
  onAction: (action: string, ids: string[]) => void
  isSelecting: boolean
  selectedUsers: User[]
  setSelectedUsers: React.Dispatch<React.SetStateAction<User[]>>
  showActionsDialog?: boolean
  setShowActionsDialog?: React.Dispatch<React.SetStateAction<boolean>>
} 