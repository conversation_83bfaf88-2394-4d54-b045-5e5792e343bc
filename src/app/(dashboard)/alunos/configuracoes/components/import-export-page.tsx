'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { ChevronLeft, Upload } from 'lucide-react'
import Link from 'next/link'
import { Dispatch, SetStateAction, useEffect } from 'react'

interface ImportExportPageProps {
  onBack: () => void
  setPageTitle: Dispatch<SetStateAction<string | null>>
  setPageSubtitle: Dispatch<SetStateAction<string | null>>
}

export default function ImportExportPage({
  onBack,
  setPageTitle,
  setPageSubtitle,
}: ImportExportPageProps) {
  useEffect(() => {
    setPageTitle('Importar / Exportar')
    setPageSubtitle('Importe e exporte dados de alunos.')
  }, [setPageTitle, setPageSubtitle])

  const importExportItems = [
    {
      title: 'Importar dados de membros',
      description:
        'Importe dados de membros usando um arquivo de planilha no formato CSV',
      icon: <Upload className="h-5 w-5" />,
      href: '/alunos/importar?from=/alunos/configuracoes',
    },
  ]

  return (
    <Card>
      <CardHeader>
        <Button
          variant="ghost"
          onClick={onBack}
          className="flex items-center gap-2 w-fit p-0 h-auto text-lg font-medium"
        >
          <ChevronLeft className="h-5 w-5" />
          Importar / Exportar
        </Button>
      </CardHeader>
      <CardContent>
        <div className="border rounded-md divide-y">
          {importExportItems.map((item) => (
            <Link
              key={item.title}
              href={item.href}
              className="flex items-center justify-between p-4 hover:bg-muted/50 transition-colors cursor-pointer"
            >
              <div className="flex flex-col gap-1">
                <h3 className="font-semibold">{item.title}</h3>
                <p className="text-sm text-muted-foreground">
                  {item.description}
                </p>
              </div>
              <div className="text-muted-foreground">{item.icon}</div>
            </Link>
          ))}
        </div>
      </CardContent>
    </Card>
  )
} 