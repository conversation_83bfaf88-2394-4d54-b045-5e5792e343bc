'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { usePageTitle } from '@/contexts/PageTitleContext'
import {
  ArrowUpOnSquareIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline'
import { useEffect, useState } from 'react'
import ImportExportPage from './components/import-export-page'

const settingsItems = [
// Deixar comentado porque não temos essas funcionalidades
//   {
//     href: '/alunos/configuracoes/portal',
//     title: 'Portal do Aluno',
//     description: 'Controle o acesso e a funcionalidade das contas online dos alunos.',
//     icon: <UsersIcon className="h-6 w-6" />,
//   },
//   {
//     href: '/alunos/configuracoes/notificacoes',
//     title: 'Notificações para Alunos',
//     description: '<PERSON>ere<PERSON>ie as notificações relacionadas aos alunos.',
//     icon: <BellIcon className="h-6 w-6" />,
//   },
//   {
//     href: '/alunos/configuracoes/listagem',
//     title: 'Configurações da Lista de Alunos',
//     description: 'Defina a ordenação padrão e o número de alunos por página.',
//     icon: <ListBulletIcon className="h-6 w-6" />,
//   },
//   {
//     href: '/alunos/configuracoes/matriculas',
//     title: 'Configurações de Matrícula',
//     description: 'Preços familiares e relatórios de matrículas.',
//     icon: <CreditCardIcon className="h-6 w-6" />,
//   },
//   {
//     href: '/alunos/configuracoes/documentos',
//     title: 'Configurações de Documentos',
//     description: 'Requisitos de assinatura e notificações por e-mail.',
//     icon: <DocumentTextIcon className="h-6 w-6" />,
//   },
//   {
//     href: '/alunos/configuracoes/formularios-inscricao',
//     title: 'Formulários de Inscrição',
//     description: 'Gerencie e personalize os formulários de inscrição de alunos.',
//     icon: <ClipboardDocumentListIcon className="h-6 w-6" />,
//   },
//   {
//     href: '/alunos/configuracoes/campos-personalizados',
//     title: 'Campos Personalizados do Aluno',
//     description: 'Adicione campos adicionais ao perfil do aluno.',
//     icon: <PencilSquareIcon className="h-6 w-6" />,
//   },
  {
    href: '/alunos/configuracoes/importar-exportar',
    title: 'Importar / Exportar',
    description: 'Importe e exporte dados de alunos.',
    icon: <ArrowUpOnSquareIcon className="h-6 w-6" />,
  },
]

export default function AlunosConfiguracoesPage() {
  const { setPageTitle, setPageSubtitle } = usePageTitle()
  const [selectedSetting, setSelectedSetting] = useState<string | null>(null)

  useEffect(() => {
    if (selectedSetting) {
      const currentSetting = settingsItems.find(
        (item) => item.href === selectedSetting
      )
      if (currentSetting) {
        setPageTitle(currentSetting.title)
        setPageSubtitle(currentSetting.description)
      }
    } else {
      setPageTitle('Configurações de Alunos')
      setPageSubtitle(
        'Gerencie contas, notificações, documentos, campos personalizados e mais.'
      )
    }
  }, [selectedSetting, setPageTitle, setPageSubtitle])

  const handleBack = () => {
    setSelectedSetting(null)
  }

  if (selectedSetting === '/alunos/configuracoes/importar-exportar') {
    return (
      <ImportExportPage
        onBack={handleBack}
        setPageTitle={setPageTitle}
        setPageSubtitle={setPageSubtitle}
      />
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Configurações de Alunos</CardTitle>
        {/* <CardDescription>
          Gerencie contas, notificações, documentos, campos personalizados e mais.
        </CardDescription> */}
      </CardHeader>
      <CardContent>
        <div className="divide-y divide-border">
          {settingsItems.map((item) => (
            <div
              key={item.href}
              onClick={() => setSelectedSetting(item.href)}
              className="flex items-center justify-between p-4 hover:bg-muted/50 transition-colors cursor-pointer"
            >
              <div className="flex items-center gap-4">
                <div className="text-primary">{item.icon}</div>
                <div>
                  <h3 className="font-semibold">{item.title}</h3>
                  <p className="text-sm text-muted-foreground">
                    {item.description}
                  </p>
                </div>
              </div>
              <ChevronRightIcon className="h-5 w-5 text-muted-foreground" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
} 