import { format, parseISO, differenceInYears } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export function useDateFormatter() {
  /**
   * Formata uma data de nascimento considerando o fuso horário
   * Para evitar problemas com fuso horário, usamos parseISO e format
   */
  const formatBirthDate = (birthDateStr: string | undefined): string => {
    if (!birthDateStr) return "";
    
    try {
      // parseISO preserva a data corretamente
      const date = parseISO(birthDateStr);
      return format(date, 'dd/MM/yyyy', { locale: ptBR });
    } catch (e) {
      return "";
    }
  };

  /**
   * Calcula a idade a partir da data de nascimento
   * Usa differenceInYears para cálculo preciso
   */
  const calculateAge = (birthDateStr: string | undefined): string => {
    if (!birthDateStr) return "N/A";
    
    try {
      const today = new Date();
      const birthDate = parseISO(birthDateStr);
      return differenceInYears(today, birthDate).toString();
    } catch (e) {
      return "N/A";
    }
  };

  return {
    formatBirthDate,
    calculateAge
  };
} 