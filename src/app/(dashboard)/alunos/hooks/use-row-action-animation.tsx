'use client';

import { createContext, useContext, useState, useCallback, ReactNode, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

export type ActionType =
  | 'delete'
  | 'delete-permanently'
  | 'inactivate'
  | 'activate'
  | 'change-belt'
  | 'change-branch'
  | 'generic'
  | 'pause'
  | 'resume';

interface RowActionState {
  [userId: string]: {
    isAnimating: boolean;
    action: ActionType;
    isRemoving: boolean;
  };
}

interface RowActionContextType {
  executeActionWithAnimation: (
    userId: string,
    action: ActionType,
    actionFunction: () => Promise<{ success: boolean; message?: string }>
  ) => Promise<{ success: boolean; message?: string }>;
  executeBulkActionWithAnimation: (
    userIds: string[],
    action: ActionType,
    actionFunction: (ids: string[]) => Promise<{ success: boolean; message?: string }>
  ) => Promise<{ success: boolean; message?: string }>;
  isUserAnimating: (userId: string) => boolean;
  isUserRemoving: (userId: string) => boolean;
  getUserAction: (userId: string) => ActionType | null;
  removeFromList: (userId: string) => void;
}

const RowActionContext = createContext<RowActionContextType | null>(null);

export function RowActionProvider({ children }: { children: ReactNode }) {
  const [actionStates, setActionStates] = useState<RowActionState>({});
  const queryClient = useQueryClient();
  const invalidateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const bulkInvalidatingRef = useRef(false);

  /* Helpers */
  const startAction = useCallback(
    (userId: string, action: ActionType) => {
      setActionStates(prev => ({
        ...prev,
        [userId]: { isAnimating: true, action, isRemoving: false },
      }));
    },
    []
  );

  const completeAction = useCallback((userId: string) => {
    setActionStates(prev => ({
      ...prev,
      [userId]: { ...prev[userId], isAnimating: false, isRemoving: true },
    }));
  }, []);

  const cancelAction = useCallback((userId: string) => {
    setActionStates(prev => {
      const copy = { ...prev };
      delete copy[userId];
      return copy;
    });
  }, []);

  const scheduleInvalidation = () => {
    if (bulkInvalidatingRef.current) return;
    if (invalidateTimeoutRef.current) return;
    invalidateTimeoutRef.current = setTimeout(() => {
      queryClient.invalidateQueries({ queryKey: ['students'] });
      queryClient.invalidateQueries({ queryKey: ['student-stats'] });
      queryClient.invalidateQueries({ queryKey: ['assinaturas'] });
      queryClient.invalidateQueries({ queryKey: ['recorrentes'] });
      invalidateTimeoutRef.current = null;
    }, 150);
  };

  const removeFromList = useCallback(
    (userId: string) => {
      setActionStates(prev => {
        const copy = { ...prev };
        delete copy[userId];
        return copy;
      });

      scheduleInvalidation();
    },
    [scheduleInvalidation]
  );

  /* Core executors */
  const executeActionWithAnimation = useCallback(
    async (
      userId: string,
      action: ActionType,
      actionFunction: () => Promise<{ success: boolean; message?: string }>,
    ) => {
      try {
        startAction(userId, action);
        const result = await actionFunction();
        result.success ? completeAction(userId) : cancelAction(userId);
        return result;
      } catch (err) {
        cancelAction(userId);
        throw err;
      }
    },
    [startAction, completeAction, cancelAction]
  );

  const executeBulkActionWithAnimation = useCallback(
    async (
      userIds: string[],
      action: ActionType,
      actionFunction: (ids: string[]) => Promise<{ success: boolean; message?: string }>,
    ) => {
      try {
        userIds.forEach(id => startAction(id, action));
        bulkInvalidatingRef.current = true;
        const result = await actionFunction(userIds);
        userIds.forEach(id => (result.success ? completeAction(id) : cancelAction(id)));

        if (result.success) {
          queryClient.invalidateQueries({ queryKey: ['students'] });
          queryClient.invalidateQueries({ queryKey: ['student-stats'] });
        }

        setTimeout(() => {
          bulkInvalidatingRef.current = false;
        }, 300);
        return result;
      } catch (err) {
        userIds.forEach(cancelAction);
        bulkInvalidatingRef.current = false;
        throw err;
      }
    },
    [startAction, completeAction, cancelAction]
  );

  /* Selectors */
  const isUserAnimating = (userId: string) => actionStates[userId]?.isAnimating ?? false;
  const isUserRemoving = (userId: string) => actionStates[userId]?.isRemoving ?? false;
  const getUserAction = (userId: string) => actionStates[userId]?.action ?? null;

  return (
    <RowActionContext.Provider
      value={{
        executeActionWithAnimation,
        executeBulkActionWithAnimation,
        isUserAnimating,
        isUserRemoving,
        getUserAction,
        removeFromList,
      }}
    >
      {children}
    </RowActionContext.Provider>
  );
}

export function useRowActionAnimation() {
  const ctx = useContext(RowActionContext);
  if (!ctx) throw new Error('useRowActionAnimation must be used within RowActionProvider');
  return ctx;
} 