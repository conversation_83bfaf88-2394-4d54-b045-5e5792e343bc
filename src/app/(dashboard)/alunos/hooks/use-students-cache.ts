'use client';

import { useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';

/**
 * Hook para gerenciar cache de estudantes de forma centralizada
 * Implementa estratégias otimizadas de cache que carregam primeiro do cache
 * e depois fazem refetch em background quando necessário
 */
export function useStudentsCache() {
  const queryClient = useQueryClient();

  /**
   * Estratégia de refetch inteligente para estudantes
   * Faz refetch em background apenas quando dados estão stale
   */
  const refreshStudentsData = useCallback(async () => {
    // Refetch apenas queries de estudantes que estão stale
    await queryClient.refetchQueries({
      queryKey: ['students'],
      type: 'active',
      stale: true // Apenas queries que estão stale
    });
  }, [queryClient]);

  /**
   * Invalida cache de estatísticas quando há mudanças
   * Mas mantém cache de listagem para performance
   */
  const invalidateStats = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: ['student-stats'],
      exact: true
    });
  }, [queryClient]);

  /**
   * Força refresh de dados quando necessário (após operações críticas)
   */
  const forceRefresh = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: ['students']
    });
    queryClient.invalidateQueries({
      queryKey: ['student-stats']
    });
  }, [queryClient]);

  /**
   * Prefetch de dados para melhorar performance
   */
  const prefetchStudentsPage = useCallback(async (page: number, filters: any) => {
    const queryKey = [
      'students',
      ((page - 1) * 10), // skip
      10, // take
      filters.search || '',
      JSON.stringify(filters.status || ['active']),
      filters.belt || [],
      filters.financialStatus || [],
      filters.branch || [],
      filters.enrollmentStatus || []
    ];

    await queryClient.prefetchQuery({
      queryKey,
      queryFn: async () => {
        const response = await fetch('/api/students/query', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            skip: (page - 1) * 10,
            take: 10,
            search: filters.search || '',
            status: filters.status || ['active'],
            belt: filters.belt || [],
            financialStatus: filters.financialStatus || [],
            branch: filters.branch || [],
            enrollmentStatus: filters.enrollmentStatus || []
          }),
        });
        return response.json();
      },
      staleTime: 5 * 60 * 1000, // 5 minutos
    });
  }, [queryClient]);

  /**
   * Otimiza cache removendo queries antigas
   */
  const optimizeCache = useCallback(() => {
    // Remove queries de estudantes mais antigas que 30 minutos
    queryClient.removeQueries({
      queryKey: ['students'],
      exact: false,
      predicate: (query) => {
        const dataUpdatedAt = query.state.dataUpdatedAt;
        const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000);
        return dataUpdatedAt < thirtyMinutesAgo;
      }
    });
  }, [queryClient]);

  /**
   * Verifica se dados estão em cache e são recentes
   */
  const hasFreshData = useCallback((queryKey: any[]) => {
    const query = queryClient.getQueryState(queryKey);
    if (!query) return false;
    
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    return query.dataUpdatedAt > fiveMinutesAgo;
  }, [queryClient]);

  return {
    refreshStudentsData,
    invalidateStats,
    forceRefresh,
    prefetchStudentsPage,
    optimizeCache,
    hasFreshData
  };
} 