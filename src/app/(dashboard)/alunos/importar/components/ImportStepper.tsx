'use client'

import { useState, useEffect } from 'react'
import { FileUploadStep } from './file-upload-step'
import { MappingStep } from './mapping-step'

export function ImportStepper() {
  const [step, setStep] = useState(1)
  const [csvData, setCsvData] = useState<string[][]>([])
  const [csvHeaders, setCsvHeaders] = useState<string[]>([])

  useEffect(() => {
    const storedHeaders = sessionStorage.getItem('csv_headers');
    const storedData = sessionStorage.getItem('csv_data');

    if (storedHeaders && storedData) {
      try {
        const headers = JSON.parse(storedHeaders);
        const data = JSON.parse(storedData);
        
        setCsvHeaders(headers);
        setCsvData(data);
        setStep(2);

        sessionStorage.removeItem('csv_headers');
        sessionStorage.removeItem('csv_data');
      } catch (error) {
        console.error("Failed to parse CSV data from session storage", error);
        sessionStorage.removeItem('csv_headers');
        sessionStorage.removeItem('csv_data');
      }
    }
  }, []);

  const handleFileUploaded = (
    data: string[][],
    headers: string[],
  ) => {
    setCsvData(data)
    setCsvHeaders(headers)
    setStep(2)
  }

  const handleCancel = () => {
    setStep(1)
    setCsvData([])
    setCsvHeaders([])
  }

  return (
    <div>
      {step === 1 && <FileUploadStep onFileUploaded={handleFileUploaded} />}
      {step === 2 && (
        <MappingStep
          headers={csvHeaders}
          data={csvData}
          onCancel={handleCancel}
        />
      )}
    </div>
  )
} 