'use client'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useEffect, Fragment, useRef, useState } from 'react'
import { AlertTriangle, Check, HelpCircle, Loader2 } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  ErrorTooltipProvider,
  ErrorTooltipRoot,
  ErrorTooltipTrigger,
  ErrorTooltipContent,
} from '@/components/ui/error-tooltip'
import { useColumnResizing } from '@/hooks/ui/use-column-resizing'
import {
  systemFields,
  validateImportData,
  ImportData,
} from '../validation'

interface DataTableProps {
  headers: string[]
  data: string[][]
  mappings: Record<string, string>
  onMappingChange: (header: string, fieldValue: string) => void
  selectedRows: Set<number>
  onSelectedRowsChange: (newSelectedRows: Set<number>) => void
  validationResults: ImportData[]
  onValidate?: (isValid: boolean, mappings: Record<string, string>) => void
  onImport?: (data: ImportData[]) => void
  isValidating?: boolean
  isImporting?: boolean
  validatingRows?: Set<number>
  importingRows?: Set<number>
  importProgressRows?: Set<number>
}

export function DataTable({
  headers,
  data,
  mappings,
  onMappingChange,
  selectedRows,
  onSelectedRowsChange,
  validationResults,
  onValidate,
  onImport,
  isValidating = false,
  isImporting = false,
  validatingRows = new Set(),
  importingRows = new Set(),
  importProgressRows = new Set(),
}: DataTableProps) {
  const tableRef = useRef<HTMLTableElement>(null)
  const { columnWidths, handleMouseDown } = useColumnResizing(tableRef)
  const [lastSelectedIndex, setLastSelectedIndex] = useState<number | null>(
    null,
  )

  // Sincronizar seleção inicial
  useEffect(() => {
    if (data.length > 0) {
      onSelectedRowsChange(new Set(data.map((_, index) => index)))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data])

  const handleSelectAll = (checked: boolean | 'indeterminate') => {
    if (checked === true) {
      onSelectedRowsChange(new Set(data.map((_, index) => index)))
    } else {
      onSelectedRowsChange(new Set())
    }
  }

  const handleRowSelection = (
    rowIndex: number,
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    if (isValidating || isImporting) return

    const newSelectedRows = new Set(selectedRows)

    if (event.shiftKey && lastSelectedIndex !== null) {
      const start = Math.min(lastSelectedIndex, rowIndex)
      const end = Math.max(lastSelectedIndex, rowIndex)

      const shouldSelect = !newSelectedRows.has(rowIndex)

      for (let i = start; i <= end; i++) {
        if (shouldSelect) {
          newSelectedRows.add(i)
        } else {
          newSelectedRows.delete(i)
        }
      }
    } else {
      if (newSelectedRows.has(rowIndex)) {
        newSelectedRows.delete(rowIndex)
      } else {
        newSelectedRows.add(rowIndex)
      }
    }

    onSelectedRowsChange(newSelectedRows)
    setLastSelectedIndex(rowIndex)
  }

  const handleMappingChange = (headerIndex: number, fieldValue: string) => {
    const headerName = headers[headerIndex]
    onMappingChange(headerName, fieldValue)
  }

  const handleImport = () => {
    if (validationResults.length > 0) {
      const validData = validationResults.filter(r => r.isValid)
      onImport?.(validData)
    }
  }

  const isAllSelected = selectedRows.size === data.length && data.length > 0
  const isSomeSelected = selectedRows.size > 0 && !isAllSelected
  const requiredFields = systemFields.filter(f => f.required)
  const mappedRequiredFields = requiredFields.filter(field =>
    Object.values(mappings).includes(field.value),
  )
  const canValidate =
    mappedRequiredFields.length === requiredFields.length &&
    selectedRows.size > 0
  const validRowsCount = validationResults.filter(r => r.isValid).length
  const invalidRowsCount = validationResults.length - validRowsCount
  const allRowsAreValid =
    validationResults.length > 0 && invalidRowsCount === 0

  const getRowClassName = (rowIndex: number, validationResult?: ImportData) => {
    const baseClasses = []
    
    // Estados de loading
    if (validatingRows.has(rowIndex)) {
      baseClasses.push('bg-blue-50/80 dark:bg-blue-900/20 animate-pulse')
    } else if (importingRows.has(rowIndex)) {
      baseClasses.push('bg-green-50/80 dark:bg-green-900/20 animate-pulse')
    } else if (importProgressRows.has(rowIndex)) {
      baseClasses.push('bg-emerald-50 dark:bg-emerald-900/30')
    }
    // Estados de validação
    else if (validationResult && !validationResult.isValid) {
      baseClasses.push('bg-red-50 dark:bg-red-900/30')
    } else if (validationResult && validationResult.isValid) {
      baseClasses.push('bg-green-50/50 dark:bg-green-900/20')
    }
    
    return baseClasses.join(' ')
  }

  const getRowStatusIcon = (rowIndex: number, validationResult?: ImportData) => {
    if (importProgressRows.has(rowIndex)) {
      return <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
    }
    
    if (importingRows.has(rowIndex)) {
      return <Loader2 className="h-4 w-4 text-green-600 dark:text-green-400 animate-spin" />
    }
    
    if (validatingRows.has(rowIndex)) {
      return <Loader2 className="h-4 w-4 text-blue-600 dark:text-blue-400 animate-spin" />
    }
    
    if (validationResult) {
      if (validationResult.isValid) {
        return <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
      } else {
        return <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400" />
      }
    }
    
    return null
  }

  return (
    <ErrorTooltipProvider>
      <div className="space-y-4">
        {/* Validation results */}
        {/* {isValidated && validationResults.length > 0 && (
          <Alert
            className={
              allRowsAreValid
                ? 'border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-950 dark:text-green-200'
                : 'border-yellow-200 bg-yellow-50 text-yellow-900 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-200'
            }
          >
            {allRowsAreValid ? (
              <Check className="h-4 w-4" />
            ) : (
              <AlertTriangle className="h-4 w-4" />
            )}
            <AlertDescription>
              Validação concluída: {validRowsCount} linhas válidas,{' '}
              {invalidRowsCount} linhas com erros
            </AlertDescription>
          </Alert>
        )} */}

        <div className="w-full overflow-x-auto border rounded-lg">
          <Table
            ref={tableRef}
            style={{ tableLayout: 'auto', minWidth: '100%' }}
          >
            <colgroup>
              <col
                style={{
                  width: columnWidths[0] ? `${columnWidths[0]}px` : '80px',
                  minWidth: '80px'
                }}
              />
              {headers.map((_, index) => (
                <col
                  key={index}
                  style={{
                    width: columnWidths[index + 1]
                      ? `${columnWidths[index + 1]}px`
                      : '250px',
                    minWidth: '200px'
                  }}
                />
              ))}
            </colgroup>
            <TableHeader>
              <TableRow>
                <TableHead className="relative w-[80px] min-w-[80px]">
                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={
                        isAllSelected || (isSomeSelected ? 'indeterminate' : false)
                      }
                      onCheckedChange={handleSelectAll}
                      disabled={isValidating || isImporting}
                    />
                    {(isValidating || isImporting) && (
                      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                    )}
                  </div>
                  <div
                    onMouseDown={e => handleMouseDown(e, 0)}
                    className="absolute top-0 right-0 h-full w-2 cursor-col-resize"
                  />
                </TableHead>
                {headers.map((header, index) => (
                  <TableHead
                    key={index}
                    className="relative min-w-[200px] max-w-[300px] select-none"
                  >
                    <Select
                      value={mappings[header] || ''}
                      onValueChange={value => handleMappingChange(index, value)}
                      disabled={isValidating || isImporting}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecionar campo...">
                          {mappings[header] &&
                            (() => {
                              const field = systemFields.find(
                                f => f.value === mappings[header],
                              )
                              if (!field) return null
                              return (
                                <div className="flex items-center gap-2">
                                  {field.label}
                                  {field.required && (
                                    <Badge
                                      variant="outline"
                                      className="border-green-500 text-green-700 text-xs dark:border-green-400 dark:text-green-400"
                                    >
                                      Obrigatório
                                    </Badge>
                                  )}
                                </div>
                              )
                            })()}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ignore">Não mapear</SelectItem>
                        {systemFields.map(field => (
                          <SelectItem key={field.value} value={field.value}>
                            <div className="flex items-center gap-2">
                              {field.label}
                              {field.required && (
                                <Badge
                                  variant="destructive"
                                  className="text-xs"
                                >
                                  Obrigatório
                                </Badge>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <div
                      onMouseDown={e => handleMouseDown(e, index + 1)}
                      className="absolute top-0 right-0 h-full w-2 cursor-col-resize"
                    />
                  </TableHead>
                ))}
              </TableRow>
              <TableRow className="bg-muted/50 hover:bg-muted/50">
                <TableCell></TableCell>
                {headers.map((header, index) => (
                  <TableCell key={index} className="font-medium text-xs">
                    {header}
                  </TableCell>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((row, rowIndex) => {
                const validationResult = validationResults.find(
                  r => r.rowIndex === rowIndex,
                )
                return (
                  <TableRow
                    key={rowIndex}
                    data-state={selectedRows.has(rowIndex) && 'selected'}
                    className={getRowClassName(rowIndex, validationResult)}
                  >
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Checkbox
                          checked={selectedRows.has(rowIndex)}
                          onClick={e => handleRowSelection(rowIndex, e)}
                          disabled={isValidating || isImporting}
                        />
                        {getRowStatusIcon(rowIndex, validationResult)}
                      </div>
                    </TableCell>
                    {row.map((cell, cellIndex) => {
                      const header = headers[cellIndex]
                      const mappedField = mappings[header]
                      const error = validationResult?.errors[mappedField]

                      return (
                        <TableCell
                          key={cellIndex}
                          className="max-w-[250px] truncate"
                        >
                          <div className="flex items-center gap-2">
                            <span className={
                              validatingRows.has(rowIndex) || importingRows.has(rowIndex) 
                                ? 'text-muted-foreground' 
                                : ''
                            }>
                              {cell}
                            </span>
                            {error && !validatingRows.has(rowIndex) && !importingRows.has(rowIndex) && (
                              <ErrorTooltipRoot>
                                <ErrorTooltipTrigger>
                                  <HelpCircle className="h-4 w-4 text-red-500" />
                                </ErrorTooltipTrigger>
                                <ErrorTooltipContent>
                                  <p>{error}</p>
                                </ErrorTooltipContent>
                              </ErrorTooltipRoot>
                            )}
                          </div>
                        </TableCell>
                      )
                    })}
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </div>

        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div className="flex flex-col items-start gap-3">
            <div className="flex items-center gap-4">
              <div className="text-sm text-muted-foreground">
                <span className="font-medium">{selectedRows.size}</span> de {data.length} linhas selecionadas
              </div>
              {validationResults.length > 0 && (
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-green-600 font-medium">{validRowsCount} válidas</span>
                  </div>
                  {invalidRowsCount > 0 && (
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-sm text-red-600 font-medium">
                        {invalidRowsCount} com erros
                      </span>
                    </div>
                  )}
                </div>
              )}
              {(isValidating || isImporting) && (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                  <span className="text-sm text-blue-600 font-medium">
                    {isValidating ? 'Validando...' : 'Importando...'}
                  </span>
                </div>
              )}
            </div>
            <div className="flex flex-wrap items-center gap-2 text-sm">
              <span className="font-medium text-muted-foreground">
                Campos obrigatórios:
              </span>
              {requiredFields.map(field => {
                const isMapped = Object.values(mappings).includes(field.value)
                return (
                  <Fragment key={field.value}>
                    <Badge
                      variant="outline"
                      className={
                        isMapped
                          ? 'border-green-500 text-green-700 dark:border-green-400 dark:text-green-400'
                          : 'border-orange-500 text-orange-700 dark:border-orange-400 dark:text-orange-400'
                      }
                    >
                      {field.label}
                      {isMapped && <Check className="ml-1 h-3 w-3" />}
                    </Badge>
                    {field.value === 'phone' && (
                      <ErrorTooltipRoot>
                        <ErrorTooltipTrigger>
                          <HelpCircle className="h-4 w-4 text-muted-foreground" />
                        </ErrorTooltipTrigger>
                        <ErrorTooltipContent>
                          <p>
                            Se o DDI não for informado, será usado o do Brasil
                            (+55).
                          </p>
                        </ErrorTooltipContent>
                      </ErrorTooltipRoot>
                    )}
                  </Fragment>
                )
              })}
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              variant="outline"
              onClick={handleImport}
              disabled={!validationResults.length || validRowsCount === 0 || isValidating || isImporting}
              className="w-full sm:w-auto"
            >
              {isImporting && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
              IMPORTAR {validRowsCount > 0 ? `${validRowsCount} ALUNOS` : 'ALUNOS'}
            </Button>
          </div>
        </div>
      </div>
    </ErrorTooltipProvider>
  )
} 