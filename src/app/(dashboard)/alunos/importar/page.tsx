'use client'

import { Button } from '@/components/ui/button'
import { usePageTitle } from '@/contexts/PageTitleContext'
import { ChevronLeft } from 'lucide-react'
import Link from 'next/link'
import { useEffect } from 'react'
import { ImportStepper } from './components/ImportStepper'
import { useSearchParams } from 'next/navigation'

export default function AlunosImportarPage() {
  const { setPageTitle, setPageSubtitle } = usePageTitle()
  const searchParams = useSearchParams()

  useEffect(() => {
    setPageTitle('Importar Alunos')
    setPageSubtitle('Importar informações de alunos através de um arquivo CSV.')

    return () => {
      setPageTitle(null)
      setPageSubtitle(null)
    }
  }, [setPageTitle, setPageSubtitle])

  const from = searchParams.get('from')
  const backHref = from === '/alunos' ? '/alunos' : '/alunos/configuracoes'
  const backText =
    from === '/alunos'
      ? 'Voltar para Alunos'
      : 'Voltar para Configurações'

  return (
    <div className="space-y-4">
      <Button
        variant="ghost"
        asChild
        className="flex items-center gap-2 -ml-4"
      >
        <Link href={backHref}>
          <ChevronLeft className="h-5 w-5" />
          {backText}
        </Link>
      </Button>

      <ImportStepper />
    </div>
  )
} 