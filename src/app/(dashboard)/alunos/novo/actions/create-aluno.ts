'use server';

import { novoAlunoSchema, type NovoAlunoFormValues } from './schemas/aluno-schema';
import { hasPermission } from '@/services/permissions/actions/check-permission';
import { createAdminClient } from '@/services/supabase/server';
import { syncUserStatusToAuth } from '@/services/user/status-service';
import { revalidatePath } from 'next/cache';
import { nanoid } from 'nanoid';
import { extractNameParts } from '@/utils/name-utils';
import { randomUUID } from 'crypto';
import { createMembership } from '@/app/(dashboard)/academia/actions/membership-actions';
// import { generateRandomString } from '@/utils/string';

type CreateAlunoResult = {
  success: boolean;
  errors?: Record<string, string[]>;
  message?: string;
  studentId?: string;
  userId?: string;
  membershipId?: string;
  membershipCreated?: boolean;
};

function sanitizePhoneNumber(phone: string | null | undefined): string | null {
  if (!phone) return null;

  const digitsOnly = phone.replace(/\D/g, '');

  if (!digitsOnly || digitsOnly === '55') return null;

  return digitsOnly;
}

// Função para converter DataURL para Blob/Buffer
async function dataURLToBlob(dataURL: string): Promise<Buffer> {
  // Remover o prefixo (ex: "data:image/jpeg;base64,")
  const base64 = dataURL.split(',')[1];
  return Buffer.from(base64, 'base64');
}

// Função para obter o tipo MIME da imagem a partir do dataURL
function getMimeType(dataURL: string): string {
  const matches = dataURL.match(/^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,/);
  return matches?.[1] || 'image/jpeg';
}

// Função para fazer upload do avatar e retornar a URL
async function uploadAvatar(supabase: any, avatarDataUrl: string, userId: string): Promise<{ url: string | null; storagePath: string | null }> {
  try {
    // Gerar nome único para o arquivo
    const timestamp = Date.now();
    const avatarFileName = `${nanoid()}_${timestamp}.png`;
    const storagePath = `${userId}_${avatarFileName}`;
    
    // Converter dataURL para buffer
    const avatarBuffer = await dataURLToBlob(avatarDataUrl);
    const mimeType = getMimeType(avatarDataUrl);
    
    // Fazer upload para o bucket 'avatars'
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('avatars')
      .upload(storagePath, avatarBuffer, {
        contentType: mimeType,
        upsert: true,
      });

    if (uploadError) {
      console.error('Erro ao fazer upload do avatar:', uploadError);
      return { url: null, storagePath: null };
    }
    
    // Obter URL pública do avatar
    const { data: publicUrlData } = supabase.storage
      .from('avatars')
      .getPublicUrl(storagePath);

    return { 
      url: publicUrlData.publicUrl, 
      storagePath: storagePath 
    };
  } catch (error) {
    console.error('Erro ao processar avatar:', error);
    return { url: null, storagePath: null };
  }
}

function generateRegistrationNumber(): string {
  return `REG-${nanoid(8)}`;
}

// Gera um código de check-in de 4 dígitos (0000-9999) único dentro do tenant informado
async function generateUniqueCheckInCode(
  supabase: any,
  tenantId: string
): Promise<string> {
  const MAX_ATTEMPTS = 15;

  for (let attempt = 0; attempt < MAX_ATTEMPTS; attempt++) {
    // Gera número aleatório entre 1000 e 9999 para evitar códigos com menos de 4 dígitos
    const code = String(Math.floor(1000 + Math.random() * 9000));

    const { data, error } = await supabase
      .from('students')
      .select('id')
      .eq('tenant_id', tenantId)
      .eq('check_in_code', code)
      .limit(1);

    if (error) {
      // Em caso de erro de consulta, tenta novo código
      continue;
    }

    if (!data || data.length === 0) {
      // Não existe nenhum aluno com este código para o tenant -> código disponível
      return code;
    }
  }

  // Se ultrapassar o número máximo de tentativas, gera erro
  throw new Error('Não foi possível gerar um código de check-in único.');
}

export async function createAluno(formData: NovoAlunoFormValues): Promise<CreateAlunoResult> {
  const hasCreatePermission = await hasPermission('students:create');
  if (!hasCreatePermission) {
    return {
      success: false,
      message: 'Você não tem permissão para criar alunos'
    };
  }

  const validationResult = novoAlunoSchema.safeParse(formData);
  if (!validationResult.success) {
    const formattedErrors: Record<string, string[]> = {};
    const errors = validationResult.error.format();
    
    Object.keys(errors).forEach(key => {
      if (key !== '_errors' && (errors as any)[key]?._errors?.length > 0) {
        formattedErrors[key] = (errors as any)[key]._errors;
      }
    });
    
    return {
      success: false,
      errors: formattedErrors
    };
  }

  const validatedData = validationResult.data;
  const supabase = await createAdminClient();

  try {
    // Extrair corretamente o primeiro nome e sobrenome usando função utilitária
    const { firstName: first_name, lastName: last_name, fullName: full_name } = extractNameParts(validatedData.full_name);
    
    // Sanitizar número de telefone
    const sanitizedPhone = sanitizePhoneNumber(validatedData.phone);
    
    // Obter tenant_id do usuário atual
    const { data: authUser } = await supabase.auth.getUser();
    const { data: userTenant } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', authUser.user?.id)
      .single();

    if (!userTenant?.tenant_id) {
      return {
        success: false,
        message: 'Não foi possível identificar a academia'
      };
    }

    // Armazenar o avatarDataUrl para uso posterior, caso exista
    const avatarDataUrl = validatedData.avatar_url && validatedData.avatar_url.startsWith('data:') 
      ? validatedData.avatar_url 
      : null;

    // 1. Criar usuário em auth.users
    const { data: userData, error: userError } = await supabase.auth.admin.createUser({
      email: validatedData.email,
      email_confirm: true,
      user_metadata: {
        first_name: first_name,
        last_name: last_name,
        full_name: full_name,
        phone: sanitizedPhone,
        email_verified: true
      },
      app_metadata: {
        tenant_id: userTenant.tenant_id,
        branch_id: validatedData.branch_id,
        role: 'student',
        status: 'active',
        provider: 'email',
        email: validatedData.email,
        first_name: first_name,
        last_name: last_name,
        avatar_url: null,
        avatar_storage_path: null,
      },
      password: '123456789'
    });

    if (userError || !userData.user) {
      console.error('Erro ao criar usuário no auth:', userError);
      return {
        success: false,
        message: 'Erro ao criar usuário. ' + (userError?.message || '')
      };
    }

    const userId = userData.user.id;

    // Upload do avatar se fornecido
    let avatarUrl = null;
    let avatarStoragePath = null;

    if (avatarDataUrl) {
      const avatarResult = await uploadAvatar(supabase, avatarDataUrl, userId);
      avatarUrl = avatarResult.url;
      avatarStoragePath = avatarResult.storagePath;
    }

    // Atualizar app_metadata com ID e avatar
    await supabase.auth.admin.updateUserById(userId, {
      app_metadata: {
        ...userData.user.app_metadata,
        id: userId,
        avatar_url: avatarUrl,
        avatar_storage_path: avatarStoragePath,
      }
    });

    // Sincronizar status no auth.users usando o serviço centralizado
    await syncUserStatusToAuth(userId, 'active');

    // 2. Criar registro em public.users
    const { error: publicUserError } = await supabase
      .from('users')
      .insert({
        id: userId,
        tenant_id: userTenant.tenant_id,
        branch_id: validatedData.branch_id,
        email: validatedData.email,
        role: 'student',
        status: 'active',
        first_name: first_name,
        last_name: last_name,
        full_name: full_name,
        phone: sanitizedPhone,
        avatar_url: avatarUrl,
        avatar_storage_path: avatarStoragePath,
        metadata: {}
      });

    if (publicUserError) {
      console.error('Erro ao criar usuário na tabela public.users:', publicUserError);
      // Tentar limpar o usuário criado no auth
      await supabase.auth.admin.deleteUser(userId);
      return {
        success: false,
        message: 'Erro ao criar registro do usuário. ' + publicUserError.message
      };
    }

    // 3. Criar registro em students
    const studentId = randomUUID();
    const registrationNumber = generateRegistrationNumber();
    const checkInCode = await generateUniqueCheckInCode(supabase, userTenant.tenant_id);
    const sanitizedEmergencyPhone = sanitizePhoneNumber(validatedData.emergency_contact_phone);

    const { error: studentError } = await supabase
      .from('students')
      .insert({
        id: studentId,
        tenant_id: userTenant.tenant_id,
        user_id: userId,
        branch_id: validatedData.branch_id,
        registration_number: registrationNumber,
        financial_status: 'up_to_date',
        birth_date: validatedData.birth_date || null,
        gender: validatedData.gender || null,
        street: validatedData.street || null,
        street_number: validatedData.street_number || null,
        complement: validatedData.complement || null,
        neighborhood: validatedData.neighborhood || null,
        city: validatedData.city || null,
        state: validatedData.state || null,
        postal_code: validatedData.postal_code || null,
        emergency_contact_name: validatedData.emergency_contact_name || null,
        emergency_contact_phone: sanitizedEmergencyPhone,
        emergency_contact_relationship: validatedData.emergency_contact_relationship || null,
        health_notes: validatedData.health_notes || null,
        allergies: validatedData.allergies || null,
        medical_conditions: validatedData.medical_conditions || null,
        medications: validatedData.medications || null,
        check_in_code: checkInCode,
        current_belt_id: null, // Será atualizado após criar a faixa
        metadata: {}
      });

    if (studentError) {
      console.error('Erro ao criar estudante:', studentError);
      // Tentar limpar os registros criados
      await supabase.from('users').delete().eq('id', userId);
      await supabase.auth.admin.deleteUser(userId);
      return {
        success: false,
        message: 'Erro ao criar estudante. ' + studentError.message
      };
    }

    // 4. Criar faixa inicial (branca) em student_belts
    const beltId = randomUUID();
    
    const { error: beltError } = await supabase
      .from('student_belts')
      .insert({
        id: beltId,
        tenant_id: userTenant.tenant_id,
        student_id: studentId,
        belt_color: 'white',
        degree: 0,
        awarded_at: new Date().toISOString(),
        awarded_by: authUser.user?.id,
        notes: 'Faixa inicial'
      });

    if (beltError) {
      console.error('Erro ao criar faixa inicial:', beltError);
      // Tentar limpar os registros criados
      await supabase.from('students').delete().eq('id', studentId);
      await supabase.from('users').delete().eq('id', userId);
      await supabase.auth.admin.deleteUser(userId);
      return {
        success: false,
        message: 'Erro ao criar faixa inicial. ' + beltError.message
      };
    }

    // 5. Atualizar current_belt_id no student
    const { error: updateBeltError } = await supabase
      .from('students')
      .update({ current_belt_id: beltId })
      .eq('id', studentId);

    if (updateBeltError) {
      console.error('Erro ao atualizar faixa atual do estudante:', updateBeltError);
      // Não é crítico, apenas log o erro
    }

    // 6. Criar matrícula se um plano foi selecionado
    let membershipId: string | undefined;
    let membershipCreated = false;
    let membershipMessage = '';

    if (validatedData.selected_plan_id) {
      try {
        const membershipResult = await createMembership({
          alunoId: studentId,
          planoId: validatedData.selected_plan_id,
          dataInicio: new Date().toISOString().split('T')[0], // Data atual
          metadata: {
            created_with_student: true,
            created_at: new Date().toISOString()
          }
        }, userTenant.tenant_id);

        if (membershipResult.success && membershipResult.data) {
          membershipId = (membershipResult.data as any).membership_id;
          membershipCreated = true;
          membershipMessage = ' e matrícula criada com sucesso';
        } else {
          console.warn('Erro ao criar matrícula:', membershipResult.errors);
          membershipMessage = ', mas houve erro ao criar a matrícula';
        }
      } catch (membershipError) {
        console.error('Erro inesperado ao criar matrícula:', membershipError);
        membershipMessage = ', mas houve erro inesperado ao criar a matrícula';
      }
    }

    revalidatePath('/alunos');
    revalidatePath('/academia');
    revalidatePath('/financeiro');

    return {
      success: true,
      message: `Aluno criado com sucesso${membershipMessage}`,
      studentId: studentId,
      userId: userId,
      membershipId: membershipId,
      membershipCreated: membershipCreated
    };
  } catch (error) {
    console.error('Erro inesperado ao criar aluno:', error);
    return {
      success: false,
      message: 'Ocorreu um erro inesperado ao criar o aluno'
    };
  }
} 