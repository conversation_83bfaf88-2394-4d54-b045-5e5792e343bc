'use client';

import { useFormContext } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { NovoAlunoFormValues } from "../../../actions/schemas/aluno-schema";
import { useEffect, useMemo, useState } from "react";
import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { cn } from "@/lib/utils";
import { AvatarCropModal } from "../../avatar-crop-modal";
import { Mail, User, Phone } from "lucide-react";
import { PhoneInput } from "@/components/shared/PhoneInput";
import { useStudentEmailAvailability } from "@/hooks/alunos/use-email-availability";

export default function BasicInfoSection() {
  const { control, watch, setValue, getValues, setError, clearErrors } = useFormContext<NovoAlunoFormValues>();
  const fullName = watch('full_name') || '';
  const avatarUrl = watch('avatar_url') || '';
  const watchedEmail = watch('email') || '';
  const { data: emailAvailability, isLoading: isCheckingEmail } = useStudentEmailAvailability(watchedEmail);
  
  const isGmailOnly = watchedEmail.trim() === "@gmail.com";
  
  // Extrai os dois primeiros nomes para exibição no cabeçalho
  const displayName = useMemo(() => {
    const parts = fullName.trim().split(' ').filter(p => p.length > 0);
    if (parts.length === 0) return '';

    // Se há apenas um nome, retorna-o
    if (parts.length === 1) return parts[0];

    const prepositions = ['de', 'da', 'do', 'dos', 'das'];
    // Se o segundo elemento é preposição e existe pelo menos mais um nome,
    // exibe os três primeiros (ex.: "Lucas de Silva")
    if (prepositions.includes(parts[1].toLowerCase()) && parts.length >= 3) {
      return `${parts[0]} ${parts[1]} ${parts[2]}`;
    }

    // Caso padrão: exibe os dois primeiros nomes
    return `${parts[0]} ${parts[1]}`;
  }, [fullName]);

  // Derivar first_name e last_name do nome completo
  // IMPORTANTE: Esta lógica deve estar alinhada com extractNameParts() do backend
  useEffect(() => {
    if (fullName) {
      const nameParts = fullName.trim().split(' ').filter(part => part.length > 0);
      
      if (nameParts.length === 0) {
        setValue('first_name', '', { shouldValidate: true });
        setValue('last_name', '', { shouldValidate: true });
      } else if (nameParts.length === 1) {
        // Se só tem um nome, vai todo para first_name
        setValue('first_name', nameParts[0], { shouldValidate: true });
        setValue('last_name', '', { shouldValidate: true });
      } else {
        // Primeiro nome para first_name, segundo nome (ou combinação) para last_name
        // Esta lógica deve estar alinhada com utils/name-utils.ts

        // Lista de preposições comuns que podem fazer parte do sobrenome
        const prepositions = ['de', 'da', 'do', 'dos', 'das'];
        let derivedLastName = nameParts[1];

        // Se a segunda parte é uma preposição e existe pelo menos mais um nome,
        // combinamos a preposição com a próxima parte para formar o sobrenome.
        if (prepositions.includes(derivedLastName.toLowerCase()) && nameParts.length >= 3) {
          derivedLastName = `${derivedLastName} ${nameParts[2]}`;
        }

        setValue('first_name', nameParts[0], { shouldValidate: true });
        setValue('last_name', derivedLastName, { shouldValidate: true });
      }
    }
  }, [fullName, setValue]);

  // Validação de disponibilidade de e-mail no formulário
  useEffect(() => {
    // limpar erros para casos ignorados ou sem valor suficiente
    if (!watchedEmail || isGmailOnly) {
      clearErrors('email');
      return;
    }

    // aguarda terminar a verificação
    if (isCheckingEmail) return;

    if (emailAvailability) {
      if (!emailAvailability.available) {
        setError('email', { type: 'manual', message: 'E-mail já está em uso' });
      } else {
        clearErrors('email');
      }
    }
  }, [watchedEmail, isGmailOnly, isCheckingEmail, emailAvailability, setError, clearErrors]);

  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const { primaryColor } = useTenantTheme();
  
  // Estados para o modal de crop
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [tempPreviewUrl, setTempPreviewUrl] = useState<string | null>(null);

  // Inicializa o preview do avatar com o valor do formulário
  useEffect(() => {
    if (avatarUrl && !avatarPreview) {
      setAvatarPreview(avatarUrl);
    }
  }, [avatarUrl, avatarPreview]);

  // Função para gerar iniciais do nome para exibir no avatar quando não há imagem
  const getInitials = (name: string): string => {
    if (!name) return "";
    return name
      .split(" ")
      .map((n: string) => n?.[0])
      .slice(0, 2)
      .join("")
      .toUpperCase();
  };

  // Manipulador para quando o usuário seleciona um arquivo
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Criar URL temporária para o preview no modal
      const reader = new FileReader();
      reader.onloadend = () => {
        setTempPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
      
      // Atualizar o arquivo selecionado
      setSelectedFile(file);
      
      // Abrir o modal de crop
      setIsModalOpen(true);
    }
  };

  // Manipulador para quando o usuário confirma o crop
  const handleCroppedImageChange = (dataUrl: string) => {
    // Atualizar o preview
    setAvatarPreview(dataUrl);
    
    // Atualizar o valor no formulário
    setValue('avatar_url', dataUrl, { 
      shouldValidate: true,
      shouldDirty: true
    });
  };

  return (
    <div className="grid grid-cols-1 gap-6">
      {/* Avatar e cabeçalho do perfil */}
      <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <User className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Foto e Perfil
          </h2>
        </div>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4 md:gap-6 items-center md:items-start">
            {/* Avatar Upload */}
            <div className="flex flex-col items-center flex-shrink-0">
              <div className="flex justify-center">
                <div className="relative">
                  <div 
                    className={cn(
                      "h-24 w-24 rounded-full flex items-center justify-center overflow-hidden bg-slate-200 dark:bg-slate-700 text-2xl font-semibold text-slate-600 dark:text-slate-300",
                      avatarPreview ? "border-2 border-primary" : ""
                    )}
                    style={primaryColor && !avatarPreview ? { backgroundColor: `${primaryColor}20` } : undefined}
                  >
                    {avatarPreview ? (
                      <img src={avatarPreview} alt={displayName} className="w-full h-full object-cover" />
                    ) : (
                      <span style={primaryColor ? { color: primaryColor } : undefined}>
                        {getInitials(displayName)}
                      </span>
                    )}
                  </div>
                  
                  <label 
                    htmlFor="avatar-upload" 
                    className="absolute bottom-0 right-0 w-7 h-7 flex items-center justify-center rounded-full bg-primary text-white cursor-pointer shadow-md hover:brightness-110 transition-all"
                    style={primaryColor ? { backgroundColor: primaryColor } : undefined}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </label>
                  
                  <input 
                    id="avatar-upload" 
                    type="file" 
                    accept="image/*" 
                    className="hidden" 
                    onChange={handleAvatarChange}
                  />
                </div>
              </div>
              <p className="mt-2 text-xs text-center text-slate-500 dark:text-slate-400">
                Clique para adicionar foto
              </p>
            </div>
            
            {/* Informações de perfil */}
            <div className="flex-grow text-center md:text-left">
              <div className="mb-4">
                <div className="flex flex-wrap items-center justify-center md:justify-start gap-2 mb-1.5">
                  <span className={cn(
                    "px-2.5 py-0.5 rounded-full text-xs font-medium border",
                    "bg-primary/10 text-primary border-primary/20"
                  )}
                  style={primaryColor ? { 
                    backgroundColor: `${primaryColor}20`, 
                    color: primaryColor,
                    borderColor: `${primaryColor}30` 
                  } : undefined}
                  >
                    Novo Aluno
                  </span>
                </div>
                
                <h2 className="text-2xl sm:text-3xl font-bold text-slate-900 dark:text-slate-100">
                  {displayName || 'Nome do Aluno'}
                </h2>
                
                <div className="flex flex-wrap items-center justify-center md:justify-start gap-3 mt-2">
                  <div className="flex items-center text-sm text-slate-500 dark:text-slate-400">
                    <svg xmlns="http://www.w3.org/2000/svg" className="w-3.5 h-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span>Novo cadastro</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modal para crop de imagem */}
      <AvatarCropModal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        file={selectedFile}
        previewUrl={tempPreviewUrl}
        onFileChange={setSelectedFile}
        onCroppedImageChange={handleCroppedImageChange}
      />

      {/* Campos ocultos para first_name e last_name derivados */}
      <FormField
        control={control}
        name="first_name"
        render={({ field }) => (
          <FormItem className="hidden">
            <FormControl>
              <Input {...field} value={field.value || ''} />
            </FormControl>
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="last_name"
        render={({ field }) => (
          <FormItem className="hidden">
            <FormControl>
              <Input {...field} value={field.value || ''} />
            </FormControl>
          </FormItem>
        )}
      />

      {/* Informações Básicas */}
      <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <User className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Informações Básicas
          </h2>
        </div>
        
        <CardContent className="p-6 space-y-6">
          {/* Nome Completo */}
          <FormField
            control={control}
            name="full_name"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center mb-2">
                  <User className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-2" />
                  <FormLabel className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    Nome Completo*
                  </FormLabel>
                </div>
                <FormControl>
                  <Input 
                    placeholder="Digite o nome completo" 
                    {...field} 
                    className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full"
                  />
                </FormControl>
                <FormMessage className="mt-1" />
              </FormItem>
            )}
          />

          {/* Email */}
          <FormField
            control={control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center mb-2">
                  <Mail className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-2" />
                  <FormLabel className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    Email*
                  </FormLabel>
                </div>
                <FormControl>
                  <Input 
                    type="email" 
                    placeholder="<EMAIL>" 
                    {...field} 
                    className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full"
                  />
                </FormControl>
                <FormMessage className="mt-1" />
                {watchedEmail && !isGmailOnly && !isCheckingEmail && emailAvailability?.available && (
                  <p className="text-xs text-green-600 mt-1">E-mail disponível</p>
                )}
                {watchedEmail && !isGmailOnly && isCheckingEmail && (
                  <p className="text-xs text-muted-foreground mt-1">Verificando...</p>
                )}
              </FormItem>
            )}
          />

          {/* Telefone */}
          <FormField
            control={control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center mb-2">
                  <Phone className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-2" />
                  <FormLabel className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    Telefone*
                  </FormLabel>
                </div>
                <FormControl>
                  <PhoneInput field={field} defaultCountryCode="+55" />
                </FormControl>
                <FormMessage className="mt-1" />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>
    </div>
  );
} 