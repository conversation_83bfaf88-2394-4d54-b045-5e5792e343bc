'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle2, User, PlusCircle } from "lucide-react";

interface FormSuccessProps {
  userId: string | null;
  onViewProfile: () => void;
  onAddAnother: () => void;
}

export default function FormSuccess({ 
  userId, 
  onViewProfile, 
  onAddAnother 
}: FormSuccessProps) {
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <div className="bg-green-100 p-3 rounded-full">
            <CheckCircle2 className="h-12 w-12 text-green-600" />
          </div>
        </div>
        <CardTitle className="text-2xl">Aluno Criado com Sucesso</CardTitle>
        <CardDescription>
          O novo aluno foi cadastrado com sucesso no sistema e já pode acessar a plataforma.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-center text-muted-foreground">
          Uma senha temporária foi gerada e pode ser alterada no primeiro acesso.
        </p>
        
        <div className="bg-muted p-4 rounded-md">
          <h3 className="font-medium mb-2">Próximos passos sugeridos:</h3>
          <ul className="list-disc pl-5 space-y-1">
            <li>A faixa branca (grau 0) já foi atribuída automaticamente</li>
            <li>Adicionar o aluno às turmas correspondentes</li>
            <li>Configurar plano de mensalidade</li>
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col sm:flex-row gap-3 justify-center">
        <Button 
          onClick={onViewProfile} 
          className="w-full sm:w-auto"
          disabled={!userId}
        >
          <User className="mr-2 h-4 w-4" />
          Ver Perfil do Aluno
        </Button>
        <Button 
          onClick={onAddAnother} 
          variant="outline" 
          className="w-full sm:w-auto"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Adicionar Outro Aluno
        </Button>
      </CardFooter>
    </Card>
  );
} 