'use client';

import { useEffect, ReactNode } from 'react';
import { usePageTitle } from '@/contexts/PageTitleContext';

interface NovoAlunoClientWrapperProps {
  children: ReactNode;
}

export default function NovoAlunoClientWrapper({ children }: NovoAlunoClientWrapperProps) {
  const { setPageTitle } = usePageTitle();

  useEffect(() => {
    setPageTitle('Novo Aluno');
    return () => setPageTitle(null);
  }, [setPageTitle]);
  
  return <>{children}</>;
} 