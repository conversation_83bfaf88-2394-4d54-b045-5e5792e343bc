"use client";

import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";
import Link from "next/link";

export default function NovoAlunoError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div className="flex flex-col items-center justify-center h-[50vh] gap-4">
      <div className="rounded-full bg-destructive/10 p-4">
        <AlertCircle className="h-8 w-8 text-destructive" />
      </div>
      <h2 className="text-xl font-semibold">Ocorreu um erro</h2>
      <p className="text-muted-foreground text-center max-w-md">
        Não foi possível carregar o formulário de cadastro de aluno. Por favor, tente novamente.
      </p>
      <div className="flex gap-4 mt-4">
        <Button onClick={reset} variant="default">
          Tentar novamente
        </Button>
        <Link href="/alunos">
          <Button variant="outline">Voltar para Alunos</Button>
        </Link>
      </div>
    </div>
  );
}