import { Metadata } from "next";
import { requireAuth } from "@/services/auth/server";

export const metadata: Metadata = {
  title: "Novo Aluno | ApexSAAS",
  description: "Cadastro de novo aluno no sistema ApexSAAS"
};

export default async function NovoAlunoLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Verificar se o usuário está autenticado (middleware já verifica permissões)
  await requireAuth();

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Cadastrar Novo Aluno</h1>
        <p className="text-muted-foreground">Preencha os dados para cadastrar um novo aluno no sistema.</p>
      </div>
      
      {children}
    </div>
  );
} 