import { Metadata } from "next";
import { ClientFilters } from "./components/filters/client-filters";
import { getBranches } from "./components/filters/server-branches";
import { AlunosListContainer } from "./components/list/alunos-list-container";
import { StudentActionsButtons } from "./components/actions/student-actions-buttons";
import { getStudentStats } from "./actions/student-stats";
import { NewStudentButton } from "./components/actions/new-student-button";
import { StudentErrorMessage } from "./components/errors/error-message";

export const metadata: Metadata = {
  title: "Alunos | ApexSAAS",
  description: "Gerenciamento de alunos no sistema ApexSAAS"
};

export default async function Alunos() {
  const branches = await getBranches();
  const stats = await getStudentStats();

  return (
    <>
      {/* Componente para exibir mensagens de erro */}
      <StudentErrorMessage />
      
      <div className="mb-2 space-y-4">
        <div className="flex flex-col sm:flex-row gap-4 items-stretch sm:items-center justify-between">
          <div className="order-2 sm:order-1">
            <StudentActionsButtons />
          </div>
          <div className="order-1 sm:order-2">
            <NewStudentButton />
          </div>
        </div>

        {/* Client Component para filtros com filiais do banco */}
        <ClientFilters branches={branches} initialStats={stats} />
      </div>

      {/* Container para lista de alunos */}
      <AlunosListContainer />
    </>
  );
}