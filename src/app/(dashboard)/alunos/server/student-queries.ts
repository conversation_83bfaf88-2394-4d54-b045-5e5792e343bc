"use server";

import { createTenantServerClient } from "@/services/supabase/server";
import { BeltColor } from "@/components/belt";
import { FilterParams, Student } from "../types";
import { SupabaseClient } from "@supabase/supabase-js";
import { calculateMultipleStudentsFinancialStatus, type StudentFinancialInfo } from "./financial-status";

// Definir a interface localmente para evitar problemas de importação
interface FetchStudentsResult {
  students: Student[];
  totalCount: number;
}

interface StudentBelt {
  id?: string;
  belt_level_id?: string;
  color: string;
  degree: number;
  stripeColor?: string | null;
  showCenterLine?: boolean | null;
  centerLineColor?: string | null;
  label?: string | null;
  sortOrder?: number | null;
  awardedAt?: string | null;
}

// Interface local para contornar problema de cache do TypeScript
interface LocalFilterParams {
  skip?: number;
  take?: number;
  search?: string;
  status?: string;
  belt?: string[];
  financialStatus?: string[];
  branch?: string[];
  enrollmentStatus?: string[];
}

export async function fetchStudentsFromServer(params: LocalFilterParams = {}): Promise<FetchStudentsResult> {
  try {
    const supabase = await createTenantServerClient();

    const {
      skip = 0,
      take = 10,
      search = "",
      status = ["active"], // Padrão como array
      belt = [],
      financialStatus = [],
      branch = [],
      enrollmentStatus = []
    } = params;

    // Criar a consulta base usando status centralizado da tabela users
    // Especificamos students_user_id_fkey para usar a relação original students.user_id -> users.id
    let query = supabase
      .from("students")
      .select(`
        id,
        financial_status,
        birth_date,
        last_attendance_date,
        current_belt_id,
        users!students_user_id_fkey!inner(
          id,
          full_name,
          email,
          avatar_url,
          role,
          phone,
          created_at,
          status
        ),
        branches(id, name),
        class_group_enrollments(
          id,
          status,
          enrollment_pauses!enrollment_pauses_enrollment_id_fkey(
            id,
            paused_at,
            resumed_at,
            reason
          )
        ),
        memberships!memberships_student_id_fkey(
          id,
          status,
          plans!memberships_plan_id_fkey(
            id,
            title,
            plan_type
          )
        )
      `, { count: 'exact' })
      .eq("users.role", "student");

    // Aplicar filtros de status usando a tabela users
    // Verificar se o array não contém 'all' antes de aplicar filtros
    if (Array.isArray(status) && status.length > 0) {
      // Mapear os valores de status para garantir que estão corretos
      const validStatuses = status
        .map(s => s === "active" ? "active" : s === "inactive" ? "inactive" : s === "suspended" ? "suspended" : null)
        .filter(Boolean);

      if (validStatuses.length > 0) {
        query = query.in("users.status", validStatuses);
      }
    }

    if (belt.length > 0) {

      const studentIds = await getStudentIdsByBelt(supabase, belt);
      if (studentIds.length > 0) {
        query = query.in("id", studentIds);
      } else {
        console.log("Nenhum aluno encontrado com as faixas selecionadas");
        return { students: [], totalCount: 0 };
      }
    }

    if (financialStatus.length > 0) {
      const mappedFinancialStatus = financialStatus.map(status => {
        switch (status) {
          case "ok": return "up_to_date";
          case "late": return "overdue";
          case "exempt": return "pending";
          default: return status;
        }
      }) as ("up_to_date" | "pending" | "overdue")[];

      query = query.in("financial_status", mappedFinancialStatus);
    }

    if (branch.length > 0) {
      query = query.in("branch_id", branch);
    }

    // Aplicar filtro de status de matrícula
    // Para o filtro de enrollmentStatus, precisamos processar após obter os dados
    // pois é um campo calculado com base nas matrículas e pausas

    // Aplicar busca por nome ou email
    if (search && search.trim() !== "") {
      const trimmedSearch = search.trim().toLowerCase();

      // Abordagem simplificada: usar .or() para combinar buscas
      if (trimmedSearch.includes('@')) {
        // Se parece com um email, priorizar busca por email
        query = query.ilike('users.email', `%${trimmedSearch}%`);
      } else {
        // Busca por nome usando full_name com operador OR para maior flexibilidade
        // Isso permite encontrar correspondências parciais em qualquer parte do nome
        query = query.or(
          `full_name.ilike.%${trimmedSearch}%,first_name.ilike.%${trimmedSearch}%,last_name.ilike.%${trimmedSearch}%`,
          { foreignTable: 'users' }
        );
      }
    }

    // Aplicar paginação apenas se NÃO houver filtro de enrollmentStatus.
    // Quando filtramos por enrollmentStatus precisamos primeiro obter todos os alunos
    // para então filtrar e paginar em memória, pois o status é calculado após o fetch.
    const shouldApplyRangeHere = enrollmentStatus.length === 0;
    if (shouldApplyRangeHere) {
      query = query.range(skip, skip + take - 1);
    }

    const { data, count, error } = await query;

    if (error) {
      console.error("Erro ao buscar alunos:", error);
      throw new Error(`Erro ao buscar alunos: ${error.message}`);
    }

    // Usar o status financeiro da tabela students (mantido atualizado por triggers)
    // Apenas calcular em tempo real se necessário para casos específicos
    const financialStatusMap: Record<string, StudentFinancialInfo> = {};
    
    // Mapear os status da tabela para o formato esperado
    (data || []).forEach((student: any) => {
      financialStatusMap[student.id] = {
        student_id: student.id,
        financial_status: student.financial_status || "no_data"
      };
    });

    const students: Student[] = await Promise.all(
      (data || []).map(async (student: any) => {
        const user = student.users || {};
        const branch = student.branches || {};
        const birthDate = student.birth_date || null;

        // Obter status financeiro calculado
        const financialInfo = financialStatusMap[student.id] || {
          student_id: student.id,
          financial_status: "no_data" as const
        };
        let currentBelt: {
          belt_color: BeltColor;
          degree: number;
          label?: string;
          stripe_color?: string | null;
          show_center_line?: boolean | null;
          center_line_color?: string | null;
        } = {
          belt_color: "white" as BeltColor,
          degree: 0
        };

        if (student.id) {
          const { data: beltDetails } = await (supabase as any).rpc('get_student_current_belt_details', { student_id_param: student.id });
          if (Array.isArray(beltDetails) && beltDetails.length > 0) {
            const belt = beltDetails[0] as any;
            currentBelt = {
              belt_color: belt.belt_color as BeltColor,
              degree: belt.degree || 0,
              label: belt.label || null,
              stripe_color: belt.stripe_color || null,
              show_center_line: belt.show_center_line || null,
              center_line_color: belt.center_line_color || null,
            };
          }
        }

        // Processar informações do plano de matrícula
        const enrollmentPlan = (() => {
          if (Array.isArray(student.memberships) && student.memberships.length > 0) {
            // Buscar a primeira matrícula ativa
            const activeMembership = student.memberships.find((membership: any) =>
              membership.status === 'active'
            ) || student.memberships[0]; // Fallback para a primeira se não houver ativa

            if (activeMembership && activeMembership.plans) {
              return {
                id: activeMembership.plans.id,
                title: activeMembership.plans.title,
                type: activeMembership.plans.plan_type,
                status: activeMembership.status
              };
            }
          }
          return null;
        })();

        return {
          id: user.id, // ID da tabela users (para links de perfil)
          student_id: student.id, // ID da tabela students (para operações específicas de student)
          name: user.full_name || "Nome não informado",
          email: user.email || "Email não informado",
          avatar: user.avatar_url || null,
          belt: currentBelt.belt_color,
          role: user.role || "student",
          createdAt: user.created_at || new Date().toISOString(),
          isActive: user.status === "active", // Usar status centralizado da tabela users
          status: user.status || "active", // Adicionar campo status centralizado
          financialStatus: financialInfo.financial_status,
          phone: user.phone || "",
          branch: branch.name || "Matriz",
          birthDate: birthDate,
          lastVisit: student.last_attendance_date || null,
          degree: typeof currentBelt.degree === 'number' ? currentBelt.degree : undefined,
          // Adicionar dados completos da faixa
          beltDetails: {
            label: currentBelt.label,
            stripe_color: currentBelt.stripe_color,
            show_center_line: currentBelt.show_center_line,
            center_line_color: currentBelt.center_line_color,
          },
          // Informações do plano de matrícula
          enrollmentPlan,
          enrollmentStatus: (() => {
            // Verificar se há matrículas ativas
            if (Array.isArray(student.class_group_enrollments) && student.class_group_enrollments.length > 0) {
              const enrollment = student.class_group_enrollments[0];

              // Verificar se há pausa ativa (resumed_at é null)
              if (Array.isArray(enrollment.enrollment_pauses) && enrollment.enrollment_pauses.length > 0) {
                const activePause = enrollment.enrollment_pauses.find((pause: any) => pause.resumed_at === null);
                if (activePause) {
                  return 'paused'; // Retornar 'paused' se há uma pausa ativa
                }
              }

              // Se não há pausa ativa, retornar o status normal da matrícula
              return enrollment.status;
            }

            return undefined;
          })()
        };
      })
    );

    // Aplicar filtro de enrollmentStatus após o mapeamento dos dados
    let filteredStudents = students;
    if (enrollmentStatus.length > 0) {
      filteredStudents = students.filter(student => {
        // Se enrollmentStatus é undefined, tratamos como 'inactive'
        const studentEnrollmentStatus = (student as any).enrollmentStatus || 'inactive';
        return enrollmentStatus.includes(studentEnrollmentStatus);
      });
    }

    // Recalcular a contagem total após aplicar o filtro de enrollmentStatus
    const finalTotalCount = enrollmentStatus.length > 0 ? filteredStudents.length : count || 0;

    // Aplicar paginação em memória caso tenhamos ignorado o range na query
    const pagedStudents = enrollmentStatus.length > 0
      ? filteredStudents.slice(skip, skip + take)
      : filteredStudents;

    return {
      students: pagedStudents,
      totalCount: finalTotalCount
    };
  } catch (error) {
    console.error("Erro ao buscar alunos:", error);
    return {
      students: [],
      totalCount: 0
    };
  }
}

// Função auxiliar para obter IDs de alunos por faixa
async function getStudentIdsByBelt(
  supabase: SupabaseClient,
  belt: string[]
): Promise<string[]> {
  const { data, error } = await supabase
    .from("student_belts")
    .select("student_id, belt_levels!inner(belt_color)")
    .in("belt_levels.belt_color", belt);
  if (error) {
    console.error("Erro ao buscar IDs de alunos por faixa:", error);
    return [];
  }
  if (data && data.length > 0) {
    return data.map((item: any) => item.student_id);
  }
  return [];
} 