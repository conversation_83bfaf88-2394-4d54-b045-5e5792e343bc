import { BeltColor } from "@/components/belt";
import { StudentStats } from "../actions/student-stats";
import { StudentStatsResponse } from "../hooks/use-student-stats";

// Interface da filial/unidade
export interface Branch {
  id: string;
  name: string;
}

// Interface do aluno
export interface Student {
  id: string; // ID da tabela users (para links de perfil)
  student_id: string; // ID da tabela students (para operações específicas)
  name: string;
  email: string;
  avatar: string | null;
  belt: BeltColor;
  role: string;
  createdAt: string;
  financialStatus: "up_to_date" | "pending" | "overdue";
  phone: string;
  branch: string;
  birthDate: string | null;
  lastVisit: string | null;
  degree?: number;
  // Dados completos da faixa da tabela belt_levels
  beltDetails?: {
    label?: string | null;
    stripe_color?: string | null;
    show_center_line?: boolean | null;
    center_line_color?: string | null;
  };
  // Campos de compatibilidade e status centralizado
  isActive: boolean; // Para compatibilidade com código existente
  status: 'active' | 'inactive' | 'suspended'; // Status centralizado da tabela users
  // Status agora vem da tabela users através do join
  user?: {
    status: 'active' | 'inactive' | 'suspended';
  };
  // NOVO: status de matrícula do aluno na turma
  enrollmentStatus?: 'active' | 'inactive' | 'paused';
  // Informações do plano de matrícula
  enrollmentPlan?: {
    id: string;
    title: string;
    type: 'individual' | 'family' | 'corporate';
    status: 'active' | 'paused' | 'canceled' | 'expired';
  } | null;
}

// Re-exportando tipos
export type { StudentStats, StudentStatsResponse };

// Interface para parâmetros de busca/filtro
export interface FetchStudentsParams {
  page: number;
  pageSize: number;
  query?: string;
  status?: "active" | "inactive" | "all";
  belt?: string[];
  financialStatus?: string[];
  branch?: string[];
  enrollmentStatus?: string[];
  startDate?: Date;
  endDate?: Date;
}

// Interface para estado de filtros
export interface FilterState {
  search?: string;
  status?: string[];
  belt?: string[];
  financialStatus?: string[];
  branch?: string[];
  enrollmentStatus?: string[];
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
}

// Interface para filtros ativos exibidos na interface
export interface ActiveFilter {
  id: string;
  type: 'belt' | 'status' | 'financialStatus' | 'date' | 'branch' | 'enrollmentStatus';
  label: string;
  value: string;
}

// Interface para parâmetros de filtro usados no fetchStudents
export interface FilterParams {
  skip?: number;
  take?: number;
  search?: string;
  status?: string;
  belt?: string[];
  financialStatus?: string[];
  branch?: string[];
  enrollmentStatus?: string[];
}

// Interface para props do componente FilterPopover
export interface FilterPopoverProps {
  filters: FilterState;
  onFilterChange: (filters: FilterState) => void;
  branches: Branch[];
}

// Interface para props do componente FilterBar
export interface StudentFiltersProps {
  filters: FilterState;
  onFilterChange: (filters: FilterState) => void;
  branches: Branch[];
  initialStats?: StudentStats;
}

// Interface para resposta da busca de alunos
export interface FetchStudentsResult {
  students: Student[];
  totalCount: number;
}

// Interface para resposta da busca de filiais
export interface BranchesResponse {
  success: boolean;
  data: Branch[];
  error?: string;
} 