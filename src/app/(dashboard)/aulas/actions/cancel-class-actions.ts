"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";

/**
 * Cancela uma aula
 */
export async function cancelClass(classId: string, reason?: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    // Verificar se a aula existe e pertence ao tenant
    const { data: existingClass, error: existingError } = await supabase
      .from("classes")
      .select("id, status, start_time, name")
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .single();

    if (existingError || !existingClass) {
      return { success: false, errors: { _form: "Aula não encontrada" } };
    }

    // Não permitir cancelamento de aulas já concluídas ou canceladas
    if (existingClass.status === "completed" || existingClass.status === "cancelled") {
      return { 
        success: false, 
        errors: { _form: "Não é possível cancelar aulas já concluídas ou canceladas" } 
      };
    }

    // Cancelar a aula
    const { data: cancelledClass, error } = await supabase
      .from("classes")
      .update({
        status: "cancelled",
        notes: reason ? `Cancelada: ${reason}` : "Aula cancelada",
        updated_at: new Date().toISOString(),
      })
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .select()
      .single();

    if (error) {
      console.error("Erro ao cancelar aula:", error);
      return { success: false, errors: { _form: "Erro ao cancelar aula" } };
    }

    revalidatePath("/aulas");
    return { success: true, data: cancelledClass };
  } catch (error) {
    console.error("Erro ao cancelar aula:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Completa uma aula
 */
export async function completeClass(classId: string, notes?: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    // Verificar se a aula existe e pertence ao tenant
    const { data: existingClass, error: existingError } = await supabase
      .from("classes")
      .select("id, status, start_time, name")
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .single();

    if (existingError || !existingClass) {
      return { success: false, errors: { _form: "Aula não encontrada" } };
    }

    // Não permitir completar aulas já concluídas ou canceladas
    if (existingClass.status === "completed") {
      return { 
        success: false, 
        errors: { _form: "Esta aula já está marcada como concluída" } 
      };
    }

    if (existingClass.status === "cancelled") {
      return { 
        success: false, 
        errors: { _form: "Não é possível completar aulas canceladas" } 
      };
    }

    // Completar a aula
    const { data: completedClass, error } = await supabase
      .from("classes")
      .update({
        status: "completed",
        notes: notes ? `Concluída: ${notes}` : "Aula concluída",
        updated_at: new Date().toISOString(),
      })
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .select()
      .single();

    if (error) {
      console.error("Erro ao completar aula:", error);
      return { success: false, errors: { _form: "Erro ao completar aula" } };
    }

    revalidatePath("/aulas");
    return { success: true, data: completedClass };
  } catch (error) {
    console.error("Erro ao completar aula:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 