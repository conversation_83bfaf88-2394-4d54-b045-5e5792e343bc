"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";
import {
  CreateClassSchema,
  type CreateClass,
} from "./schemas/index";
import { checkScheduleConflicts } from "./utils/schedule-utils";
import { generateRecurringClassDates } from "./utils/recurrence-utils";

/**
 * Cria uma nova aula individual
 */
export async function createClass(data: unknown) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = CreateClassSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const validatedData = validationResult.data;

    // Verificação de permissão: apenas administradores podem criar aulas livres (sem turma associada)
    const userRole = user.app_metadata?.role as string | undefined;
    if (!validatedData.class_group_id && userRole !== "admin") {
      return {
        success: false,
        errors: {
          _form: "Apenas administradores podem criar aulas livres",
        },
      };
    }

    const supabase = await createClient();

    // Verificar se o instrutor existe e está ativo
    const { data: instructor, error: instructorError } = await supabase
      .from("users")
      .select("id, role, status")
      .eq("id", validatedData.instructor_id)
      .eq("tenant_id", tenantId)
      .single();

    if (instructorError || !instructor || instructor.role !== "instructor" || instructor.status !== "active") {
      return { success: false, errors: { instructor_id: "Instrutor não encontrado ou inativo" } };
    }

    // Verificar se a filial existe
    const { data: branch, error: branchError } = await supabase
      .from("branches")
      .select("id")
      .eq("id", validatedData.branch_id)
      .eq("tenant_id", tenantId)
      .single();

    if (branchError || !branch) {
      return { success: false, errors: { branch_id: "Filial não encontrada" } };
    }

    // Se há class_group_id, verificar se existe
    if (validatedData.class_group_id) {
      const { data: classGroup, error: classGroupError } = await supabase
        .from("class_groups")
        .select("id, is_active")
        .eq("id", validatedData.class_group_id)
        .eq("tenant_id", tenantId)
        .single();

      if (classGroupError || !classGroup || !classGroup.is_active) {
        return { success: false, errors: { class_group_id: "Turma não encontrada ou inativa" } };
      }
    }

    // Verificar conflitos de horário do instrutor
    const startTimeISO = validatedData.start_time;
    const endTimeISO = validatedData.end_time;
    
    const { data: conflictingClasses, error: conflictError } = await supabase
      .from("classes")
      .select("id, name, start_time, end_time")
      .eq("instructor_id", validatedData.instructor_id)
      .eq("tenant_id", tenantId)
      .in("status", ["scheduled", "ongoing"])
      .or(`and(start_time.lte.${endTimeISO},end_time.gte.${startTimeISO})`);

    if (conflictError) {
      console.error("Erro ao verificar conflitos:", conflictError);
    } else if (conflictingClasses && conflictingClasses.length > 0) {
      return { 
        success: false, 
        errors: { 
          start_time: "Instrutor já possui aula agendada neste horário",
          _conflicts: conflictingClasses 
        } 
      };
    }

    // Criar a aula
    const { data: newClass, error } = await supabase
      .from("classes")
      .insert({
        ...validatedData,
        tenant_id: tenantId,
        start_time: startTimeISO,
        end_time: endTimeISO,
      })
      .select()
      .single();

    if (error) {
      console.error("Erro ao criar aula:", error);
      return { success: false, errors: { _form: "Erro ao criar aula" } };
    }

    revalidatePath("/aulas");
    return { success: true, data: newClass };
  } catch (error) {
    console.error("Erro ao criar aula:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}