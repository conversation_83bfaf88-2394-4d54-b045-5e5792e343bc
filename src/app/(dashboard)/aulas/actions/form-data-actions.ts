"use server";

import { createClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";

// Tipos para o formulário
type FormInstructor = {
  id: string;
  first_name: string;
  last_name: string | null;
  full_name: string | null;
};

type FormBranch = {
  id: string;
  name: string;
};

type FormDataResult = {
  success: true;
  data: {
    instructors: FormInstructor[];
    branches: FormBranch[];
  };
} | {
  success: false;
  errors: { _form: string };
};

/**
 * Busca instrutores ativos para o formulário de turmas
 */
export async function getInstructorsForForm() {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    const { data: instructors, error } = await supabase
      .from("users")
      .select("id, first_name, last_name, full_name")
      .eq("tenant_id", tenantId)
      .eq("role", "instructor")
      .eq("status", "active")
      .order("first_name");

    if (error) {
      console.error("Erro ao buscar instrutores:", error);
      return { success: false, errors: { _form: "Erro ao buscar instrutores" } };
    }

    return { success: true, data: instructors || [] };
  } catch (error) {
    console.error("Erro ao buscar instrutores:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Busca filiais ativas para o formulário de turmas
 */
export async function getBranchesForForm() {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    const { data: branches, error } = await supabase
      .from("branches")
      .select("id, name")
      .eq("tenant_id", tenantId)
      .is("deleted_at", null)
      .order("name");

    if (error) {
      console.error("Erro ao buscar filiais:", error);
      return { success: false, errors: { _form: "Erro ao buscar filiais" } };
    }

    return { success: true, data: branches || [] };
  } catch (error) {
    console.error("Erro ao buscar filiais:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Busca dados necessários para o formulário de turmas (instrutores e filiais)
 */
export async function getFormData(): Promise<FormDataResult> {
  try {
    const [instructorsResult, branchesResult] = await Promise.all([
      getInstructorsForForm(),
      getBranchesForForm(),
    ]);

    if (!instructorsResult.success) {
      return { success: false, errors: { _form: "Erro ao buscar instrutores" } };
    }

    if (!branchesResult.success) {
      return { success: false, errors: { _form: "Erro ao buscar filiais" } };
    }

    return {
      success: true,
      data: {
        instructors: instructorsResult.data as FormInstructor[],
        branches: branchesResult.data as FormBranch[],
      },
    };
  } catch (error) {
    console.error("Erro ao buscar dados do formulário:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 