"use server";

import { createClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";
import {
  ClassFilterSchema,
  type ClassFilter,
} from "./schemas/index";
import type { 
  ClassWithDetails, 
  PaginatedResult
} from "../types";

/**
 * Lista aulas com filtros e paginação
 */
export async function getClasses(filters: unknown): Promise<{ 
  success: boolean; 
  data?: PaginatedResult<ClassWithDetails>; 
  errors?: any; 
}> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = ClassFilterSchema.safeParse(filters);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const filterData = validationResult.data;
    const supabase = await createClient();

    // Verificar se há filtro especial de instrutor
    const rawFilters = filters as any;
    let instructorClassGroupIds: string[] = [];
    
    // Se há _instructorFilter, buscar as turmas do instrutor uma única vez
    if (rawFilters?._instructorFilter) {
      const { classGroupIds } = rawFilters._instructorFilter;
      instructorClassGroupIds = classGroupIds || [];
    }

    // Construir query base com contagem de presenças
    let query = supabase
      .from("classes")
      .select(`
        *,
        instructor:users!classes_instructor_id_fkey(
          id,
          first_name,
          last_name,
          full_name
        ),
        branch:branches!classes_branch_id_fkey(
          id,
          name
        ),
        class_group:class_groups!classes_class_group_id_fkey(
          id,
          name,
          category
        ),
        attendance(count)
      `)
      .eq("tenant_id", tenantId);

    // Aplicar filtro de instrutor se presente
    if (rawFilters?._instructorFilter) {
      const { instructorId } = rawFilters._instructorFilter;
      if (instructorClassGroupIds.length > 0) {
        // Incluir aulas que o instrutor ministra diretamente OU aulas das turmas que ele é responsável
        query = query.or(`instructor_id.eq.${instructorId},class_group_id.in.(${instructorClassGroupIds.join(',')})`);
      } else {
        // Se não tem turmas, usar apenas filtro direto
        query = query.eq("instructor_id", instructorId);
      }
    }

    // Aplicar filtros
    if (filterData.search) {
      query = query.or(`name.ilike.%${filterData.search}%,description.ilike.%${filterData.search}%`);
    }

    if (filterData.instructor_id) {
      query = query.eq("instructor_id", filterData.instructor_id);
    }

    if (filterData.branch_id) {
      query = query.eq("branch_id", filterData.branch_id);
    }

    if (filterData.class_group_id !== undefined) {
      if (filterData.class_group_id === null) {
        query = query.is("class_group_id", null);
      } else {
        query = query.eq("class_group_id", filterData.class_group_id);
      }
    }

    if (filterData.status) {
      query = query.eq("status", filterData.status);
    }

    if (filterData.date_from) {
      query = query.gte("start_time", filterData.date_from);
    }

    if (filterData.date_to) {
      query = query.lte("start_time", filterData.date_to);
    }

    if (filterData.attendance_recorded !== undefined) {
      query = query.eq("attendance_recorded", filterData.attendance_recorded);
    }

    // Contar total de registros com os mesmos filtros
    let countQuery = supabase
      .from("classes")
      .select("*", { count: "exact", head: true })
      .eq("tenant_id", tenantId);

    // Aplicar filtro de instrutor na contagem se presente
    if (rawFilters?._instructorFilter) {
      const { instructorId } = rawFilters._instructorFilter;
      if (instructorClassGroupIds.length > 0) {
        // Incluir aulas que o instrutor ministra diretamente OU aulas das turmas que ele é responsável
        countQuery = countQuery.or(`instructor_id.eq.${instructorId},class_group_id.in.(${instructorClassGroupIds.join(',')})`);
      } else {
        // Se não tem turmas, usar apenas filtro direto
        countQuery = countQuery.eq("instructor_id", instructorId);
      }
    }

    // Aplicar os mesmos filtros na contagem
    if (filterData.search) {
      countQuery = countQuery.or(`name.ilike.%${filterData.search}%,description.ilike.%${filterData.search}%`);
    }

    if (filterData.instructor_id) {
      countQuery = countQuery.eq("instructor_id", filterData.instructor_id);
    }

    if (filterData.branch_id) {
      countQuery = countQuery.eq("branch_id", filterData.branch_id);
    }

    if (filterData.class_group_id !== undefined) {
      if (filterData.class_group_id === null) {
        countQuery = countQuery.is("class_group_id", null);
      } else {
        countQuery = countQuery.eq("class_group_id", filterData.class_group_id);
      }
    }

    if (filterData.status) {
      countQuery = countQuery.eq("status", filterData.status);
    }

    if (filterData.date_from) {
      countQuery = countQuery.gte("start_time", filterData.date_from);
    }

    if (filterData.date_to) {
      countQuery = countQuery.lte("start_time", filterData.date_to);
    }

    if (filterData.attendance_recorded !== undefined) {
      countQuery = countQuery.eq("attendance_recorded", filterData.attendance_recorded);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error("Erro ao contar aulas:", countError);
      return { success: false, errors: { _form: "Erro ao buscar aulas" } };
    }

    // Aplicar ordenação
    query = query.order(filterData.sort_by, { ascending: filterData.sort_order === "asc" });

    // Aplicar paginação
    const start = (filterData.page - 1) * filterData.limit;
    const end = start + filterData.limit - 1;
    query = query.range(start, end);

    const { data: classes, error } = await query;

    if (error) {
      console.error("Erro ao buscar aulas:", error);
      return { success: false, errors: { _form: "Erro ao buscar aulas" } };
    }

    // Transformar os dados para incluir _count no formato esperado
    const transformedClasses = (classes || []).map(classItem => ({
      ...classItem,
      _count: {
        attendance: classItem.attendance?.[0]?.count || 0
      },
      // Remover o campo attendance original
      attendance: undefined
    }));

    // Calcular dados de paginação
    const totalPages = Math.ceil((count || 0) / filterData.limit);
    const hasNext = filterData.page < totalPages;
    const hasPrev = filterData.page > 1;

    return {
      success: true,
      data: {
        data: transformedClasses as ClassWithDetails[],
        pagination: {
          page: filterData.page,
          limit: filterData.limit,
          total: count || 0,
          totalPages,
          hasNext,
          hasPrev,
        },
      },
    };
  } catch (error) {
    console.error("Erro ao buscar aulas:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Lista aulas de uma turma específica
 */
export async function getClassesByGroup(classGroupId: string, filters?: {
  status?: string;
  date_from?: string;
  date_to?: string;
  page?: number;
  limit?: number;
}): Promise<{ 
  success: boolean; 
  data?: PaginatedResult<ClassWithDetails>; 
  errors?: any; 
}> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    // Verificar se a turma existe
    const { data: classGroup, error: classGroupError } = await supabase
      .from("class_groups")
      .select("id")
      .eq("id", classGroupId)
      .eq("tenant_id", tenantId)
      .single();

    if (classGroupError || !classGroup) {
      return { success: false, errors: { _form: "Turma não encontrada" } };
    }

    // Construir query base
    let query = supabase
      .from("classes")
      .select(`
        *,
        instructor:users!classes_instructor_id_fkey(
          id,
          first_name,
          last_name,
          full_name
        ),
        branch:branches!classes_branch_id_fkey(
          id,
          name
        ),
        class_group:class_groups!classes_class_group_id_fkey(
          id,
          name,
          category
        ),
        attendance(count)
      `)
      .eq("tenant_id", tenantId)
      .eq("class_group_id", classGroupId);

    // Aplicar filtros opcionais
    if (filters?.status) {
      query = query.eq("status", filters.status);
    }

    if (filters?.date_from) {
      query = query.gte("start_time", filters.date_from);
    }

    if (filters?.date_to) {
      query = query.lte("start_time", filters.date_to);
    }

    // Contar total de registros
    const { count, error: countError } = await supabase
      .from("classes")
      .select("*", { count: "exact", head: true })
      .eq("tenant_id", tenantId)
      .eq("class_group_id", classGroupId);

    if (countError) {
      console.error("Erro ao contar aulas da turma:", countError);
      return { success: false, errors: { _form: "Erro ao buscar aulas da turma" } };
    }

    // Aplicar ordenação (por data/hora)
    query = query.order("start_time", { ascending: true });

    // Aplicar paginação
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    const start = (page - 1) * limit;
    const end = start + limit - 1;
    query = query.range(start, end);

    const { data: classes, error } = await query;

    if (error) {
      console.error("Erro ao buscar aulas da turma:", error);
      return { success: false, errors: { _form: "Erro ao buscar aulas da turma" } };
    }

    // Transformar dados para incluir contagens
    const transformedClasses = classes?.map((classItem: any) => ({
      ...classItem,
      _count: {
        attendance: classItem.attendance?.[0]?.count || 0,
      },
      // Remover o campo attendance original
      attendance: undefined
    })) || [];

    // Calcular dados de paginação
    const totalPages = Math.ceil((count || 0) / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      success: true,
      data: {
        data: transformedClasses as ClassWithDetails[],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages,
          hasNext,
          hasPrev,
        },
      },
    };
  } catch (error) {
    console.error("Erro ao buscar aulas da turma:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Busca uma aula específica por ID
 */
export async function getClassById(classId: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    const { data: classData, error } = await supabase
      .from("classes")
      .select(`
        *,
        instructor:users!classes_instructor_id_fkey(
          id,
          first_name,
          last_name,
          full_name
        ),
        branch:branches!classes_branch_id_fkey(
          id,
          name
        ),
        class_group:class_groups!classes_class_group_id_fkey(
          id,
          name,
          category
        )
      `)
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .single();

    if (error) {
      console.error("Erro ao buscar aula:", error);
      return { success: false, errors: { _form: "Aula não encontrada" } };
    }

    return { success: true, data: classData as ClassWithDetails };
  } catch (error) {
    console.error("Erro ao buscar aula:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 