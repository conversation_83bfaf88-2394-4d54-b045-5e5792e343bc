import { z } from 'zod';
import { PaginationSchema, SortOrderSchema, SearchFilterSchema, DateRangeFilterSchema } from '../../../turmas/actions/schemas/shared';

// Schema base para Attendance
const BaseAttendanceSchema = z.object({
  id: z.string().uuid().optional(),
  tenant_id: z.string().uuid(),
  student_id: z.string().uuid(),
  class_id: z.string().uuid(),
  checked_in_at: z.string().datetime().optional(),
  checked_in_by: z.string().uuid(),
  notes: z.string().max(500).optional().nullable().transform(val => {
    // Transformar strings vazias em null
    if (val === '' || val === undefined) return null;
    return val;
  }),
});

// Schema completo
export const AttendanceSchema = BaseAttendanceSchema;

// Schema para criação
export const CreateAttendanceSchema = BaseAttendanceSchema.omit({ 
  id: true,
  tenant_id: true,
  checked_in_at: true 
});

// Schema para check-in simples
export const CheckInStudentSchema = z.object({
  student_id: z.string().uuid('ID do aluno inválido'),
  class_id: z.string().uuid('ID da aula inválido'),
  notes: z.string().max(500, 'Notas devem ter no máximo 500 caracteres').optional().nullable().transform(val => {
    // Transformar strings vazias em null
    if (val === '' || val === undefined) return null;
    return val;
  }),
});

// Schema para check-in por QR Code
export const CheckInByQRSchema = z.object({
  qr_code: z.string().min(1, 'Código QR é obrigatório'),
  student_check_in_code: z.string().min(1, 'Código de check-in do aluno é obrigatório'),
  notes: z.string().max(500, 'Notas devem ter no máximo 500 caracteres').optional().nullable().transform(val => {
    // Transformar strings vazias em null
    if (val === '' || val === undefined) return null;
    return val;
  }),
});

// Schema para check-in em lote
export const BulkCheckInSchema = z.object({
  class_id: z.string().uuid('ID da aula inválido'),
  student_ids: z.array(z.string().uuid()).min(1, 'Selecione pelo menos um aluno'),
  notes: z.string().max(500).optional().nullable().transform(val => {
    // Transformar strings vazias em null
    if (val === '' || val === undefined) return null;
    return val;
  }),
});

// Schema para gerar QR Code de aula
export const GenerateClassQRSchema = z.object({
  class_id: z.string().uuid('ID da aula inválido'),
  expires_in_minutes: z.number().min(5).max(1440).default(60), // 5 min a 24h
});

// Schema para validar QR Code
export const ValidateQRCodeSchema = z.object({
  qr_code: z.string().min(1, 'Código QR é obrigatório'),
  class_id: z.string().uuid('ID da aula inválido'),
});

// Schema para filtros de presença
export const AttendanceFilterSchema = SearchFilterSchema.extend({
  class_id: z.string().uuid().optional(),
  student_id: z.string().uuid().optional(),
  checked_in_by: z.string().uuid().optional(),
}).merge(DateRangeFilterSchema).merge(PaginationSchema).extend({
  sort_by: z.enum(['checked_in_at', 'student_name', 'class_name']).default('checked_in_at'),
  sort_order: SortOrderSchema,
});

// Schema para filtros de presença por aluno
export const StudentAttendanceFilterSchema = z.object({
  student_id: z.string().uuid('ID do aluno inválido'),
  class_group_id: z.string().uuid().optional(),
  search: z.string().optional(),
}).merge(DateRangeFilterSchema).merge(PaginationSchema).extend({
  sort_by: z.enum(['checked_in_at', 'class_name']).default('checked_in_at'),
  sort_order: SortOrderSchema,
});

// Schema para relatório de presença
export const AttendanceReportSchema = z.object({
  class_id: z.string().uuid().optional(),
  class_group_id: z.string().uuid().optional(),
  student_id: z.string().uuid().optional(),
  instructor_id: z.string().uuid().optional(),
  period_start: z.string().datetime(),
  period_end: z.string().datetime(),
  include_details: z.boolean().default(false),
}).refine(data => {
  return new Date(data.period_end) > new Date(data.period_start);
}, {
  message: 'Data final deve ser posterior à data inicial',
  path: ['period_end']
});

// Schema para estatísticas de presença
export const AttendanceStatsSchema = z.object({
  total_attendances: z.number(),
  unique_students: z.number(),
  average_attendance_per_class: z.number(),
  attendance_rate: z.number(),
  busiest_day: z.string().optional(),
  busiest_time: z.string().optional(),
});

// Schema para remoção de presença
export const RemoveAttendanceSchema = z.object({
  attendance_id: z.string().uuid('ID da presença inválido'),
  reason: z.string().max(500, 'Motivo deve ter no máximo 500 caracteres').optional().nullable().transform(val => {
    // Transformar strings vazias em null
    if (val === '' || val === undefined) return null;
    return val;
  }),
});

// Schema para filtros da listagem de presença
export const AttendanceListFilterSchema = z.object({
  search: z.string().optional(),
  instructor_id: z.string().uuid().optional(),
  branch_id: z.string().uuid().optional(),
  class_group_id: z.string().uuid().nullable().optional(),
  status: z.enum(['scheduled', 'ongoing', 'completed', 'cancelled']).optional(),
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional(),
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(20),
});

// Tipos inferidos
export type Attendance = z.infer<typeof AttendanceSchema>;
export type CreateAttendance = z.infer<typeof CreateAttendanceSchema>;
export type CheckInStudent = z.infer<typeof CheckInStudentSchema>;
export type CheckInByQR = z.infer<typeof CheckInByQRSchema>;
export type BulkCheckIn = z.infer<typeof BulkCheckInSchema>;
export type GenerateClassQR = z.infer<typeof GenerateClassQRSchema>;
export type ValidateQRCode = z.infer<typeof ValidateQRCodeSchema>;
export type AttendanceFilter = z.infer<typeof AttendanceFilterSchema>;
export type StudentAttendanceFilter = z.infer<typeof StudentAttendanceFilterSchema>;
export type AttendanceReport = z.infer<typeof AttendanceReportSchema>;
export type AttendanceStats = z.infer<typeof AttendanceStatsSchema>;
export type RemoveAttendance = z.infer<typeof RemoveAttendanceSchema>;
export type AttendanceListFilter = z.infer<typeof AttendanceListFilterSchema>; 