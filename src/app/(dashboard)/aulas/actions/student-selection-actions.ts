'use server';

import { z } from 'zod';
import { createAdminClient } from '@/services/supabase/server';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';

// Schema para buscar alunos matriculados
const getEnrolledStudentsSchema = z.object({
  class_group_id: z.string().uuid(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(10),
  search: z.string().optional()
});

// Schema para buscar todos os alunos (aulas livres)
const getAllStudentsSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(10),
  search: z.string().optional(),
  tenant_id: z.string().uuid()
});

export async function getEnrolledStudents(data: unknown) {
  try {
    const result = getEnrolledStudentsSchema.safeParse(data);
    
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format()
      };
    }

    const { class_group_id, page, limit, search } = result.data;
    const supabase = await createAdminClient();
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      };
    }

    const offset = (page - 1) * limit;
    
    // Primeiro, buscar IDs das matrículas que têm pausas ativas
    const { data: pausedEnrollments, error: pauseError } = await supabase
      .from('enrollment_pauses')
      .select('enrollment_id')
      .is('resumed_at', null);

    if (pauseError) {
      console.error('Erro ao buscar pausas ativas:', pauseError);
      return {
        success: false,
        errors: { _form: 'Erro ao verificar pausas de matrícula' }
      };
    }

    const pausedEnrollmentIds = (pausedEnrollments || []).map(p => p.enrollment_id);

    // Buscar alunos matriculados excluindo os com pausas ativas
    let query = supabase
      .from('class_group_enrollments')
      .select(`
        id,
        student:students!inner (
          id,
          check_in_code,
          current_belt_id,
          user:users!students_user_id_fkey!inner (
            id,
            first_name,
            last_name,
            full_name,
            email,
            avatar_url
          )
        )
      `)
      .eq('class_group_id', class_group_id)
      .eq('status', 'active');

    // Excluir matrículas com pausas ativas
    if (pausedEnrollmentIds.length > 0) {
      query = query.not('id', 'in', `(${pausedEnrollmentIds.join(',')})`);
    }

    const { data: enrollments, error } = await query
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Erro ao buscar alunos matriculados:', error);
      return {
        success: false,
        errors: { _form: 'Erro ao buscar alunos matriculados' }
      };
    }

    // Transformar dados para o formato esperado
    let students = (enrollments || []).map(enrollment => enrollment.student);

    // Para cada estudante, buscar detalhes da faixa via RPC
    if (students.length > 0) {
      const beltsPromises = students.map(async (student: any) => {
        const { data: beltData, error: beltError } = await supabase.rpc(
          'get_student_current_belt_details',
          { student_id_param: student.id }
        );
        if (!beltError && beltData && beltData.length > 0) {
          student.current_belt = beltData[0];
        }
        return student;
      });
      students = await Promise.all(beltsPromises);
    }

    // Filtrar por busca se necessário
    if (search && search.trim()) {
      const searchTerm = search.toLowerCase().trim();
      students = students.filter((student: any) => {
        const user = student.user;
        return (
          user.full_name?.toLowerCase().includes(searchTerm) ||
          user.first_name?.toLowerCase().includes(searchTerm) ||
          user.last_name?.toLowerCase().includes(searchTerm) ||
          user.email?.toLowerCase().includes(searchTerm)
        );
      });
    }

    return {
      success: true,
      data: {
        students,
        pagination: {
          page,
          limit,
          total: students.length,
          hasMore: students.length === limit
        }
      }
    };

  } catch (error) {
    console.error('Erro inesperado ao buscar alunos matriculados:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' }
    };
  }
}

export async function getAllStudents(data: unknown) {
  try {
    const result = getAllStudentsSchema.safeParse(data);
    
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format()
      };
    }

    const { page, limit, search, tenant_id } = result.data;
    const supabase = await createAdminClient();
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      };
    }

    const offset = (page - 1) * limit;
    
    // Primeiro, buscar IDs das matrículas que têm pausas ativas
    const { data: pausedEnrollments, error: pauseError } = await supabase
      .from('enrollment_pauses')
      .select('enrollment_id')
      .is('resumed_at', null);

    if (pauseError) {
      console.error('Erro ao buscar pausas ativas:', pauseError);
      return {
        success: false,
        errors: { _form: 'Erro ao verificar pausas de matrícula' }
      };
    }

    const pausedEnrollmentIds = (pausedEnrollments || []).map(p => p.enrollment_id);

    // Buscar alunos do tenant que têm matrículas ativas
    let enrollmentQuery = supabase
      .from('class_group_enrollments')
      .select('student_id')
      .eq('status', 'active');

    // Excluir matrículas com pausas ativas
    if (pausedEnrollmentIds.length > 0) {
      enrollmentQuery = enrollmentQuery.not('id', 'in', `(${pausedEnrollmentIds.join(',')})`);
    }

    const { data: activeEnrollments, error: enrollmentError } = await enrollmentQuery;

    if (enrollmentError) {
      console.error('Erro ao buscar matrículas ativas:', enrollmentError);
      return {
        success: false,
        errors: { _form: 'Erro ao buscar matrículas ativas' }
      };
    }

    const activeStudentIds = Array.from(new Set((activeEnrollments || []).map(e => e.student_id)));

    if (activeStudentIds.length === 0) {
      return {
        success: true,
        data: {
          students: [],
          pagination: {
            page,
            limit,
            total: 0,
            hasMore: false
          }
        }
      };
    }

    // Buscar dados dos alunos ativos
    let query = supabase
      .from('students')
      .select(`
        id,
        check_in_code,
        current_belt_id,
        user:users!students_user_id_fkey!inner (
          id,
          first_name,
          last_name,
          full_name,
          email,
          avatar_url
        )
      `)
      .eq('tenant_id', tenant_id)
      .in('id', activeStudentIds);

    // Aplicar filtro de busca se fornecido
    if (search && search.trim()) {
      query = query.or(`
        users.full_name.ilike.%${search}%,
        users.first_name.ilike.%${search}%,
        users.last_name.ilike.%${search}%,
        users.email.ilike.%${search}%
      `);
    }

    const { data: students, error } = await query
      .range(offset, offset + limit - 1);

    // Buscar detalhes de faixa para cada estudante
    let enrichedStudents: any[] = [];
    if (students && students.length > 0) {
      enrichedStudents = await Promise.all(
        students.map(async (student: any) => {
          const { data: beltData, error: beltError } = await supabase.rpc(
            'get_student_current_belt_details',
            { student_id_param: student.id }
          );
          if (!beltError && beltData && beltData.length > 0) {
            student.current_belt = beltData[0];
          }
          return student;
        })
      );
    }

    if (error) {
      console.error('Erro ao buscar todos os alunos:', error);
      return {
        success: false,
        errors: { _form: 'Erro ao buscar alunos' }
      };
    }

    return {
      success: true,
      data: {
        students: enrichedStudents || [],
        pagination: {
          page,
          limit,
          total: (students || []).length,
          hasMore: (students || []).length === limit
        }
      }
    };

  } catch (error) {
    console.error('Erro inesperado ao buscar todos os alunos:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' }
    };
  }
} 