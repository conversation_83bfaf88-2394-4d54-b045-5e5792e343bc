import type { RecurrencePattern, CreateClass } from "../schemas/index";

/**
 * Gera datas de aulas baseado no padrão de recorrência
 */
export function generateRecurringClassDates(
  pattern: RecurrencePattern,
  template: CreateClass,
  classGroup: any
): any[] {
  if (!pattern) return [];

  const classes: any[] = [];
  const startDate = new Date(template.start_time);
  const templateDuration = new Date(template.end_time).getTime() - new Date(template.start_time).getTime();
  
  let currentDate = new Date(startDate);
  let occurrenceCount = 0;
  const maxOccurrences = pattern.maxOccurrences || 100; // Limite padrão
  const endDate = pattern.endDate ? new Date(pattern.endDate) : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 1 ano no futuro

  while (currentDate <= endDate && occurrenceCount < maxOccurrences) {
    // Verificar se o dia da semana está incluído no padrão
    if (!pattern.daysOfWeek || pattern.daysOfWeek.includes(currentDate.getDay())) {
      const classStartTime = new Date(currentDate);
      const classEndTime = new Date(classStartTime.getTime() + templateDuration);

      classes.push({
        ...template,
        start_time: classStartTime.toISOString(),
        end_time: classEndTime.toISOString(),
        recurring: true,
        recurrence_pattern: JSON.stringify(pattern),
      });

      occurrenceCount++;
    }

    // Avançar para a próxima data baseado na frequência
    switch (pattern.frequency) {
      case 'daily':
        currentDate.setDate(currentDate.getDate() + pattern.interval);
        break;
      case 'weekly':
        currentDate.setDate(currentDate.getDate() + (pattern.interval * 7));
        break;
      case 'monthly':
        currentDate.setMonth(currentDate.getMonth() + pattern.interval);
        break;
    }
  }

  return classes;
} 