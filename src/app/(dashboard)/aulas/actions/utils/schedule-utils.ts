import type { ScheduleConflict } from "../../types";

/**
 * Verifica conflitos de agenda para múltiplas aulas
 */
export async function checkScheduleConflicts(
  supabase: any,
  classesToCheck: any[],
  tenantId: string
): Promise<ScheduleConflict[]> {
  const conflicts: ScheduleConflict[] = [];

  for (const classData of classesToCheck) {
    const startTime = new Date(classData.start_time);
    const endTime = new Date(classData.end_time);

    // Verificar conflitos do instrutor
    const { data: instructorConflicts } = await supabase
      .from("classes")
      .select("id, name, start_time, end_time")
      .eq("instructor_id", classData.instructor_id)
      .eq("tenant_id", tenantId)
      .in("status", ["scheduled", "ongoing"])
      .or(`and(start_time.lte.${endTime.toISOString()},end_time.gte.${startTime.toISOString()})`);

    if (instructorConflicts && instructorConflicts.length > 0) {
      conflicts.push({
        type: "instructor_conflict",
        message: `Instrutor já possui aula agendada em ${startTime.toLocaleString()}`,
        conflicting_class_id: instructorConflicts[0].id,
      });
    }
  }

  return conflicts;
} 