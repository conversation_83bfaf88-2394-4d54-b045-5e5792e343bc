'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Filter } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback } from 'react';

interface CalendarFiltersProps {
  instructors: Array<{
    id: string;
    full_name?: string;
    first_name?: string;
    last_name?: string;
  }>;
  branches: Array<{
    id: string;
    name: string;
  }>;
  groups: Array<{
    id: string;
    name: string;
  }>;
}

export function CalendarFilters({ instructors, branches, groups }: CalendarFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      if (value) {
        params.set(name, value);
      } else {
        params.delete(name);
      }
      return params.toString();
    },
    [searchParams]
  );

  const handleFilterChange = (key: string, value: string) => {
    router.push(`/aulas/calendario?${createQueryString(key, value)}`);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center space-x-2">
          <Filter className="h-5 w-5" />
          <span>Filtros</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-4">
          <div>
            <label className="text-sm font-medium text-foreground">Instrutor</label>
            <select 
              className="w-full mt-1 px-3 py-2 border border-input bg-background text-foreground rounded-md focus:ring-2 focus:ring-ring focus:border-ring"
              value={searchParams.get('instructor') || ''}
              onChange={(e) => handleFilterChange('instructor', e.target.value)}
            >
              <option value="">Todos os instrutores</option>
              {instructors.map((instructor) => (
                <option key={instructor.id} value={instructor.id}>
                  {instructor.full_name || `${instructor.first_name} ${instructor.last_name || ''}`.trim()}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="text-sm font-medium text-foreground">Filial</label>
            <select 
              className="w-full mt-1 px-3 py-2 border border-input bg-background text-foreground rounded-md focus:ring-2 focus:ring-ring focus:border-ring"
              value={searchParams.get('branch') || ''}
              onChange={(e) => handleFilterChange('branch', e.target.value)}
            >
              <option value="">Todas as filiais</option>
              {branches.map((branch) => (
                <option key={branch.id} value={branch.id}>
                  {branch.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="text-sm font-medium text-foreground">Turma</label>
            <select 
              className="w-full mt-1 px-3 py-2 border border-input bg-background text-foreground rounded-md focus:ring-2 focus:ring-ring focus:border-ring"
              value={searchParams.get('group') || ''}
              onChange={(e) => handleFilterChange('group', e.target.value)}
            >
              <option value="">Todas as turmas</option>
              {groups.map((group) => (
                <option key={group.id} value={group.id}>
                  {group.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="text-sm font-medium text-foreground">Status</label>
            <select 
              className="w-full mt-1 px-3 py-2 border border-input bg-background text-foreground rounded-md focus:ring-2 focus:ring-ring focus:border-ring"
              value={searchParams.get('status') || ''}
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="">Todos os status</option>
              <option value="scheduled">Agendada</option>
              <option value="ongoing">Em andamento</option>
              <option value="completed">Concluída</option>
              <option value="cancelled">Cancelada</option>
            </select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 