import { Suspense } from 'react';
import { Metadata } from 'next';
import { getClassGroups } from '../../turmas/actions/class-group';
import { getClasses, getInstructorsForForm, getBranchesForForm } from '../actions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, Users, MapPin, Filter, ChevronLeft, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import type { ClassWithDetails } from '../types';
import { cn } from '@/lib/utils';
import { CalendarFilters } from './components/calendar-filters';

// Helper functions for date/time formatting
const formatDate = (dateString: string | Date) => {
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  return date.toLocaleDateString('pt-BR');
};

const formatTime = (dateString: string | Date) => {
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
};

export const metadata: Metadata = {
  title: 'Calendário de Aulas | Apex SaaS',
  description: 'Visualize todas as aulas em formato de calendário',
};

interface PageProps {
  searchParams: Promise<{
    view?: 'month' | 'week' | 'day';
    date?: string;
    instructor?: string;
    branch?: string;
    group?: string;
    status?: string;
  }>;
}



async function CalendarView({ searchParams }: { searchParams: any }) {
  // Filtrar apenas propriedades string do searchParams
  const cleanSearchParams = Object.keys(searchParams).reduce((acc, key) => {
    const value = searchParams[key];
    if (typeof value === 'string' || typeof value === 'number') {
      acc[key] = value;
    }
    return acc;
  }, {} as Record<string, string | number>);
  
  const currentDate = cleanSearchParams.date ? new Date(cleanSearchParams.date as string) : new Date();
  const viewType = cleanSearchParams.view || 'month';

  // Calcular período baseado na visualização
  const getDateRange = () => {
    const start = new Date(currentDate);
    const end = new Date(currentDate);

    switch (viewType) {
      case 'day':
        start.setHours(0, 0, 0, 0);
        end.setHours(23, 59, 59, 999);
        break;
      case 'week':
        const startOfWeek = start.getDate() - start.getDay();
        start.setDate(startOfWeek);
        start.setHours(0, 0, 0, 0);
        end.setDate(startOfWeek + 6);
        end.setHours(23, 59, 59, 999);
        break;
      case 'month':
      default:
        start.setDate(1);
        start.setHours(0, 0, 0, 0);
        end.setMonth(end.getMonth() + 1);
        end.setDate(0);
        end.setHours(23, 59, 59, 999);
        break;
    }

    return {
      start_date: start.toISOString(),
      end_date: end.toISOString(),
      start,
      end
    };
  };

  const dateRange = getDateRange();
  
  const [classesResult, groupsResult, instructorsResult, branchesResult] = await Promise.all([
    getClasses({ 
      ...cleanSearchParams, 
      start_date: dateRange.start_date,
      end_date: dateRange.end_date,
      limit: 1000 // Para calendário, queremos todas as aulas do período
    }),
    getClassGroups({ limit: 1000 }),
    getInstructorsForForm(),
    getBranchesForForm()
  ]);

  // Extract data arrays with proper type guards
  const classes: ClassWithDetails[] = classesResult.success && Array.isArray(classesResult.data) 
    ? classesResult.data 
    : classesResult.success && classesResult.data?.data 
    ? classesResult.data.data 
    : [];

  const groups = groupsResult.success && Array.isArray(groupsResult.data) 
    ? groupsResult.data 
    : groupsResult.success && groupsResult.data?.data 
    ? groupsResult.data.data 
    : [];

  const instructors = instructorsResult.success ? instructorsResult.data || [] : [];
  const branches = branchesResult.success ? branchesResult.data || [] : [];

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'scheduled': { label: 'Agendada', variant: 'secondary' as const },
      'ongoing': { label: 'Em andamento', variant: 'default' as const },
      'completed': { label: 'Concluída', variant: 'outline' as const },
      'cancelled': { label: 'Cancelada', variant: 'destructive' as const },
      'rescheduled': { label: 'Reagendada', variant: 'secondary' as const }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'secondary' as const };
    return <Badge variant={config.variant} className="text-xs">{config.label}</Badge>;
  };

  const getClassTypeColor = (classType: string) => {
    const colors = {
      'regular': 'bg-blue-50 dark:bg-blue-950/50 border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300',
      'free': 'bg-green-50 dark:bg-green-950/50 border-green-200 dark:border-green-800 text-green-700 dark:text-green-300',
      'workshop': 'bg-purple-50 dark:bg-purple-950/50 border-purple-200 dark:border-purple-800 text-purple-700 dark:text-purple-300',
      'exam': 'bg-orange-50 dark:bg-orange-950/50 border-orange-200 dark:border-orange-800 text-orange-700 dark:text-orange-300'
    };
    return colors[classType as keyof typeof colors] || colors.regular;
  };

  // Agrupar aulas por data para visualização em calendário
  const classesByDate = classes.reduce((acc: Record<string, ClassWithDetails[]>, classItem: ClassWithDetails) => {
    const date = formatDate(classItem.start_time);
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(classItem);
    return acc;
  }, {});

  // Gerar navegação de datas
  const getNavigationDates = () => {
    const prev = new Date(currentDate);
    const next = new Date(currentDate);

    switch (viewType) {
      case 'day':
        prev.setDate(prev.getDate() - 1);
        next.setDate(next.getDate() + 1);
        break;
      case 'week':
        prev.setDate(prev.getDate() - 7);
        next.setDate(next.getDate() + 7);
        break;
      case 'month':
      default:
        prev.setMonth(prev.getMonth() - 1);
        next.setMonth(next.getMonth() + 1);
        break;
    }

    return { prev, next };
  };

  const { prev, next } = getNavigationDates();

  // Função para gerar grid do calendário mensal
  const generateMonthGrid = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    const firstDayOfMonth = new Date(year, month, 1);
    const lastDayOfMonth = new Date(year, month + 1, 0);
    const startDate = new Date(firstDayOfMonth);
    startDate.setDate(startDate.getDate() - firstDayOfMonth.getDay()); // Início da semana
    
    const days = [];
    const currentDay = new Date(startDate);
    
    // Gerar 42 dias (6 semanas x 7 dias)
    for (let i = 0; i < 42; i++) {
      const dateStr = formatDate(currentDay);
      const isCurrentMonth = currentDay.getMonth() === month;
      const isToday = formatDate(new Date()) === dateStr;
      const dayClasses = classesByDate[dateStr] || [];
      
      days.push({
        date: new Date(currentDay),
        dateStr,
        isCurrentMonth,
        isToday,
        classes: dayClasses,
        day: currentDay.getDate()
      });
      
      currentDay.setDate(currentDay.getDate() + 1);
    }
    
    return days;
  };

  const monthDays = viewType === 'month' ? generateMonthGrid() : [];

  return (
    <div className="space-y-6">
      {/* Header com navegação */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h1 className="text-2xl font-bold text-foreground">
            Calendário de Aulas
          </h1>
          <p className="text-muted-foreground">
            Visualize todas as aulas em formato de calendário
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Navegação de data */}
          <div className="flex items-center space-x-1">
            <Link href={`/aulas/calendario?${new URLSearchParams({
              ...cleanSearchParams,
              date: prev.toISOString().split('T')[0]
            } as Record<string, string>).toString()}`}>
              <Button variant="outline" size="sm">
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </Link>
            
            <Button variant="outline" size="sm" asChild>
              <Link href={`/aulas/calendario?${new URLSearchParams({
                ...cleanSearchParams,
                date: new Date().toISOString().split('T')[0]
              } as Record<string, string>).toString()}`}>
                Hoje
              </Link>
            </Button>
            
            <Link href={`/aulas/calendario?${new URLSearchParams({
              ...cleanSearchParams,
              date: next.toISOString().split('T')[0]
            } as Record<string, string>).toString()}`}>
              <Button variant="outline" size="sm">
                <ChevronRight className="h-4 w-4" />
              </Button>
            </Link>
          </div>
          
          {/* Toggle de visualização */}
          <div className="flex items-center space-x-1 border rounded-md p-1">
            <Link href={`/aulas/calendario?${new URLSearchParams({
              ...cleanSearchParams,
              view: 'day'
            } as Record<string, string>).toString()}`}>
              <Button variant={viewType === 'day' ? 'default' : 'ghost'} size="sm">
                Dia
              </Button>
            </Link>
            <Link href={`/aulas/calendario?${new URLSearchParams({
              ...cleanSearchParams,
              view: 'week'
            } as Record<string, string>).toString()}`}>
              <Button variant={viewType === 'week' ? 'default' : 'ghost'} size="sm">
                Semana
              </Button>
            </Link>
            <Link href={`/aulas/calendario?${new URLSearchParams({
              ...cleanSearchParams,
              view: 'month'
            } as Record<string, string>).toString()}`}>
              <Button variant={viewType === 'month' ? 'default' : 'ghost'} size="sm">
                Mês
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Título do período atual */}
      <div className="text-center">
        <h2 className="text-xl font-semibold text-foreground">
          {viewType === 'month' && currentDate.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })}
          {viewType === 'week' && `Semana de ${formatDate(dateRange.start)} a ${formatDate(dateRange.end)}`}
          {viewType === 'day' && currentDate.toLocaleDateString('pt-BR', { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' })}
        </h2>
      </div>

      {/* Filtros */}
      <CalendarFilters 
        instructors={instructors}
        branches={branches}
        groups={groups}
      />

      {/* Visualização do Calendário */}
      {viewType === 'month' && (
        <Card>
          <CardContent className="p-4">
            {/* Cabeçalho dos dias da semana */}
            <div className="grid grid-cols-7 gap-2 mb-4">
              {['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'].map(day => (
                <div key={day} className="text-center text-sm font-medium text-muted-foreground py-2">
                  {day}
                </div>
              ))}
            </div>
            
            {/* Grid do calendário */}
            <div className="grid grid-cols-7 gap-2">
              {monthDays.map((day, index) => (
                <div
                  key={index}
                  className={cn(
                    "min-h-24 p-2 border rounded-lg transition-colors",
                    day.isCurrentMonth 
                      ? 'bg-card border-border hover:bg-accent/50' 
                      : 'bg-muted/50 border-border/50',
                    day.isToday && 'ring-2 ring-primary'
                  )}
                >
                  <div className={cn(
                    "text-sm font-medium mb-1",
                    day.isCurrentMonth ? 'text-foreground' : 'text-muted-foreground',
                    day.isToday && 'text-primary font-semibold'
                  )}>
                    {day.day}
                  </div>
                  
                  <div className="space-y-1">
                    {day.classes.slice(0, 2).map((classItem: ClassWithDetails) => (
                      <Link
                        key={classItem.id}
                        href={`/presenca/${classItem.id}`}
                        className={cn(
                          "block text-xs p-1 rounded border-l-2 hover:opacity-80 transition-opacity",
                          getClassTypeColor(classItem.name || 'regular')
                        )}
                      >
                        <div className="font-medium truncate">
                          {classItem.class_group?.name || 'Aula Livre'}
                        </div>
                        <div className="text-xs opacity-75">
                          {formatTime(classItem.start_time)}
                        </div>
                      </Link>
                    ))}
                    {day.classes.length > 2 && (
                      <div className="text-xs text-muted-foreground text-center">
                        +{day.classes.length - 2} mais
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Visualização em Lista (para semana e dia) */}
      {(viewType === 'week' || viewType === 'day') && (
        <div className="space-y-4">
          {Object.entries(classesByDate).length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">
                  Nenhuma aula encontrada
                </h3>
                <p className="text-muted-foreground">
                  Não há aulas agendadas para este período.
                </p>
              </CardContent>
            </Card>
          ) : (
            Object.entries(classesByDate)
              .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
              .map(([date, dayClasses]) => (
                <Card key={date}>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      {new Date(date).toLocaleDateString('pt-BR', { 
                        weekday: 'long', 
                        day: 'numeric', 
                        month: 'long' 
                      })}
                    </CardTitle>
                    <CardDescription>
                      {(dayClasses as ClassWithDetails[]).length} aula(s) agendada(s)
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {(dayClasses as ClassWithDetails[])
                        .sort((a: ClassWithDetails, b: ClassWithDetails) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime())
                        .map((classItem: ClassWithDetails) => (
                          <Link
                            key={classItem.id}
                            href={`/presenca/${classItem.id}`}
                            className="block"
                          >
                            <div className={cn(
                              "p-4 rounded-lg border-l-4 hover:shadow-md transition-shadow",
                              getClassTypeColor(classItem.name || 'regular')
                            )}>
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-2">
                                    <h4 className="font-medium">
                                      {classItem.class_group?.name || 'Aula Livre'}
                                    </h4>
                                    {getStatusBadge(classItem.status)}
                                  </div>
                                  
                                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                    <div className="flex items-center space-x-1">
                                      <Clock className="h-3 w-3" />
                                      <span>
                                        {formatTime(classItem.start_time)} - {formatTime(classItem.end_time)}
                                      </span>
                                    </div>
                                    
                                    {classItem.instructor?.full_name && (
                                      <div className="flex items-center space-x-1">
                                        <Users className="h-3 w-3" />
                                        <span>{classItem.instructor.full_name}</span>
                                      </div>
                                    )}
                                    
                                    {classItem.branch?.name && (
                                      <div className="flex items-center space-x-1">
                                        <MapPin className="h-3 w-3" />
                                        <span>{classItem.branch.name}</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                
                                <div className="text-sm text-muted-foreground">
                                  {classItem._count?.attendance || 0}
                                  {classItem.class_group_id && classItem.max_capacity ? `/${classItem.max_capacity}` : ''} alunos
                                </div>
                              </div>
                            </div>
                          </Link>
                        ))}
                    </div>
                  </CardContent>
                </Card>
              ))
          )}
        </div>
      )}

      {/* Estatísticas rápidas */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{classes.length}</div>
              <div className="text-sm text-muted-foreground">Total de aulas</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {classes.filter((c: ClassWithDetails) => c.status === 'completed').length}
              </div>
              <div className="text-sm text-muted-foreground">Concluídas</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {classes.filter((c: ClassWithDetails) => c.status === 'scheduled').length}
              </div>
              <div className="text-sm text-muted-foreground">Agendadas</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {classes.filter((c: ClassWithDetails) => c.status === 'cancelled').length}
              </div>
              <div className="text-sm text-muted-foreground">Canceladas</div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default async function CalendarPage({ searchParams }: PageProps) {
  const resolvedSearchParams = await searchParams;

  return (
    <Suspense fallback={
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
        </div>
        <div className="h-96 bg-muted rounded animate-pulse"></div>
      </div>
    }>
      <CalendarView searchParams={resolvedSearchParams} />
    </Suspense>
  );
} 