'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  QrCode, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  ArrowLeft,
  User,
  BookOpen
} from 'lucide-react';
import { checkInByQR } from '../../../../presenca/actions/check-in-actions';
import { toast } from 'sonner';
import { PageHeader } from '../../../components/page-header';

// Helper para extrair mensagem de erro do Zod
const getErrorMessage = (error: any): string => {
  if (typeof error === 'string') return error;
  if (error?._errors?.[0]) return error._errors[0];
  return 'Erro desconhecido';
};

export default function QRCheckInPage() {
  const params = useParams();
  const router = useRouter();
  const qrCode = params.codigo as string;
  
  const [studentCode, setStudentCode] = useState('');
  const [notes, setNotes] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [checkInSuccess, setCheckInSuccess] = useState(false);
  const [successData, setSuccessData] = useState<any>(null);
  const [validationError, setValidationError] = useState<string>('');

  // Validar o formato do código QR na inicialização
  useEffect(() => {
    if (!qrCode) {
      setValidationError('Código QR não fornecido');
      return;
    }

    // Verificar se é um formato válido de QR Code (base64)
    try {
      if (qrCode.length < 10) {
        throw new Error('Código muito curto');
      }
      
      // Decodificar o URL encoding primeiro, se necessário
      const decodedQRCode = decodeURIComponent(qrCode);
      
      // Tentar decodificar o base64 para verificar se é válido
      const decoded = Buffer.from(decodedQRCode, 'base64').toString();
      const qrData = JSON.parse(decoded);
      
      if (!qrData.class_id || !qrData.expires_at) {
        throw new Error('Formato inválido');
      }

      // Verificar se não expirou
      const now = new Date();
      const expirationDate = new Date(qrData.expires_at);
      
      if (now > expirationDate) {
        setValidationError('Este QR Code expirou. Solicite um novo código ao instrutor.');
        return;
      }

      setValidationError('');
    } catch (error) {
      console.error('Erro ao validar QR Code:', error);
      setValidationError('Código QR inválido. Verifique o código e tente novamente.');
    }
  }, [qrCode]);

  const handleCheckIn = async () => {
    if (!studentCode.trim()) {
      toast.error('Por favor, digite seu código de check-in');
      return;
    }

    if (validationError) {
      toast.error('Código QR inválido ou expirado');
      return;
    }

    setIsProcessing(true);
    
    try {
      // Decodificar o URL encoding antes de enviar para o servidor
      const decodedQRCode = decodeURIComponent(qrCode);
      
      const result = await checkInByQR({
        qr_code: decodedQRCode,
        student_check_in_code: studentCode.trim(),
        notes: notes.trim() || null
      });

      if (result.success) {
        setCheckInSuccess(true);
        setSuccessData(result.data);
        toast.success('Check-in realizado com sucesso!');
      } else {
        // Tratar diferentes tipos de erro
        if (result.errors?.student_check_in_code) {
          const errorMsg = getErrorMessage(result.errors.student_check_in_code);
          
          // Verificar se é erro de check-in duplicado
          const isDuplicateCheckin = errorMsg.includes('já fez check-in') || 
                                   errorMsg.includes('já possui check-in') ||
                                   (result.errors as any)?._meta?.duplicate_checkin;
          
          if (isDuplicateCheckin) {
            const metadata = (result.errors as any)?._meta;
            const isSelfCheckIn = metadata?.is_self_checkin === true;
            const studentName = metadata?.student_name || 'O aluno';
            
            // Personalizar mensagem e estilo baseado em quem está fazendo o check-in
            if (isSelfCheckIn) {
              toast.info(errorMsg, {
                duration: 6000,
                description: 'Sua presença já foi registrada para esta aula'
              });
            } else {
              toast.warning(errorMsg, {
                duration: 6000,
                description: `${studentName} já tem presença confirmada`
              });
            }
            
            // Resetar campos após erro de duplicação
            setTimeout(() => {
              setStudentCode('');
              setNotes('');
            }, 2000);
          } else {
            toast.error(errorMsg);
          }
        } else if (result.errors?.qr_code) {
          const errorMsg = getErrorMessage(result.errors.qr_code);
          setValidationError(errorMsg);
          toast.error(errorMsg);
        } else {
          const errorMessage = (result.errors as any)?._form || 'Erro ao realizar check-in';
          toast.error(errorMessage);
        }
      }
    } catch (error) {
      toast.error('Erro inesperado ao processar check-in');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleNewCheckIn = () => {
    setCheckInSuccess(false);
    setSuccessData(null);
    setStudentCode('');
    setNotes('');
  };

  // Se houve sucesso no check-in, mostrar tela de confirmação
  if (checkInSuccess && successData) {
    return (
      <div className="space-y-6">
        <PageHeader
          title="Check-in Confirmado"
          description="Sua presença foi registrada com sucesso"
        />

        <div className="max-w-md mx-auto">
          <Card className="border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-800">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 w-16 h-16 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
              <CardTitle className="text-green-800 dark:text-green-200">
                Check-in Realizado!
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 text-center">
              <div className="space-y-2">
                <div className="flex items-center justify-center gap-2 text-sm text-green-700 dark:text-green-300">
                  <User className="h-4 w-4" />
                  <span className="font-medium">{successData.student?.name}</span>
                </div>
                <div className="flex items-center justify-center gap-2 text-sm text-green-700 dark:text-green-300">
                  <BookOpen className="h-4 w-4" />
                  <span>{successData.class?.name}</span>
                </div>
                <div className="flex items-center justify-center gap-2 text-xs text-green-600 dark:text-green-400">
                  <Clock className="h-3 w-3" />
                  <span>
                    {new Date().toLocaleString('pt-BR', {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </span>
                </div>
              </div>

              <div className="space-y-2 pt-4">
                <Button
                  onClick={handleNewCheckIn}
                  variant="outline"
                  className="w-full"
                >
                  Fazer Outro Check-in
                </Button>
                <Button
                  onClick={handleGoBack}
                  variant="ghost"
                  className="w-full"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Voltar
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Check-in via QR Code"
        description="Digite seu código de check-in para marcar sua presença na aula"
      />

      <div className="max-w-md mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <QrCode className="h-5 w-5" />
              Confirmar Presença
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Status do QR Code */}
            <div className="p-3 rounded-lg border bg-muted/50">
              <div className="flex items-center gap-2 text-sm">
                {validationError ? (
                  <>
                    <AlertCircle className="h-4 w-4 text-red-500" />
                    <span className="text-red-700 dark:text-red-300">
                      QR Code Inválido
                    </span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-green-700 dark:text-green-300">
                      QR Code Válido
                    </span>
                  </>
                )}
              </div>
              {validationError && (
                <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                  {validationError}
                </p>
              )}
            </div>

            {/* Formulário de check-in */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="student-code">
                  Seu Código de Check-in
                </Label>
                <Input
                  id="student-code"
                  placeholder="Digite seu código pessoal..."
                  value={studentCode}
                  onChange={(e) => setStudentCode(e.target.value)}
                  disabled={!!validationError}
                  className="text-center text-lg font-mono"
                />
                <p className="mt-1 text-xs text-muted-foreground">
                  Este código foi fornecido pela academia quando você se matriculou
                </p>
              </div>

              <div>
                <Label htmlFor="notes">
                  Observações (opcional)
                </Label>
                <Textarea
                  id="notes"
                  placeholder="Alguma observação sobre sua presença..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  maxLength={500}
                  disabled={!!validationError}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {notes.length}/500 caracteres
                </p>
              </div>

              <Button
                onClick={handleCheckIn}
                disabled={isProcessing || !studentCode.trim() || !!validationError}
                className="w-full"
                size="lg"
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Processando...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Confirmar Presença
                  </>
                )}
              </Button>

              <Button
                onClick={handleGoBack}
                variant="ghost"
                className="w-full"
                disabled={isProcessing}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Button>
            </div>

            {/* Instruções */}
            <div className="mt-6 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                Como fazer check-in:
              </h4>
              <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                <li>• Digite seu código de check-in pessoal</li>
                <li>• Adicione observações se necessário</li>
                <li>• Clique em "Confirmar Presença"</li>
                <li>• Aguarde a confirmação</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 