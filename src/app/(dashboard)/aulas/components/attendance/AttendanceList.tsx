'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { UserAvatar } from '@/components/ui/user-avatar';
import { BeltDisplay } from '@/components/belt';
import { Users, Clock, CheckCircle2, UserPlus, UserMinus, AlertTriangle } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { removeAttendance } from '../../../presenca/actions/attendance-actions';
import { checkInStudent } from '../../../presenca/actions/check-in-actions';
import { useUserRole } from '@/hooks/user/Permissions/useUserRole';
import { toast } from 'sonner';

interface Student {
  id: string;
  user: {
    first_name: string;
    last_name: string | null;
    full_name: string | null;
    email: string;
    avatar_url?: string | null;
  };
  check_in_code: string | null;
  current_belt?: {
    belt_color: string;
    degree: number;
  } | null;
}

interface AttendanceRecord {
  id: string;
  student_id: string;
  checked_in_at: string;
  notes: string | null;
  student: Student;
}

interface AttendanceListProps {
  classId: string;
  attendanceRecords: AttendanceRecord[];
  enrolledStudents: Student[];
  className?: string;
  canCheckIn?: boolean;
  onRefresh?: () => void;
}

export function AttendanceList({
  classId,
  attendanceRecords,
  enrolledStudents,
  className,
  canCheckIn = true,
  onRefresh
}: AttendanceListProps) {
  const [isCheckingIn, setIsCheckingIn] = useState(false);
  const [isRemovingAttendance, setIsRemovingAttendance] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [selectedAttendanceRecord, setSelectedAttendanceRecord] = useState<AttendanceRecord | null>(null);
  const [notes, setNotes] = useState('');
  const [removeReason, setRemoveReason] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);

  // Verificar permissões do usuário
  const { isAdmin, isInstructor } = useUserRole();
  const canRemoveAttendance = isAdmin || isInstructor;

  const presentStudentIds = new Set(attendanceRecords.map(record => record.student_id));
  const presentStudents = attendanceRecords.map(record => record.student);
  const absentStudents = enrolledStudents.filter(student => !presentStudentIds.has(student.id));

  // Filtrar estudantes baseado na busca
  const filteredPresentStudents = presentStudents.filter(student =>
    student.user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.user.first_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredAbsentStudents = absentStudents.filter(student =>
    student.user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.user.first_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCheckIn = async (student: Student) => {
    setIsCheckingIn(true);
    try {
      const result = await checkInStudent({
        class_id: classId,
        student_id: student.id,
        notes: notes.trim() || null,
      });

      if (result.success) {
        toast.success(`Check-in realizado para ${student.user.full_name || student.user.first_name}`);
        setSelectedStudent(null);
        setNotes('');
        setIsDialogOpen(false);
        onRefresh?.();
      } else {
        // Verificar se é erro de check-in duplicado
        const isDuplicateCheckin = result.errors && (
          (result.errors as any).student_id?.includes('já fez check-in') ||
          (result.errors as any).student_id?.includes('já possui check-in') ||
          (result.errors as any)._meta?.duplicate_checkin
        );

        if (isDuplicateCheckin) {
          const errorMessage = (result.errors as any)?._form || 
                             (result.errors as any)?.student_id || 
                             'Aluno já fez check-in nesta aula';
          
          // Usar toast informativo em vez de erro para check-in duplicado
          toast.info(errorMessage, {
            description: 'Cada aluno pode fazer check-in apenas uma vez por aula.',
            duration: 5000,
          });
          
          // Fechar o diálogo já que não é um erro crítico
          setSelectedStudent(null);
          setNotes('');
          setIsDialogOpen(false);
          onRefresh?.(); // Atualizar para mostrar o estado atual
        } else {
          const errorMessage = (result.errors as any)?._form || 'Erro ao fazer check-in';
          toast.error(errorMessage);
        }
      }
    } catch (error) {
      toast.error('Erro inesperado ao fazer check-in');
    } finally {
      setIsCheckingIn(false);
    }
  };

  const handleRemoveAttendance = async (attendanceRecord: AttendanceRecord) => {
    setIsRemovingAttendance(true);
    try {
      const result = await removeAttendance({
        attendance_id: attendanceRecord.id,
        reason: removeReason.trim() || null,
      });

      if (result.success) {
        const studentName = attendanceRecord.student.user.full_name || attendanceRecord.student.user.first_name;
        toast.success(`Presença removida para ${studentName}`);
        setSelectedAttendanceRecord(null);
        setRemoveReason('');
        setIsRemoveDialogOpen(false);
        onRefresh?.();
      } else {
        const errorMessage = (result.errors as any)?._form || 'Erro ao remover presença';
        toast.error(errorMessage);
      }
    } catch (error) {
      toast.error('Erro inesperado ao remover presença');
    } finally {
      setIsRemovingAttendance(false);
    }
  };

  const renderStudentCard = (student: Student, isPresent: boolean, attendanceRecord?: AttendanceRecord) => (
    <div key={student.id} className="flex items-center justify-between p-4 border border-border rounded-lg bg-card hover:bg-accent/20 transition-colors">
      <div className="flex items-center gap-4 flex-1 min-w-0">
        <UserAvatar 
          src={student.user.avatar_url} 
          name={student.user.full_name || student.user.first_name}
          size="lg"
          className="h-12 w-12 flex-shrink-0"
        />
        
        <div className="flex-1 min-w-0">
          <p className="font-medium text-foreground truncate">
            {student.user.full_name || student.user.first_name}
          </p>
          <p className="text-sm text-muted-foreground truncate">
            {student.user.email}
          </p>
          {student.current_belt && (
            <div className="mt-2">
              <BeltDisplay 
                belt={student.current_belt.belt_color as any}
                stripes={Math.max(0, (student.current_belt.degree || 1) - 1)}
                size="sm"
                showTranslation
              />
            </div>
          )}
        </div>
      </div>

      <div className="text-right flex-shrink-0">
        {isPresent && attendanceRecord ? (
          <div className="space-y-2">
            <div className="space-y-1">
              <Badge 
                variant="default" 
                className="bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50 border-green-200 dark:border-green-800"
              >
                Presente
              </Badge>
              <p className="text-xs text-muted-foreground">
                {(() => {
                  try {
                    const date = new Date(attendanceRecord.checked_in_at);
                    if (isNaN(date.getTime())) {
                      return '--:--';
                    }
                    return format(date, 'HH:mm', { locale: ptBR });
                  } catch (error) {
                    console.error('Erro ao formatar horário de presença:', error);
                    return '--:--';
                  }
                })()}
              </p>
              {attendanceRecord.notes && (
                <p className="text-xs text-muted-foreground italic max-w-32 truncate" title={attendanceRecord.notes}>
                  {attendanceRecord.notes}
                </p>
              )}
            </div>
            
            {/* Botão para remover presença - apenas para admins e instrutores e dentro do limite de tempo */}
            {canRemoveAttendance && canCheckIn && (
              <Dialog open={isRemoveDialogOpen && selectedAttendanceRecord?.id === attendanceRecord.id} onOpenChange={setIsRemoveDialogOpen}>
                <DialogTrigger asChild>
                  <Button 
                    size="sm" 
                    variant="outline"
                    className="w-full hover:bg-red-50 hover:text-red-600 hover:border-red-200 dark:hover:bg-red-900/20 dark:hover:text-red-400 dark:hover:border-red-800 transition-colors"
                    onClick={() => {
                      setSelectedAttendanceRecord(attendanceRecord);
                      setIsRemoveDialogOpen(true);
                    }}
                  >
                    <UserMinus className="h-4 w-4 mr-1" />
                    Remover
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle className="flex items-center gap-3">
                      <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-full">
                        <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
                      </div>
                      <div>
                        <p className="text-lg">Remover Presença</p>
                        <p className="text-sm font-normal text-muted-foreground">
                          {student.user.full_name || student.user.first_name}
                        </p>
                      </div>
                    </DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                      <p className="text-sm text-amber-800 dark:text-amber-200">
                        <strong>Atenção:</strong> Esta ação removerá permanentemente o registro de presença do aluno nesta aula.
                      </p>
                    </div>
                    
                    <div>
                      <Label htmlFor="removeReason" className="text-foreground">Motivo da remoção (opcional)</Label>
                      <Textarea
                        id="removeReason"
                        placeholder="Digite o motivo para remover a presença..."
                        value={removeReason}
                        onChange={(e) => setRemoveReason(e.target.value)}
                        maxLength={500}
                        className="mt-2"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        {removeReason.length}/500 caracteres
                      </p>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSelectedAttendanceRecord(null);
                          setRemoveReason('');
                          setIsRemoveDialogOpen(false);
                        }}
                        className="flex-1"
                      >
                        Cancelar
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={() => handleRemoveAttendance(attendanceRecord)}
                        disabled={isRemovingAttendance}
                        className="flex-1"
                      >
                        {isRemovingAttendance ? 'Removendo...' : 'Confirmar Remoção'}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>
        ) : (
          // Botão de Check-in só aparece se estiver dentro do limite de tempo
          canCheckIn ? (
            <Dialog open={isDialogOpen && selectedStudent?.id === student.id} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button 
                  size="sm" 
                  variant="outline"
                  className="hover:bg-primary hover:text-primary-foreground transition-colors"
                  onClick={() => {
                    setSelectedStudent(student);
                    setIsDialogOpen(true);
                  }}
                >
                  <UserPlus className="h-4 w-4 mr-1" />
                  Check-in
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-3">
                    <UserAvatar 
                      src={student.user.avatar_url} 
                      name={student.user.full_name || student.user.first_name}
                      size="md"
                    />
                    <span>{student.user.full_name || student.user.first_name}</span>
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="notes" className="text-foreground">Observações (opcional)</Label>
                    <Textarea
                      id="notes"
                      placeholder="Digite observações sobre a presença..."
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      maxLength={500}
                      className="mt-2"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      {notes.length}/500 caracteres
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => handleCheckIn(student)}
                      disabled={isCheckingIn}
                      className="flex-1"
                    >
                      {isCheckingIn ? 'Confirmando...' : 'Confirmar Check-in'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSelectedStudent(null);
                        setNotes('');
                        setIsDialogOpen(false);
                      }}
                    >
                      Cancelar
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          ) : (
            // Quando o check-in não está disponível, mostrar um botão desabilitado
            <Button 
              size="sm" 
              variant="outline"
              disabled
              className="opacity-50 cursor-not-allowed"
            >
              <UserPlus className="h-4 w-4 mr-1" />
              Check-in indisponível
            </Button>
          )
        )}
      </div>
    </div>
  );

  return (
    <div className={className}>
      {/* Header com estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card className="border-border bg-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-foreground">Total de Alunos</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{enrolledStudents.length}</div>
          </CardContent>
        </Card>

        <Card className="border-border bg-card border-l-4 border-l-green-500 dark:border-l-green-400">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-foreground">Presentes</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{presentStudents.length}</div>
          </CardContent>
        </Card>

        <Card className="border-border bg-card border-l-4 border-l-blue-500 dark:border-l-blue-400">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-foreground">Taxa de Presença</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {enrolledStudents.length > 0 
                ? `${Math.round((presentStudents.length / enrolledStudents.length) * 100)}%`
                : '0%'
              }
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Busca */}
      <div className="mb-6">
        <Input
          placeholder="Buscar aluno..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm bg-background border-border text-foreground placeholder:text-muted-foreground"
        />
      </div>

      {/* Lista de Presentes */}
      <Card className="mb-6 border-border bg-card">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2 text-foreground">
            <CheckCircle2 className="h-5 w-5 text-green-600 dark:text-green-400" />
            Alunos Presentes ({filteredPresentStudents.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredPresentStudents.length > 0 ? (
            <div className="space-y-3">
              {filteredPresentStudents.map(student => {
                const attendanceRecord = attendanceRecords.find(record => record.student_id === student.id);
                return (
                  <div key={student.id}>
                    {renderStudentCard(student, true, attendanceRecord)}
                  </div>
                );
              })}
            </div>
          ) : (
            <p className="text-muted-foreground text-center py-8">
              {searchTerm ? 'Nenhum aluno presente encontrado com este filtro' : 'Nenhum aluno fez check-in ainda'}
            </p>
          )}
        </CardContent>
      </Card>

      {/* Lista de Ausentes */}
      <Card className="border-border bg-card">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2 text-foreground">
            <Users className="h-5 w-5 text-muted-foreground" />
            Alunos Ausentes ({filteredAbsentStudents.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredAbsentStudents.length > 0 ? (
            <div className="space-y-3">
              {filteredAbsentStudents.map(student => renderStudentCard(student, false))}
            </div>
          ) : (
            <p className="text-muted-foreground text-center py-8">
              {searchTerm 
                ? 'Nenhum aluno ausente encontrado com este filtro'
                : presentStudents.length === enrolledStudents.length
                  ? 'Todos os alunos fizeram check-in! 🎉'
                  : 'Carregando...'
              }
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 