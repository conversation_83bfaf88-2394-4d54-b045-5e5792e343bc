'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { UserCheck, Loader2 } from 'lucide-react';
import { checkInStudent } from '../../../presenca/actions/check-in-actions';
import { toast } from 'sonner';

interface CheckInButtonProps {
  studentId: string;
  classId: string;
  studentName: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  disabled?: boolean;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function CheckInButton({
  studentId,
  classId,
  studentName,
  variant = 'default',
  size = 'default',
  disabled = false,
  onSuccess,
  onError
}: CheckInButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleCheckIn = async () => {
    setIsLoading(true);
    
    try {
      const result = await checkInStudent({
        student_id: studentId,
        class_id: classId
      });

      if (result.success) {
        toast.success(`Check-in realizado para ${studentName}`);
        onSuccess?.();
      } else {
        const errorMessage = (result.errors as any)?._form || 'Erro ao realizar check-in';
        toast.error(errorMessage);
        onError?.(errorMessage);
      }
    } catch (error) {
      const errorMessage = 'Erro inesperado ao realizar check-in';
      toast.error(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handleCheckIn}
      disabled={disabled || isLoading}
      variant={variant}
      size={size}
      className="min-w-[100px]"
    >
      {isLoading ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Confirmando...
        </>
      ) : (
        <>
          <UserCheck className="h-4 w-4 mr-2" />
          Check-in
        </>
      )}
    </Button>
  );
} 