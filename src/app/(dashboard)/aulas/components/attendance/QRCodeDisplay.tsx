'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  QrCode, 
  Copy, 
  Check, 
  RefreshCw, 
  Timer, 
  Eye, 
  EyeOff, 
  Share2,
  Download,
  ExternalLink
} from 'lucide-react';
import QRCodeLib from 'qrcode';
import { toast } from 'sonner';

interface QRCodeDisplayProps {
  qrCode: string;
  expiresAt: string;
  onRegenerateQR: () => void;
  isRegenerating?: boolean;
  className?: string;
}

export function QRCodeDisplay({ 
  qrCode, 
  expiresAt, 
  onRegenerateQR, 
  isRegenerating = false,
  className 
}: QRCodeDisplayProps) {
  const [qrDataUrl, setQrDataUrl] = useState<string>('');
  const [showQRCode, setShowQRCode] = useState(true);
  const [copied, setCopied] = useState(false);
  const [copiedLink, setCopiedLink] = useState(false);
  const [timeUntilExpiration, setTimeUntilExpiration] = useState<string>('');
  const [isExpired, setIsExpired] = useState(false);

  // Gerar QR code visual
  useEffect(() => {
    if (qrCode) {
      generateQRDataUrl(qrCode);
    }
  }, [qrCode]);

  // Atualizar contador de tempo para expiração
  useEffect(() => {
    if (!expiresAt) return;

    const updateTimer = () => {
      const now = new Date();
      const expiration = new Date(expiresAt);
      const diff = expiration.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeUntilExpiration('Expirado');
        setIsExpired(true);
        return;
      }

      setIsExpired(false);
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      if (hours > 0) {
        setTimeUntilExpiration(`${hours}h ${minutes}m`);
      } else if (minutes > 0) {
        setTimeUntilExpiration(`${minutes}m ${seconds}s`);
      } else {
        setTimeUntilExpiration(`${seconds}s`);
      }
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [expiresAt]);

  const generateQRDataUrl = async (code: string) => {
    try {
      const checkInUrl = `${window.location.origin}/aulas/checkin/qr/${encodeURIComponent(code)}`;
      const dataUrl = await QRCodeLib.toDataURL(checkInUrl, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'M'
      });
      setQrDataUrl(dataUrl);
    } catch (error) {
      console.error('Erro ao gerar QR Code visual:', error);
    }
  };

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(qrCode);
      setCopied(true);
      toast.success('Código copiado!');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Erro ao copiar código');
    }
  };

  const handleCopyLink = async () => {
    try {
      const checkInUrl = `${window.location.origin}/aulas/checkin/qr/${encodeURIComponent(qrCode)}`;
      await navigator.clipboard.writeText(checkInUrl);
      setCopiedLink(true);
      toast.success('Link copiado!');
      setTimeout(() => setCopiedLink(false), 2000);
    } catch (error) {
      toast.error('Erro ao copiar link');
    }
  };

  const handleDownloadQR = () => {
    if (!qrDataUrl) return;
    
    const link = document.createElement('a');
    link.download = `qr-code-aula-${new Date().toISOString().split('T')[0]}.png`;
    link.href = qrDataUrl;
    link.click();
    toast.success('QR Code baixado!');
  };

  const handleShareAPI = async () => {
    if (!navigator.share) {
      toast.error('Compartilhamento não suportado neste dispositivo');
      return;
    }

    try {
      const checkInUrl = `${window.location.origin}/aulas/checkin/qr/${encodeURIComponent(qrCode)}`;
      await navigator.share({
        title: 'Check-in da Aula',
        text: 'Faça seu check-in escaneando o QR Code ou acessando o link:',
        url: checkInUrl
      });
    } catch (error) {
      // Usuário cancelou o compartilhamento ou erro
      console.log('Compartilhamento cancelado ou erro:', error);
    }
  };

  const handleOpenLink = () => {
    const checkInUrl = `${window.location.origin}/aulas/checkin/qr/${encodeURIComponent(qrCode)}`;
    window.open(checkInUrl, '_blank');
  };

  const formatExpirationTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card className={`h-fit ${className || ''}`}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            QR Code da Aula
          </span>
          <div className="flex items-center gap-2">
            {isExpired ? (
              <Badge variant="destructive" className="text-xs">
                <Timer className="h-3 w-3 mr-1" />
                Expirado
              </Badge>
            ) : (
              <Badge 
                variant="outline" 
                className={`text-xs ${
                  timeUntilExpiration.includes('s') && !timeUntilExpiration.includes('m') 
                    ? 'border-red-300 text-red-700 dark:border-red-700 dark:text-red-300' 
                    : 'border-green-300 text-green-700 dark:border-green-700 dark:text-green-300'
                }`}
              >
                <Timer className="h-3 w-3 mr-1" />
                {timeUntilExpiration}
              </Badge>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Status Visual */}
        <div className={`p-3 rounded-lg border ${
          isExpired 
            ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800' 
            : 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
        }`}>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              isExpired ? 'bg-red-500' : 'bg-green-500 animate-pulse'
            }`} />
            <span className={`text-sm font-medium ${
              isExpired 
                ? 'text-red-800 dark:text-red-200' 
                : 'text-green-800 dark:text-green-200'
            }`}>
              {isExpired ? 'QR Code Expirado' : 'QR Code Ativo'}
            </span>
          </div>
          
          {isExpired && (
            <div className="mt-2">
              <Button
                size="sm"
                onClick={onRegenerateQR}
                disabled={isRegenerating}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isRegenerating ? (
                  <>
                    <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                    Gerando...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Gerar Novo QR Code
                  </>
                )}
              </Button>
            </div>
          )}
        </div>

        {/* QR Code Visual */}
        {!isExpired && (
          <>
            {showQRCode && qrDataUrl ? (
              <div className="text-center space-y-4">
                <div className="inline-block">
                  <img 
                    src={qrDataUrl} 
                    alt="QR Code da Aula" 
                    className="w-64 h-64 mx-auto rounded-lg shadow-lg border-2 border-border bg-white"
                  />
                </div>
                
                <div className="flex flex-col items-center gap-2">
                  <div className="text-xs text-muted-foreground">
                    Os alunos podem escanear este código ou acessar o link diretamente
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowQRCode(false)}
                    className="text-xs"
                  >
                    <EyeOff className="h-3 w-3 mr-1" />
                    Ocultar QR Code
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center">
                <Button
                  variant="outline"
                  onClick={() => setShowQRCode(true)}
                  className="mb-4"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Mostrar QR Code
                </Button>
              </div>
            )}

            <Separator />

            {/* Informações do QR Code */}
            <div className="space-y-3">
              <div>
                <Label className="text-xs text-muted-foreground">Código da Aula:</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    value={qrCode}
                    readOnly
                    className="font-mono text-xs"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyCode}
                    disabled={copied}
                  >
                    {copied ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div>
                <Label className="text-xs text-muted-foreground">Válido até:</Label>
                <p className="text-sm font-medium mt-1">
                  {formatExpirationTime(expiresAt)}
                </p>
              </div>
            </div>

            <Separator />

            {/* Ações */}
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                onClick={handleCopyLink}
                disabled={copiedLink}
                className="text-xs"
              >
                {copiedLink ? (
                  <Check className="h-3 w-3 mr-1 text-green-500" />
                ) : (
                  <Copy className="h-3 w-3 mr-1" />
                )}
                {copiedLink ? 'Copiado!' : 'Copiar Link'}
              </Button>

              <Button
                variant="outline"
                onClick={handleDownloadQR}
                disabled={!qrDataUrl}
                className="text-xs"
              >
                <Download className="h-3 w-3 mr-1" />
                Baixar QR
              </Button>

              <Button
                variant="outline"
                onClick={handleOpenLink}
                className="text-xs"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Abrir Link
              </Button>

              <Button
                variant="outline"
                onClick={handleShareAPI}
                className="text-xs"
              >
                <Share2 className="h-3 w-3 mr-1" />
                Compartilhar
              </Button>
            </div>

            {/* Ação de Renovação */}
            <div className="pt-2">
              <Button
                variant="secondary"
                onClick={onRegenerateQR}
                disabled={isRegenerating}
                className="w-full"
              >
                {isRegenerating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Gerando Novo QR Code...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Gerar Novo QR Code
                  </>
                )}
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
} 