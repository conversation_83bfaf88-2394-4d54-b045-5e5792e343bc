'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, CheckCircle, Clock, X, RefreshCw } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface QRStatusNotificationProps {
  isVisible: boolean;
  type: 'warning' | 'success' | 'info' | 'error';
  title: string;
  message: string;
  action?: {
    label: string;
    onClick: () => void;
    loading?: boolean;
  };
  autoHide?: boolean;
  duration?: number;
  onClose?: () => void;
}

export function QRStatusNotification({
  isVisible,
  type,
  title,
  message,
  action,
  autoHide = true,
  duration = 5000,
  onClose
}: QRStatusNotificationProps) {
  const [isClosing, setIsClosing] = useState(false);

  useEffect(() => {
    if (isVisible && autoHide) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [isVisible, autoHide, duration]);

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onClose?.();
      setIsClosing(false);
    }, 300);
  };

  const getIcon = () => {
    switch (type) {
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />;
      case 'info':
        return <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />;
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />;
      default:
        return null;
    }
  };

  const getColorClasses = () => {
    switch (type) {
      case 'warning':
        return 'border-amber-200 dark:border-amber-800 bg-amber-50 dark:bg-amber-900/20';
      case 'success':
        return 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20';
      case 'info':
        return 'border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20';
      case 'error':
        return 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20';
      default:
        return 'border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getTitleColor = () => {
    switch (type) {
      case 'warning':
        return 'text-amber-800 dark:text-amber-200';
      case 'success':
        return 'text-green-800 dark:text-green-200';
      case 'info':
        return 'text-blue-800 dark:text-blue-200';
      case 'error':
        return 'text-red-800 dark:text-red-200';
      default:
        return 'text-gray-800 dark:text-gray-200';
    }
  };

  const getMessageColor = () => {
    switch (type) {
      case 'warning':
        return 'text-amber-700 dark:text-amber-300';
      case 'success':
        return 'text-green-700 dark:text-green-300';
      case 'info':
        return 'text-blue-700 dark:text-blue-300';
      case 'error':
        return 'text-red-700 dark:text-red-300';
      default:
        return 'text-gray-700 dark:text-gray-300';
    }
  };

  return (
    <AnimatePresence>
      {isVisible && !isClosing && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className="fixed top-4 right-4 z-50 w-96 max-w-sm"
        >
          <Card className={`border-2 ${getColorClasses()} shadow-lg`}>
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 mt-0.5">
                  {getIcon()}
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className={`text-sm font-semibold ${getTitleColor()}`}>
                    {title}
                  </h4>
                  <p className={`text-sm mt-1 ${getMessageColor()}`}>
                    {message}
                  </p>
                  
                  {action && (
                    <div className="mt-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={action.onClick}
                        disabled={action.loading}
                        className="h-8 text-xs"
                      >
                        {action.loading ? (
                          <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                        ) : null}
                        {action.label}
                      </Button>
                    </div>
                  )}
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClose}
                  className="h-6 w-6 p-0 hover:bg-transparent"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Hook para controlar notificações de QR Code
export function useQRNotifications() {
  const [notifications, setNotifications] = useState<Array<{
    id: string;
    type: 'warning' | 'success' | 'info' | 'error';
    title: string;
    message: string;
    action?: {
      label: string;
      onClick: () => void;
      loading?: boolean;
    };
    autoHide?: boolean;
    duration?: number;
  }>>([]);

  const addNotification = (notification: Omit<typeof notifications[0], 'id'>) => {
    const id = crypto.randomUUID();
    setNotifications(prev => [...prev, { ...notification, id }]);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const clearAll = () => {
    setNotifications([]);
  };

  return {
    notifications,
    addNotification,
    removeNotification,
    clearAll
  };
}

// Componente para exibir múltiplas notificações
export function QRNotificationContainer() {
  const { notifications, removeNotification } = useQRNotifications();

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notifications.map((notification) => (
        <QRStatusNotification
          key={notification.id}
          isVisible={true}
          type={notification.type}
          title={notification.title}
          message={notification.message}
          action={notification.action}
          autoHide={notification.autoHide}
          duration={notification.duration}
          onClose={() => removeNotification(notification.id)}
        />
      ))}
    </div>
  );
} 