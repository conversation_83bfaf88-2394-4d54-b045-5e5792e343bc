'use client';

import { useEffect, useState, useRef } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { UserAvatar } from '@/components/ui/user-avatar';
import { BeltDisplay } from '@/components/belt';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Search, 
  Users, 
  Loader2, 
  UserCheck, 
  AlertCircle, 
  RefreshCw,
  ChevronRight
} from 'lucide-react';
import { useStudentSelection, type Student } from '../../hooks/useStudentSelection';
import { toast } from 'sonner';

interface StudentSelectionModalProps {
  // Propriedades da aula
  classId: string;
  classGroupId?: string | null;
  tenantId?: string;
  
  // Propriedades do modal
  open: boolean;
  onOpenChange: (open: boolean) => void;
  
  // Callback para seleção
  onStudentSelect: (student: Student) => void;
  
  // Customização
  title?: string;
  description?: string;
  className?: string;
  
  // Trigger button (opcional)
  children?: React.ReactNode;
}

export function StudentSelectionModal({
  classId,
  classGroupId,
  tenantId,
  open,
  onOpenChange,
  onStudentSelect,
  title,
  description,
  className,
  children
}: StudentSelectionModalProps) {
  const [searchInput, setSearchInput] = useState('');
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [hasLoaded, setHasLoaded] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const {
    students,
    pagination,
    searchTerm,
    error,
    isLoading,
    fetchStudents,
    searchStudents,
    loadNextPage,
    refresh,
    reset,
    hasNextPage,
    hasStudents,
    isEmpty,
    isGroupClass,
    isFreeClass
  } = useStudentSelection({
    classGroupId,
    tenantId,
    initialLimit: 10
  });

  // Buscar alunos quando o modal abrir (apenas uma vez)
  useEffect(() => {
    if (open && !hasLoaded && !isLoading && !error) {
      setHasLoaded(true);
      fetchStudents();
    }
  }, [open, hasLoaded, isLoading, error, fetchStudents]);

  // Limpar estado quando fechar o modal
  useEffect(() => {
    if (!open) {
      setSearchInput('');
      setSelectedStudent(null);
      setHasLoaded(false);
      
      // Limpar timeout se existir
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
        searchTimeoutRef.current = null;
      }
    }
  }, [open]);

  // Cleanup do timeout quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const handleSearch = (value: string) => {
    setSearchInput(value);
    
    // Limpar timeout anterior se existir
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Implementar debounce corretamente
    searchTimeoutRef.current = setTimeout(() => {
      searchStudents(value);
    }, 300);
  };

  const handleStudentSelect = (student: Student) => {
    setSelectedStudent(student);
    onStudentSelect(student);
    onOpenChange(false);
    toast.success(`Aluno selecionado: ${student.user.full_name || student.user.first_name}`);
  };

  const handleRetry = () => {
    setHasLoaded(false);
    refresh();
  };

  const handleClearSearch = () => {
    setSearchInput('');
    
    // Limpar timeout se existir
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }
    
    searchStudents('');
  };

  const renderStudentCard = (student: Student) => (
    <button
      key={student.id}
      onClick={() => handleStudentSelect(student)}
      className="w-full p-4 border border-border rounded-lg bg-card hover:bg-accent/50 transition-colors text-left group focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
    >
      <div className="flex items-center gap-4">
        <UserAvatar 
          src={student.user.avatar_url} 
          name={student.user.full_name || student.user.first_name}
          size="md"
          className="flex-shrink-0"
        />
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="font-medium text-foreground truncate">
              {student.user.full_name || student.user.first_name}
            </h4>
            {student.check_in_code && (
              <Badge variant="secondary" className="text-xs">
                {student.check_in_code}
              </Badge>
            )}
          </div>
          
          <p className="text-sm text-muted-foreground truncate mb-2">
            {student.user.email}
          </p>
          
          {student.current_belt && (
            <div className="flex items-center">
              <BeltDisplay 
                belt={student.current_belt.belt_color as any}
                stripes={Math.max(0, (student.current_belt.degree || 1) - 1)}
                size="sm"
                showTranslation
              />
            </div>
          )}
        </div>
        
        <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors flex-shrink-0" />
      </div>
    </button>
  );

  const renderEmptyState = () => {
    if (isLoading) {
      return (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-4" />
          <p className="text-muted-foreground">Carregando alunos...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <AlertCircle className="h-8 w-8 text-destructive mb-4" />
          <p className="text-destructive mb-4">{error}</p>
          <Button 
            onClick={handleRetry} 
            variant="outline" 
            size="sm"
            disabled={isLoading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Tentar novamente
          </Button>
        </div>
      );
    }

    if (isEmpty) {
      return (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Users className="h-8 w-8 text-muted-foreground mb-4" />
          <h3 className="font-medium text-foreground mb-2">
            {searchTerm ? 'Nenhum aluno encontrado' : 'Nenhum aluno disponível'}
          </h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm 
              ? `Não há alunos que correspondam à busca "${searchTerm}"`
              : isGroupClass 
                ? 'Não há alunos matriculados nesta turma'
                : 'Não há alunos ativos no sistema'
            }
          </p>
          {searchTerm && (
            <Button 
              onClick={handleClearSearch} 
              variant="outline" 
              size="sm"
            >
              Limpar busca
            </Button>
          )}
        </div>
      );
    }

    return null;
  };

  const modalTitle = title || (isGroupClass ? 'Selecionar Aluno da Turma' : 'Selecionar Aluno');
  const modalDescription = description || (
    isGroupClass 
      ? 'Escolha um aluno matriculado na turma para fazer check-in'
      : 'Escolha qualquer aluno ativo para fazer check-in'
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {children && (
        <DialogTrigger asChild>
          {children}
        </DialogTrigger>
      )}
      
      <DialogContent className={`sm:max-w-2xl max-h-[90vh] ${className || ''}`}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {modalTitle}
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            {modalDescription}
          </p>
        </DialogHeader>

        <div className="space-y-4">
          {/* Busca */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar aluno por nome ou email..."
              value={searchInput}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-9"
              disabled={isLoading}
            />
          </div>

          {/* Estatísticas */}
          {hasStudents && (
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>
                {pagination.total} aluno{pagination.total !== 1 ? 's' : ''} {isGroupClass ? 'matriculado' : 'ativo'}{pagination.total !== 1 ? 's' : ''}
              </span>
              {searchTerm && (
                <span>
                  Mostrando {students.length} resultado{students.length !== 1 ? 's' : ''}
                </span>
              )}
            </div>
          )}

          <Separator />

          {/* Lista de alunos */}
          <ScrollArea className="h-[400px] w-full">
            <div className="space-y-2">
              {hasStudents ? (
                <>
                  {students.map((student) => renderStudentCard(student))}
                  
                  {/* Botão para carregar mais */}
                  {hasNextPage && (
                    <div className="pt-4">
                      <Button
                        onClick={loadNextPage}
                        disabled={isLoading}
                        variant="outline"
                        className="w-full"
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Carregando...
                          </>
                        ) : (
                          <>
                            <UserCheck className="h-4 w-4 mr-2" />
                            Carregar mais alunos
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </>
              ) : (
                renderEmptyState()
              )}
            </div>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
} 