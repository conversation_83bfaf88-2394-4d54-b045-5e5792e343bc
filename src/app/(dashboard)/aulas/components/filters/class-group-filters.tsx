'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { X, Filter, Search } from 'lucide-react';
import { useState, useEffect, useCallback } from 'react';
import type { ClassGroupFilters } from '@/hooks/turmas/use-class-groups';
import { InstructorsFilter } from '../../../turmas/components/filters/instructors-filter';
import { useInstructorsWithClassGroups } from '@/hooks/turmas/use-instructors-with-class-groups';
import { useRolePermissions } from '@/hooks/user/Permissions/use-role-permissions';

interface ClassGroupFiltersProps {
  instructors?: Array<{ id: string; name: string }>;
  branches?: Array<{ id: string; name: string }>;
  filters: ClassGroupFilters;
  onFiltersChange: (filters: Partial<ClassGroupFilters>) => void;
  onClearFilters: () => void;
}

export function ClassGroupFilters({ 
  instructors = [], 
  branches = [],
  filters,
  onFiltersChange,
  onClearFilters
}: ClassGroupFiltersProps) {
  const { data: instructorsWithBelts = [] } = useInstructorsWithClassGroups();
  const { canAccessAllTurmas, userRole } = useRolePermissions();
  const [localSearch, setLocalSearch] = useState(filters.search || '');
  
  // Update local search when filters change externally
  useEffect(() => {
    setLocalSearch(filters.search || '');
  }, [filters.search]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const trimmedSearch = localSearch.trim();
      // Só aplica o filtro se realmente mudou
      if (trimmedSearch !== (filters.search || '')) {
        onFiltersChange({ search: trimmedSearch || undefined });
      }
    }, 500); // 500ms de debounce

    return () => clearTimeout(timeoutId);
  }, [localSearch, filters.search, onFiltersChange]);

  const categories = [
    { value: 'all', label: 'Todas as categorias' },
    { value: 'kids', label: 'Infantil' },
    { value: 'teens', label: 'Juvenil' },
    { value: 'adults', label: 'Adultos' },
    { value: 'seniors', label: 'Sênior' }
  ];

  const statusOptions = [
    { value: 'all', label: 'Todos os status' },
    { value: 'active', label: 'Ativo' },
    { value: 'inactive', label: 'Inativo' }
  ];

  const handleCategoryChange = (value: string) => {
    onFiltersChange({ 
      category: value === 'all' ? undefined : value as 'kids' | 'teens' | 'adults' | 'seniors'
    });
  };

  const handleInstructorChange = (value: string) => {
    onFiltersChange({ 
      instructor_id: value === 'all' ? undefined : value
    });
  };

  const handleBranchChange = (value: string) => {
    onFiltersChange({ 
      branch_id: value === 'all' ? undefined : value
    });
  };

  const handleStatusChange = (value: string) => {
    onFiltersChange({ 
      is_active: value === 'all' ? undefined : value === 'active'
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.search?.trim()) count++;
    if (filters.category) count++;
    // Só conta o filtro de instrutor se o usuário pode acessar todas as turmas
    if (filters.instructor_id && canAccessAllTurmas) count++;
    if (filters.branch_id) count++;
    if (filters.is_active !== undefined) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  const clearSearch = () => {
    setLocalSearch('');
    // onFiltersChange será chamado automaticamente pelo useEffect
  };

  const clearCategory = () => {
    onFiltersChange({ category: undefined });
  };

  const clearInstructor = () => {
    onFiltersChange({ instructor_id: undefined });
  };

  const clearBranch = () => {
    onFiltersChange({ branch_id: undefined });
  };

  const clearStatus = () => {
    onFiltersChange({ is_active: undefined });
  };

  // Determinar quantas colunas usar no grid baseado nas permissões
  const getGridColumns = () => {
    const baseColumns = 3; // categoria, filial, status
    const instructorColumn = canAccessAllTurmas ? 1 : 0;
    const totalColumns = baseColumns + instructorColumn;
    
    // Mapear para classes do Tailwind
    switch (totalColumns) {
      case 3: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
      case 4: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
      default: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
    }
  };

  return (
    <div className="space-y-4">
      {/* Campo de busca sem formulário - busca automática */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Buscar por nome do grupo..."
          value={localSearch}
          onChange={(e) => setLocalSearch(e.target.value)}
          className="pl-10"
        />
        {localSearch && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearSearch}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      <div className={`grid ${getGridColumns()} gap-3`}>
        <Select 
          value={filters.category || 'all'} 
          onValueChange={handleCategoryChange}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Categoria" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((cat) => (
              <SelectItem key={cat.value} value={cat.value}>
                {cat.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Filtro de instrutor: apenas visível para usuários que podem acessar todas as turmas */}
        {canAccessAllTurmas && (
          <InstructorsFilter
            selectedInstructorId={filters.instructor_id}
            onChange={(instructorId) => onFiltersChange({ instructor_id: instructorId })}
            className="w-full"
          />
        )}

        {branches.length > 0 && (
          <Select 
            value={filters.branch_id || 'all'} 
            onValueChange={handleBranchChange}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Filial" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas as filiais</SelectItem>
              {branches.map((branch) => (
                <SelectItem key={branch.id} value={branch.id}>
                  {branch.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        <Select 
          value={filters.is_active === undefined ? 'all' : filters.is_active ? 'active' : 'inactive'} 
          onValueChange={handleStatusChange}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            {statusOptions.map((statusOption) => (
              <SelectItem key={statusOption.value} value={statusOption.value}>
                {statusOption.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Botão de limpar filtros centralizado */}
        <div className="flex items-end">
          <Button
            variant="outline"
            onClick={onClearFilters}
            className="w-full"
            disabled={activeFiltersCount === 0}
          >
            <Filter className="h-4 w-4 mr-2" />
            Limpar{activeFiltersCount > 0 && ` (${activeFiltersCount})`}
          </Button>
        </div>
      </div>

      {/* Badges dos filtros ativos */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.search?.trim() && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Busca: "{filters.search}"
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={clearSearch}
              />
            </Badge>
          )}
          
          {filters.category && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {categories.find(cat => cat.value === filters.category)?.label}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={clearCategory}
              />
            </Badge>
          )}
          
          {/* Badge de instrutor: apenas visível para usuários que podem acessar todas as turmas */}
          {filters.instructor_id && canAccessAllTurmas && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Instrutor: {instructors.find(inst => inst.id === filters.instructor_id)?.name || 'Desconhecido'}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={clearInstructor}
              />
            </Badge>
          )}
          
          {filters.branch_id && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {branches.find(branch => branch.id === filters.branch_id)?.name}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={clearBranch}
              />
            </Badge>
          )}
          
          {filters.is_active !== undefined && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {filters.is_active ? 'Ativo' : 'Inativo'}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={clearStatus}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
} 