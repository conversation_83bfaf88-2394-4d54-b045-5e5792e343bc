import { ClassGroupsList } from './class-groups-list';
import { EmptyState } from './empty-state';
import { getClassGroups } from '../../../turmas/actions/class-group';
import { ClassGroupFilter, PaginationParams } from '../../types';

interface ClassGroupsListContainerProps {
  searchParams: {
    search?: string;
    instructor_id?: string;
    branch_id?: string;
    category?: string;
    is_active?: string;
    has_availability?: string;
    page?: string;
    limit?: string;
    sort_by?: string;
    sort_order?: string;
  };
}

export async function ClassGroupsListContainer({ 
  searchParams 
}: ClassGroupsListContainerProps) {
  const filters: ClassGroupFilter = {
    search: searchParams.search,
    instructor_id: searchParams.instructor_id,
    branch_id: searchParams.branch_id,
    category: searchParams.category as 'kids' | 'teens' | 'adults' | 'seniors' | undefined,
    ...(searchParams.is_active !== undefined && {
      is_active: searchParams.is_active === 'true'
    }),
    ...(searchParams.has_availability !== undefined && {
      has_availability: searchParams.has_availability === 'true'
    }),
  };

  const pagination: PaginationParams = {
    page: parseInt(searchParams.page || '1'),
    limit: parseInt(searchParams.limit || '10'),
    sort_by: searchParams.sort_by || 'name',
    sort_order: (searchParams.sort_order as 'asc' | 'desc') || 'asc',
  };

  const result = await getClassGroups(filters);

  if (!result.success) {
    throw new Error(result.errors?._form || 'Erro ao carregar grupos de aulas');
  }

  const data = result.data!;
  const hasFilters = Boolean(
    filters.search || 
    filters.instructor_id || 
    filters.branch_id || 
    filters.category ||
    searchParams.is_active !== undefined ||
    searchParams.has_availability !== undefined
  );

  if (data.data.length === 0) {
    return <EmptyState hasFilters={hasFilters} />;
  }

  return (
    <ClassGroupsList 
      classGroups={data.data}
      pagination={data.pagination}
      currentFilters={filters}
    />
  );
}