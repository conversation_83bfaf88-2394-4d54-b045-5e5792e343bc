'use client';

import { ClassGroupCard } from './class-group-card';
import { ClassGroupsTable } from './class-groups-table';
import { Pagination } from '../pagination/pagination';
import { ViewToggle, ViewMode } from './view-toggle';
import { RefreshIndicator } from './refresh-indicator';
import { ClassGroupWithDetails, ClassGroupFilter, PaginatedResult } from '../../types';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface ClassGroupsListProps {
  classGroups: ClassGroupWithDetails[];
  pagination: PaginatedResult<ClassGroupWithDetails>['pagination'];
  currentFilters: ClassGroupFilter;
}

export function ClassGroupsList({ 
  classGroups, 
  pagination, 
  currentFilters 
}: ClassGroupsListProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('card');
  const router = useRouter();

  const handleUpdate = () => {
    router.refresh();
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">
            Mostrando {classGroups.length} de {pagination.total} grupos de aulas
          </span>
          <RefreshIndicator />
        </div>
        
        <ViewToggle 
          mode={viewMode} 
          onModeChange={setViewMode} 
        />
      </div>

      {viewMode === 'card' ? (
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {classGroups.map((classGroup) => (
            <ClassGroupCard 
              key={classGroup.id} 
              classGroup={classGroup}
              onUpdate={handleUpdate} 
            />
          ))}
        </div>
      ) : (
        <ClassGroupsTable 
          groups={classGroups}
        />
      )}

      {pagination.totalPages > 1 && (
        <Pagination
          currentPage={pagination.page}
          totalPages={pagination.totalPages}
          hasNext={pagination.hasNext}
          hasPrev={pagination.hasPrev}
        />
      )}
    </div>
  );
} 