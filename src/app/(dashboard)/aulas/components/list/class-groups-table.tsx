'use client';

import { ClassGroupWithDetails } from '../../types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, 
  Eye, 
  Users, 
  Settings,
  Clock,
  Edit
} from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import Link from 'next/link';

interface ClassGroupsTableProps {
  groups: ClassGroupWithDetails[];
}

const categoryLabels: Record<string, string> = {
  'kids': 'Infantil',
  'teens': 'Juvenil', 
  'adults': 'Adultos',
  'seniors': 'Sênior'
};

const categoryColors: Record<string, string> = {
  'kids': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
  'teens': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
  'adults': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
  'seniors': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400'
};

export function ClassGroupsTable({ groups }: ClassGroupsTableProps) {
  const formatCapacity = (current: number = 0, max: number = 0) => {
    const percentage = max > 0 ? (current / max) * 100 : 0;
    return {
      text: `${current}/${max}`,
      percentage,
      isFull: current >= max
    };
  };

  const formatDateValue = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: ptBR });
    } catch {
      return 'Data inválida';
    }
  };

  return (
    <div className="rounded-md border">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="border-b bg-muted/50">
            <tr>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                Grupo
              </th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                Instrutor
              </th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                Categoria
              </th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                Capacidade
              </th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                Status
              </th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                Criado em
              </th>
              <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground">
                Ações
              </th>
            </tr>
          </thead>
          <tbody>
            {groups.map((group) => {
              const capacity = formatCapacity(
                group.current_enrollment_count || 0, 
                group.max_capacity || 0
              );
              const waitlistCount = group._count?.waitlist || 0;
              
              return (
                <tr key={group.id} className="border-b transition-colors hover:bg-muted/50">
                  <td className="p-4">
                    <div className="space-y-1">
                      <div className="font-medium">
                        <Link 
                          href={`/turmas/${group.id}`}
                          className="hover:text-primary transition-colors cursor-pointer"
                        >
                          {group.name}
                        </Link>
                      </div>
                      {group.description && (
                        <div className="text-sm text-muted-foreground line-clamp-2">
                          {group.description}
                        </div>
                      )}
                    </div>
                  </td>
                  
                  <td className="p-4">
                    <div className="space-y-1">
                      <div className="font-medium">
                        {group.instructor?.full_name || 
                         `${group.instructor?.first_name} ${group.instructor?.last_name || ''}`.trim() ||
                         'N/A'}
                      </div>
                      {group.branch?.name && (
                        <div className="text-sm text-muted-foreground">
                          {group.branch.name}
                        </div>
                      )}
                    </div>
                  </td>
                  
                  <td className="p-4">
                    <Badge 
                      variant="secondary"
                      className={group.category ? (categoryColors[group.category] || 'bg-muted text-muted-foreground') : 'bg-muted text-muted-foreground'}
                    >
                      {group.category ? (categoryLabels[group.category] || group.category) : 'Geral'}
                    </Badge>
                  </td>
                  
                  <td className="p-4">
                    <div className="space-y-2">
                      <div className="text-sm font-medium">
                        {capacity.text}
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all ${
                            capacity.isFull 
                              ? 'bg-red-500 dark:bg-red-400' 
                              : capacity.percentage > 80 
                                ? 'bg-yellow-500 dark:bg-yellow-400' 
                                : 'bg-green-500 dark:bg-green-400'
                          }`}
                          style={{ width: `${Math.min(capacity.percentage, 100)}%` }}
                        />
                      </div>
                      {waitlistCount > 0 && (
                        <div className="text-xs text-muted-foreground">
                          <Clock className="w-3 h-3 inline mr-1" />
                          {waitlistCount} em espera
                        </div>
                      )}
                    </div>
                  </td>
                  
                  <td className="p-4">
                    <Badge 
                      variant={group.is_active ? "default" : "secondary"}
                      className={group.is_active ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400" : "bg-muted text-muted-foreground"}
                    >
                      {group.is_active ? 'Ativo' : 'Inativo'}
                    </Badge>
                  </td>
                  
                  <td className="p-4 text-sm text-muted-foreground">
                    {group.created_at ? formatDateValue(group.created_at) : 'N/A'}
                  </td>
                  
                  <td className="p-4 text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem asChild>
                          <Link href={`/turmas/${group.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            Ver Detalhes
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/turmas/${group.id}/editar`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Editar Turma
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/turmas/${group.id}/alunos`}>
                            <Users className="mr-2 h-4 w-4" />
                            Gerenciar Matrículas
                          </Link>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
} 