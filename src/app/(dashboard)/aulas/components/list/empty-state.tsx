import { Button } from '@/components/ui/button';
import { CalendarDays, Plus } from 'lucide-react';
import Link from 'next/link';

interface EmptyStateProps {
  hasFilters?: boolean;
  onClearFilters?: () => void;
}

export function EmptyState({ hasFilters, onClearFilters }: EmptyStateProps) {
  if (hasFilters) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted">
          <CalendarDays className="h-6 w-6 text-muted-foreground" />
        </div>
        
        <h3 className="mt-4 text-lg font-semibold">
          Nenhuma turma encontrada
        </h3>
        
        <p className="mt-2 text-sm text-muted-foreground max-w-sm">
          Não encontramos nenhuma turma que corresponda aos filtros aplicados.
        </p>
        
        <div className="mt-6">
          <Button variant="outline" onClick={onClearFilters}>
            Limpar filtros
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted">
        <CalendarDays className="h-6 w-6 text-muted-foreground" />
      </div>
      
      <h3 className="mt-4 text-lg font-semibold">
        Nenhuma turma cadastrada
      </h3>
      
      <p className="mt-2 text-sm text-muted-foreground max-w-sm">
        Você ainda não criou nenhuma turma. 
        Comece criando sua primeira turma para organizar suas turmas.
      </p>
      
      <div className="mt-6">
        <Button asChild>
          <Link href="/turmas/nova">
            <Plus className="mr-2 h-4 w-4" />
            Criar primeira turma
          </Link>
        </Button>
      </div>
    </div>
  );
} 