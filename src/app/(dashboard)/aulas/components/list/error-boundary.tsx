'use client';

import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface ErrorBoundaryProps {
  error?: Error | null;
  onRetry?: () => void;
}

export function ErrorBoundary({ error, onRetry }: ErrorBoundaryProps) {
  const router = useRouter();

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      router.refresh();
    }
  };

  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
        <AlertTriangle className="h-6 w-6 text-red-600" />
      </div>
      
      <h3 className="mt-4 text-lg font-semibold text-red-900">
        Erro ao carregar grupos
      </h3>
      
      <p className="mt-2 text-sm text-red-700 max-w-sm">
        {error?.message || 'Ocorreu um erro inesperado ao carregar as turmas. Tente novamente.'}
      </p>
      
      <div className="mt-6 flex gap-3">
        <Button 
          variant="outline" 
          onClick={handleRetry}
          className="gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Tentar novamente
        </Button>
        
        <Button 
          variant="ghost"
          onClick={() => router.push('/dashboard')}
        >
          Voltar ao dashboard
        </Button>
      </div>
      
      {process.env.NODE_ENV === 'development' && error && (
        <details className="mt-6 max-w-lg">
          <summary className="cursor-pointer text-sm text-muted-foreground">
            Detalhes técnicos
          </summary>
          <pre className="mt-2 text-xs bg-muted p-3 rounded overflow-auto text-left">
            {error.stack}
          </pre>
        </details>
      )}
    </div>
  );
}