'use client';

import { Button } from '@/components/ui/button';
import { Grid3X3, List } from 'lucide-react';
import { cn } from '@/lib/utils';

export type ViewMode = 'card' | 'table';

interface ViewToggleProps {
  mode: ViewMode;
  onModeChange: (mode: ViewMode) => void;
  className?: string;
}

export function ViewToggle({ mode, onModeChange, className }: ViewToggleProps) {
  return (
    <div className={cn("flex items-center space-x-1 border rounded-md p-1", className)}>
      <Button
        variant={mode === 'card' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onModeChange('card')}
        className={cn(
          "h-8 w-8 p-0 transition-colors",
          mode === 'card' 
            ? "bg-primary text-primary-foreground" 
            : "hover:bg-muted"
        )}
        aria-label="Visualização em cards"
      >
        <Grid3X3 className="h-4 w-4" />
      </Button>
      
      <Button
        variant={mode === 'table' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onModeChange('table')}
        className={cn(
          "h-8 w-8 p-0 transition-colors",
          mode === 'table' 
            ? "bg-primary text-primary-foreground" 
            : "hover:bg-muted"
        )}
        aria-label="Visualização em tabela"
      >
        <List className="h-4 w-4" />
      </Button>
    </div>
  );
} 