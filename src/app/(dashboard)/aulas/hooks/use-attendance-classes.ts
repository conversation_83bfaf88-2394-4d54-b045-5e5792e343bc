'use client';

import { useQuery, useMutation, useQueryClient, keepPreviousData } from '@tanstack/react-query';
import { useToast } from '@/hooks/ui/use-toast';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { 
  getClassesWithAttendance, 
  getAttendanceStats,
  removeAttendance
} from '../../presenca/actions/attendance-actions';
import {
  checkInStudent,
  checkInByQR,
} from '../../presenca/actions/check-in-actions';
import type { 
  AttendanceListFilter,
  CheckInStudent,
  CheckInByQR,
  RemoveAttendance
} from '../actions/schemas/index';
import type { ClassWithDetails, PaginatedResult } from '../types';

// Helper para extrair mensagem de erro de forma segura
function getErrorMessage(errors: any, fallback: string): string {
  if (typeof errors === 'string') return errors;
  if (errors?._form) return errors._form;
  return fallback;
}

// Configurações de cache para diferentes tipos de dados
const CACHE_CONFIG = {
  // Classes com presença - cache por 2 minutos (dados frequentemente atualizados)
  CLASSES_WITH_ATTENDANCE: {
    staleTime: 2 * 60 * 1000, // 2 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos no cache
  },
  // Estatísticas de presença - cache por 5 minutos
  ATTENDANCE_STATS: {
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 15 * 60 * 1000, // 15 minutos no cache
  }
};

/**
 * Hook para buscar aulas com informações de presença (listagem da página presença)
 */
export function useAttendanceClasses(filters: AttendanceListFilter) {
  return useQuery({
    queryKey: CACHE_KEYS.PRESENCE.CLASSES_WITH_ATTENDANCE(filters),
    queryFn: async (): Promise<PaginatedResult<ClassWithDetails>> => {
      const result = await getClassesWithAttendance(filters);
      if (!result.success) {
        throw new Error(result.errors?._form || 'Erro ao carregar aulas');
      }
      return result.data!;
    },
    staleTime: CACHE_CONFIG.CLASSES_WITH_ATTENDANCE.staleTime,
    gcTime: CACHE_CONFIG.CLASSES_WITH_ATTENDANCE.gcTime,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // Manter dados anteriores durante refetch para melhor UX
    placeholderData: keepPreviousData,
    // Refetch quando a janela recebe foco (dados de presença mudam frequentemente)
    refetchOnWindowFocus: true,
    // Refetch a cada 3 minutos para manter dados atualizados
    refetchInterval: 3 * 60 * 1000,
  });
}

/**
 * Hook para buscar estatísticas de presença
 */
export function useAttendanceStats() {
  return useQuery({
    queryKey: CACHE_KEYS.PRESENCE.ATTENDANCE_STATS,
    queryFn: async () => {
      const result = await getAttendanceStats();
      if (!result.success) {
        throw new Error(result.errors?._form || 'Erro ao carregar estatísticas');
      }
      return result.data!;
    },
    staleTime: CACHE_CONFIG.ATTENDANCE_STATS.staleTime,
    gcTime: CACHE_CONFIG.ATTENDANCE_STATS.gcTime,
    retry: 2,
    refetchOnWindowFocus: true,
    refetchInterval: 5 * 60 * 1000, // Refetch a cada 5 minutos
  });
}

/**
 * Hook para mutations relacionadas à presença
 */
export function useAttendanceMutations() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Função para invalidar caches relacionados à presença
  const invalidateAttendanceCaches = () => {
    // Invalidar todas as queries de classes com presença
    queryClient.invalidateQueries({
      predicate: (query) => {
        const queryKey = query.queryKey;
        return Array.isArray(queryKey) && 
               queryKey[0] === 'presence-classes-with-attendance';
      }
    });
    
    // Invalidar estatísticas de presença
    queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.PRESENCE.ATTENDANCE_STATS
    });
    
    // Invalidar estatísticas gerais
    queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.CLASSES.STATS
    });
  };

  // Mutation para check-in manual
  const checkInMutation = useMutation({
    mutationFn: async (data: CheckInStudent) => {
      const result = await checkInStudent(data);
      if (!result.success) {
        throw new Error(getErrorMessage(result.errors, 'Erro ao fazer check-in'));
      }
      return result.data;
    },
    onSuccess: (data, variables) => {
      toast({
        title: "Check-in realizado",
        description: "Presença registrada com sucesso!",
      });
      invalidateAttendanceCaches();
    },
    onError: (error: Error) => {
      toast({
        title: "Erro no check-in",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Mutation para check-in via QR Code
  const checkInByQRMutation = useMutation({
    mutationFn: async (data: CheckInByQR) => {
      const result = await checkInByQR(data);
      if (!result.success) {
        throw new Error(getErrorMessage(result.errors, 'Erro ao fazer check-in via QR'));
      }
      return result.data;
    },
    onSuccess: (data) => {
      const studentName = data?.student?.name || 'Aluno';
      toast({
        title: "Check-in via QR realizado",
        description: `Presença de ${studentName} registrada com sucesso!`,
      });
      invalidateAttendanceCaches();
    },
    onError: (error: Error) => {
      toast({
        title: "Erro no check-in via QR",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Mutation para remover presença
  const removeAttendanceMutation = useMutation({
    mutationFn: async (data: RemoveAttendance) => {
      const result = await removeAttendance(data);
      if (!result.success) {
        throw new Error(getErrorMessage(result.errors, 'Erro ao remover presença'));
      }
      return result.data;
    },
    onSuccess: (data) => {
      const studentName = data?.removed_attendance?.student_name || 'Aluno';
      toast({
        title: "Presença removida",
        description: `Presença de ${studentName} foi removida com sucesso.`,
      });
      invalidateAttendanceCaches();
    },
    onError: (error: Error) => {
      toast({
        title: "Erro ao remover presença",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return {
    checkIn: checkInMutation,
    checkInByQR: checkInByQRMutation,
    removeAttendance: removeAttendanceMutation,
  };
}

/**
 * Hook para controle de cache das queries de presença
 */
export function useAttendanceCacheControl() {
  const queryClient = useQueryClient();

  const prefetchClasses = async (filters: AttendanceListFilter) => {
    await queryClient.prefetchQuery({
      queryKey: CACHE_KEYS.PRESENCE.CLASSES_WITH_ATTENDANCE(filters),
      queryFn: async () => {
        const result = await getClassesWithAttendance(filters);
        if (!result.success) {
          throw new Error(result.errors?._form || 'Erro ao carregar aulas');
        }
        return result.data!;
      },
      staleTime: CACHE_CONFIG.CLASSES_WITH_ATTENDANCE.staleTime,
    });
  };

  const invalidateAllAttendanceQueries = () => {
    queryClient.invalidateQueries({
      predicate: (query) => {
        const queryKey = query.queryKey;
        return Array.isArray(queryKey) && 
               (queryKey[0] === 'presence-classes-with-attendance' ||
                queryKey[0] === 'presence-attendance-stats');
      }
    });
  };

  const setClassesData = (filters: AttendanceListFilter, data: PaginatedResult<ClassWithDetails>) => {
    queryClient.setQueryData(
      CACHE_KEYS.PRESENCE.CLASSES_WITH_ATTENDANCE(filters),
      data
    );
  };

  const getClassesData = (filters: AttendanceListFilter): PaginatedResult<ClassWithDetails> | undefined => {
    return queryClient.getQueryData(CACHE_KEYS.PRESENCE.CLASSES_WITH_ATTENDANCE(filters));
  };

  return {
    prefetchClasses,
    invalidateAllAttendanceQueries,
    setClassesData,
    getClassesData,
  };
} 