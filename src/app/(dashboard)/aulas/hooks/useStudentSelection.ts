'use client';

import { useState, useCallback, useTransition, useMemo } from 'react';
import { getEnrolledStudents, getAllStudents } from '../actions/student-selection-actions';
import { toast } from 'sonner';

export interface Student {
  id: string;
  check_in_code: string | null;
  current_belt?: {
    belt_color: string;
    degree: number;
  } | null;
  user: {
    id: string;
    first_name: string;
    last_name: string | null;
    full_name: string | null;
    email: string;
    avatar_url?: string | null;
  };
}

export interface StudentSelectionResult {
  students: Student[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface UseStudentSelectionOptions {
  classGroupId?: string | null;
  tenantId?: string;
  initialLimit?: number;
}

export function useStudentSelection(options: UseStudentSelectionOptions = {}) {
  const { classGroupId, tenantId, initialLimit = 10 } = options;
  
  const [students, setStudents] = useState<Student[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: initialLimit,
    total: 0,
    totalPages: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, startTransition] = useTransition();

  // Memoizar os valores estáveis para evitar re-renders
  const stableOptions = useMemo(() => ({
    classGroupId,
    tenantId,
    limit: initialLimit
  }), [classGroupId, tenantId, initialLimit]);

  const fetchStudents = useCallback(async (page = 1, search = '', resetData = false) => {
    if (!stableOptions.classGroupId && !stableOptions.tenantId) {
      setError('Parâmetros insuficientes para buscar alunos');
      return;
    }

    startTransition(async () => {
      try {
        setError(null);

        let result;

        if (stableOptions.classGroupId) {
          // Buscar alunos matriculados na turma
          result = await getEnrolledStudents({
            class_group_id: stableOptions.classGroupId,
            page,
            limit: stableOptions.limit,
            search: search.trim() || undefined
          });
        } else if (stableOptions.tenantId) {
          // Buscar todos os alunos ativos (aulas livres)
          result = await getAllStudents({
            page,
            limit: stableOptions.limit,
            search: search.trim() || undefined,
            tenant_id: stableOptions.tenantId
          });
        } else {
          throw new Error('Parâmetros insuficientes');
        }

        if (result.success && result.data) {
          // Transformar dados do Supabase para a estrutura esperada
          const studentsData: Student[] = result.data.students.map((student: any) => ({
            id: student.id,
            check_in_code: student.check_in_code,
            current_belt: Array.isArray(student.current_belt) && student.current_belt.length > 0 
              ? student.current_belt[0] 
              : student.current_belt,
            user: Array.isArray(student.user) && student.user.length > 0 
              ? student.user[0] 
              : student.user
          }));

          if (resetData || page === 1) {
            setStudents(studentsData);
          } else {
            // Adicionar à lista existente (carregamento de mais itens)
            setStudents(prev => [...prev, ...studentsData]);
          }
          
          setPagination({
            ...result.data.pagination,
            totalPages: Math.ceil(result.data.pagination.total / result.data.pagination.limit)
          });
        } else {
          const errorMessage = typeof result.errors === 'object' && result.errors && '_form' in result.errors
            ? (result.errors as any)._form 
            : 'Erro ao buscar alunos';
          setError(errorMessage);
          toast.error(errorMessage);
        }
      } catch (error) {
        const errorMessage = 'Erro inesperado ao buscar alunos';
        setError(errorMessage);
        toast.error(errorMessage);
        console.error('Erro ao buscar alunos:', error);
      }
    });
  }, [stableOptions]);

  const searchStudents = useCallback((searchValue: string) => {
    setSearchTerm(searchValue);
    fetchStudents(1, searchValue, true);
  }, [fetchStudents]);

  const loadNextPage = useCallback(() => {
    if (pagination.page < pagination.totalPages && !isLoading) {
      fetchStudents(pagination.page + 1, searchTerm, false);
    }
  }, [pagination.page, pagination.totalPages, isLoading, searchTerm, fetchStudents]);

  const refresh = useCallback(() => {
    fetchStudents(1, searchTerm, true);
  }, [fetchStudents, searchTerm]);

  const reset = useCallback(() => {
    setStudents([]);
    setPagination({
      page: 1,
      limit: initialLimit,
      total: 0,
      totalPages: 0
    });
    setSearchTerm('');
    setError(null);
  }, [initialLimit]);

  return {
    // Estados
    students,
    pagination,
    searchTerm,
    error,
    isLoading,
    
    // Ações - Usar fetchStudents diretamente para evitar loop infinito
    fetchStudents: useCallback(() => fetchStudents(1, searchTerm, true), [fetchStudents, searchTerm]),
    searchStudents,
    loadNextPage,
    refresh,
    reset,
    
    // Helpers
    hasNextPage: pagination.page < pagination.totalPages,
    hasStudents: students.length > 0,
    isEmpty: !isLoading && students.length === 0 && !error,
    isGroupClass: !!stableOptions.classGroupId,
    isFreeClass: !stableOptions.classGroupId && !!stableOptions.tenantId
  };
} 