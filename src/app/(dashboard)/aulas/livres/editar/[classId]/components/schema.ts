import { z } from 'zod';

export const editFreeClassSchema = z.object({
  name: z
    .string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres'),
  description: z
    .string()
    .max(500, 'Descrição deve ter no máximo 500 caracteres')
    .nullish()
    .transform(val => val || undefined),
  instructor_id: z
    .string()
    .min(1, 'Instrutor é obrigatório'),
  branch_id: z
    .string()
    .min(1, 'Filial é obrigatória'),
  start_time: z
    .string()
    .min(1, 'Data e hora de início são obrigatórias'),
  end_time: z
    .string()
    .min(1, 'Data e hora de fim são obrigatórias'),
  max_capacity: z.union([
    z.string().transform((val) => {
      if (!val || val.trim() === '') return undefined;
      const num = parseInt(val, 10);
      return isNaN(num) ? undefined : num;
    }),
    z.number(),
    z.undefined()
  ]).refine((val) => val === undefined || (typeof val === 'number' && val >= 1 && val <= 100), {
    message: 'Capacidade deve ser entre 1 e 100 alunos',
  }).optional(),
  notes: z
    .string()
    .max(1000, 'Observações devem ter no máximo 1000 caracteres')
    .nullish()
    .transform(val => val || undefined),
}).refine((data) => {
  if (!data.start_time || !data.end_time) return true; // Não validar se os campos estão vazios
  
  try {
    const startTime = new Date(data.start_time);
    const endTime = new Date(data.end_time);
    
    // Verificar se ambas as datas são válidas
    if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
      return false;
    }
    
    return endTime > startTime;
  } catch (error) {
    return false;
  }
}, {
  message: 'Horário de fim deve ser posterior ao horário de início',
  path: ['end_time'],
});

export type EditFreeClassFormData = z.infer<typeof editFreeClassSchema>; 