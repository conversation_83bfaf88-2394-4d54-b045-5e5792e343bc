'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, ChevronLeft, RefreshCw } from 'lucide-react';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Log do erro para debugging
    console.error('Erro na edição de aula livre:', error);
  }, [error]);

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-md mx-auto">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
              <AlertCircle className="h-6 w-6 text-destructive" />
            </div>
            <CardTitle className="text-xl text-destructive">Erro ao Carregar Aula</CardTitle>
            <CardDescription>
              Ocorreu um erro ao tentar carregar as informações da aula livre para edição.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              <p className="font-medium mb-2">Detalhes do erro:</p>
              <div className="p-3 bg-muted rounded-lg border">
                <code className="text-xs break-all">
                  {error.message || 'Erro interno do servidor'}
                </code>
              </div>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Você pode tentar recarregar a página ou voltar às aulas livres.
              </p>
            </div>
            
            <div className="flex flex-col gap-2">
              <Button onClick={reset} className="w-full">
                <RefreshCw className="h-4 w-4 mr-2" />
                Tentar Novamente
              </Button>
              <Button variant="outline" asChild className="w-full">
                <Link href="/aulas/livres">
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Voltar às Aulas Livres
                </Link>
              </Button>
              <Button variant="ghost" asChild className="w-full">
                <Link href="/aulas">
                  Ver Todas as Aulas
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 