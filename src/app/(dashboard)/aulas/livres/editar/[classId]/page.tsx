import { Suspense } from 'react';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getClassById, getInstructorsForForm, getBranchesForForm } from '../../../actions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, Calendar } from 'lucide-react';
import Link from 'next/link';
import { EditFreeClassForm } from './components/EditFreeClassForm';
import { checkAdminPermission, checkResourceAccess } from '@/services/permissions/utils/role-verification';

interface EditFreeClassPageProps {
  params: Promise<{
    classId: string;
  }>;
}

export async function generateMetadata({ params }: EditFreeClassPageProps): Promise<Metadata> {
  const { classId } = await params;
  return {
    title: 'Editar Aula Livre | Apex SaaS',
    description: 'Editar uma aula livre existente',
  };
}

async function EditFreeClassPageContent({ params }: EditFreeClassPageProps) {
  const { classId } = await params;

  // Verificar permissões de admin primeiro
  const userContext = await checkAdminPermission();

  // Carregar dados da aula e formulários em paralelo
  const [classResult, instructorsResult, branchesResult] = await Promise.all([
    getClassById(classId),
    getInstructorsForForm(),
    getBranchesForForm()
  ]);

  // Verificar se a aula existe
  if (!classResult.success || !classResult.data) {
    notFound();
  }

  const classData = classResult.data;

  // Verificar se é uma aula livre (não vinculada a turma)
  if (classData.class_group_id !== null) {
    notFound();
  }

  // Verificar se o usuário pode acessar este recurso específico
  const hasAccess = await checkResourceAccess(classId, 'class', userContext);
  if (!hasAccess) {
    notFound();
  }

  const instructors = instructorsResult.success 
    ? (instructorsResult.data || []).map(instructor => ({
        id: instructor.id,
        name: instructor.full_name || `${instructor.first_name} ${instructor.last_name || ''}`.trim()
      }))
    : [];
  const branches = branchesResult.success ? branchesResult.data || [] : [];

  return (
    <div className="space-y-6">
      {/* Breadcrumb e Header */}
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link href="/aulas" className="hover:text-foreground transition-colors">Aulas</Link>
        <span>/</span>
        <Link href="/aulas/livres" className="hover:text-foreground transition-colors">Aulas Livres</Link>
        <span>/</span>
        <span className="text-foreground">Editar Aula</span>
      </div>

      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <Button variant="outline" asChild>
          <Link href="/aulas/livres">
            <ChevronLeft className="h-4 w-4 mr-2" />
            Voltar às Aulas Livres
          </Link>
        </Button>
      </div>

      {/* Formulário */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Editar Detalhes da Aula Livre</span>
          </CardTitle>
          <CardDescription>
            Modifique as informações da aula livre. As alterações serão aplicadas imediatamente.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EditFreeClassForm
            classId={classId}
            classData={classData}
            instructors={instructors}
            branches={branches}
            redirectPath="/aulas/livres"
          />
        </CardContent>
      </Card>

      {/* Informações sobre Edição */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Importante sobre Edição de Aulas</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium text-foreground mb-2">Restrições</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Aulas concluídas não podem ser editadas</li>
                <li>• Aulas com presença registrada têm edição limitada</li>
                <li>• Mudanças de horário podem afetar inscrições</li>
                <li>• Redução de capacidade pode remover alunos da lista</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-foreground mb-2">Notificações</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Alunos inscritos são notificados sobre mudanças</li>
                <li>• Mudanças significativas requerem confirmação</li>
                <li>• Histórico de alterações é mantido</li>
                <li>• Instrutor é informado sobre as modificações</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default async function EditFreeClassPage({ params }: EditFreeClassPageProps) {
  return (
    <Suspense fallback={
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
        </div>
        <div className="h-96 bg-muted rounded animate-pulse"></div>
      </div>
    }>
      <EditFreeClassPageContent params={params} />
    </Suspense>
  );
}