import { Suspense } from 'react';
import { Metadata } from 'next';
import { getInstructorsForForm, getBranchesForForm } from '../../actions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, Calendar } from 'lucide-react';
import Link from 'next/link';
import { ClassForm } from '../../components/classes/ClassForm';
import { checkAdminPermission } from '@/services/permissions/utils/role-verification';

export const metadata: Metadata = {
  title: 'Nova Aula Livre | Apex SaaS',
  description: 'Criar uma nova aula livre não vinculada a turmas específicas',
};

async function NewFreeClassForm() {
  // Verificar permissões de admin antes de carregar dados
  await checkAdminPermission();

  const [instructorsResult, branchesResult] = await Promise.all([
    getInstructorsForForm(),
    getBranchesForForm()
  ]);

  const instructors = instructorsResult.success 
    ? (instructorsResult.data || []).map(instructor => ({
        id: instructor.id,
        name: instructor.full_name || `${instructor.first_name} ${instructor.last_name || ''}`.trim()
      }))
    : [];
  const branches = branchesResult.success ? branchesResult.data || [] : [];

  return (
    <div className="space-y-6">
      {/* Breadcrumb e Header */}
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link href="/aulas" className="hover:text-foreground transition-colors">Aulas</Link>
        <span>/</span>
        <Link href="/aulas/livres" className="hover:text-foreground transition-colors">Aulas Livres</Link>
        <span>/</span>
        <span className="text-foreground">Nova Aula</span>
      </div>

      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h1 className="text-2xl font-bold text-foreground">
            Nova Aula Livre
          </h1>
          <p className="text-muted-foreground">
            Crie uma aula não vinculada a turmas específicas
          </p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/aulas/livres">
            <ChevronLeft className="h-4 w-4 mr-2" />
            Voltar às Aulas Livres
          </Link>
        </Button>
      </div>

      {/* Formulário */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Detalhes da Aula Livre</span>
          </CardTitle>
          <CardDescription>
            Preencha as informações da nova aula livre. Esta aula estará disponível para inscrições avulsas.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ClassForm
            mode="create"
            classType="free"
            instructors={instructors}
            branches={branches}
            redirectPath="/aulas/livres"
          />
        </CardContent>
      </Card>

      {/* Informações sobre Aulas Livres */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Sobre Aulas Livres</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium text-foreground mb-2">Características</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Não vinculada a turmas específicas</li>
                <li>• Inscrições abertas a todos os alunos</li>
                <li>• Capacidade limitada configurável</li>
                <li>• Sistema de lista de espera opcional</li>
                <li>• Check-in manual ou por QR Code</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-foreground mb-2">Tipos Suportados</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• <strong>Aula Livre:</strong> Prática regular aberta</li>
                <li>• <strong>Workshop:</strong> Aula temática específica</li>
                <li>• <strong>Seminário:</strong> Evento educacional</li>
                <li>• <strong>Exame:</strong> Avaliação de graduação</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function NewFreeClassPage() {
  return (
    <Suspense fallback={
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
        </div>
        <div className="h-96 bg-muted rounded animate-pulse"></div>
      </div>
    }>
      <NewFreeClassForm />
    </Suspense>
  );
} 