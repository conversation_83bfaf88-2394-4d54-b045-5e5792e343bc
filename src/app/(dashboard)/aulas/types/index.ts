import { Tables, TablesInsert, TablesUpdate } from '@/services/supabase/types/database.types';

// Tipos base das tabelas
export type ClassGroup = Tables<'class_groups'>;
export type ClassGroupInsert = TablesInsert<'class_groups'>;
export type ClassGroupUpdate = TablesUpdate<'class_groups'>;

export type ClassGroupEnrollment = Tables<'class_group_enrollments'>;
export type ClassGroupEnrollmentInsert = TablesInsert<'class_group_enrollments'>;
export type ClassGroupEnrollmentUpdate = TablesUpdate<'class_group_enrollments'>;

export type ClassWaitlist = Tables<'class_waitlist'>;
export type ClassWaitlistInsert = TablesInsert<'class_waitlist'>;
export type ClassWaitlistUpdate = TablesUpdate<'class_waitlist'>;

export type Class = Tables<'classes'>;
export type ClassInsert = TablesInsert<'classes'>;
export type ClassUpdate = TablesUpdate<'classes'>;

// Tipos estendidos com relacionamentos para visualização
export type ClassGroupWithDetails = ClassGroup & {
  instructor: {
    id: string;
    first_name: string;
    last_name: string | null;
    full_name: string | null;
    avatar_url: string | null;
  };
  branch: {
    id: string;
    name: string;
  };
  _count: {
    enrollments: number;
    waitlist: number;
    classes: number;
  };
  current_enrollment_count?: number;
  capacity_usage_percentage?: number | null;
  allow_waitlist?: boolean;
};

export type ClassGroupEnrollmentWithDetails = ClassGroupEnrollment & {
  student: {
    id: string;
    user: {
      first_name: string;
      last_name: string | null;
      full_name: string | null;
      email: string;
      phone: string | null;
    };
    current_belt?: {
      belt_color: string;
      degree: number;
    };
    birth_date: string | null;
  };
  class_group: {
    id: string;
    name: string;
    category: string | null;
  };
};

export type ClassWaitlistWithDetails = ClassWaitlist & {
  student: {
    id: string;
    user: {
      first_name: string;
      last_name: string | null;
      full_name: string | null;
      email: string;
      phone: string | null;
    };
    current_belt?: {
      belt_color: string;
      degree: number;
    };
  };
  class_group: {
    id: string;
    name: string;
    max_capacity: number | null;
    current_enrollment_count: number;
  };
};

export type ClassWithDetails = Class & {
  instructor: {
    id: string;
    first_name: string;
    last_name: string | null;
    full_name: string | null;
    avatar_url: string | null;
  };
  branch: {
    id: string;
    name: string;
  };
  class_group?: {
    id: string;
    name: string;
    category: string | null;
  };
  _count: {
    attendance: number;
  };
  attendance_rate?: number;
  class_type?: 'regular' | 'free' | 'workshop' | 'exam';
};

// Tipos para estatísticas e métricas
export interface ClassGroupStatistics {
  total_groups: number;
  active_groups: number;
  inactive_groups: number;
  total_enrollments: number;
  total_waitlist: number;
  average_capacity_usage: number;
  groups_by_category: Record<string, number>;
  groups_by_instructor: Array<{
    instructor_id: string;
    instructor_name: string;
    group_count: number;
    total_enrollments: number;
  }>;
}

export interface ClassStatistics {
  total_classes: number;
  classes_this_week: number;
  classes_this_month: number;
  completed_classes: number;
  cancelled_classes: number;
  scheduled_classes: number;
  average_attendance_rate: number;
  classes_by_status: Record<string, number>;
  classes_by_day_of_week: Record<string, number>;
  busiest_time_slots: Array<{
    time_slot: string;
    class_count: number;
  }>;
}

export interface EnrollmentAnalytics {
  total_active_enrollments: number;
  total_inactive_enrollments: number;
  enrollment_trends: Array<{
    date: string;
    enrollments: number;
    cancellations: number;
  }>;
  popular_class_groups: Array<{
    class_group_id: string;
    class_group_name: string;
    enrollment_count: number;
    waitlist_count: number;
    capacity_usage: number;
  }>;
}

// Tipos para filtros e parâmetros de busca
export interface ClassGroupFilter {
  search?: string;
  instructor_id?: string;
  branch_id?: string;
  category?: 'kids' | 'teens' | 'adults' | 'seniors';
  is_active?: boolean;
  min_belt_level?: string;
  max_belt_level?: string;
  has_availability?: boolean;
  start_date_from?: string;
  start_date_to?: string;
}

export interface ClassFilter {
  search?: string;
  instructor_id?: string;
  branch_id?: string;
  class_group_id?: string;
  status?: 'scheduled' | 'ongoing' | 'completed' | 'cancelled' | 'rescheduled';
  date_from?: string;
  date_to?: string;
  attendance_recorded?: boolean;
  time_from?: string;
  time_to?: string;
}

export interface EnrollmentFilter {
  class_group_id?: string;
  student_id?: string;
  status?: 'active' | 'inactive' | 'suspended' | 'completed';
  enrollment_date_from?: string;
  enrollment_date_to?: string;
}

export interface WaitlistFilter {
  class_group_id?: string;
  student_id?: string;
  status?: 'waiting' | 'notified' | 'enrolled' | 'expired' | 'cancelled';
  position_from?: number;
  position_to?: number;
}

// Tipos para paginação
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: PaginationInfo;
}

// Tipos para operações em lote
export interface BulkEnrollmentOperation {
  class_group_id: string;
  student_ids: string[];
  notes?: string;
}

export interface BulkClassOperation {
  class_group_id: string;
  operation: 'create' | 'cancel' | 'reschedule';
  classes: Array<{
    start_time: string;
    end_time: string;
    notes?: string;
  }>;
}

// Tipos para notificações e eventos
export interface ClassNotification {
  type: 'class_scheduled' | 'class_cancelled' | 'enrollment_confirmed' | 'waitlist_spot_available';
  recipient_id: string;
  class_id?: string;
  class_group_id?: string;
  message: string;
  scheduled_for?: string;
}

export interface WaitlistNotification {
  student_id: string;
  class_group_id: string;
  position: number;
  estimated_wait_time?: string;
}

// Tipos para horários e agendamento
export interface TimeSlot {
  start_time: string;
  end_time: string;
  day_of_week: number; // 0 = Sunday, 1 = Monday, etc.
}

export interface RecurringSchedule {
  start_date: string;
  end_date?: string;
  time_slots: TimeSlot[];
  repeat_pattern: 'daily' | 'weekly' | 'monthly';
  exceptions?: string[]; // Dates to skip
}

export interface InstructorAvailability {
  instructor_id: string;
  available_slots: TimeSlot[];
  busy_slots: TimeSlot[];
}

export interface ClassroomCapacity {
  branch_id: string;
  max_concurrent_classes: number;
  current_utilization: number;
  peak_hours: string[];
}

// Tipos para análise e insights
export interface AttendanceTrend {
  date: string;
  total_attendances: number;
  expected_attendances: number;
  attendance_rate: number;
}

// Tipos para relatórios
export interface ClassGroupReport {
  class_group: ClassGroupWithDetails;
  enrollment_history: Array<{
    date: string;
    enrollments: number;
    cancellations: number;
  }>;
  attendance_trends: Array<{
    class_date: string;
    attendance_count: number;
    attendance_rate: number;
  }>;
  financial_summary: {
    total_revenue: number;
    average_monthly_revenue: number;
    outstanding_payments: number;
  };
}

export interface AttendanceReport {
  period: {
    start_date: string;
    end_date: string;
  };
  overall_stats: {
    total_classes: number;
    total_attendances: number;
    average_attendance_rate: number;
  };
  by_class_group: Array<{
    class_group_name: string;
    classes_count: number;
    total_attendances: number;
    attendance_rate: number;
  }>;
  by_student: Array<{
    student_name: string;
    classes_attended: number;
    total_classes_available: number;
    attendance_rate: number;
  }>;
} 