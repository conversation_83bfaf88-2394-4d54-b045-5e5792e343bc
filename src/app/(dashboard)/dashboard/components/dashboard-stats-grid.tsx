import {
  UsersIcon,
  UserGroupIcon,
  BanknotesIcon,
  ArrowTrendingUpIcon,
} from '@heroicons/react/24/outline';
import { DashboardStat } from "../types";
import { DashboardStatsCard } from "./dashboard-stats-card";

interface DashboardStatsGridProps {
  stats: DashboardStat[];
}

const iconMap: Record<string, React.ReactNode> = {
  "users": <UsersIcon className="h-5 w-5" />,
  "user-group": <UserGroupIcon className="h-5 w-5" />,
  "banknotes": <BanknotesIcon className="h-5 w-5" />,
  "arrow-trending-up": <ArrowTrendingUpIcon className="h-5 w-5" />,
};

export function DashboardStatsGrid({ stats }: DashboardStatsGridProps) {
  if (!stats) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="bg-gray-800/50 border-gray-700/80 rounded-xl h-40 animate-pulse" />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat) => (
        <DashboardStatsCard
          key={stat.name}
          title={stat.name}
          value={stat.value}
          change={stat.change}
          icon={iconMap[stat.iconName]}
          chartData={stat.chartData}
        />
      ))}
    </div>
  );
} 