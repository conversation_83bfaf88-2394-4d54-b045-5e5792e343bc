'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import {
  <PERSON><PERSON>ontainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig,
} from "@/components/ui/chart";
import { PieChartData } from '../types';

interface InstructorsBeltChartProps {
  data: PieChartData[];
}

export function InstructorsBeltChart({ data }: InstructorsBeltChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className="dashboard-stats-card h-[320px] flex items-center justify-center">
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Sem dados disponíveis
        </p>
      </div>
    );
  }

  const total = data.reduce((sum, item) => sum + item.value, 0);

  const chartConfig: ChartConfig = Object.fromEntries(
    data.map((d) => [d.name, { label: d.name, color: d.color }])
  );

  return (
    <div className="dashboard-stats-card h-[320px]">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-base font-semibold text-gray-900 dark:text-white">
          Graduações - Instrutores
        </h3>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          Total: {total}
        </span>
      </div>
      
      <div className="h-[200px] flex justify-center">
        <ChartContainer config={chartConfig} className="w-[280px] h-[200px]">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              innerRadius={50}
              outerRadius={80}
              paddingAngle={2}
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <ChartTooltip content={<ChartTooltipContent />} />
          </PieChart>
        </ChartContainer>
      </div>
      
      <div className="flex justify-center gap-4 mt-4 flex-wrap">
        {data.map((entry, index) => (
          <div key={index} className="flex items-center gap-2">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-xs text-gray-600 dark:text-gray-400">
              {entry.name}: {entry.value}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
} 