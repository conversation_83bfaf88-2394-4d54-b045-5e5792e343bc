'use client';

import { 
  ExclamationTriangleIcon, 
  CheckCircleIcon, 
  ArrowTrendingUpIcon,
  CalendarDaysIcon 
} from '@heroicons/react/24/outline';
import { DashboardStat } from '../types';

interface QuickInsightsProps {
  stats: DashboardStat[];
}

export function QuickInsights({ stats }: QuickInsightsProps) {
  const activeStudents = parseInt(stats.find(s => s.name === "Alunos Ativos")?.value || "0");
  const newStudents = parseInt(stats.find(s => s.name.includes("Novos Alunos"))?.value || "0");
  const retentionRate = parseFloat(stats.find(s => s.name.includes("Retenção"))?.value.replace("%", "") || "0");
  
  const insights = [
    {
      type: activeStudents > 50 ? 'success' : activeStudents > 20 ? 'warning' : 'danger',
      icon: activeStudents > 50 ? CheckCircleIcon : ExclamationTriangleIcon,
      title: 'Base de Alunos',
      message: activeStudents > 50 
        ? `Ótima base com ${activeStudents} alunos ativos` 
        : activeStudents > 20 
        ? `Base moderada: ${activeStudents} alunos ativos`
        : `Base pequena: apenas ${activeStudents} alunos`,
    },
    {
      type: newStudents >= 5 ? 'success' : newStudents >= 2 ? 'warning' : 'danger',
      icon: newStudents >= 5 ? ArrowTrendingUpIcon : ExclamationTriangleIcon,
      title: 'Crescimento',
      message: newStudents >= 5 
        ? `Excelente! ${newStudents} novos alunos este mês` 
        : newStudents >= 2 
        ? `${newStudents} novos alunos. Pode melhorar`
        : `Apenas ${newStudents} novo(s) aluno(s). Atenção!`,
    },
    {
      type: retentionRate >= 80 ? 'success' : retentionRate >= 60 ? 'warning' : 'danger',
      icon: retentionRate >= 80 ? CheckCircleIcon : ExclamationTriangleIcon,
      title: 'Retenção',
      message: retentionRate >= 80 
        ? `Ótima retenção: ${retentionRate}%` 
        : retentionRate >= 60 
        ? `Retenção razoável: ${retentionRate}%`
        : `Retenção baixa: ${retentionRate}%`,
    },
    {
      type: 'info' as const,
      icon: CalendarDaysIcon,
      title: 'Próximas Ações',
      message: 'Revisar campanhas de marketing e feedback dos alunos',
    },
  ];

  const getColorClasses = (type: string) => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-green-50 dark:bg-green-900/20',
          border: 'border-green-200 dark:border-green-800',
          icon: 'text-green-600 dark:text-green-400',
          text: 'text-green-800 dark:text-green-200'
        };
      case 'warning':
        return {
          bg: 'bg-yellow-50 dark:bg-yellow-900/20',
          border: 'border-yellow-200 dark:border-yellow-800',
          icon: 'text-yellow-600 dark:text-yellow-400',
          text: 'text-yellow-800 dark:text-yellow-200'
        };
      case 'danger':
        return {
          bg: 'bg-red-50 dark:bg-red-900/20',
          border: 'border-red-200 dark:border-red-800',
          icon: 'text-red-600 dark:text-red-400',
          text: 'text-red-800 dark:text-red-200'
        };
      default:
        return {
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          border: 'border-blue-200 dark:border-blue-800',
          icon: 'text-blue-600 dark:text-blue-400',
          text: 'text-blue-800 dark:text-blue-200'
        };
    }
  };

  return (
    <div className="dashboard-stats-card ">
      <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
        Insights Rápidos
      </h3>
      
      <div className="space-y-2 overflow-y-auto max-h-[180px]">
        {insights.map((insight, index) => {
          const colors = getColorClasses(insight.type);
          const Icon = insight.icon;
          
          return (
            <div
              key={index}
              className={`p-2 rounded-lg border ${colors.bg} ${colors.border}`}
            >
              <div className="flex items-start gap-2">
                <Icon className={`h-4 w-4 mt-0.5 ${colors.icon} flex-shrink-0`} />
                <div className="min-w-0 flex-1">
                  <p className={`text-xs font-medium ${colors.text}`}>
                    {insight.title}
                  </p>
                  <p className={`text-xs ${colors.text} opacity-90 mt-0.5`}>
                    {insight.message}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
} 