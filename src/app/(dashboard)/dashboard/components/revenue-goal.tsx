'use client';

import { useEffect, useState } from 'react';
import { getRevenueGoalMetrics } from '../actions/metrics-actions';

interface RevenueGoalProps {
  tenantId: string;
}

export function RevenueGoal({ tenantId }: RevenueGoalProps) {
  const [revenueData, setRevenueData] = useState({
    currentRevenue: 0,
    expectedRevenue: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchRevenueData() {
      try {
        const data = await getRevenueGoalMetrics(tenantId);
        setRevenueData(data);
      } catch (error) {
        console.error('Error fetching revenue goal metrics:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchRevenueData();
  }, [tenantId]);

  const { currentRevenue, expectedRevenue } = revenueData;
  const progress = expectedRevenue > 0 ? Math.min((currentRevenue / expectedRevenue) * 100, 100) : 0;
  const remaining = Math.max(expectedRevenue - currentRevenue, 0);
  
  const circumference = 2 * Math.PI * 60; // raio aumentado para 60
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const getProgressColor = () => {
    if (progress >= 100) return 'text-green-500';
    if (progress >= 75) return 'text-blue-500';
    if (progress >= 50) return 'text-yellow-500';
    return 'text-red-500';
  };

  return (
    <div className="dashboard-stats-card h-[320px] flex flex-col">
      <h3 className="text-base font-semibold text-gray-900 dark:text-white mb-4">
        Receita Mensal
      </h3>
      
      <div className="flex-1 flex flex-col items-center justify-center">
        <div className="relative w-32 h-32 mb-4">
          <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 140 140">
            {/* Círculo de fundo */}
            <circle
              cx="70"
              cy="70"
              r="60"
              fill="none"
              stroke="currentColor"
              strokeWidth="10"
              className="text-gray-200 dark:text-gray-700"
            />
            {/* Círculo de progresso */}
            <circle
              cx="70"
              cy="70"
              r="60"
              fill="none"
              stroke="currentColor"
              strokeWidth="10"
              strokeLinecap="round"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              className={`transition-all duration-500 ${getProgressColor()}`}
            />
          </svg>
          
          {/* Percentual no centro */}
          <div className="absolute inset-0 flex items-center justify-center">
            <span className={`text-lg font-bold ${getProgressColor()}`}>
              {progress.toFixed(0)}%
            </span>
          </div>
        </div>
        
        {/* Informações */}
        <div className="text-center space-y-2">
          {loading ? (
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-24 mx-auto mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-20 mx-auto"></div>
            </div>
          ) : (
            <>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Recebido: <span className="font-medium text-green-600 dark:text-green-400">
                  {formatCurrency(currentRevenue)}
                </span>
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Previsto: <span className="font-medium text-gray-900 dark:text-white">
                  {formatCurrency(expectedRevenue)}
                </span>
              </p>
              {remaining > 0 && expectedRevenue > 0 && (
                <p className="text-sm text-gray-500 dark:text-gray-500">
                  Pendente: {formatCurrency(remaining)}
                </p>
              )}
              {progress >= 100 && expectedRevenue > 0 && (
                <p className="text-sm text-green-600 dark:text-green-400 font-medium">
                  🎉 Meta atingida!
                </p>
              )}
              {expectedRevenue === 0 && (
                <p className="text-sm text-gray-500 dark:text-gray-500">
                  Nenhum pagamento agendado
                </p>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
} 