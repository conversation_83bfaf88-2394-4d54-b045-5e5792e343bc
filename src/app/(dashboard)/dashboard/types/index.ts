export interface ChartData {
  name: string;
  value: number;
}

export interface PieChartData {
  name: string;
  value: number;
  color: string;
}

export interface DashboardStat {
  name: string;
  value: string;
  change: number;
  iconName: "users" | "user-group" | "banknotes" | "arrow-trending-up";
  chartData?: ChartData[];
}

export interface ActivityItem {
  id: string;
  type: "payment" | "graduation" | "attendance";
  person: { 
    name: string; 
    href?: string; 
  };
  date: string;
  timestamp: string;
  description: string;
}

export interface StudentsChartData {
  date: string;
  activeStudents: number;
  inactiveStudents: number;
}

export interface DashboardData {
  stats: DashboardStat[];
  recentActivity: ActivityItem[];
  studentsChart: StudentsChartData[];
  instructorsBeltChart: PieChartData[];
  instructorsContractChart: PieChartData[];
}

export interface StudentsMetrics {
  activeStudentsNow: number | null;
  activeStudentsLast: number | null;
  newStudentsNow: number | null;
  newStudentsLast: number | null;
}

export interface RevenueMetrics {
  revenueNow: number;
  revenueLast: number;
}

export interface RetentionRateMetrics {
  retentionRate: number;
  retentionRateLast: number;
}