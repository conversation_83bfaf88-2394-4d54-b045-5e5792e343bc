import { startOfMonth, endOfMonth, subMonths } from "date-fns";

export function getMonthDateRanges() {
  const thisMonthStart = startOfMonth(new Date());
  const thisMonthEnd = endOfMonth(new Date());
  const lastMonthStart = startOfMonth(subMonths(new Date(), 1));
  const lastMonthEnd = endOfMonth(subMonths(new Date(), 1));

  return {
    thisMonthStart,
    thisMonthEnd,
    lastMonthStart,
    lastMonthEnd,
  };
}

export function getThirtyDaysAgo() {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  return thirtyDaysAgo;
}

export function calculatePercentageChange(
  current?: number | null, 
  previous?: number | null
): string {
  if (!previous || !current) return "0%";
  const pct = ((current - previous) / previous) * 100;
  return `${pct.toFixed(1)}%`;
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(amount);
} 