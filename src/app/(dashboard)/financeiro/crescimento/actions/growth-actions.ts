'use server';

import { createTenantServerClient } from '@/services/supabase/server';
import { TenantExtractorServer } from '@/services/tenant/tenant-extractor-server';

interface ActiveMembership {
  id: string;
  status: string;
  start_date: string;
  end_date: string | null;
  // NOTE: next_billing_date removed - now using payments table as source of truth
  plan: {
    title: string;
    pricing_config: {
      type: 'recurring' | 'one-time' | 'per-session' | 'trial';
      amount: number;
      currency: string;
      frequency?: 'month' | 'week' | 'year';
      frequency_number?: number;
    };
    duration_config: {
      type: 'ongoing' | 'limited' | 'specific';
      end_date?: string;
    };
  };
}

export interface GrowthMetrics {
  yearToDateRevenue: number;
  projectedYearRevenue: number;
  totalToReceiveUntilYearEnd: number;
  monthlyRevenueData: Array<{
    month: string;
    revenue: number;
    isProjected?: boolean;
  }>;
  monthlyToReceiveData: Array<{
    month: string;
    toReceive: number;
  }>;
}

/**
 * Calcula o total a receber baseado nos planos ativos do mês atual até o final do ano
 * Considera pagamentos pendentes e exclui valores que já foram pagos (status 'paid')
 */
async function calculateToReceiveMetrics(supabase: any, currentYear: number, currentMonth: number) {
  // Buscar memberships ativas com seus planos
  const { data: activeMemberships, error } = await supabase
    .from('memberships')
    .select(`
      id,
      status,
      start_date,
      end_date,
      plans!inner (
        title,
        pricing_config,
        duration_config
      )
    `)
    .eq('status', 'active');

  if (error) {
    console.error('Erro ao buscar memberships ativas:', error);
    return { totalToReceive: 0, monthlyToReceive: [] };
  }

  // Buscar todos os pagamentos do período (paid e pending)
  const { data: allPayments, error: paymentsError } = await supabase
    .from('payments')
    .select('membership_id, amount, due_date, status')
    .gte('due_date', `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`)
    .lte('due_date', `${currentYear}-12-31`);

  if (paymentsError) {
    console.error('Erro ao buscar pagamentos:', paymentsError);
  }

  // Separar pagamentos por status e criar mapas por membership e mês
  const paidPaymentsMap = new Map<string, number>();
  const pendingPaymentsMap = new Map<string, number>();

  for (const payment of allPayments || []) {
    const paymentDate = new Date(payment.due_date);
    const paymentMonth = paymentDate.getMonth() + 1;
    const paymentYear = paymentDate.getFullYear();

    if (paymentYear === currentYear && paymentMonth >= currentMonth) {
      const key = `${payment.membership_id}-${paymentMonth}`;
      const amount = parseFloat(String(payment.amount || '0'));

      if (payment.status === 'paid') {
        const currentAmount = paidPaymentsMap.get(key) || 0;
        paidPaymentsMap.set(key, currentAmount + amount);
      } else if (payment.status === 'pending') {
        const currentAmount = pendingPaymentsMap.get(key) || 0;
        pendingPaymentsMap.set(key, currentAmount + amount);
      }
    }
  }

  const monthlyToReceive: Array<{ month: string; toReceive: number }> = [];
  let totalToReceive = 0;

  // Inicializar array mensal (incluindo mês atual)
  for (let month = currentMonth; month <= 12; month++) {
    monthlyToReceive.push({
      month: new Date(currentYear, month - 1).toLocaleDateString('pt-BR', { month: 'short' }),
      toReceive: 0
    });
  }

  // Primeiro, adicionar pagamentos pendentes que já existem
  for (let month = currentMonth; month <= 12; month++) {
    const monthIndex = month - currentMonth;
    if (monthIndex >= 0 && monthIndex < monthlyToReceive.length) {
      // Somar todos os pagamentos pendentes deste mês
      for (const membership of activeMemberships || []) {
        const pendingKey = `${membership.id}-${month}`;
        const pendingAmount = pendingPaymentsMap.get(pendingKey) || 0;

        if (pendingAmount > 0) {
          monthlyToReceive[monthIndex].toReceive += pendingAmount;
          totalToReceive += pendingAmount;
        }
      }
    }
  }

  // Depois, calcular receita baseada nos planos para meses que não têm pagamentos criados
  for (const membership of activeMemberships || []) {
    const plan = membership.plans;
    const pricingConfig = plan.pricing_config;

    // Apenas processar planos recorrentes
    if (pricingConfig.type === 'recurring' && pricingConfig.frequency) {
      const amount = pricingConfig.amount || 0;
      const frequency = pricingConfig.frequency;

      // Calcular quantos pagamentos haverá até o final do ano (incluindo mês atual)
      for (let month = currentMonth; month <= 12; month++) {
        let shouldCharge = false;

        if (frequency === 'month') {
          // Para planos mensais, cobra todo mês
          shouldCharge = true;
        } else if (frequency === 'year') {
          // Para planos anuais, cobra apenas no mês de início
          const startDate = new Date(membership.start_date);
          const startMonth = startDate.getMonth() + 1;
          shouldCharge = month === startMonth;
        }

        if (shouldCharge) {
          const paymentKey = `${membership.id}-${month}`;
          const paidAmount = paidPaymentsMap.get(paymentKey) || 0;
          const pendingAmount = pendingPaymentsMap.get(paymentKey) || 0;

          // Se já foi pago, não conta
          // Se já tem pagamento pendente, não precisa calcular novamente (já foi adicionado acima)
          // Só adiciona se não existe nenhum pagamento para este mês
          if (paidAmount === 0 && pendingAmount === 0) {
            const monthIndex = month - currentMonth;
            if (monthIndex >= 0 && monthIndex < monthlyToReceive.length) {
              monthlyToReceive[monthIndex].toReceive += amount;
              totalToReceive += amount;
            }
          }
        }
      }
    }
  }

  return { totalToReceive, monthlyToReceive };
}

/**
 * Busca métricas de crescimento da receita
 */
export async function getGrowthMetrics(): Promise<{
  success: boolean;
  data?: GrowthMetrics;
  error?: string;
}> {
  try {
    // Obter tenant ID
    const extractor = new TenantExtractorServer();
    const tenant = await extractor.getTenantIdentifier();

    if (!tenant.id) {
      return { success: false, error: 'Tenant não identificado' };
    }

    const supabase = await createTenantServerClient();
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;

    // Buscar receita mensal do ano atual até agora
    const { data: yearToDateData, error: ytdError } = await supabase
      .from('payments')
      .select('amount, due_date')
      .eq('status', 'paid')
      .gte('due_date', `${currentYear}-01-01`)
      .lte('due_date', `${currentYear}-${currentMonth.toString().padStart(2, '0')}-31`);

    if (ytdError) {
      console.error('Erro ao buscar receita do ano:', ytdError);
      return { success: false, error: 'Erro ao buscar dados de receita' };
    }

    // Calcular receita do ano até agora
    const yearToDateRevenue = (yearToDateData || []).reduce(
      (sum, payment) => sum + parseFloat(String(payment.amount || '0')),
      0
    );

    // Buscar dados mensais para o gráfico (ano atual)
    const monthlyData: Array<{ month: string; revenue: number; isProjected?: boolean }> = [];
    
    for (let month = 1; month <= 12; month++) {
      const monthStr = month.toString().padStart(2, '0');
      const startDate = `${currentYear}-${monthStr}-01`;
      const endDate = new Date(currentYear, month, 0).toISOString().split('T')[0];

      if (month <= currentMonth) {
        // Dados reais para meses já passados
        const { data: monthData } = await supabase
          .from('payments')
          .select('amount')
          .eq('status', 'paid')
          .gte('due_date', startDate)
          .lte('due_date', endDate);

        const monthRevenue = (monthData || []).reduce(
          (sum, payment) => sum + parseFloat(String(payment.amount || '0')),
          0
        );

        monthlyData.push({
          month: new Date(currentYear, month - 1).toLocaleDateString('pt-BR', { month: 'short' }),
          revenue: monthRevenue,
          isProjected: false
        });
      } else {
        // Projeção para meses futuros baseada na média dos meses anteriores
        const averageRevenue = monthlyData.length > 0 
          ? monthlyData.reduce((sum, m) => sum + m.revenue, 0) / monthlyData.length
          : 0;

        monthlyData.push({
          month: new Date(currentYear, month - 1).toLocaleDateString('pt-BR', { month: 'short' }),
          revenue: averageRevenue,
          isProjected: true
        });
      }
    }

    // Calcular projeção para o ano todo
    const projectedYearRevenue = monthlyData.reduce((sum, m) => sum + m.revenue, 0);

    // Calcular total a receber baseado nos planos ativos
    const { totalToReceive, monthlyToReceive } = await calculateToReceiveMetrics(
      supabase,
      currentYear,
      currentMonth
    );

    return {
      success: true,
      data: {
        yearToDateRevenue,
        projectedYearRevenue,
        totalToReceiveUntilYearEnd: totalToReceive,
        monthlyRevenueData: monthlyData,
        monthlyToReceiveData: monthlyToReceive
      }
    };

  } catch (error) {
    console.error('Erro ao buscar métricas de crescimento:', error);
    return {
      success: false,
      error: 'Erro interno do servidor'
    };
  }
}
