"use server";

/**
 * Server Actions principais para consolidação de dados do dashboard
 */

import { 
  DashboardActionResult, 
  FinancialKPIs, 
  DashboardData, 
  DateRange 
} from '../../types/dashboard-types';
import { 
  createMetricWithGrowth, 
  ensureNumber, 
  getPreviousPeriodRange 
} from '../../utils/dashboard-utils';
import { getAuthenticatedClient, formatDateForSQL } from '../shared/auth-utils';
import { getRevenueMetrics } from '../metrics/revenue-actions';
import { getExpenseMetrics } from '../metrics/expense-actions';
import { getCashFlowMetrics } from '../metrics/cashflow-actions';
import { getStudentMetrics } from '../metrics/student-actions';

/**
 * Busca todos os dados do dashboard para um período
 */
export async function getDashboardData(
  currentRange: DateRange,
  previousRange: DateRange
): Promise<DashboardActionResult<DashboardData>> {
  try {
    // Buscar todas as métricas em paralelo
    const [
      revenueResult,
      expenseResult,
      cashFlowResult,
      studentResult
    ] = await Promise.all([
      getRevenueMetrics(currentRange, previousRange),
      getExpenseMetrics(currentRange, previousRange),
      getCashFlowMetrics(currentRange),
      getStudentMetrics(currentRange, previousRange)
    ]);

    // Verificar se alguma busca falhou
    if (!revenueResult.success) {
      return { success: false, error: revenueResult.error };
    }
    if (!expenseResult.success) {
      return { success: false, error: expenseResult.error };
    }
    if (!cashFlowResult.success) {
      return { success: false, error: cashFlowResult.error };
    }
    if (!studentResult.success) {
      return { success: false, error: studentResult.error };
    }

    const revenueMetrics = revenueResult.data!;
    const expenseMetrics = expenseResult.data!;
    const cashFlowMetrics = cashFlowResult.data!;
    const studentMetrics = studentResult.data!;

    // Calcular KPIs principais com crescimento
    // Para MVP, usar valores zerados para período anterior
    const kpis: FinancialKPIs = {
      totalRevenue: createMetricWithGrowth(
        revenueMetrics.totalRevenue,
        0, // TODO: Buscar receita do período anterior
        false,
        true
      ),
      totalExpenses: createMetricWithGrowth(
        expenseMetrics.totalExpenses,
        0, // TODO: Buscar despesas do período anterior
        false,
        true
      ),
      netProfit: createMetricWithGrowth(
        revenueMetrics.totalRevenue - expenseMetrics.totalExpenses,
        0, // TODO: Calcular lucro do período anterior
        false,
        true
      ),
      activeStudents: createMetricWithGrowth(
        studentMetrics.activeStudents,
        0, // TODO: Buscar alunos ativos do período anterior
        false,
        false
      ),
      averageRevenuePerUser: createMetricWithGrowth(
        studentMetrics.activeStudents > 0
          ? revenueMetrics.totalRevenue / studentMetrics.activeStudents
          : 0,
        0, // TODO: Calcular ARPU do período anterior
        false,
        true
      ),
      profitMargin: createMetricWithGrowth(
        revenueMetrics.totalRevenue > 0
          ? ((revenueMetrics.totalRevenue - expenseMetrics.totalExpenses) / revenueMetrics.totalRevenue) * 100
          : 0,
        0, // TODO: Calcular margem do período anterior
        true,
        false
      )
    };

    const dashboardData: DashboardData = {
      kpis,
      revenueMetrics,
      expenseMetrics,
      cashFlowMetrics,
      studentMetrics,
      lastUpdated: new Date()
    };

    return {
      success: true,
      data: dashboardData,
      message: 'Dados do dashboard obtidos com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

/**
 * Busca apenas os KPIs principais com comparação de período anterior (Fase 2)
 */
export async function getDashboardKPIs(
  currentRange: DateRange
): Promise<DashboardActionResult<FinancialKPIs>> {
  try {
    // ============================================================================
    // NORMALIZAÇÃO DE DATAS
    // ============================================================================

    // Quando o objeto DateRange chega via JSON, as datas são serializadas como string.
    // Precisamos garantir que "startDate" e "endDate" sejam instâncias válidas de Date
    // antes de utilizá-las em cálculos ou em utilitários como getPreviousPeriodRange.

    const normalizedCurrentRange: DateRange = {
      ...currentRange,
      startDate: currentRange.startDate instanceof Date
        ? currentRange.startDate
        : new Date(currentRange.startDate as unknown as string),
      endDate: currentRange.endDate instanceof Date
        ? currentRange.endDate
        : new Date(currentRange.endDate as unknown as string),
    };

    // Se as datas convertidas forem inválidas, lançar erro explícito para facilitar debug
    if (isNaN(normalizedCurrentRange.startDate.getTime()) || isNaN(normalizedCurrentRange.endDate.getTime())) {
      throw new Error('Parâmetro currentRange contém datas inválidas');
    }

    const { supabase, tenantId } = await getAuthenticatedClient();

    // Importar função para calcular período anterior
    const previousRange = getPreviousPeriodRange(normalizedCurrentRange);

    // Query otimizada para buscar dados do período atual e anterior
    const [
      currentRevenueQuery,
      currentExpenseQuery,
      currentStudentQuery,
      previousRevenueQuery,
      previousExpenseQuery,
      previousStudentQuery
    ] = await Promise.all([
      // Receitas período atual
      supabase
        .from('payments')
        .select('amount, status')
        .eq('tenant_id', tenantId)
        .eq('status', 'paid')
        .gte('due_date', formatDateForSQL(normalizedCurrentRange.startDate))
        .lte('due_date', formatDateForSQL(normalizedCurrentRange.endDate)),

      // Despesas período atual
      supabase
        .from('expenses')
        .select('amount, status')
        .eq('tenant_id', tenantId)
        .eq('status', 'paid')
        .gte('due_date', formatDateForSQL(normalizedCurrentRange.startDate))
        .lte('due_date', formatDateForSQL(normalizedCurrentRange.endDate)),

      // Alunos ativos período atual
      supabase
        .from('memberships')
        .select('student_id')
        .eq('tenant_id', tenantId)
        .eq('status', 'active'),

      // Receitas período anterior
      supabase
        .from('payments')
        .select('amount, status')
        .eq('tenant_id', tenantId)
        .eq('status', 'paid')
        .gte('due_date', formatDateForSQL(previousRange.startDate))
        .lte('due_date', formatDateForSQL(previousRange.endDate)),

      // Despesas período anterior
      supabase
        .from('expenses')
        .select('amount, status')
        .eq('tenant_id', tenantId)
        .eq('status', 'paid')
        .gte('due_date', formatDateForSQL(previousRange.startDate))
        .lte('due_date', formatDateForSQL(previousRange.endDate)),

      // Para alunos, vamos usar uma aproximação baseada em matrículas criadas
      supabase
        .from('memberships')
        .select('student_id, created_at')
        .eq('tenant_id', tenantId)
        .lte('created_at', formatDateForSQL(previousRange.endDate))
    ]);

    // Calcular métricas do período atual
    const currentTotalRevenue = (currentRevenueQuery.data || [])
      .reduce((sum, p) => sum + ensureNumber(p.amount), 0);

    const currentTotalExpenses = (currentExpenseQuery.data || [])
      .reduce((sum, e) => sum + ensureNumber(e.amount), 0);

    const currentActiveStudents = (currentStudentQuery.data || []).length;

    // Calcular métricas do período anterior
    const previousTotalRevenue = (previousRevenueQuery.data || [])
      .reduce((sum, p) => sum + ensureNumber(p.amount), 0);

    const previousTotalExpenses = (previousExpenseQuery.data || [])
      .reduce((sum, e) => sum + ensureNumber(e.amount), 0);

    // Para alunos anteriores, contar matrículas que existiam até o final do período anterior
    const previousActiveStudents = (previousStudentQuery.data || [])
      .filter(m => new Date(m.created_at) <= previousRange.endDate).length;

    // Calcular métricas derivadas
    const currentNetProfit = currentTotalRevenue - currentTotalExpenses;
    const previousNetProfit = previousTotalRevenue - previousTotalExpenses;

    const currentAverageRevenuePerUser = currentActiveStudents > 0
      ? currentTotalRevenue / currentActiveStudents : 0;
    const previousAverageRevenuePerUser = previousActiveStudents > 0
      ? previousTotalRevenue / previousActiveStudents : 0;

    const currentProfitMargin = currentTotalRevenue > 0
      ? (currentNetProfit / currentTotalRevenue) * 100 : 0;
    const previousProfitMargin = previousTotalRevenue > 0
      ? (previousNetProfit / previousTotalRevenue) * 100 : 0;

    // Criar KPIs com comparações reais
    const kpis: FinancialKPIs = {
      totalRevenue: createMetricWithGrowth(
        currentTotalRevenue,
        previousTotalRevenue,
        false,
        true
      ),
      totalExpenses: createMetricWithGrowth(
        currentTotalExpenses,
        previousTotalExpenses,
        false,
        true
      ),
      netProfit: createMetricWithGrowth(
        currentNetProfit,
        previousNetProfit,
        false,
        true
      ),
      activeStudents: createMetricWithGrowth(
        currentActiveStudents,
        previousActiveStudents,
        false,
        false
      ),
      averageRevenuePerUser: createMetricWithGrowth(
        currentAverageRevenuePerUser,
        previousAverageRevenuePerUser,
        false,
        true
      ),
      profitMargin: createMetricWithGrowth(
        currentProfitMargin,
        previousProfitMargin,
        true,
        false
      )
    };

    return {
      success: true,
      data: kpis,
      message: 'KPIs com comparações obtidos com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}
