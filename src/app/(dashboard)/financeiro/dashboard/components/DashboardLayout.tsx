"use client";

/**
 * Layout Responsivo do Dashboard Financeiro
 * Organiza as seções conforme especificado no documento
 */

import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, TrendingUp, TrendingDown } from 'lucide-react';
import { cn } from '@/lib/utils';

import { DashboardLayoutProps } from '../types/dashboard-types';
import { LoadingStates } from './LoadingStates';
import { EnhancedKPICard } from './EnhancedKPICard';
import { ComparisonMetrics } from './ComparisonMetrics'; // Comentado temporariamente




// ============================================================================
// SEÇÃO DE KPIs
// ============================================================================

interface KPISectionProps {
  kpis: DashboardLayoutProps['kpis'];
  loading?: boolean;
}

const KPISection: React.FC<KPISectionProps> = ({ kpis, loading }) => {
  if (loading) {
    return <LoadingStates.KPIs />;
  }

  // Dados simulados para sparklines (serão implementados com dados reais nas próximas iterações)
  const generateSparklineData = (trend: string) => {
    const baseData = [100, 105, 98, 110, 115, 108, 120];
    if (trend === 'up') return baseData.map((v, i) => v + i * 2);
    if (trend === 'down') return baseData.map((v, i) => v - i * 1.5);
    return baseData;
  };

  return (
    <div className="space-y-6">
      {/* KPIs Principais - Fase 2 com componentes aprimorados (Métricas de receita movidas para aba específica) */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <EnhancedKPICard
          title="Despesas Totais"
          metric={kpis.totalExpenses}
          icon={<TrendingDown className="h-5 w-5" />}
          description="Total de despesas pagas no período"
          sparklineData={generateSparklineData(kpis.totalExpenses.trend)}
          variant="default"
        />
        <EnhancedKPICard
          title="Lucro Líquido"
          metric={kpis.netProfit}
          icon={<TrendingUp className="h-5 w-5" />}
          description="Receitas menos despesas do período"
          sparklineData={generateSparklineData(kpis.netProfit.trend)}
          variant="default"
        />
        <EnhancedKPICard
          title="Alunos Ativos"
          metric={kpis.activeStudents}
          icon={<TrendingUp className="h-5 w-5" />}
          description="Número de alunos com matrículas ativas"
          sparklineData={generateSparklineData(kpis.activeStudents.trend)}
          variant="default"
        />
      </div>
    </div>
  );
};



// ============================================================================
// COMPONENTE PRINCIPAL DO LAYOUT
// ============================================================================

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  kpis,
  data,
  loading = false,
  error = null,
  className
}) => {
  // Mostrar erro se houver
  if (error) {
    return (
      <div className={cn("space-y-6", className)}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Erro ao carregar dados do dashboard: {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Loading completo
  if (loading && !data) {
    return (
      <div className={cn("space-y-6", className)}>
        <LoadingStates.Dashboard />
      </div>
    );
  }

  return (
    <div className={cn("space-y-8", className)}>
      {/* Seção 1: KPIs Principais (Topo) - Fase 2 Aprimorada */}
      <section>
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Resumo Executivo
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Principais indicadores financeiros com comparações e tendências
              </p>
            </div>
          </div>
        </div>
        <KPISection kpis={kpis} loading={loading} />
      </section>

      {/* Seção 1.5: Métricas de Comparação - Nova na Fase 2 */}
      <section>
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Análise Comparativa
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Comparações detalhadas com período anterior e indicadores de tendência
          </p>
        </div>
        <ComparisonMetrics
          kpis={kpis}
          periodLabel={data?.lastUpdated ?
            new Date(data.lastUpdated).toLocaleDateString('pt-BR', {
              month: 'long',
              year: 'numeric'
            }) : 'Período Atual'
          }
        />
      </section>

      {/* Seção de Receitas movida para aba específica - Ver aba "Receitas" */}

      {/* Informações de Atualização */}
      <section className="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Última atualização: {data?.lastUpdated ?
            new Date(data.lastUpdated).toLocaleString('pt-BR') :
            'Carregando...'
          }
        </div>
      </section>
    </div>
  );
};
