import { Metadata } from 'next';
import { FinancialDashboardTabs } from './components/FinancialDashboardTabs';

// ============================================================================
// METADATA
// ============================================================================

export const metadata: Metadata = {
  title: 'Dashboard Financeiro | Academia',
  description: 'Dashboard completo com análises financeiras, KPIs e relatórios da academia',
  keywords: [
    'dashboard financeiro',
    'KPIs',
    'receitas',
    'despesas',
    'fluxo de caixa',
    'análises financeiras',
    'academia',
    'gestão financeira'
  ].join(', '),
  openGraph: {
    title: 'Dashboard Financeiro | Academia',
    description: 'Visão completa da saúde financeira da sua academia',
    type: 'website',
  },
};

// ============================================================================
// PÁGINA PRINCIPAL
// ============================================================================

export default function DashboardFinanceiroPage() {
  return (
    <div className="px-4 py-6">
      {/* Header da Página */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
              Dashboard Financeiro
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Visão completa da saúde financeira da sua academia
            </p>
          </div>
        </div>
      </div>

      {/* Dashboard com Abas */}
      <FinancialDashboardTabs />
    </div>
  );
}
