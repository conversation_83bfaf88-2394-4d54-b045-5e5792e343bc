'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Receipt,
  Calendar as CalendarIcon,
  Building2,
  FileText,
  CreditCard,
  Edit3,
  Save,
  X,
  Loader2,
  Alert<PERSON>ircle,
  Trash2
} from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { getExpenseById } from '../../actions/expense-queries';
import { updateExpense, markExpenseAsPaid, cancelExpense, deleteExpense, type ExpenseData } from '../../actions/expense-actions';
import { getExpenseCategories } from '../../actions/expense-categories-actions';
import { getPaymentMethods } from '../../actions/payment-methods-actions';
import { useCurrencyFormat } from '@/hooks/form/useCurrencyFormat';

interface ExpenseModalProps {
  isOpen: boolean;
  onClose: () => void;
  expenseId: string | null;
  onExpenseUpdated?: () => void;
}

interface ExpenseCategory {
  id: string;
  name: string;
  color: string;
}

interface PaymentMethod {
  id: string;
  name: string;
  slug: string;
}

interface ExpenseDetails {
  id: string;
  supplier_name: string;
  supplier_document?: string;
  amount: number;
  currency: string;
  description?: string;
  due_date?: string;
  paid_at?: string;
  status: 'pending' | 'paid' | 'overdue' | 'canceled';
  is_recurring?: boolean;
  expense_categories?: ExpenseCategory;
  payment_methods?: PaymentMethod;
  created_at: string;
  updated_at?: string;
}

export function ExpenseModal({ isOpen, onClose, expenseId, onExpenseUpdated }: ExpenseModalProps) {
  const [expense, setExpense] = useState<ExpenseDetails | null>(null);
  const [categories, setCategories] = useState<ExpenseCategory[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Estados do formulário
  const [formData, setFormData] = useState({
    supplier_name: '',
    supplier_document: '',
    amount: 0,
    description: '',
    due_date: '',
    category_id: '',
    payment_method_id: '',
    status: 'pending' as const
  });

  // Hook para formatação monetária
  const currencyFormat = useCurrencyFormat({
    value: formData.amount,
    onChange: (value) => setFormData(prev => ({ ...prev, amount: value }))
  });

  // Carregar dados iniciais
  useEffect(() => {
    if (isOpen && expenseId) {
      loadExpenseData();
      loadCategories();
      loadPaymentMethods();
    }
  }, [isOpen, expenseId]);

  const loadExpenseData = async () => {
    if (!expenseId) return;
    
    setIsLoading(true);
    try {
      const result = await getExpenseById(expenseId);
      if (result.success && result.data) {
        setExpense(result.data);
        setFormData({
          supplier_name: result.data.supplier_name || '',
          supplier_document: result.data.supplier_document || '',
          amount: result.data.amount || 0,
          description: result.data.description || '',
          due_date: result.data.due_date || '',
          category_id: result.data.expense_categories?.id || '',
          payment_method_id: result.data.payment_methods?.id || '',
          status: result.data.status || 'pending'
        });
        
        if (result.data.due_date) {
          setSelectedDate(new Date(result.data.due_date));
        }
      } else {
        toast.error('Erro ao carregar despesa: ' + (result.error || 'Erro desconhecido'));
      }
    } catch (error) {
      toast.error('Erro ao carregar despesa');
      console.error('Erro ao carregar despesa:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const result = await getExpenseCategories();
      setCategories(result);
    } catch (error) {
      console.error('Erro ao carregar categorias:', error);
    }
  };

  const loadPaymentMethods = async () => {
    try {
      const result = await getPaymentMethods();
      setPaymentMethods(result);
    } catch (error) {
      console.error('Erro ao carregar métodos de pagamento:', error);
    }
  };

  const handleSave = async () => {
    if (!expense) return;

    setIsSaving(true);
    try {
      const updateData: Partial<ExpenseData> = {
        supplier_name: formData.supplier_name,
        supplier_document: formData.supplier_document || undefined,
        amount: formData.amount,
        description: formData.description || undefined,
        due_date: formData.due_date || undefined,
        category_id: formData.category_id || undefined,
        payment_method_id: formData.payment_method_id || undefined,
        status: formData.status
      };

      const result = await updateExpense(expense.id, updateData);
      
      if (result.success) {
        toast.success('Despesa atualizada com sucesso!');
        setIsEditing(false);
        await loadExpenseData(); // Recarregar dados
        onExpenseUpdated?.();
      } else {
        toast.error('Erro ao atualizar despesa: ' + (result.error || 'Erro desconhecido'));
      }
    } catch (error) {
      toast.error('Erro ao atualizar despesa');
      console.error('Erro ao atualizar despesa:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleMarkAsPaid = async () => {
    if (!expense) return;

    setIsSaving(true);
    try {
      const result = await markExpenseAsPaid(expense.id, formData.payment_method_id || undefined);
      
      if (result.success) {
        toast.success('Despesa marcada como paga!');
        await loadExpenseData();
        onExpenseUpdated?.();
      } else {
        toast.error('Erro ao marcar despesa como paga: ' + (result.error || 'Erro desconhecido'));
      }
    } catch (error) {
      toast.error('Erro ao marcar despesa como paga');
      console.error('Erro ao marcar despesa como paga:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = async () => {
    if (!expense) return;

    setIsSaving(true);
    try {
      const result = await cancelExpense(expense.id);

      if (result.success) {
        toast.success('Despesa cancelada!');
        await loadExpenseData();
        onExpenseUpdated?.();
      } else {
        toast.error('Erro ao cancelar despesa: ' + (result.error || 'Erro desconhecido'));
      }
    } catch (error) {
      toast.error('Erro ao cancelar despesa');
      console.error('Erro ao cancelar despesa:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    if (!expense) return;

    setIsSaving(true);
    setShowDeleteDialog(false);

    try {
      const result = await deleteExpense(expense.id);

      if (result.success) {
        toast.success('Despesa deletada com sucesso!');
        onExpenseUpdated?.();
        handleClose(); // Fechar o modal após deletar
      } else {
        toast.error('Erro ao deletar despesa: ' + (result.error || 'Erro desconhecido'));
      }
    } catch (error) {
      toast.error('Erro ao deletar despesa');
      console.error('Erro ao deletar despesa:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
  };

  const handleClose = () => {
    setIsEditing(false);
    setExpense(null);
    onClose();
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'overdue':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'canceled':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'paid':
        return 'Pago';
      case 'pending':
        return 'Pendente';
      case 'overdue':
        return 'Vencido';
      case 'canceled':
        return 'Cancelado';
      default:
        return status;
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0 pb-4">
          <DialogTitle className="flex items-center gap-2">
            <Receipt className="h-5 w-5" />
            {isEditing ? 'Editar Despesa' : 'Detalhes da Despesa'}
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : expense ? (
          <div
            className="flex-1 overflow-y-auto min-h-0 max-h-[calc(90vh-200px)] custom-scrollbar"
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: '#cbd5e1 transparent',
            }}
          >
            <div className="space-y-6 pr-2 pb-4">
              {/* Status e Valor */}
              <div className="flex items-center justify-between">
                <Badge className={cn('text-sm font-medium', getStatusColor(expense.status))}>
                  {getStatusLabel(expense.status)}
                </Badge>
                <div className="text-right">
                  <p className="text-2xl font-bold">{formatCurrency(expense.amount)}</p>
                  <p className="text-sm text-muted-foreground">{expense.currency}</p>
                </div>
              </div>

              <Separator />

              {/* Informações do Fornecedor */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  <h3 className="font-semibold">Fornecedor</h3>
                </div>
                
                {isEditing ? (
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="supplier_name">Nome do Fornecedor</Label>
                      <Input
                        id="supplier_name"
                        value={formData.supplier_name}
                        onChange={(e) => setFormData(prev => ({ ...prev, supplier_name: e.target.value }))}
                        placeholder="Nome do fornecedor"
                      />
                    </div>
                    <div>
                      <Label htmlFor="supplier_document">Documento (CPF/CNPJ)</Label>
                      <Input
                        id="supplier_document"
                        value={formData.supplier_document}
                        onChange={(e) => setFormData(prev => ({ ...prev, supplier_document: e.target.value }))}
                        placeholder="CPF ou CNPJ do fornecedor"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <p className="font-medium">{expense.supplier_name}</p>
                    {expense.supplier_document && (
                      <p className="text-sm text-muted-foreground">{expense.supplier_document}</p>
                    )}
                  </div>
                )}
              </div>

              <Separator />

              {/* Detalhes da Despesa */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  <h3 className="font-semibold">Detalhes</h3>
                </div>

                {isEditing ? (
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="amount">Valor</Label>
                      <Input
                        id="amount"
                        type="text"
                        inputMode="decimal"
                        value={currencyFormat.displayValue}
                        onChange={currencyFormat.handleChange}
                        onBlur={currencyFormat.handleBlur}
                        placeholder="R$ 0,00"
                        className="font-mono"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="category">Categoria</Label>
                      <Select
                        value={formData.category_id}
                        onValueChange={(value) => setFormData(prev => ({ ...prev, category_id: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione uma categoria" />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              <div className="flex items-center gap-2">
                                <div
                                  className="w-3 h-3 rounded-full"
                                  style={{ backgroundColor: category.color }}
                                />
                                {category.name}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="description">Descrição</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Descrição da despesa"
                        rows={3}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {expense.expense_categories && (
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: expense.expense_categories.color }}
                        />
                        <span className="text-sm font-medium">{expense.expense_categories.name}</span>
                      </div>
                    )}
                    
                    {expense.description && (
                      <p className="text-sm text-muted-foreground">{expense.description}</p>
                    )}
                  </div>
                )}
              </div>

              <Separator />

              {/* Datas e Pagamento */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4" />
                  <h3 className="font-semibold">Datas e Pagamento</h3>
                </div>

                {isEditing ? (
                  <div className="space-y-3">
                    <div>
                      <Label>Data de Vencimento</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !selectedDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {selectedDate ? format(selectedDate, "PPP", { locale: ptBR }) : "Selecione uma data"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={selectedDate}
                            onSelect={(date) => {
                              setSelectedDate(date);
                              setFormData(prev => ({
                                ...prev,
                                due_date: date ? format(date, 'yyyy-MM-dd') : ''
                              }));
                            }}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    <div>
                      <Label htmlFor="payment_method">Método de Pagamento</Label>
                      <Select
                        value={formData.payment_method_id}
                        onValueChange={(value) => setFormData(prev => ({ ...prev, payment_method_id: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um método" />
                        </SelectTrigger>
                        <SelectContent>
                          {paymentMethods.map((method) => (
                            <SelectItem key={method.id} value={method.id}>
                              {method.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="status">Status</Label>
                      <Select
                        value={formData.status}
                        onValueChange={(value) => setFormData(prev => ({ ...prev, status: value as any }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pending">Pendente</SelectItem>
                          <SelectItem value="paid">Pago</SelectItem>
                          <SelectItem value="overdue">Vencido</SelectItem>
                          <SelectItem value="canceled">Cancelado</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {expense.due_date && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Vencimento:</span>
                        <span className="text-sm font-medium">
                          {format(new Date(expense.due_date), "PPP", { locale: ptBR })}
                        </span>
                      </div>
                    )}
                    
                    {expense.paid_at && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Pago em:</span>
                        <span className="text-sm font-medium">
                          {format(new Date(expense.paid_at), "PPP", { locale: ptBR })}
                        </span>
                      </div>
                    )}

                    {expense.payment_methods && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Método de Pagamento:</span>
                        <span className="text-sm font-medium">{expense.payment_methods.name}</span>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Criado em:</span>
                      <span className="text-sm font-medium">
                        {format(new Date(expense.created_at), "PPP", { locale: ptBR })}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">Despesa não encontrada</p>
            </div>
          </div>
        )}

        {/* Botões de Ação */}
        {expense && (
          <div className="flex items-center justify-between pt-4 border-t flex-shrink-0">
            <div className="flex gap-2">
              {!isEditing && expense.status !== 'canceled' && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                    disabled={isSaving}
                  >
                    <Edit3 className="h-4 w-4 mr-2" />
                    Editar
                  </Button>

                  {expense.status === 'pending' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleMarkAsPaid}
                      disabled={isSaving}
                    >
                      <CreditCard className="h-4 w-4 mr-2" />
                      Marcar como Pago
                    </Button>
                  )}

                  {expense.status !== 'paid' && (
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={handleCancel}
                      disabled={isSaving}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancelar
                    </Button>
                  )}

                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleDelete}
                    disabled={isSaving}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Deletar
                  </Button>
                </>
              )}
            </div>

            <div className="flex gap-2">
              {isEditing ? (
                <>
                  <Button
                    variant="outline"
                    onClick={() => setIsEditing(false)}
                    disabled={isSaving}
                  >
                    Cancelar
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    Salvar
                  </Button>
                </>
              ) : (
                <Button variant="outline" onClick={handleClose}>
                  Fechar
                </Button>
              )}
            </div>
          </div>
        )}
      </DialogContent>

      {/* Dialog de confirmação para deletar */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30">
              <Trash2 className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
            <DialogTitle className="text-lg font-semibold">
              Deletar Despesa
            </DialogTitle>
            <DialogDescription className="text-sm text-muted-foreground">
              Tem certeza que deseja deletar esta despesa? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2 pt-4">
            <Button
              variant="outline"
              onClick={handleCancelDelete}
              disabled={isSaving}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirmDelete}
              disabled={isSaving}
              className="flex-1"
            >
              {isSaving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deletando...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Deletar
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}
