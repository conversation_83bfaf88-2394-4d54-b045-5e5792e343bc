export interface StudentDashboardData {
  proximasAulas: number
  tarefasPendentes: number
  mensagens: number
}

export interface TeacherDashboardData {
  meusAlunos: number
  aulasProgramadas: number
  avaliacoesPendentes: number
}

export interface InstructorDashboardData {
  alunosAtribuidos: number
  aulasHoje: number
  graduacoesPendentes: number
  presencasRegistrar: number
}

export interface RecentActivity {
  id: string
  description: string
  timestamp: string
  type: 'attendance' | 'grade' | 'message' | 'assignment' | 'evaluation'
}

export type UserRole = 'student' | 'teacher' | 'instructor' | 'admin' 