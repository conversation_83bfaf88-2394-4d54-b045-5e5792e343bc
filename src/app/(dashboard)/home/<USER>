# Página Home - Dashboard Multi-Role

Esta implementação fornece uma página home personalizada baseada na role do usuário, incluindo suporte para **instrutores**, **professores** e **estudantes**.

## Estrutura da Implementação

### Componentes

- **`StudentDashboard`**: Dashboard específico para estudantes
- **`TeacherDashboard`**: Dashboard específico para professores  
- **`InstructorDashboard`**: Dashboard específico para instrutores

### Server Actions

- **`getStudentDashboardData()`**: Busca dados específicos do estudante
- **`getTeacherDashboardData()`**: Busca dados específicos do professor
- **`getInstructorDashboardData()`**: Busca dados específicos do instrutor
- **`getRecentActivities()`**: Busca atividades recentes baseadas na role

### Tipagem

Todos os tipos estão centralizados no arquivo `types.ts`:

```typescript
export interface StudentDashboardData {
  proximasAulas: number
  tarefasPendentes: number
  mensagens: number
}

export interface TeacherDashboardData {
  meusAlunos: number
  aulasProgramadas: number
  avaliacoesPendentes: number
}

export interface InstructorDashboardData {
  alunosAtribuidos: number
  aulasHoje: number
  graduacoesPendentes: number
  presencasRegistrar: number
}
```

## Cards de Dashboard por Role

### Estudante
- **Próximas Aulas**: Aulas agendadas
- **Tarefas Pendentes**: Tarefas a concluir
- **Mensagens**: Novas mensagens

### Professor
- **Meus Alunos**: Alunos sob responsabilidade
- **Aulas Programadas**: Próximas aulas agendadas
- **Avaliações Pendentes**: Avaliações a realizar

### Instrutor
- **Alunos Atribuídos**: Alunos sob orientação
- **Aulas Hoje**: Aulas programadas para hoje
- **Graduações Pendentes**: Avaliações de graduação
- **Presenças a Registrar**: Chamadas em aberto

## Funcionalidades Implementadas

### ✅ Controle de Acesso
- Admins são redirecionados para `/dashboard`
- Cada role tem acesso apenas ao seu dashboard específico
- Suporte para parâmetros de erro (`?erro=`)

### ✅ Dados Dinâmicos
- Busca dados reais do Supabase
- Fallback gracioso em caso de erro
- Contagem real de alunos atribuídos para instrutores

### ✅ Interface Responsiva
- Grid adaptativo para diferentes tamanhos de tela
- Layout otimizado para cada tipo de dashboard
- Componentes reutilizáveis

### ✅ Tipagem Completa
- TypeScript em todos os componentes
- Tipos centralizados e reutilizáveis
- Inferência de tipos automática

## Expansões Futuras

### Dados Reais a Implementar
Quando as tabelas relevantes estiverem disponíveis, os seguintes dados podem ser conectados:

```typescript
// Para estudantes
- Aulas da agenda
- Sistema de tarefas/assignments  
- Sistema de mensagens

// Para professores
- Relacionamento professor-aluno
- Sistema de agendamento de aulas
- Sistema de avaliações

// Para instrutores
- Sistema de agendamento diário
- Sistema de graduações/belts
- Sistema de presença/chamada
```

### Atividades Recentes
O sistema já está preparado para exibir atividades recentes com tipagem:

```typescript
export interface RecentActivity {
  id: string
  description: string
  timestamp: string
  type: 'attendance' | 'grade' | 'message' | 'assignment' | 'evaluation'
}
```

## Uso

A página é automaticamente renderizada baseada na role do usuário autenticado. Não requer configuração adicional além da autenticação padrão do sistema. 