import { redirect } from 'next/navigation';
import { getUserRole } from '@/services/user/role-service';
import { getCurrentUser, requireAuth } from '@/services/auth/actions/auth-actions';
import { AccessDeniedNotification } from '@/components/notifications/access-denied-notification';
import { 
  getStudentDashboardData, 
  getTeacherDashboardData, 
  getInstructorDashboardData,
  getRecentActivities 
} from './actions';
import { StudentDashboard, TeacherDashboard, InstructorDashboard } from './components';
import { 
  UserRole, 
  StudentDashboardData, 
  TeacherDashboardData, 
  InstructorDashboardData 
} from './types';

export const metadata = {
  title: 'Início | Apex SaaS',
  description: 'Área do usuário do sistema Apex SaaS',
};

function getRoleDisplayName(role: UserRole): string {
  switch (role) {
    case 'teacher':
      return 'Professor'
    case 'instructor':
      return 'Instrutor'
    case 'student':
      return 'Aluno'
    default:
      return 'Usuário'
  }
}

export default async function HomePage({ 
  searchParams 
}: { 
  searchParams: Promise<{ erro?: string }> 
}) {
  const params = await searchParams;
  
  const { user } = await requireAuth();
  
  const roleData = await getUserRole(user.id);
  const role = ('error' in roleData ? 'student' : roleData.role) as UserRole;
  
  if (role === 'admin' && !params.erro) {
    redirect('/dashboard');
  }

  let studentData: StudentDashboardData | null = null;
  let teacherData: TeacherDashboardData | null = null;
  let instructorData: InstructorDashboardData | null = null;

  switch (role) {
    case 'student':
      studentData = await getStudentDashboardData(user.id);
      break;
    case 'teacher':
      teacherData = await getTeacherDashboardData(user.id);
      break;
    case 'instructor':
      instructorData = await getInstructorDashboardData(user.id);
      break;
  }

  const recentActivities = await getRecentActivities(user.id, role);
  
  return (
    <div className="space-y-6">
      {params.erro && (
        <AccessDeniedNotification errorType={params.erro} />
      )}
      
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          Área do {getRoleDisplayName(role)}
        </h1>
        <p className="text-subtle">
          Bem-vindo à sua área pessoal no sistema Apex SaaS.
        </p>
      </div>
      
      {role === 'student' && studentData && (
        <StudentDashboard data={studentData} />
      )}
      
      {role === 'teacher' && teacherData && (
        <TeacherDashboard data={teacherData} />
      )}
      
      {role === 'instructor' && instructorData && (
        <InstructorDashboard data={instructorData} />
      )}
      
      <div className="rounded-xl">
        <div className="p-6">
          <h3 className="font-semibold mb-4">Atividades Recentes</h3>
          {recentActivities && recentActivities.length > 0 ? (
            <div className="space-y-3">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-center gap-3 text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-muted-foreground">
                    {activity.description}
                  </span>
                  <span className="text-xs text-muted-foreground ml-auto">
                    {activity.timestamp}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-muted-foreground">
              Nenhuma atividade registrada.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}