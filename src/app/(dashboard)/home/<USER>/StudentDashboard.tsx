import { StudentDashboardData } from '../types'

interface StudentDashboardProps {
  data: StudentDashboardData
}

export function StudentDashboard({ data }: StudentDashboardProps) {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <div className="rounded-xl border bg-card text-card-foreground shadow">
        <div className="p-6 flex flex-col gap-2">
          <h3 className="font-semibold">Próximas Aulas</h3>
          <div className="text-3xl font-bold">{data.proximasAulas}</div>
          <p className="text-muted-foreground text-sm">
            Aulas agendadas para você
          </p>
        </div>
      </div>
      
      <div className="rounded-xl border bg-card text-card-foreground shadow">
        <div className="p-6 flex flex-col gap-2">
          <h3 className="font-semibold">Tare<PERSON>s Pendentes</h3>
          <div className="text-3xl font-bold">{data.tarefasPendentes}</div>
          <p className="text-muted-foreground text-sm">
            Tarefas a serem concluídas
          </p>
        </div>
      </div>
      
      <div className="rounded-xl border bg-card text-card-foreground shadow">
        <div className="p-6 flex flex-col gap-2">
          <h3 className="font-semibold">Mensagens</h3>
          <div className="text-3xl font-bold">{data.mensagens}</div>
          <p className="text-muted-foreground text-sm">
            Novas mensagens recebidas
          </p>
        </div>
      </div>
    </div>
  )
} 