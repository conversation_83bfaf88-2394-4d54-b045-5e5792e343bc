'use server';

import { createClient } from '@/services/supabase/server';
import { FetchInstructorsParams, FetchInstructorsResult, Instructor } from '../types/types';
import { cache } from 'react';
import { BeltColor } from '@/components/belt';

export const fetchInstructors = cache(async (params: FetchInstructorsParams = {}): Promise<FetchInstructorsResult> => {
  try {
    const supabase = await createClient();
    
    const {
      skip = 0,
      take = 10,
      search = "",
      status = ["active"],
      branch = [],
      specialties = [],
      contractType = [],
      experienceLevel = [],
      belts = [],
      startDate,
      endDate
    } = params;
    
    // Buscar instrutores sem usar foreign key hints
    let instructorQuery = supabase
      .from('instructors')
      .select(`
        id,
        user_id,
        specialties,
        contract_type,
        payment_model,
        experience_years,
        created_at,
        updated_at,
        branch_id
      `, { count: 'exact' });

    // Aplicar filtros que não dependem de outras tabelas
    if (branch && branch.length > 0) {
      instructorQuery = instructorQuery.in("branch_id", branch);
    }
    
    if (specialties && specialties.length > 0) {
      instructorQuery = instructorQuery.overlaps("specialties", specialties);
    }
    
    if (contractType && contractType.length > 0) {
      instructorQuery = instructorQuery.in("contract_type", contractType);
    }

    // Filtro por faixas
    if (belts && belts.length > 0) {
      const { data: instructorIdsWithBelts } = await supabase
        .from('instructor_belts')
        .select('instructor_id, belt_levels!inner(belt_color)')
        .in('belt_levels.belt_color', belts)
        .order('awarded_at', { ascending: false });

      if (instructorIdsWithBelts && instructorIdsWithBelts.length > 0) {
        const instructorIds = Array.from(new Set(instructorIdsWithBelts.map(item => item.instructor_id)));
        instructorQuery = instructorQuery.in('id', instructorIds);
      } else {
        return {
          data: [],
          pagination: {
            total: 0,
            total_pages: 0,
            current_page: 1,
            page_size: take
          }
        };
      }
    }
    
    if (experienceLevel && experienceLevel.length > 0) {
      const firstLevel = experienceLevel[0];
      switch (firstLevel) {
        case 'beginner':
          instructorQuery = instructorQuery.lte('experience_years', 2);
          break;
        case 'intermediate':
          instructorQuery = instructorQuery.gte('experience_years', 3).lte('experience_years', 5);
          break;
        case 'advanced':
          instructorQuery = instructorQuery.gte('experience_years', 6).lte('experience_years', 10);
          break;
        case 'expert':
          instructorQuery = instructorQuery.gte('experience_years', 10);
          break;
      }
    }
    
    if (startDate) {
      instructorQuery = instructorQuery.gte('created_at', startDate.toISOString());
    }
    
    if (endDate) {
      const endOfDay = new Date(endDate);
      endOfDay.setHours(23, 59, 59, 999);
      instructorQuery = instructorQuery.lte('created_at', endOfDay.toISOString());
    }

    // Aplicar paginação
    instructorQuery = instructorQuery.range(skip, skip + take - 1);
    
    const { data: instructorsData, count: initialCount, error: instructorsError } = await instructorQuery;
    
    if (instructorsError) {
      console.error('❌ Erro ao buscar instrutores:', instructorsError);
      throw new Error('Falha ao buscar instrutores');
    }

    if (!instructorsData || instructorsData.length === 0) {
      return {
        data: [],
        pagination: {
          total: 0,
          total_pages: 0,
          current_page: Math.floor(skip / take) + 1,
          page_size: take
        }
      };
    }

    // Buscar dados das tabelas relacionadas separadamente
    const instructorIds = instructorsData.map(instructor => instructor.id);
    const userIds = instructorsData.map(instructor => instructor.user_id);
    const branchIds = Array.from(new Set(instructorsData.map(instructor => instructor.branch_id).filter(Boolean)));

    // Buscar usuários
    const { data: usersData, error: usersError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        full_name,
        first_name,
        last_name,
        avatar_url,
        phone,
        status
      `)
      .in('id', userIds);

    if (usersError) {
      console.error('❌ Erro ao buscar dados dos usuários:', usersError);
      throw new Error('Falha ao buscar dados dos usuários');
    }

    // Buscar branches
    const { data: branchesData } = await supabase
      .from('branches')
      .select('id, name')
      .in('id', branchIds);

    // Criar mapas para facilitar o join
    const usersMap = new Map();
    (usersData || []).forEach(user => {
      usersMap.set(user.id, user);
    });

    const branchesMap = new Map();
    (branchesData || []).forEach(branch => {
      branchesMap.set(branch.id, branch);
    });

    // Transformar os dados para o formato esperado pela UI
    const instructors: Instructor[] = await Promise.all(
      instructorsData.map(async (instructor: any) => {
        const user = usersMap.get(instructor.user_id) || {};
        const branch = branchesMap.get(instructor.branch_id) || {};
        
        // Buscar detalhes completos da faixa atual usando a função RPC
        let currentBelt: {
          belt_color: BeltColor;
          degree: number;
          label?: string | null;
          stripe_color?: string | null;
          show_center_line?: boolean | null;
          center_line_color?: string | null;
        } = {
          belt_color: "white" as BeltColor,
          degree: 0
        };

        if (instructor.id) {
          const { data: beltDetails } = await (supabase as any).rpc('get_instructor_current_belt_details', { 
            instructor_id_param: instructor.id 
          });
          
          if (Array.isArray(beltDetails) && beltDetails.length > 0) {
            const belt = beltDetails[0] as any;
            currentBelt = {
              belt_color: belt.belt_color as BeltColor,
              degree: belt.degree || 0,
              label: belt.label || null,
              stripe_color: belt.stripe_color || null,
              show_center_line: belt.show_center_line || null,
              center_line_color: belt.center_line_color || null,
            };
          }
        }
        
        return {
          id: instructor.id,
          user_id: instructor.user_id,
          name: user.full_name || "Nome não informado",
          email: user.email || "Email não informado",
          phone: user.phone || null,
          avatar: user.avatar_url || null,
          branch_id: branch.id || null,
          branch_name: branch.name || null,
          specialties: instructor.specialties || [],
          experience_years: instructor.experience_years || 0,
          contract_type: instructor.contract_type || null,
          payment_model: instructor.payment_model || null,
          is_active: user.status === 'active',
          status: user.status || 'active',
          created_at: instructor.created_at,
          updated_at: instructor.updated_at,
          current_belt: currentBelt.belt_color,
          current_belt_degree: currentBelt.degree,
          // Adicionar dados completos da faixa
          beltDetails: {
            label: currentBelt.label,
            stripe_color: currentBelt.stripe_color,
            show_center_line: currentBelt.show_center_line,
            center_line_color: currentBelt.center_line_color,
          }
        };
      })
    );

    // Aplicar filtros que dependem dos dados dos usuários
    let filteredInstructors = instructors;
    if (status.length > 0 && !status.includes("all")) {
      if (!(status.includes("active") && status.includes("inactive") && status.includes("suspended"))) {
        const statusValues = status.map(s => {
          switch (s) {
            case "active": return "active";
            case "inactive": return "inactive";
            case "suspended": return "suspended";
            default: return "active";
          }
        });
        
        filteredInstructors = instructors.filter(instructor => 
          statusValues.includes(instructor.status)
        );
      }
    }

    // Aplicar filtro de busca por nome ou email
    if (search && search.trim() !== "") {
      const trimmedSearch = search.trim().toLowerCase();
      
      filteredInstructors = filteredInstructors.filter(instructor => {
        if (trimmedSearch.includes('@')) {
          return instructor.email.toLowerCase().includes(trimmedSearch);
        } else {
          return instructor.name.toLowerCase().includes(trimmedSearch);
        }
      });
      
      // Para busca, precisamos ajustar a lógica de contagem e paginação
      const totalFiltered = filteredInstructors.length;
      
      // Aplicar paginação manual nos resultados filtrados
      const startIndex = skip;
      const endIndex = skip + take;
      filteredInstructors = filteredInstructors.slice(startIndex, endIndex);
      
      return {
        data: filteredInstructors,
        pagination: {
          total: totalFiltered,
          total_pages: Math.ceil(totalFiltered / take),
          current_page: Math.floor(skip / take) + 1,
          page_size: take
        }
      };
    }
    
    return {
      data: filteredInstructors,
      pagination: {
        total: initialCount || 0,
        total_pages: Math.ceil((initialCount || 0) / take),
        current_page: Math.floor(skip / take) + 1,
        page_size: take
      }
    };
  } catch (error) {
    console.error('❌ Erro geral ao buscar instrutores:', error);
    return {
      data: [],
      pagination: {
        total: 0,
        total_pages: 0,
        current_page: 1,
        page_size: 10
      }
    };
  }
}); 