'use server';

import { createClient } from '@/services/supabase/server';
import { cache } from 'react';

interface SpecialtiesResponse {
  success: boolean;
  data: string[];
  error?: string;
}

/**
 * Busca todas as especialidades dos instrutores cadastradas no sistema
 */
export const fetchSpecialties = cache(async (): Promise<SpecialtiesResponse> => {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase.rpc('get_all_instructor_specialties');
    
    if (error) {
      console.error('Erro ao buscar especialidades:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
    
    // Se não tiver especialidades, retorna lista vazia
    if (!data || data.length === 0) {
      return {
        success: true,
        data: [],
        error: 'Nenhuma especialidade encontrada'
      };
    }
    
    // Ordenar alfabeticamente
    return {
      success: true,
      data: (data as string[]).sort()
    };
  } catch (error) {
    console.error('Erro ao buscar especialidades:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido ao buscar especialidades',
      data: []
    };
  }
}); 