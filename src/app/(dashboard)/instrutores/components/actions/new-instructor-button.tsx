'use client';

import Link from 'next/link';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { usePermission } from '@/hooks/user/Permissions/usePermission';

export function NewInstructorButton() {
  const { isAllowed } = usePermission('students', 'create');
  
  if (!isAllowed) return null;

  return (
    <Button asChild size="sm" className="bg-white text-black hover:bg-gray-100">
      <Link href="/instrutores/novo" className="flex items-center gap-2">
        <Plus className="h-4 w-4" />
        Novo instrutor
      </Link>
    </Button>
  );
} 