'use client';

import Link from 'next/link';
import { UserPlus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { usePermission } from '@/hooks/user/Permissions/usePermission';

export function EmptyState() {
  const { isAllowed: canCreate } = usePermission('students', 'create');

  return (
    <div className="flex min-h-[400px] flex-col items-center justify-center rounded-md border border-dashed p-8 text-center animate-in fade-in-50">
      <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center text-center">
        <div className="flex h-20 w-20 items-center justify-center rounded-full bg-muted">
          <UserPlus className="h-10 w-10 text-muted-foreground" />
        </div>
        
        <h3 className="mt-4 text-lg font-semibold">Nenhum instrutor encontrado</h3>
        <p className="mb-4 mt-2 text-sm text-muted-foreground">
          Não foram encontrados instrutores com os filtros atuais. Tente mudar os filtros ou adicione um novo instrutor.
        </p>
        
        {canCreate && (
          <Button asChild>
            <Link href="/instrutores/novo">Adicionar Instrutor</Link>
          </Button>
        )}
      </div>
    </div>
  );
} 