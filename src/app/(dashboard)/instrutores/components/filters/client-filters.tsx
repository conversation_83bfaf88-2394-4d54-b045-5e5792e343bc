"use client";

import { useState, useEffect } from "react";
import { Branch, InstructorFilterState, InstructorStats } from "../../types/types";
import { useInstructorsFilter } from "../../hooks/use-instructors-filter";
import { FilterBar } from "./filter-bar";
import { SearchInput } from "./search-input";

interface ClientFiltersProps {
  branches?: Branch[];
  initialStats?: InstructorStats;
}

export function ClientFilters({ branches = [], initialStats }: ClientFiltersProps) {
  const { filters, updateFilters } = useInstructorsFilter();
  
  // Estado local para a busca com debounce
  const [searchValue, setSearchValue] = useState(filters.search || '');
  
  // Mapeamento do estado dos filtros para o formato esperado pelo FilterBar
  const [filterBarState, setFilterBarState] = useState<InstructorFilterState>({
    status: filters.status || [],
    specialties: filters.specialties || [],
    branch: filters.branch || [],
    contractType: filters.contractType || [],
    experienceLevel: filters.experienceLevel || [],
    belts: filters.belts || [],
    startDate: filters.startDate,
    endDate: filters.endDate,
    page: filters.page,
    limit: filters.limit,
  });
  
  // Sincronizar os filtros do hook com o estado local do FilterBar
  useEffect(() => {
    setFilterBarState({
      status: filters.status || [],
      specialties: filters.specialties || [],
      branch: filters.branch || [],
      contractType: filters.contractType || [],
      experienceLevel: filters.experienceLevel || [],
      belts: filters.belts || [],
      startDate: filters.startDate,
      endDate: filters.endDate,
      page: filters.page,
      limit: filters.limit,
    });
  }, [filters]);
  
  // Atualizar searchValue quando filters.search mudar
  useEffect(() => {
    setSearchValue(filters.search || '');
  }, [filters.search]);
  
  // Função para atualizar o campo de busca
  const handleSearchChange = (newSearchValue: string) => {
    updateFilters({ 
      search: newSearchValue,
      page: 1 
    });
  };
  
  // Função para atualizar os filtros quando o FilterBar mudar
  const handleFilterChange = (newFilters: InstructorFilterState) => {
    setFilterBarState(newFilters);
    
    // Atualizando os filtros no hook
    updateFilters({
      status: newFilters.status,
      specialties: newFilters.specialties,
      branch: newFilters.branch,
      contractType: newFilters.contractType,
      experienceLevel: newFilters.experienceLevel,
      belts: newFilters.belts,
      startDate: newFilters.startDate,
      endDate: newFilters.endDate,
      // Manter o campo search atual
      search: filters.search,
      // Manter a página 1 ao mudar filtros
      page: 1
    });
  };
  
  return (
    <div className="space-y-4">
      <SearchInput
        value={searchValue}
        onChange={handleSearchChange}
        placeholder="Buscar instrutores..."
      />
      
      <FilterBar 
        filters={filterBarState}
        onFilterChange={handleFilterChange}
        branches={branches}
        initialStats={initialStats}
      />
    </div>
  );
} 