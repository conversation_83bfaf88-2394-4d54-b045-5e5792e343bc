"use client";

import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { useState, useEffect } from "react";

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export function SearchInput({ 
  value, 
  onChange, 
  placeholder = "Buscar instrutores...",
  className = ""
}: SearchInputProps) {
  const [searchValue, setSearchValue] = useState(value);

  // Sincronizar com o valor externo
  useEffect(() => {
    setSearchValue(value);
  }, [value]);

  // Debounce para pesquisa
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (searchValue !== value) {
        onChange(searchValue);
      }
    }, 300);

    return () => clearTimeout(timeout);
  }, [searchValue, onChange, value]);

  return (
    <div className={`relative ${className}`}>
      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      <Input
        placeholder={placeholder}
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        className="pl-9 bg-background w-full"
      />
    </div>
  );
} 