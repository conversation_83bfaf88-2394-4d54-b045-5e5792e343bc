'use client';

import { useState } from 'react';
import Link from 'next/link';
import { MoreHorizontal, Trash2 } from 'lucide-react';
import { Instructor } from '../../types/types';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import { deleteInstructorCompletely } from '../../actions/bulk-actions';
import { useRowActionAnimation } from '../../hooks/use-row-action-animation';
import { RowActionAnimation } from '@/app/(dashboard)/alunos/components/list/row-action-animation';
import { useAdminStatus } from '@/hooks/user/Permissions/useAdminStatus';

interface InstructorRowProps {
  instructor: Instructor;
  onRefresh: () => void;
}

interface DeleteConfirmation {
  isOpen: boolean;
  instructor: Instructor | null;
}

export function InstructorRow({ instructor, onRefresh }: InstructorRowProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState<DeleteConfirmation>({ 
    isOpen: false, 
    instructor: null 
  });
  
  const { isAdmin } = useAdminStatus();
  
  const {
    isUserAnimating,
    getUserAction,
    executeActionWithAnimation
  } = useRowActionAnimation();

  // Função para formatar nomes de modelo de pagamento
  const formatPaymentModel = (paymentModel: string | null | undefined): string => {
    switch (paymentModel) {
      case 'hora_aula': return 'Por Hora/Aula';
      case 'salario_mensal': return 'Salário Mensal';
      case 'comissao': return 'Comissão';
      case 'participacao_lucros': return 'Participação nos Lucros';
      default: return paymentModel || '-';
    }
  };

  const handleStatusToggle = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/instructors/${instructor.id}/toggle-status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Falha ao atualizar status');
      }

      toast.success('Sucesso', {
        description: `Status do instrutor atualizado com sucesso`,
      });
      
      onRefresh();
    } catch (error) {
      toast.error('Erro', {
        description: 'Não foi possível atualizar o status do instrutor',
      });
      console.error('Erro ao atualizar status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para deletar permanentemente um instrutor individual
  const handleDeletePermanently = async (instructor: Instructor) => {
    setDeleteConfirmation({ isOpen: true, instructor });
  };

  const confirmDelete = async () => {
    if (!deleteConfirmation.instructor) return;
    
    const instructor = deleteConfirmation.instructor;
    setDeleteConfirmation({ isOpen: false, instructor: null });
    
    try {
      const result = await executeActionWithAnimation(
        instructor.user_id,
        'delete-permanently',
        () => deleteInstructorCompletely(instructor.user_id)
      );
      
      if (result.success) {
        toast.success(result.message);
        onRefresh();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error("Erro ao deletar instrutor: " + (error instanceof Error ? error.message : "Erro desconhecido"));
    }
  };

  return (
    <>
      <tr className="border-b hover:bg-muted/50 relative">
        {/* Animação de ação sobrepondo a linha */}
        {isUserAnimating(instructor.user_id) && (
          <td colSpan={6} className="p-0 relative">
            <RowActionAnimation
              action={getUserAction(instructor.user_id) || 'generic'}
              isVisible={isUserAnimating(instructor.user_id)}
            />
          </td>
        )}
        
        {!isUserAnimating(instructor.user_id) && (
          <>
            <td className="p-2 align-middle">
              <Link 
                href={`/perfil/${instructor.user_id}?from=instrutores`}
                className="font-medium hover:underline"
              >
                {instructor.name}
              </Link>
            </td>
            <td className="p-2 align-middle hidden md:table-cell">
              {instructor.specialties?.join(', ') || '-'}
            </td>
            <td className="p-2 align-middle hidden md:table-cell">
              {instructor.experience_years} {instructor.experience_years === 1 ? 'ano' : 'anos'}
            </td>
            <td className="p-2 align-middle hidden lg:table-cell">
              {instructor.contract_type || '-'}
            </td>
            <td className="p-2 align-middle hidden lg:table-cell">
              {formatPaymentModel(instructor.payment_model)}
            </td>
            <td className="p-2 align-middle">
              <Badge variant={instructor.is_active ? 'default' : 'secondary'}>
                {instructor.is_active ? 'Ativo' : 'Inativo'}
              </Badge>
            </td>
            <td className="p-2 align-middle text-right">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-8 w-8 p-0"
                    disabled={isUserAnimating(instructor.user_id)}
                  >
                    <span className="sr-only">Abrir menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link href={`/perfil/${instructor.user_id}?from=instrutores`}>Visualizar</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href={`/perfil/${instructor.user_id}?from=instrutores`}>Editar</Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleStatusToggle}
                    disabled={isLoading || isUserAnimating(instructor.user_id)}
                  >
                    {instructor.is_active ? 'Desativar' : 'Ativar'}
                  </DropdownMenuItem>
                  {/* Opção de deletar permanentemente - apenas para admins */}
                  {isAdmin && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => handleDeletePermanently(instructor)}
                        disabled={isUserAnimating(instructor.user_id)}
                        className="text-red-600 focus:text-red-600 focus:bg-red-50 cursor-pointer"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Deletar Permanentemente
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </td>
          </>
        )}
      </tr>

      {/* Dialog de confirmação para exclusão permanente */}
      <AlertDialog 
        open={deleteConfirmation.isOpen} 
        onOpenChange={(open) => setDeleteConfirmation({ isOpen: open, instructor: null })}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Deletar Permanentemente</AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="space-y-2">
                <p>
                  Tem certeza que deseja deletar permanentemente o instrutor{' '}
                  <strong>{deleteConfirmation.instructor?.name}</strong>?
                </p>
                <p>
                  Esta ação é <strong>irreversível</strong> e removerá:
                </p>
                <ul className="mt-2 space-y-1 text-sm list-disc list-inside">
                  <li>Todos os dados do instrutor</li>
                  <li>Histórico de turmas (se não houver turmas ativas)</li>
                  <li>Conta de usuário do sistema</li>
                </ul>
                <p className="text-red-600 font-semibold">
                  Esta ação não pode ser desfeita!
                </p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              Sim, deletar permanentemente
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 