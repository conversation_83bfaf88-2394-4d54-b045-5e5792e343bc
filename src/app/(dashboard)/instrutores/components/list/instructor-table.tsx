'use client';

import { useState } from 'react';
import Link from "next/link";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Instructor, InstructorUser } from "../../types/types";
import { Button } from "@/components/ui/button";
import { 
  MoreHorizontal, 
  Mail, 
  Phone, 
  MapPin, 
  Award, 
  Clock, 
  FileText,
  CreditCard,
  CheckCircle,
  XCircle,
  Star,
  Trash2
} from "lucide-react";
import { beltColorTranslation, BeltColor } from "@/components/belt";
import { BeltWithDetails } from "@/components/belt/BeltWithDetails";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Checkbox } from "@/components/ui/checkbox";
import {
  TooltipContent,
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { formatPhoneNumber } from "@/hooks/form/usePhoneFormat";
import { toast } from "sonner";
import { AnimatePresence, motion } from "framer-motion";
import { useRowActionAnimation } from "../../hooks/use-row-action-animation";
import { RowActionAnimation, RowRemovalAnimation } from "@/app/(dashboard)/alunos/components/list/row-action-animation";
import { deleteInstructorCompletely } from "../../actions/bulk-actions";
import { useAdminStatus } from "@/hooks/user/Permissions/useAdminStatus";

interface InstructorTableProps {
  instructors: Instructor[];
  isSelecting?: boolean;
  selectedUsers?: InstructorUser[];
  onSelectUser?: (user: InstructorUser) => void;
  onRefresh?: () => void;
}

interface DeleteConfirmation {
  isOpen: boolean;
  instructor: Instructor | null;
}

export function InstructorTable({ 
  instructors, 
  isSelecting = false, 
  selectedUsers = [], 
  onSelectUser,
  onRefresh 
}: InstructorTableProps) {
  const [deleteConfirmation, setDeleteConfirmation] = useState<DeleteConfirmation>({ 
    isOpen: false, 
    instructor: null 
  });
  
  const { isAdmin } = useAdminStatus();
  const {
    isUserAnimating,
    isUserRemoving,
    getUserAction,
    executeActionWithAnimation,
    removeFromList
  } = useRowActionAnimation();

  // Função para gerar as iniciais do nome
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Função para obter cor do badge de especialidade
  const getSpecialtyBadgeVariant = (specialty: string) => {
    const specialtyColors: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      'Jiu-Jitsu': 'default',
      'Muay Thai': 'secondary',
      'Boxe': 'destructive',
      'MMA': 'outline',
    };
    return specialtyColors[specialty] || 'outline';
  };

  // Função para obter variante do badge de contrato
  const getContractBadgeVariant = (contractType: string | null): "default" | "secondary" | "destructive" | "outline" => {
    switch (contractType) {
      case 'CLT': return 'default';
      case 'PJ': return 'secondary';
      case 'Freelancer': return 'outline';
      default: return 'outline';
    }
  };

  // Função para formatar nomes de modelo de pagamento
  const formatPaymentModel = (paymentModel: string | null | undefined): string => {
    switch (paymentModel) {
      case 'hora_aula': return 'Por Hora/Aula';
      case 'salario_mensal': return 'Salário Mensal';
      case 'comissao': return 'Comissão';
      case 'participacao_lucros': return 'Participação nos Lucros';
      default: return paymentModel || 'Não informado';
    }
  };

  // Função para obter variante do badge de pagamento
  const getPaymentBadgeVariant = (paymentModel: string | null | undefined): "default" | "secondary" | "destructive" | "outline" => {
    switch (paymentModel) {
      case 'salario_mensal': return 'default';
      case 'hora_aula': return 'secondary';
      case 'comissao': return 'outline';
      case 'participacao_lucros': return 'destructive';
      default: return 'outline';
    }
  };

  // Função para obter ícone do tipo de contrato
  const getContractIcon = (contractType: string | null) => {
    switch (contractType) {
      case 'CLT': return <FileText className="h-3 w-3" />;
      case 'PJ': return <CreditCard className="h-3 w-3" />;
      case 'Freelancer': return <Star className="h-3 w-3" />;
      default: return <FileText className="h-3 w-3" />;
    }
  };
  
  // Lidar com a alteração de status
  const handleStatusToggle = async (instructorId: string) => {
    try {
      const response = await fetch(`/api/instructors/${instructorId}/toggle-status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Falha ao atualizar status');
      }
      
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
    }
  };

  // Função para deletar permanentemente um instrutor individual
  const handleDeletePermanently = async (instructor: Instructor) => {
    setDeleteConfirmation({ isOpen: true, instructor });
  };

  const confirmDelete = async () => {
    if (!deleteConfirmation.instructor) return;
    
    const instructor = deleteConfirmation.instructor;
    setDeleteConfirmation({ isOpen: false, instructor: null });
    
    try {
      const result = await executeActionWithAnimation(
        instructor.user_id,
        'delete-permanently',
        () => deleteInstructorCompletely(instructor.user_id)
      );
      
      if (result.success) {
        toast.success(result.message);
        if (onRefresh) {
          onRefresh();
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error("Erro ao deletar instrutor: " + (error instanceof Error ? error.message : "Erro desconhecido"));
    }
  };
  
  // Converter instrutor para o formato do seletor de usuários
  const mapInstructorToUser = (instructor: Instructor): InstructorUser => {
    return {
      id: instructor.id,
      name: instructor.name,
      email: instructor.email,
      avatar: instructor.avatar || undefined,
      specialties: instructor.specialties,
      contract_type: instructor.contract_type || undefined,
      payment_model: instructor.payment_model || undefined,
      experience_years: instructor.experience_years
    };
  };
  
  // Verificar se todos os instrutores estão selecionados
  const areAllSelected = 
    instructors.length > 0 && 
    selectedUsers.length === instructors.length &&
    instructors.every(instructor => 
      selectedUsers.some(user => user.id === instructor.id)
    );

  // Selecionar ou desselecionar todos os instrutores
  const toggleSelectAll = () => {
    if (!onSelectUser) return;
    
    if (areAllSelected) {
      // Desselecionar todos
      instructors.forEach(instructor => {
        const user = mapInstructorToUser(instructor);
        onSelectUser(user);
      });
    } else {
      // Selecionar todos que não estão selecionados
      instructors.forEach(instructor => {
        const isSelected = selectedUsers.some(user => user.id === instructor.id);
        if (!isSelected) {
          const user = mapInstructorToUser(instructor);
          onSelectUser(user);
        }
      });
    }
  };

  return (
    <>
      <TooltipProvider>
        <div className="rounded-lg border bg-card shadow-sm">
          <Table>
            <TableHeader>
              <TableRow className="border-b">
                {isSelecting && (
                  <TableHead className="w-12">
                    <Checkbox 
                      checked={areAllSelected}
                      onCheckedChange={() => toggleSelectAll()}
                      className="opacity-90"
                    />
                  </TableHead>
                )}
                <TableHead className="font-semibold">Instrutor</TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Award className="h-4 w-4" />
                    Faixa
                  </div>
                </TableHead>
                <TableHead className="font-semibold">Especialidades</TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Experiência
                  </div>
                </TableHead>
                <TableHead className="hidden lg:table-cell font-semibold w-32">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Contrato
                  </div>
                </TableHead>
                <TableHead className="hidden lg:table-cell font-semibold w-36">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Pagamento
                  </div>
                </TableHead>
                <TableHead className="font-semibold">Status</TableHead>
                <TableHead className="text-right font-semibold">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <AnimatePresence mode="popLayout">
                {instructors.map((instructor) => {
                  const isSelected = selectedUsers.some(user => user.id === instructor.id);
                  
                  // Verificar se este instrutor está sendo removido
                  if (isUserRemoving(instructor.user_id)) {
                    const columnCount = isSelecting ? 9 : 8;
                    return (
                      <RowRemovalAnimation
                        key={`removing-${instructor.user_id}`}
                        onComplete={() => removeFromList(instructor.user_id)}
                        colSpan={columnCount}
                      />
                    );
                  }
                  
                  return (
                    <motion.tr
                      key={instructor.id}
                      layout
                      initial={{ opacity: 1 }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2 }}
                      className="relative hover:bg-muted/50 transition-colors"
                    >
                      {/* Animação de ação sobrepondo a linha */}
                      <AnimatePresence>
                        {isUserAnimating(instructor.user_id) && (
                          <motion.td
                            colSpan={isSelecting ? 9 : 8}
                            className="p-0 absolute inset-0 z-10"
                          >
                            <RowActionAnimation
                              action={getUserAction(instructor.user_id) || 'generic'}
                              isVisible={isUserAnimating(instructor.user_id)}
                            />
                          </motion.td>
                        )}
                      </AnimatePresence>

                      {isSelecting && (
                        <TableCell className="w-12">
                          <Checkbox 
                            checked={isSelected}
                            disabled={isUserAnimating(instructor.user_id)}
                            onCheckedChange={() => {
                              if (onSelectUser && !isUserAnimating(instructor.user_id)) {
                                onSelectUser(mapInstructorToUser(instructor));
                              }
                            }}
                            className="border-gray-300 dark:border-gray-500"
                          />
                        </TableCell>
                      )}
                      <TableCell>
                        <div className="flex items-center gap-3 min-w-0">
                          <Avatar className="h-10 w-10 border-2 border-muted">
                            <AvatarImage src={instructor.avatar || ""} alt={instructor.name} />
                            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
                              {getInitials(instructor.name)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <Link 
                              href={`/perfil/${instructor.user_id}?from=instrutores`} 
                              className="font-semibold text-foreground hover:text-primary transition-colors hover:underline block truncate"
                            >
                              {instructor.name}
                            </Link>
                            <div className="flex items-center gap-1 text-sm text-muted-foreground mt-1">
                              <Mail className="h-3 w-3 flex-shrink-0" />
                              <span className="truncate">{instructor.email}</span>
                            </div>
                            {instructor.phone && (
                              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                <Phone className="h-3 w-3 flex-shrink-0" />
                                <span>{formatPhoneNumber(instructor.phone)}</span>
                              </div>
                            )}
                            {instructor.branch_name && (
                              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                <MapPin className="h-3 w-3 flex-shrink-0" />
                                <span className="truncate">{instructor.branch_name}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {instructor.current_belt ? (
                            <TooltipRoot>
                              <TooltipTrigger asChild>
                                <div className="flex items-center gap-2 cursor-help">
                                  <BeltWithDetails 
                                    color={instructor.current_belt as BeltColor} 
                                    degree={instructor.current_belt_degree}
                                    label={instructor.beltDetails?.label}
                                    stripeColor={instructor.beltDetails?.stripe_color}
                                    showCenterLine={instructor.beltDetails?.show_center_line}
                                    centerLineColor={instructor.beltDetails?.center_line_color}
                                    size="md" 
                                  />
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>
                                  {instructor.beltDetails?.label || 
                                    `${beltColorTranslation[instructor.current_belt as BeltColor]} ${
                                      instructor.current_belt_degree ? `(${instructor.current_belt_degree}° grau)` : ''
                                    }`
                                  }
                                </p>
                              </TooltipContent>
                            </TooltipRoot>
                          ) : (
                            <span className="text-sm text-muted-foreground">Não informado</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1 max-w-[200px]">
                          {instructor.specialties && instructor.specialties.length > 0 ? (
                            instructor.specialties.map((specialty, index) => (
                              <Badge 
                                key={index}
                                variant={getSpecialtyBadgeVariant(specialty)}
                                className="text-xs font-medium"
                              >
                                {specialty}
                              </Badge>
                            ))
                          ) : (
                            <span className="text-sm text-muted-foreground">Não informado</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">
                            {instructor.experience_years}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            {instructor.experience_years === 1 ? 'ano' : 'anos'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">
                        {instructor.contract_type ? (
                          <TooltipRoot>
                            <TooltipTrigger asChild>
                              <div className="cursor-help">
                                <Badge 
                                  variant={getContractBadgeVariant(instructor.contract_type)}
                                  className="flex items-center gap-1 w-fit font-medium"
                                >
                                  {getContractIcon(instructor.contract_type)}
                                  <span className="text-xs">{instructor.contract_type}</span>
                                </Badge>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Tipo de contrato do instrutor</p>
                            </TooltipContent>
                          </TooltipRoot>
                        ) : (
                          <Badge variant="outline" className="text-xs text-muted-foreground">
                            Não informado
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">
                        {instructor.payment_model ? (
                          <TooltipRoot>
                            <TooltipTrigger asChild>
                              <div className="cursor-help">
                                <Badge 
                                  variant={getPaymentBadgeVariant(instructor.payment_model)}
                                  className="flex items-center gap-1 w-fit font-medium"
                                >
                                  <CreditCard className="h-3 w-3" />
                                  <span className="text-xs">{formatPaymentModel(instructor.payment_model)}</span>
                                </Badge>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Modelo de pagamento do instrutor</p>
                            </TooltipContent>
                          </TooltipRoot>
                        ) : (
                          <Badge variant="outline" className="text-xs text-muted-foreground">
                            Não informado
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={instructor.is_active ? "default" : "destructive"}
                          className="flex items-center gap-1 w-fit font-medium"
                        >
                          {instructor.is_active ? (
                            <>
                              <CheckCircle className="h-3 w-3" />
                              Ativo
                            </>
                          ) : (
                            <>
                              <XCircle className="h-3 w-3" />
                              Inativo
                            </>
                          )}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button 
                              variant="ghost" 
                              className="h-8 w-8 p-0 hover:bg-muted"
                              disabled={isUserAnimating(instructor.user_id)}
                            >
                              <span className="sr-only">Abrir menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            <DropdownMenuItem className="cursor-pointer">
                              <Link 
                                href={`/perfil/${instructor.user_id}?from=instrutores`} 
                                className="flex items-center w-full"
                              >
                                Ver perfil
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem className="cursor-pointer">
                              <Link 
                                href={`/perfil/${instructor.user_id}?from=instrutores`} 
                                className="flex items-center w-full"
                              >
                                Editar
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleStatusToggle(instructor.id)}
                              disabled={isUserAnimating(instructor.user_id)}
                              className="cursor-pointer"
                            >
                              {instructor.is_active ? 'Desativar' : 'Ativar'}
                            </DropdownMenuItem>
                            {/* Opção de deletar permanentemente - apenas para admins */}
                            {isAdmin && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  onClick={() => handleDeletePermanently(instructor)}
                                  disabled={isUserAnimating(instructor.user_id)}
                                  className="text-red-600 focus:text-red-600 focus:bg-red-50 cursor-pointer"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Deletar Permanentemente
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </motion.tr>
                  );
                })}
              </AnimatePresence>
            </TableBody>
          </Table>
        </div>
      </TooltipProvider>

      {/* Dialog de confirmação para exclusão permanente */}
      <AlertDialog 
        open={deleteConfirmation.isOpen} 
        onOpenChange={(open) => setDeleteConfirmation({ isOpen: open, instructor: null })}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Deletar Permanentemente</AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="space-y-2">
                <p>
                  Tem certeza que deseja deletar permanentemente o instrutor{' '}
                  <strong>{deleteConfirmation.instructor?.name}</strong>?
                </p>
                <p className="text-sm text-red-600 font-medium">
                  Esta ação é IRREVERSÍVEL e removerá:
                </p>
                <ul className="text-sm space-y-1 ml-4 list-disc list-inside">
                  <li>Todos os dados do instrutor</li>
                  <li>Histórico de turmas (se não houver turmas ativas)</li>
                  <li>Conta de usuário do sistema</li>
                </ul>
                <p className="text-sm font-bold text-red-600">
                  Esta ação não pode ser desfeita!
                </p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              Sim, deletar permanentemente
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 