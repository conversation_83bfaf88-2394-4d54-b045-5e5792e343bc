export function ListSkeleton() {
  return (
    <div className="space-y-4">
      <div className="h-10 rounded-md bg-muted animate-pulse"></div>
      
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} className="p-4 border rounded-md bg-card animate-pulse">
          <div className="flex items-start gap-4">
            <div className="h-12 w-12 rounded-full bg-muted"></div>
            <div className="space-y-3 flex-1">
              <div className="h-5 rounded bg-muted w-1/3"></div>
              <div className="h-4 rounded bg-muted w-2/3"></div>
              <div className="h-4 rounded bg-muted w-1/2"></div>
            </div>
            <div className="h-8 w-20 rounded bg-muted"></div>
          </div>
        </div>
      ))}
      
      <div className="h-10 flex justify-end gap-2">
        <div className="h-full w-24 rounded-md bg-muted animate-pulse"></div>
        <div className="h-full w-24 rounded-md bg-muted animate-pulse"></div>
      </div>
    </div>
  );
} 