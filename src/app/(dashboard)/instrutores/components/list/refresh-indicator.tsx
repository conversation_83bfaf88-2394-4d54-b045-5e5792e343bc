'use client';

import { motion } from "framer-motion";
import { RefreshCcw } from "lucide-react";
import { cn } from "@/lib/utils";

interface RefreshIndicatorProps {
  className?: string;
}

export function RefreshIndicator({ className }: RefreshIndicatorProps) {
  return (
    <div className={cn(
      "absolute top-0 left-0 right-0 z-10 flex justify-center", 
      className
    )}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-primary/10 backdrop-blur-sm text-primary rounded-b-md px-4 py-1.5 flex items-center gap-2 shadow-sm"
      >
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ 
            repeat: Infinity, 
            duration: 1.5, 
            ease: "linear" 
          }}
        >
          <RefreshCcw className="h-3.5 w-3.5" />
        </motion.div>
        <span className="text-xs font-medium">Atualizando dados...</span>
      </motion.div>
    </div>
  );
} 