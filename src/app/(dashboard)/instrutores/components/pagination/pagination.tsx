"use client";

import { ChevronLeft, ChevronRight } from "lucide-react";
import { generatePaginationRange } from "./utils";
import { useInstructorsFilter } from "../../hooks/use-instructors-filter";
import { PaginationInfo } from "./pagination-info";
import { PaginationButton } from "./pagination-button";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  per_page?: number;
  siblingCount?: number;
  onPageChange?: (page: number) => void;
}

export function Pagination({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  per_page,
  siblingCount = 1,
  onPageChange,
}: PaginationProps) {
  const { updateFilters } = useInstructorsFilter();
  
  // Não renderizar paginação se houver apenas uma página
  if (totalPages <= 1) return null;

  // Função para navegar para a página
  const handlePageChange = (pageNumber: number) => {
    if (onPageChange) {
      onPageChange(pageNumber);
    } else {
      updateFilters({ page: pageNumber });
    }
  };

  const paginationRange = generatePaginationRange(currentPage, totalPages, siblingCount);

  const onNext = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  const onPrevious = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  return (
    <div className="flex flex-col md:flex-row gap-4 items-center justify-between border-t pt-4">
      {/* Informações de paginação */}
      <PaginationInfo 
        currentPage={currentPage}
        pageSize={itemsPerPage}
        totalItems={totalItems}
      />

      {/* Botões de paginação */}
      <div className="flex items-center justify-center gap-2">
        <PaginationButton
          page="prev"
          onClick={onPrevious}
          isDisabled={currentPage === 1}
        >
          <ChevronLeft className="h-4 w-4" />
        </PaginationButton>

        {paginationRange.map((pageNumber, index) => {
          if (pageNumber === "...") {
            return (
              <span
                key={`dots-${index}`}
                className="px-2 text-sm text-muted-foreground"
              >
                ...
              </span>
            );
          }

          const page = pageNumber as number;
          const isActive = page === currentPage;
          
          return (
            <PaginationButton
              key={page}
              page={page}
              isActive={isActive}
              onClick={() => handlePageChange(page)}
            >
              {page}
            </PaginationButton>
          );
        })}

        <PaginationButton
          page="next"
          onClick={onNext}
          isDisabled={currentPage === totalPages}
        >
          <ChevronRight className="h-4 w-4" />
        </PaginationButton>
      </div>
    </div>
  );
} 