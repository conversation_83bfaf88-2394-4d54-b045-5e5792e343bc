'use client';

import { useEffect, useState } from 'react';
import { AlertTriangle, Check, ChevronDown, Users } from "lucide-react";
import { AnimatePresence, motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { User } from "./types";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useAdminStatus } from "@/hooks/user/Permissions/useAdminStatus";

interface SelectUsersProps {
  instructors?: User[];
  isSelecting: boolean;
  selectedUsers: User[];
  setSelectedUsers: React.Dispatch<React.SetStateAction<User[]>>;
  onClose: () => void;
  onAction: (action: string, ids: string[]) => void;
  showActionsDialog: boolean;
  setShowActionsDialog: React.Dispatch<React.SetStateAction<boolean>>;
}

export function SelectUsers({
  instructors = [],
  isSelecting,
  selectedUsers,
  setSelectedUsers,
  onClose,
  onAction,
  showActionsDialog,
  setShowActionsDialog,
}: SelectUsersProps) {
  const { isAdmin } = useAdminStatus();
  
  // Ações disponíveis
  const availableActions = [
    { id: "delete", label: "Excluir selecionados", destructive: true },
    { id: "inactivate", label: "Marcar como inativos", destructive: false },
    { id: "activate", label: "Marcar como ativos", destructive: false },
  ];
  
  // Adicionar opção de deletar permanentemente apenas para admins
  if (isAdmin) {
    availableActions.push({ 
      id: "delete-permanently", 
      label: "Deletar permanentemente", 
      destructive: true 
    });
  }
  
  // Ação selecionada
  const [selectedAction, setSelectedAction] = useState<string | null>(null);

  // Selecionando a primeira ação por padrão ao abrir o diálogo
  useEffect(() => {
    if (showActionsDialog && !selectedAction && availableActions.length > 0) {
      setSelectedAction(availableActions[0].id);
    }
  }, [showActionsDialog, selectedAction, availableActions]);

  // Fechar o diálogo de ações
  const handleCloseDialog = () => {
    setShowActionsDialog(false);
    setSelectedAction(null);
  };

  // Executar a ação selecionada
  const handleExecuteAction = () => {
    if (selectedAction && selectedUsers.length > 0) {
      onAction(selectedAction, selectedUsers.map((user) => user.id));
      handleCloseDialog();
    }
  };

  // Se não estiver no modo de seleção, não mostrar nada
  if (!isSelecting) {
    return null;
  }

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.2 }}
        className="bg-background border rounded-md p-3 mb-4 flex items-center justify-between"
      >
        <div className="flex items-center gap-3">
          <div className="bg-primary/10 p-2 rounded-full">
            <Users className="h-5 w-5 text-primary" />
          </div>
          <div>
            <p className="text-sm font-medium">
              {selectedUsers.length === 0
                ? "Nenhum instrutor selecionado"
                : `${selectedUsers.length} ${
                    selectedUsers.length === 1
                      ? "instrutor selecionado"
                      : "instrutores selecionados"
                  }`}
            </p>
            <p className="text-xs text-muted-foreground">
              Selecione os instrutores para realizar ações em massa
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {selectedUsers.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  Ações
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {availableActions.map((action, index) => (
                  <div key={action.id}>
                    {/* Separador antes das ações destrutivas para admins */}
                    {action.id === "delete-permanently" && (
                      <DropdownMenuSeparator />
                    )}
                    <DropdownMenuItem
                      className={
                        action.destructive
                          ? "text-destructive focus:text-destructive"
                          : ""
                      }
                      onClick={() => {
                        setSelectedAction(action.id);
                        setShowActionsDialog(true);
                      }}
                    >
                      {action.label}
                    </DropdownMenuItem>
                  </div>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          <Button variant="ghost" size="sm" onClick={onClose}>
            Cancelar
          </Button>
        </div>
      </motion.div>

      <AlertDialog open={showActionsDialog} onOpenChange={setShowActionsDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              Confirmar ação
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div>
                {selectedAction === "delete" && (
                  <p>
                    Tem certeza que deseja excluir {selectedUsers.length}{" "}
                    {selectedUsers.length === 1
                      ? "instrutor selecionado"
                      : "instrutores selecionados"}
                    ? Esta ação não pode ser desfeita.
                  </p>
                )}
                {selectedAction === "delete-permanently" && (
                  <div className="space-y-2">
                    <p>
                      Tem certeza que deseja <strong>deletar permanentemente</strong> {selectedUsers.length}{" "}
                      {selectedUsers.length === 1
                        ? "instrutor selecionado"
                        : "instrutores selecionados"}?
                    </p>
                    <p className="text-sm text-red-600 font-medium">
                      Esta ação é IRREVERSÍVEL e removerá:
                    </p>
                    <ul className="text-sm space-y-1 ml-4 list-disc list-inside">
                      <li>Todos os dados dos instrutores</li>
                      <li>Histórico de turmas (se não houver turmas ativas)</li>
                      <li>Contas de usuário do sistema</li>
                    </ul>
                    <p className="text-sm font-bold text-red-600">
                      Esta ação não pode ser desfeita!
                    </p>
                  </div>
                )}
                {selectedAction === "inactivate" && (
                  <p>
                    Tem certeza que deseja marcar {selectedUsers.length}{" "}
                    {selectedUsers.length === 1 ? "instrutor" : "instrutores"} como
                    inativos?
                  </p>
                )}
                {selectedAction === "activate" && (
                  <p>
                    Tem certeza que deseja marcar {selectedUsers.length}{" "}
                    {selectedUsers.length === 1 ? "instrutor" : "instrutores"} como
                    ativos?
                  </p>
                )}
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCloseDialog}>
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleExecuteAction}
              className={
                selectedAction === "delete" || selectedAction === "delete-permanently" 
                  ? "bg-destructive hover:bg-destructive/90" 
                  : ""
              }
            >
              <Check className="mr-2 h-4 w-4" />
              {selectedAction === "delete-permanently" ? "Sim, deletar permanentemente" : "Confirmar"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 