'use client';

import { useQuery } from '@tanstack/react-query';
import { InstructorStats } from '../types/types';
import { getInstructorStats } from '../actions/instructor-stats';

export function useInstructorStats(initialStats?: InstructorStats) {
  const {
    data: stats = initialStats || { total: 0, active: 0, inactive: 0, specialistCount: {} },
    isLoading,
    error,
  } = useQuery({
    queryKey: ['instructorStats'],
    queryFn: getInstructorStats,
    initialData: initialStats,
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchOnWindowFocus: false,
  });

  return {
    stats,
    isLoading,
    error,
  };
} 