"use client";

import { useInstructorsFilterState } from '@/hooks/instrutores/use-instructors-filter-state';
import { InstructorFilterState } from '../types/types';

export function useInstructorsFilter(initialState?: Partial<InstructorFilterState>) {
  const { filters, updateFilters, clearFilters, updatePage } = useInstructorsFilterState(initialState);
  
  return {
    filters,
    updateFilters,
    clearFilters,
    updatePage
  };
} 