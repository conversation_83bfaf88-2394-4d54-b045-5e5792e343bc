'use server';

import { z } from 'zod';
import { createClient } from '@/services/supabase/server';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';

/**
 * Resultado da verificação de disponibilidade de e-mail
 */
export type EmailAvailabilityResult = {
  available: boolean;
  error?: string;
};

const emailSchema = z.string().email('E-mail inválido');

/**
 * Verifica se um e-mail já está em uso dentro do tenant do usuário autenticado
 *
 * 1. Valida o formato do e-mail
 * 2. Identifica o tenant do usuário atual
 * 3. Consulta a tabela `users` (escopada por tenant) para contar registros com o e-mail informado
 */
export async function checkEmailAvailability(email: string): Promise<EmailAvailabilityResult> {
  // 1. Validação de formato
  const parsed = emailSchema.safeParse(email.trim().toLowerCase());
  if (!parsed.success) {
    return { available: false, error: parsed.error.errors[0]?.message || 'E-mail inválido' };
  }

  // 2. Obter usuário atual e tenant
  const currentUser = await getCurrentUser();
  if (!currentUser) {
    return { available: false, error: 'Usuário não autenticado' };
  }

  const supabase = await createClient();

  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('tenant_id')
    .eq('id', currentUser.id)
    .single();

  if (userError || !userData?.tenant_id) {
    return { available: false, error: 'Não foi possível determinar o tenant' };
  }

  // 3. Verificar existência do e-mail dentro do tenant
  const { count, error } = await supabase
    .from('users')
    .select('id', { count: 'exact', head: true })
    .eq('tenant_id', userData.tenant_id)
    .ilike('email', parsed.data); // ilike para ser case-insensitive

  if (error) {
    console.error('Erro ao verificar disponibilidade de e-mail:', error);
    return { available: false, error: 'Erro ao verificar e-mail' };
  }

  return { available: (count || 0) === 0 };
} 