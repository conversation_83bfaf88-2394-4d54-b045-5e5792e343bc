'use server';

import { z } from 'zod';

const cepSchema = z
  .string()
  .regex(/^\d{8}$/ , 'CEP deve conter exatamente 8 dígitos numéricos');

export type CepAddress = {
  street: string;
  neighborhood: string;
  city: string;
  state: string;
};

export type FetchCepResult =
  | { success: true; data: CepAddress }
  | { success: false; error: string };

/**
 * Busca dados de endereço no ViaCEP.
 * @param cep CEP somente números, 8 dígitos.
 */
export async function fetchAddressByCep(cep: string): Promise<FetchCepResult> {
  const parse = cepSchema.safeParse(cep.replace(/\D/g, ''));
  if (!parse.success) {
    return { success: false, error: 'CEP inválido. Deve ter 8 dígitos numéricos.' };
  }

  const sanitizedCep = parse.data;

  try {
    const response = await fetch(`https://viacep.com.br/ws/${sanitizedCep}/json/`, {
      // Evita que o Next.js faça cache indefinido
      cache: 'no-store',
    });

    if (!response.ok) {
      return { success: false, error: 'Falha ao buscar CEP. Verifique o formato.' };
    }

    const data = (await response.json()) as { erro?: boolean } & Record<string, string>;

    if (data.erro) {
      return { success: false, error: 'CEP não encontrado.' };
    }

    const address: CepAddress = {
      street: data.logradouro || '',
      neighborhood: data.bairro || '',
      city: data.localidade || '',
      state: data.uf || '',
    };

    return { success: true, data: address };
  } catch (e) {
    return { success: false, error: 'Erro inesperado ao buscar CEP.' };
  }
} 