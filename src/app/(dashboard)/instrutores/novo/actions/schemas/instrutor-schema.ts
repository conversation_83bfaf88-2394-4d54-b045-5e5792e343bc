import { z } from "zod";

export const novoInstrutorSchema = z.object({
  full_name: z.string().min(1, "Nome completo é obrigatório"),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  email: z.string()
    .email("Email inválido")
    .transform((val) => val.trim().toLowerCase()),
  phone: z.string().optional(),
  avatar_url: z.string().optional(),
  
  branch_id: z.string().uuid("ID de filial inválido").min(1, "Filial é obrigatória"),
  birth_date: z.string().optional().refine(data => !data || /^\d{4}-\d{2}-\d{2}$/.test(data), {
    message: "Data deve estar no formato YYYY-MM-DD"
  }),
  gender: z.enum(["masculino", "feminino", "outro", "prefiro_nao_informar"]).optional(),
  
  current_belt_level_id: z.string().uuid("ID do nível de faixa inválido").optional(),
  experience_years: z.number().int().min(0, "Experiência não pode ser negativa").optional(),
  
  cbjj_certified: z.boolean().default(false),
  ibjjf_certified: z.boolean().default(false),
  first_aid_certified: z.boolean().default(false),
  cpr_certified: z.boolean().default(false),
  
  specialties: z.array(z.string()).default([]),
  
  contract_type: z.enum([
    "clt", 
    "pj", 
    "autonomo", 
    "parceria", 
    "associacao"
  ]).optional(),
  payment_model: z.enum([
    "hora_aula", 
    "salario_mensal", 
    "comissao", 
    "participacao_lucros"
  ]).optional(),
  hourly_rate: z.number().min(0, "Valor por hora deve ser no mínimo 0").optional(),
  monthly_salary: z.number().min(0, "Salário mensal deve ser no mínimo 0").optional(),
  commission_percentage: z.number().min(0).max(100, "Percentual deve estar entre 0 e 100").optional(),
  
  street: z.string().optional(),
  street_number: z.string().optional(),
  complement: z.string().optional(),
  neighborhood: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postal_code: z.string().optional(),
  
  emergency_contact_name: z.string().optional(),
  emergency_contact_phone: z.string().optional(),
  emergency_contact_relationship: z.string().optional(),
  
  notes: z.string().optional(),
  teaching_notes: z.string().optional(),
});

export type NovoInstrutorFormValues = z.infer<typeof novoInstrutorSchema>; 