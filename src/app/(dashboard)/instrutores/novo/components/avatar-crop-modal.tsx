'use client';

import { useState, useCallback } from 'react';
import <PERSON><PERSON><PERSON> from 'react-easy-crop';
import { <PERSON>lide<PERSON> } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Loader2, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';

interface AvatarCropModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  imageUrl: string;
  onCroppedImageChange: (dataUrl: string) => void;
}

interface CroppedAreaPixels {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Funções auxiliares para processamento de imagem
const createImage = (url: string): Promise<HTMLImageElement> =>
  new Promise((resolve, reject) => {
    const image = new Image();
    image.addEventListener('load', () => resolve(image));
    image.addEventListener('error', (error) => reject(error));
    image.src = url;
  });

const getCroppedImg = async (
  imageSrc: string,
  pixelCrop: { x: number; y: number; width: number; height: number }
): Promise<Blob> => {
  const image = await createImage(imageSrc);
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('Não foi possível obter contexto do canvas');
  }

  // Definir as dimensões do canvas para criar uma imagem quadrada
  const maxSize = Math.max(pixelCrop.width, pixelCrop.height);
  canvas.width = maxSize;
  canvas.height = maxSize;

  // Preencher com fundo transparente
  ctx.fillStyle = 'rgba(0, 0, 0, 0)';
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // Desenhar a imagem recortada no centro do canvas
  ctx.drawImage(
    image,
    pixelCrop.x,
    pixelCrop.y,
    pixelCrop.width,
    pixelCrop.height,
    0,
    0,
    maxSize,
    maxSize
  );

  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      if (!blob) throw new Error('Falha ao criar blob');
      resolve(blob);
    }, 'image/jpeg', 0.95);
  });
};

export const AvatarCropModal = ({
  open,
  onOpenChange,
  imageUrl,
  onCroppedImageChange
}: AvatarCropModalProps) => {
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<CroppedAreaPixels | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const onCropComplete = useCallback((croppedArea: any, croppedAreaPixels: CroppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const handleConfirm = async () => {
    if (!imageUrl || !croppedAreaPixels) return;

    try {
      setIsProcessing(true);
      setProgress(10);

      // Obter a imagem recortada como blob
      const croppedImageBlob = await getCroppedImg(imageUrl, croppedAreaPixels);

      // Simular progresso de processamento
      let currentProgress = 10;
      const progressInterval = setInterval(() => {
        currentProgress = Math.min(90, currentProgress + 20);
        setProgress(currentProgress);
        
        if (currentProgress >= 90) {
          clearInterval(progressInterval);
        }
      }, 100);

      // Converter para data URL para preview
      const reader = new FileReader();
      reader.onloadend = () => {
        clearInterval(progressInterval);
        setProgress(100);
        
        const dataUrl = reader.result as string;
        // Atualizar o avatar no formulário
        onCroppedImageChange(dataUrl);
        
        // Fechar o modal
        onOpenChange(false);
      };
      reader.readAsDataURL(croppedImageBlob);
    } catch (error) {
      setError('Ocorreu um erro ao processar a imagem');
      console.error('Erro ao processar a imagem:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancel = () => {
    // Fechar o modal sem salvar alterações
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      if (!isOpen && !isProcessing) {
        handleCancel();
      } else {
        onOpenChange(isOpen);
      }
    }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Foto do Instrutor</DialogTitle>
        </DialogHeader>
        
        {imageUrl && (
          <>
            <div className="relative w-full h-64 overflow-hidden rounded-lg my-4">
              <Cropper
                image={imageUrl}
                crop={crop}
                zoom={zoom}
                aspect={1}
                cropShape="round"
                showGrid={false}
                onCropChange={setCrop}
                onCropComplete={onCropComplete}
                onZoomChange={setZoom}
              />
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-slate-500 dark:text-slate-400 mb-2">Zoom</p>
              <Slider
                value={[zoom]}
                min={1}
                max={3}
                step={0.1}
                onValueChange={(value: number[]) => setZoom(value[0])}
                className="w-full"
              />
            </div>
          </>
        )}
        
        {isProcessing && (
          <div className="w-full mt-2 mb-4">
            <Progress value={progress} className="h-2" />
            <p className="text-xs text-center mt-1 text-muted-foreground">
              Processando imagem... {progress}%
            </p>
          </div>
        )}
        
        {error && (
          <Alert variant="destructive" className="mt-2 mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="ml-2">{error}</AlertDescription>
            <button 
              onClick={() => setError(null)}
              className="ml-auto text-xs underline"
              type="button"
            >
              Tentar novamente
            </button>
          </Alert>
        )}
        
        <DialogFooter>
          <Button variant="outline" onClick={handleCancel} disabled={isProcessing}>
            Cancelar
          </Button>
          
          {imageUrl && (
            <Button onClick={handleConfirm} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processando...
                </>
              ) : (
                'Salvar'
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 