'use client';

import { useFormContext } from "react-hook-form";
import { useEffect, useRef, useState } from "react";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { NovoInstrutorFormValues } from "../../../actions/schemas/instrutor-schema";
import { MapPin, Home, Building } from "lucide-react";
import { fetchAddressByCep } from "../../../actions/fetch-address-by-cep";

export default function AddressInfoSection() {
  const {
    control,
    watch,
    setValue,
    setError,
    clearErrors,
  } = useFormContext<NovoInstrutorFormValues>();

  const [isFetchingCep, setIsFetchingCep] = useState(false);
  const lastFetchedCep = useRef<string>("");

  const postalCode = watch("postal_code");

  useEffect(() => {
    if (!postalCode) return;

    const sanitizedCep = postalCode.replace(/\D/g, "");

    if (sanitizedCep.length !== 8) return;

    if (lastFetchedCep.current === sanitizedCep) return;

    lastFetchedCep.current = sanitizedCep;
    setIsFetchingCep(true);

    const fetchData = async () => {
      const result = await fetchAddressByCep(sanitizedCep);

      if (result.success) {
        setValue("street", result.data.street);
        setValue("neighborhood", result.data.neighborhood);
        setValue("city", result.data.city);
        setValue("state", result.data.state);
        clearErrors("postal_code");
      } else {
        setError("postal_code", {
          type: "validate",
          message: result.error,
        });
      }

      setIsFetchingCep(false);
    };

    fetchData();

    // não há abort no server action, mas mantemos cleanup se necessário
    return () => {};
  }, [postalCode, setValue, setError, clearErrors]);

  return (
    <div className="grid grid-cols-1 gap-6">
      {/* Endereço */}
      <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <MapPin className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Endereço
          </h2>
        </div>
        
        <CardContent className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <FormField
                control={control}
                name="street"
                render={({ field }) => (
                  <FormItem className="space-y-1">
                    <div className="flex items-center">
                      <MapPin className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                      <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                        Rua
                      </FormLabel>
                    </div>
                    <FormControl>
                      <Input 
                        placeholder="Digite a rua" 
                        {...field} 
                        className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={control}
              name="street_number"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <div className="flex items-center">
                    <Home className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                    <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                      Número
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Input 
                      placeholder="Digite o número" 
                      {...field} 
                      className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={control}
              name="complement"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <div className="flex items-center">
                    <Building className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                    <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                      Complemento
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Input 
                      placeholder="Apto, bloco, etc." 
                      {...field} 
                      className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="neighborhood"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <div className="flex items-center">
                    <MapPin className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                    <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                      Bairro
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Input 
                      placeholder="Digite o bairro" 
                      {...field} 
                      className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormField
              control={control}
              name="postal_code"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <div className="flex items-center">
                    <MapPin className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                    <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                      CEP
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Input 
                      placeholder="00000-000" 
                      {...field} 
                      disabled={isFetchingCep}
                      className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full disabled:opacity-75"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="city"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <div className="flex items-center">
                    <Building className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                    <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                      Cidade
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Input 
                      placeholder="Digite a cidade" 
                      {...field} 
                      className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="state"
              render={({ field }) => (
                <FormItem className="space-y-1">
                  <div className="flex items-center">
                    <MapPin className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-3" />
                    <FormLabel className="text-sm font-medium text-slate-500 dark:text-slate-400">
                      Estado
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Input 
                      placeholder="UF" 
                      maxLength={2} 
                      {...field} 
                      className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 