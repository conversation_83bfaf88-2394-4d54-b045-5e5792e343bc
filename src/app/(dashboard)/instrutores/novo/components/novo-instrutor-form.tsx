'use client';

import { useState, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/ui/use-toast';
import { createInstrutor } from '../actions/create-instrutor';
import { novoInstrutorSchema, type NovoInstrutorFormValues } from '../actions/schemas/instrutor-schema';
import { 
  BasicInfoSection, 
  ProfessionalInfoSection, 
  AddressInfoSection, 
  EmergencyInfoSection 
} from './form-sections';
import FormSuccess from './form-success';

const steps = [
  { id: 1, title: 'Informações Básicas', component: BasicInfoSection },
  { id: 2, title: 'Informações Profissionais', component: ProfessionalInfoSection },
  { id: 3, title: 'Endereço', component: AddressInfoSection },
  { id: 4, title: 'Contato de Emergência', component: EmergencyInfoSection },
];

export function NovoInstrutorForm() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [createdInstructorId, setCreatedInstructorId] = useState<string | null>(null);
  const [createdUserId, setCreatedUserId] = useState<string | null>(null);
  const [temporaryPassword, setTemporaryPassword] = useState<string | null>(null);
  const { toast } = useToast();

  // useEffect para monitorar mudanças no currentStep
  useEffect(() => {
    // Sempre que trocar de etapa, rolar suavemente para o topo da página
    if (typeof window !== 'undefined') {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [currentStep]);

  const methods = useForm<NovoInstrutorFormValues>({
    resolver: zodResolver(novoInstrutorSchema),
    mode: 'onChange',
    defaultValues: {
      // Informações básicas
      full_name: '',
      email: '',
      phone: '',
      birth_date: '',
      gender: 'masculino',
      current_belt: 'white',
      current_belt_degree: 1,
      experience_years: 0,
      avatar_url: '',
      
      // Informações profissionais
      cbjj_certified: false,
      ibjjf_certified: false,
      first_aid_certified: false,
      cpr_certified: false,
      specialties: [],
      contract_type: 'clt',
      payment_model: 'hora_aula',
      hourly_rate: undefined,
      monthly_salary: undefined,
      commission_percentage: undefined,
      
      // Endereço
      street: '',
      street_number: '',
      complement: '',
      neighborhood: '',
      city: '',
      state: '',
      postal_code: '',
      
      // Contato de emergência
      emergency_contact_name: '',
      emergency_contact_phone: '',
      emergency_contact_relationship: '',
      
      // Notas
      notes: '',
      teaching_notes: '',
    },
  });

  const currentStepComponent = steps.find(step => step.id === currentStep);
  const CurrentStepComponent = currentStepComponent?.component;

  const nextStep = async () => {
    // Prevenir múltiplos cliques
    if (isSubmitting) return;
    
    // Log para debug
    console.log(`Tentando avançar da etapa ${currentStep} para ${currentStep + 1}`);
    
    // Validar apenas se não estivermos na última etapa
    if (currentStep >= steps.length) {
      console.warn('Tentativa de avançar além da última etapa');
      return;
    }
    
    const isStepValid = await methods.trigger();
    
    if (isStepValid) {
      const newStep = currentStep + 1;
      console.log(`Avançando para etapa ${newStep}`);
      setCurrentStep(newStep);
    } else {
      console.log('Validação falhou na etapa', currentStep);
      toast({
        title: "Erro de validação",
        description: "Por favor, corrija os erros antes de continuar.",
        variant: "destructive",
      });
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => prev - 1);
  };

  const onSubmit = async (data: NovoInstrutorFormValues) => {
    console.log('onSubmit chamado - Etapa atual:', currentStep);
    console.log('Dados do formulário:', data);
    
    // Verificar se estamos realmente na última etapa
    if (currentStep !== steps.length) {
      console.error(`Tentativa de submit na etapa ${currentStep}, mas deveria estar na etapa ${steps.length}`);
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      console.log('Iniciando criação do instrutor...');
      const result = await createInstrutor(data);
      
      if (result.success && result.data) {
        console.log('Instrutor criado com sucesso:', result.data);
        setCreatedInstructorId(result.data.instructorId);
        setCreatedUserId(result.data.userId);
        setTemporaryPassword(result.data.temporaryPassword || null);
        setSubmitSuccess(true);
        toast({
          title: "Sucesso!",
          description: "Instrutor cadastrado com sucesso.",
        });
      } else {
        if (result.errors && '_form' in result.errors && Array.isArray(result.errors._form)) {
          toast({
            title: "Erro",
            description: result.errors._form[0] || "Erro ao cadastrar instrutor",
            variant: "destructive",
          });
        } else {
          toast({
            title: "Erro",
            description: "Erro ao cadastrar instrutor",
            variant: "destructive",
          });
        }
        
        // Definir erros específicos dos campos se existirem
        if (result.errors) {
          Object.entries(result.errors).forEach(([field, errors]) => {
            if (field !== '_form' && Array.isArray(errors) && errors.length > 0) {
              methods.setError(field as any, {
                type: 'manual',
                message: errors[0],
              });
            }
          });
        }
      }
    } catch (error) {
      console.error('Erro inesperado:', error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro inesperado. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewProfile = () => {
    if (createdUserId) {
      window.location.href = `/perfil/${createdUserId}`;
    }
  };

  const handleAddAnother = () => {
    setSubmitSuccess(false);
    setCreatedInstructorId(null);
    setCreatedUserId(null);
    setTemporaryPassword(null);
    setCurrentStep(1);
    methods.reset();
  };

  if (submitSuccess) {
    return (
      <FormSuccess 
        instructorId={createdInstructorId} 
        userId={createdUserId}
        temporaryPassword={temporaryPassword || undefined}
        onViewProfile={handleViewProfile}
        onAddAnother={handleAddAnother}
      />
    );
  }

  // Handler para prevenir submits acidentais
  const handleFormSubmit = (e: React.FormEvent) => {
    console.log('handleFormSubmit chamado - Etapa atual:', currentStep);
    
    // Só permitir submit na última etapa
    if (currentStep !== steps.length) {
      console.log('Prevenindo submit - não está na última etapa');
      e.preventDefault();
      return;
    }
    
    // Chamar o handler do React Hook Form apenas na última etapa
    methods.handleSubmit(onSubmit)(e);
  };

  // Handler para prevenir Enter de fazer submit prematuro
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && currentStep !== steps.length) {
      console.log('Prevenindo Enter submit - não está na última etapa');
      e.preventDefault();
      // Opcionalmente, avançar para próxima etapa
      if (currentStep < steps.length) {
        nextStep();
      }
    }
  };

  return (
    <FormProvider {...methods}>
      <form 
        onSubmit={handleFormSubmit}
        onKeyDown={handleKeyDown}
        className="space-y-6"
      >
        {/* Progress Bar */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Cadastro de Novo Instrutor - Etapa {currentStep} de {steps.length}</span>
              <span className="text-sm font-normal text-muted-foreground">
                {currentStepComponent?.title}
              </span>
            </CardTitle>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-blue-600 h-3 rounded-full transition-all duration-300 flex items-center justify-end pr-2"
                style={{ width: `${(currentStep / steps.length) * 100}%` }}
              >
                <span className="text-white text-xs font-medium">
                  {Math.round((currentStep / steps.length) * 100)}%
                </span>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Current Step Content */}
        <Card>
          <CardHeader>
            <CardTitle>{currentStepComponent?.title}</CardTitle>
          </CardHeader>
          <CardContent>
            {CurrentStepComponent && <CurrentStepComponent />}
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 1 || isSubmitting}
          >
            Anterior
          </Button>

          {currentStep < steps.length ? (
            <Button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                console.log('Botão Próxima clicado na etapa:', currentStep);
                nextStep();
              }}
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? 'Processando...' : 'Próxima'}
            </Button>
          ) : (
            <Button
              type="submit"
              onClick={(e) => {
                console.log('Botão Finalizar clicado na etapa:', currentStep);
              }}
              disabled={isSubmitting}
              className="bg-green-600 hover:bg-green-700"
            >
              {isSubmitting ? 'Cadastrando...' : 'Finalizar Cadastro'}
            </Button>
          )}
        </div>
      </form>
    </FormProvider>
  );
} 