import { Metadata } from "next";
import { NovoInstrutorForm } from "./components/novo-instrutor-form";
import NovoInstrutorClientWrapper from "./components/novo-instrutor-client-wrapper";

export const metadata: Metadata = {
  title: "Novo Instrutor | ApexSAAS",
  description: "Cadastro de novo instrutor",
};

export default function NovoInstrutorPage() {
  return (
    <div className="space-y-6">
      <div className="p-6 rounded-lg shadow">
        <NovoInstrutorClientWrapper>
          <NovoInstrutorForm />
        </NovoInstrutorClientWrapper>
      </div>
    </div>
  );
}