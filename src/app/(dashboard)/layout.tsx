import { Metadata } from 'next';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { PageTitleProvider } from '@/contexts/PageTitleContext';
import { validateLayoutAuth } from '@/services/auth/layout-auth';

export const metadata: Metadata = {
  title: 'Dashboard',
  description: 'Dashboard da aplicação',
};

export default async function DashboardRootLayout({
  children,
}: {  
  children: React.ReactNode;
}) {
  // Aplicar as mesmas regras de autenticação e tenant através do serviço compartilhado
  const { user, tenantSlug, tenantData } = await validateLayoutAuth('/login');
  
  // Se não há tenant, usar layout padrão
  if (!tenantSlug || !tenantData) {
    return (
      <PageTitleProvider>
        <DashboardLayout 
          title="Dashboard" 
          subtitle="Gerenciamento de Academia"
        >
          {children}
        </DashboardLayout>
      </PageTitleProvider>
    );
  }
  
  return (
    <PageTitleProvider>
      <DashboardLayout 
        title={tenantData.name}
        subtitle="Gerenciamento de Academia"
        primaryColor={tenantData.primary_color}
        secondaryColor={tenantData.secondary_color}
        tenantLogo={tenantData.logo_url || '/placeholder-logo.png'}
      >
        {children}
      </DashboardLayout>
    </PageTitleProvider>
  );
} 