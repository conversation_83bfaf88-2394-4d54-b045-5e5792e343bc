'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useNotificationPreferences } from '@/hooks/notifications/use-notification-preferences';
import { toast } from 'sonner';
import {
  BellIcon,
  EnvelopeIcon,
  DevicePhoneMobileIcon,
  ClockIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';
import type { NotificationType } from '@/services/notifications/types/notification-types';

interface NotificationPreferencesProps {
  userId: string;
}

const notificationTypes: { value: NotificationType; label: string; description: string }[] = [
  { 
    value: 'payment', 
    label: 'Pagamentos', 
    description: 'Lembretes de mensalidades, confirmações de pagamento e avisos de atraso' 
  },
  { 
    value: 'class', 
    label: 'Aulas', 
    description: 'Lembretes de aulas, cancelamentos e alterações de horário' 
  },
  { 
    value: 'system', 
    label: 'Sistema', 
    description: 'Atualizações do sistema, manutenções e avisos importantes' 
  },
  { 
    value: 'enrollment', 
    label: 'Matrículas', 
    description: 'Confirmações de matrícula, renovações e alterações de plano' 
  },
  { 
    value: 'event', 
    label: 'Eventos', 
    description: 'Convites para eventos, competições e atividades especiais' 
  },
];

const timezones = [
  { value: 'America/Sao_Paulo', label: 'Brasília (GMT-3)' },
  { value: 'America/Manaus', label: 'Manaus (GMT-4)' },
  { value: 'America/Rio_Branco', label: 'Rio Branco (GMT-5)' },
];

export function NotificationPreferences({ userId }: NotificationPreferencesProps) {
  const {
    preferences,
    loading,
    error,
    updatePreferences,
    createPreferences
  } = useNotificationPreferences({ userId });

  const [localPreferences, setLocalPreferences] = useState<Record<string, any>>({});
  const [saving, setSaving] = useState(false);

  // Inicializar preferências locais quando carregadas
  useEffect(() => {
    if (preferences && preferences.length > 0) {
      const prefsMap = preferences.reduce((acc, pref) => {
        acc[pref.notification_type] = pref;
        return acc;
      }, {} as Record<string, any>);
      setLocalPreferences(prefsMap);
    }
  }, [preferences]);

  const getPreferenceForType = (type: NotificationType) => {
    return localPreferences[type] || {
      notification_type: type,
      in_app_enabled: true,
      email_enabled: true,
      whatsapp_enabled: false,
      quiet_hours_start: '22:00',
      quiet_hours_end: '08:00',
      timezone: 'America/Sao_Paulo'
    };
  };

  const updatePreferenceForType = (type: NotificationType, updates: Partial<any>) => {
    setLocalPreferences(prev => ({
      ...prev,
      [type]: {
        ...getPreferenceForType(type),
        ...updates
      }
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const promises = notificationTypes.map(async ({ value: type }) => {
        const pref = localPreferences[type];
        if (!pref) return;

        if (pref.id) {
          // Atualizar existente
          return updatePreferences(pref.id, {
            in_app_enabled: pref.in_app_enabled,
            email_enabled: pref.email_enabled,
            whatsapp_enabled: pref.whatsapp_enabled,
            quiet_hours_start: pref.quiet_hours_start,
            quiet_hours_end: pref.quiet_hours_end,
            timezone: pref.timezone
          });
        } else {
          // Criar nova
          return createPreferences({
            notification_type: type,
            in_app_enabled: pref.in_app_enabled,
            email_enabled: pref.email_enabled,
            whatsapp_enabled: pref.whatsapp_enabled,
            quiet_hours_start: pref.quiet_hours_start,
            quiet_hours_end: pref.quiet_hours_end,
            timezone: pref.timezone
          });
        }
      });

      await Promise.all(promises);
      toast.success('Preferências salvas com sucesso!');
    } catch (error) {
      console.error('Erro ao salvar preferências:', error);
      toast.error('Erro ao salvar preferências');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardHeader>
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-red-600 dark:text-red-400">
              Erro ao carregar preferências: {error}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Configurações por tipo de notificação */}
      {notificationTypes.map(({ value: type, label, description }) => {
        const pref = getPreferenceForType(type);
        
        return (
          <Card key={type}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BellIcon className="h-5 w-5" />
                {label}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                {description}
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Canais de notificação */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium">Canais de Notificação</h4>
                
                <div className="space-y-3">
                  {/* In-App */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <BellIcon className="h-4 w-4 text-muted-foreground" />
                      <Label>Notificações no App</Label>
                    </div>
                    <Switch
                      checked={pref.in_app_enabled}
                      onCheckedChange={(checked) => 
                        updatePreferenceForType(type, { in_app_enabled: checked })
                      }
                    />
                  </div>

                  {/* Email */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <EnvelopeIcon className="h-4 w-4 text-muted-foreground" />
                      <Label>E-mail</Label>
                    </div>
                    <Switch
                      checked={pref.email_enabled}
                      onCheckedChange={(checked) => 
                        updatePreferenceForType(type, { email_enabled: checked })
                      }
                    />
                  </div>

                  {/* WhatsApp */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <DevicePhoneMobileIcon className="h-4 w-4 text-muted-foreground" />
                      <Label>WhatsApp</Label>
                    </div>
                    <Switch
                      checked={pref.whatsapp_enabled}
                      onCheckedChange={(checked) => 
                        updatePreferenceForType(type, { whatsapp_enabled: checked })
                      }
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Horário de silêncio */}
              {/* <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <ClockIcon className="h-4 w-4 text-muted-foreground" />
                  <h4 className="text-sm font-medium">Horário de Silêncio</h4>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Início</Label>
                    <Input
                      type="time"
                      value={pref.quiet_hours_start}
                      onChange={(e) => 
                        updatePreferenceForType(type, { quiet_hours_start: e.target.value })
                      }
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Fim</Label>
                    <Input
                      type="time"
                      value={pref.quiet_hours_end}
                      onChange={(e) => 
                        updatePreferenceForType(type, { quiet_hours_end: e.target.value })
                      }
                    />
                  </div>
                </div>
              </div> */}

              {/* <Separator /> */}

              {/* Fuso horário */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <GlobeAltIcon className="h-4 w-4 text-muted-foreground" />
                  <h4 className="text-sm font-medium">Fuso Horário</h4>
                </div>
                
                <Select
                  value={pref.timezone}
                  onValueChange={(value) => 
                    updatePreferenceForType(type, { timezone: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {timezones.map((tz) => (
                      <SelectItem key={tz.value} value={tz.value}>
                        {tz.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        );
      })}

      {/* Botão de salvar */}
      <div className="flex justify-end">
        <Button onClick={handleSave} disabled={saving}>
          {saving ? 'Salvando...' : 'Salvar Preferências'}
        </Button>
      </div>
    </div>
  );
}
