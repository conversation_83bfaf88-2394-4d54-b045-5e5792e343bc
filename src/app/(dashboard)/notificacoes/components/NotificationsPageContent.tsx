'use client';

import { useState } from 'react';
import { PageWrapper } from '@/components/layout';
import { BellIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { NotificationsList } from './NotificationsList';
import { NotificationFilters } from './NotificationFilters';
import { NotificationPreferences } from './NotificationPreferences';
import { useUserMetadata } from '@/hooks/user/Auth/useUserMetadata';
import type { NotificationFilters as NotificationFiltersType } from '@/services/notifications/types/notification-types';

export function NotificationsPageContent() {
  const { metadata } = useUserMetadata();
  const [activeTab, setActiveTab] = useState('all');
  const [showPreferences, setShowPreferences] = useState(false);
  const [filters, setFilters] = useState<NotificationFiltersType>({});

  if (!metadata?.id) {
    return (
      <PageWrapper
        title="Notificações"
        subtitle="Gerencie suas notificações e preferências"
        icon={<BellIcon className="h-6 w-6 text-primary" />}
      >
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Carregando...</p>
        </div>
      </PageWrapper>
    );
  }

  const getFiltersForTab = (tab: string): NotificationFiltersType => {
    const baseFilters = { ...filters };
    
    switch (tab) {
      case 'unread':
        return { ...baseFilters, status: ['unread'] };
      case 'archived':
        return { ...baseFilters, status: ['archived'] };
      case 'all':
      default:
        return { ...baseFilters, status: ['unread', 'read'] };
    }
  };

  if (showPreferences) {
    return (
      <PageWrapper
        title="Preferências de Notificação"
        subtitle="Configure como e quando receber notificações"
        icon={<Cog6ToothIcon className="h-6 w-6 text-primary" />}
        actions={
          <Button
            variant="outline"
            onClick={() => setShowPreferences(false)}
          >
            Voltar para Notificações
          </Button>
        }
      >
        <NotificationPreferences userId={metadata.id} />
      </PageWrapper>
    );
  }

  return (
    <PageWrapper
      title="Notificações"
      subtitle="Gerencie suas notificações e preferências"
      icon={<BellIcon className="h-6 w-6 text-primary" />}
      actions={
        <Button
          variant="outline"
          onClick={() => setShowPreferences(true)}
        >
          <Cog6ToothIcon className="h-4 w-4 mr-2" />
          Preferências
        </Button>
      }
    >
      <div className="space-y-6">
        {/* Filtros */}
        <NotificationFilters
          filters={filters}
          onFiltersChange={setFilters}
        />

        {/* Tabs de status */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">Todas</TabsTrigger>
            <TabsTrigger value="unread">Não Lidas</TabsTrigger>
            <TabsTrigger value="archived">Arquivadas</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-6">
            <NotificationsList
              userId={metadata.id}
              filters={getFiltersForTab('all')}
              showActions={true}
            />
          </TabsContent>

          <TabsContent value="unread" className="mt-6">
            <NotificationsList
              userId={metadata.id}
              filters={getFiltersForTab('unread')}
              showActions={true}
            />
          </TabsContent>

          <TabsContent value="archived" className="mt-6">
            <NotificationsList
              userId={metadata.id}
              filters={getFiltersForTab('archived')}
              showActions={true}
            />
          </TabsContent>
        </Tabs>
      </div>
    </PageWrapper>
  );
}
