'use client';

import { AlertCircle } from 'lucide-react';
import { useAvatarUpload } from './AvatarUploadContext';
import { cn } from '@/lib/utils';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface AvatarUploadErrorProps {
  className?: string;
}

export const AvatarUploadError = ({ className }: AvatarUploadErrorProps) => {
  const { status, error, reset } = useAvatarUpload();
  
  if (status !== 'error' || !error) {
    return null;
  }
  
  return (
    <Alert variant="destructive" className={cn('mt-2', className)}>
      <AlertCircle className="h-4 w-4" />
      <AlertDescription className="ml-2">{error}</AlertDescription>
      <button 
        onClick={reset}
        className="ml-auto text-xs underline"
        type="button"
      >
        Tentar novamente
      </button>
    </Alert>
  );
}; 