'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import <PERSON><PERSON><PERSON> from 'react-easy-crop';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import {
  Di<PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { useAvatarUpload } from './AvatarUploadContext';
import { updateUserAvatar, deleteUserAvatar } from '@/app/(dashboard)/perfil/actions';
import { Loader2, UploadCloud, AlertCircle, Trash2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { useUserAvatar } from '@/hooks/user/use-user-context';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import { useQueryClient } from '@tanstack/react-query';

interface AvatarUploadModalProps {
  userId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAvatarUpdated?: (url: string) => void;
}

interface CroppedAreaPixels {
  x: number;
  y: number;
  width: number;
  height: number;
}

const createImage = (url: string): Promise<HTMLImageElement> =>
  new Promise((resolve, reject) => {
    const image = new Image();
    image.addEventListener('load', () => resolve(image));
    image.addEventListener('error', (error) => reject(error));
    image.src = url;
  });

const getCroppedImg = async (
  imageSrc: string,
  pixelCrop: { x: number; y: number; width: number; height: number }
): Promise<Blob> => {
  const image = await createImage(imageSrc);
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('Não foi possível obter contexto do canvas');
  }

  // Definir as dimensões do canvas para criar uma imagem quadrada
  const maxSize = Math.max(pixelCrop.width, pixelCrop.height);
  canvas.width = maxSize;
  canvas.height = maxSize;

  // Preencher com fundo transparente
  ctx.fillStyle = 'rgba(0, 0, 0, 0)';
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // Desenhar a imagem recortada no centro do canvas
  ctx.drawImage(
    image,
    pixelCrop.x,
    pixelCrop.y,
    pixelCrop.width,
    pixelCrop.height,
    0,
    0,
    maxSize,
    maxSize
  );

  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      if (!blob) throw new Error('Falha ao criar blob');
      resolve(blob);
    }, 'image/jpeg', 0.95);
  });
};

export const AvatarUploadModal = ({
  userId,
  open,
  onOpenChange,
  onAvatarUpdated
}: AvatarUploadModalProps) => {
  const { 
    file, 
    previewUrl, 
    setFile, 
    setStatus, 
    setProgress, 
    setError,
    setAvatarUrl,
    reset,
    status,
    error,
    progress,
    avatarUrl,
    isDeleting,
    setIsDeleting
  } = useAvatarUpload();

  const { updateAvatar } = useUserAvatar();
  const [isCurrentUser, setIsCurrentUser] = useState(false);
  const queryClient = useQueryClient();
  
  // Verificar se o avatar sendo atualizado é do usuário atual
  useEffect(() => {
    const checkCurrentUser = async () => {
      try {
        const user = await getCurrentUser();
        setIsCurrentUser(user?.id === userId);
      } catch (error) {
        console.error('Erro ao verificar usuário atual:', error);
      }
    };
    
    checkCurrentUser();
  }, [userId]);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<{ x: number; y: number; width: number; height: number } | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  
  // Limpar estados quando o modal é fechado
  useEffect(() => {
    if (!open) {
      // Se não estiver no meio de um upload, limpar os estados temporários
      if (status !== 'uploading') {
        setFile(null);
      }
    }
  }, [open, setFile, status]);

  const onCropComplete = useCallback((croppedArea: any, croppedAreaPixels: CroppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const handleConfirm = async () => {
    if (!previewUrl || !croppedAreaPixels) return;

    try {
      setIsUploading(true);
      setStatus('uploading');
      setProgress(10);

      // Obter a imagem recortada como blob
      const croppedImageBlob = await getCroppedImg(previewUrl, croppedAreaPixels);
      
      // Converter blob para File
      const croppedFile = new File(
        [croppedImageBlob], 
        file?.name || `avatar-${Date.now()}.jpg`,
        { type: 'image/jpeg' }
      );

      // Atualizar o arquivo no contexto
      setFile(croppedFile);

      // Simular progresso de upload
      let currentProgress = 10;
      const progressInterval = setInterval(() => {
        currentProgress = Math.min(90, currentProgress + 10);
        setProgress(currentProgress);
        
        if (currentProgress >= 90) {
          clearInterval(progressInterval);
        }
      }, 100);

      // Criar FormData e anexar o arquivo
      const formData = new FormData();
      formData.append('file', croppedFile);

      // Chamar a Server Action para fazer upload
      const result = await updateUserAvatar(userId, formData);

      clearInterval(progressInterval);

      if (result.success) {
        setProgress(100);
        setStatus('success');
        setAvatarUrl(result.avatarUrl || null);
        
        // Se for o perfil do usuário atual, atualizar o avatar globalmente
        if (isCurrentUser && result.avatarUrl) {
          updateAvatar(result.avatarUrl);
        }
        
        if (onAvatarUpdated && result.avatarUrl) {
          onAvatarUpdated(result.avatarUrl);
        }
        
        // Invalidar a consulta dos estudantes para forçar a atualização do cache
        queryClient.invalidateQueries({
          queryKey: ['students']
        });
        
        onOpenChange(false);
      } else {
        setStatus('error');
        setError(result.error || 'Erro ao fazer upload');
      }
    } catch (error) {
      setStatus('error');
      setError('Ocorreu um erro ao processar a imagem');
      console.error('Erro ao processar a imagem:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleSelectFile = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    if (selectedFile) {
      setFile(selectedFile);
      setCrop({ x: 0, y: 0 });
      setZoom(1);
    }
  };

  const handleCancel = () => {
    // Limpar o arquivo e preview para restaurar o avatar original
    setFile(null);
    // Fechar o modal
    onOpenChange(false);
  };

  const handleReset = () => {
    reset();
  };

  const handleDeleteAvatar = async () => {
    try {
      setIsDeleting(true);
      setStatus('deleting');
      setError(null);

      const result = await deleteUserAvatar(userId);

      if (result.success) {
        setAvatarUrl(null);
        setStatus('success');
        
        // Se for o perfil do usuário atual, atualizar o avatar globalmente
        if (isCurrentUser) {
          updateAvatar('');
        }
        
        if (onAvatarUpdated) {
          onAvatarUpdated('');
        }
        
        // Invalidar a consulta dos estudantes para forçar a atualização do cache
        queryClient.invalidateQueries({
          queryKey: ['students']
        });
        
        onOpenChange(false);
      } else {
        setStatus('error');
        setError(result.error || 'Erro ao excluir avatar');
      }
    } catch (error) {
      setStatus('error');
      setError('Ocorreu um erro ao excluir o avatar');
      console.error('Erro ao excluir avatar:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      if (!isOpen && !isUploading && !isDeleting) {
        // Limpar estados ao fechar o modal com o "X" ou ESC
        handleCancel();
      } else {
        onOpenChange(isOpen);
      }
    }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Foto de perfil</DialogTitle>
        </DialogHeader>
        
        {!previewUrl ? (
          <div className="flex flex-col items-center justify-center py-10">
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/jpeg,image/png,image/webp"
              onChange={handleFileChange}
            />
            
            <div className="mb-6 p-4 rounded-full bg-primary/10 dark:bg-primary/20">
              <UploadCloud className="w-10 h-10 text-primary dark:text-primary-foreground" />
            </div>
            
            <h3 className="mb-2 text-xl font-medium text-slate-900 dark:text-slate-100">
              Selecione uma imagem
            </h3>
            
            <p className="mb-6 text-center text-sm text-slate-500 dark:text-slate-400">
              Formatos suportados: JPG, PNG e WebP
            </p>
            
            <div className="flex flex-col gap-2 w-full max-w-xs">
              <Button onClick={handleSelectFile} className="w-full">
                Escolher arquivo
              </Button>
              
              {avatarUrl && (
                <Button 
                  variant="destructive" 
                  onClick={handleDeleteAvatar}
                  disabled={isDeleting}
                  className="w-full"
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Excluindo...
                    </>
                  ) : (
                    <>
                      <Trash2 className="mr-2 h-4 w-4" />
                      Excluir avatar
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        ) : (
          <>
            <div className="relative w-full h-64 overflow-hidden rounded-lg my-4">
              <Cropper
                image={previewUrl}
                crop={crop}
                zoom={zoom}
                aspect={1}
                cropShape="round"
                showGrid={false}
                onCropChange={setCrop}
                onCropComplete={onCropComplete}
                onZoomChange={setZoom}
              />
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-slate-500 dark:text-slate-400 mb-2">Zoom</p>
              <Slider
                value={[zoom]}
                min={1}
                max={3}
                step={0.1}
                onValueChange={(value: number[]) => setZoom(value[0])}
                className="w-full"
              />
            </div>
          </>
        )}
        
        {status === 'uploading' && (
          <div className="w-full mt-2 mb-4">
            <Progress value={progress} className="h-2" />
            <p className="text-xs text-center mt-1 text-muted-foreground">
              Enviando imagem... {progress}%
            </p>
          </div>
        )}
        
        {status === 'deleting' && (
          <div className="w-full mt-2 mb-4">
            <Progress value={100} className="h-2 bg-red-100" />
            <p className="text-xs text-center mt-1 text-muted-foreground">
              Excluindo avatar...
            </p>
          </div>
        )}
        
        {status === 'error' && error && (
          <Alert variant="destructive" className="mt-2 mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="ml-2">{error}</AlertDescription>
            <button 
              onClick={handleReset}
              className="ml-auto text-xs underline"
              type="button"
            >
              Tentar novamente
            </button>
          </Alert>
        )}
        
        <DialogFooter>
          <Button variant="outline" onClick={handleCancel} disabled={isUploading || isDeleting}>
            Cancelar
          </Button>
          
          {previewUrl && (
            <Button onClick={handleConfirm} disabled={isUploading || isDeleting}>
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Enviando...
                </>
              ) : (
                'Salvar'
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 