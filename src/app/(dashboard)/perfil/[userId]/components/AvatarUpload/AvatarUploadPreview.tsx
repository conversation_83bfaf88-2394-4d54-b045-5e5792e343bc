'use client';

import Image from 'next/image';
import { useAvatarUpload } from './AvatarUploadContext';
import { cn } from '@/lib/utils';

interface AvatarUploadPreviewProps {
  name: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const AvatarUploadPreview = ({
  name,
  className,
  size = 'md'
}: AvatarUploadPreviewProps) => {
  const { previewUrl, avatarUrl, status, isDeleting } = useAvatarUpload();
  
  // Determinar a imagem a ser exibida
  const imageUrl = previewUrl || avatarUrl;
  
  // Função para mostrar as iniciais do nome quando não há imagem
  const getInitials = (name: string): string => {
    if (!name) return "";
    return name
      .split(" ")
      .map((n: string) => n?.[0])
      .slice(0, 2)
      .join("")
      .toUpperCase();
  };

  const sizeClasses = {
    sm: 'w-24 h-24 text-3xl',
    md: 'w-32 h-32 text-5xl',
    lg: 'w-32 h-32 sm:w-40 sm:h-40 text-5xl sm:text-6xl'
  };

  const isLoading = status === 'uploading' || isDeleting;
  
  return (
    <div className={cn(
      'relative',
      isLoading ? 'opacity-70' : '',
      className
    )}>
      {imageUrl ? (
        <div className={cn(
          'rounded-full overflow-hidden bg-primary/10 dark:bg-primary/20 ring-4 ring-white dark:ring-slate-700 shadow-md',
          sizeClasses[size]
        )}>
          <Image 
            src={imageUrl}
            alt={`Foto de ${name}`}
            width={size === 'lg' ? 160 : 128}
            height={size === 'lg' ? 160 : 128}
            className="object-cover w-full h-full"
            priority
          />
        </div>
      ) : (
        <div className={cn(
          'rounded-full bg-slate-200 dark:bg-slate-700 flex items-center justify-center font-bold text-slate-600 dark:text-slate-200 ring-4 ring-white dark:ring-slate-700 shadow-md',
          sizeClasses[size]
        )}>
          {getInitials(name)}
        </div>
      )}
    </div>
  );
}; 