'use client';

import { useAvatarUpload } from './AvatarUploadContext';
import { cn } from '@/lib/utils';
import { Progress } from '@/components/ui/progress';

interface AvatarUploadProgressProps {
  className?: string;
}

export const AvatarUploadProgress = ({ className }: AvatarUploadProgressProps) => {
  const { status, progress } = useAvatarUpload();
  
  if (status !== 'uploading') {
    return null;
  }
  
  return (
    <div className={cn('w-full mt-2', className)}>
      <Progress value={progress} className="h-2" />
      <p className="text-xs text-center mt-1 text-muted-foreground">
        Enviando imagem... {progress}%
      </p>
    </div>
  );
}; 