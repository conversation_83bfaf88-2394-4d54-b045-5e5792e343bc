'use client';

import { ReactNode } from 'react';
import { AvatarUploadProvider } from './AvatarUploadContext';

interface AvatarUploadRootProps {
  children: ReactNode;
  userId: string;
  initialAvatarUrl?: string | null;
}

export const AvatarUploadRoot = ({
  children,
  userId,
  initialAvatarUrl
}: AvatarUploadRootProps) => {
  return (
    <AvatarUploadProvider initialAvatarUrl={initialAvatarUrl}>
      <div className="relative" data-user-id={userId}>
        {children}
      </div>
    </AvatarUploadProvider>
  );
}; 