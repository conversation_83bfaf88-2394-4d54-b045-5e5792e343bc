'use client';

import { Mail, Phone, QrCode, User, ShieldCheckIcon } from "lucide-react";
import { EditableField } from "../../components/editable-field";
import { EditablePhoneField } from "../../components/editable-phone-field";
import { EditableAddress } from "../../components/editable-address";
import { cn } from "@/lib/utils";
import { useProfileWithGuardian } from '@/hooks/user/Profile/useProfileWithGuardian';
import { formatGuardianRelationship, formatGuardianCPF } from '@/utils/guardian-utils';
import { Badge } from '@/components/ui/badge';

interface ProfileContactCardsProps {
  userId: string;
  userData: any;
  refreshUserData: () => void;
}

export function ProfileContactCards({ userId, userData, refreshUserData }: ProfileContactCardsProps) {
  const { guardianDisplayInfo } = useProfileWithGuardian(userId);
  const isAdmin = userData.role === 'admin';
  const isStudent = userData.role === 'student';
  const isInstructor = userData.role === 'instructor';
  
  // Obter os dados de contato de emergência, priorizando os campos específicos
  // Para instrutores, os dados vêm da tabela instructors, para estudantes da tabela students
  const emergencyContact = userData.emergency_contact_name || userData.emergency_contact || '';
  const emergencyPhone = userData.emergency_contact_phone || userData.emergency_phone || '';
  const emergencyRelationship = userData.emergency_contact_relationship || '';
  
  // Verificar se há dados de contato de emergência
  const hasEmergencyContact = !!emergencyContact || !!emergencyPhone || !!emergencyRelationship;

  const shouldShowGuardianCard = guardianDisplayInfo?.isMinor && guardianDisplayInfo?.hasGuardian;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
      {/* Card do Responsável Legal - apenas para menores com responsável configurado */}
      {shouldShowGuardianCard && (
        <div className="rounded-lg p-5 border border-blue-200 dark:border-blue-700 shadow-sm hover:shadow-md transition-shadow bg-blue-50 dark:bg-blue-900/20">
          <h3 className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-4 flex items-center">
            <ShieldCheckIcon className="w-4 h-4 mr-1.5" />
            Responsável Legal
          </h3>
          <div className="space-y-4">
            {/* Nome do Responsável */}
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-800/50 flex items-center justify-center mr-3 flex-shrink-0">
                <User className="w-5 h-5 text-blue-600 dark:text-blue-300" />
              </div>
              <div className="flex flex-col">
                <EditableField
                  inline
                  label="Nome do Responsável"
                  value={userData.guardian_name || ''}
                  fieldName="guardian_name"
                  userId={userId}
                  className="font-medium text-blue-900 dark:text-blue-100"
                />
                {userData.guardian_relationship && (
                  <span className="text-xs text-blue-600 dark:text-blue-400">
                    {formatGuardianRelationship(userData.guardian_relationship)}
                  </span>
                )}
              </div>
            </div>

            {/* Email do Responsável */}
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-800/50 flex items-center justify-center mr-3 flex-shrink-0">
                <Mail className="w-5 h-5 text-blue-600 dark:text-blue-300" />
              </div>
              <EditableField
                inline
                label="Email do Responsável"
                value={userData.guardian_email || ''}
                fieldName="guardian_email"
                userId={userId}
                type="email"
                className="font-medium text-blue-900 dark:text-blue-100"
              />
            </div>

            {/* Telefone do Responsável */}
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-800/50 flex items-center justify-center mr-3 flex-shrink-0">
                <Phone className="w-5 h-5 text-blue-600 dark:text-blue-300" />
              </div>
              <EditablePhoneField
                inline
                label="Telefone do Responsável"
                value={userData.guardian_phone || ''}
                fieldName="guardian_phone"
                userId={userId}
                className="font-medium text-blue-900 dark:text-blue-100"
              />
            </div>

            {/* CPF do Responsável */}
            {/* {userData.guardian_document && (
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-800/50 flex items-center justify-center mr-3 flex-shrink-0">
                  <User className="w-5 h-5 text-blue-600 dark:text-blue-300" />
                </div>
                <div className="flex flex-col">
                  <span className="text-xs text-blue-600 dark:text-blue-400">CPF</span>
                  <span className="font-medium text-blue-900 dark:text-blue-100">
                    {formatGuardianCPF(userData.guardian_document)}
                  </span>
                </div>
              </div>
            )} */}
          </div>
        </div>
      )}

      {/* Informações de Contato do Usuário */}
      <div className="rounded-lg p-5 border border-slate-200 dark:border-slate-700 shadow-sm hover:shadow-md transition-shadow bg-white dark:bg-slate-800/50">
        <h3 className="text-sm font-medium text-slate-500 dark:text-slate-300 mb-4 flex items-center">
          <Mail className="w-4 h-4 mr-1.5" />
          {shouldShowGuardianCard ? 'Contato do Aluno' : 'Contato Principal'}
        </h3>
        <div className="space-y-4">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/30 flex items-center justify-center mr-3 flex-shrink-0">
              <Phone className="w-5 h-5 text-primary dark:text-white" />
            </div>
            <EditablePhoneField
              inline
              label="Telefone"
              value={userData.phone || ''}
              fieldName="phone"
              userId={userId}
              className="font-medium text-slate-900 dark:text-slate-100"
            />
          </div>
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/30 flex items-center justify-center mr-3 flex-shrink-0">
              <Mail className="w-5 h-5 text-primary dark:text-white" />
            </div>
            <span className="font-medium text-slate-900 dark:text-slate-100">{userData.email}</span>
          </div>
        </div>
      </div>

      {/* Endereço - apenas para não-administradores */}
      {!isAdmin && (
        <div className="rounded-lg p-5 border border-slate-200 dark:border-slate-700 shadow-sm hover:shadow-md transition-shadow bg-white dark:bg-slate-800/50">
          <div className="text-sm leading-relaxed text-slate-900 dark:text-slate-100">
            <EditableAddress
              address={userData.address || ''}
              userId={userId}
              className="font-medium"
              useDirectFields={true}
              onAddressUpdated={refreshUserData}
            />
          </div>
        </div>
      )}

      {/* Contato de Emergência - apenas para não-administradores e se não for menor com responsável */}
      {!isAdmin && !shouldShowGuardianCard && (
        <div className={cn(
          "rounded-lg p-5 border border-slate-200 dark:border-slate-700 shadow-sm hover:shadow-md transition-shadow bg-white dark:bg-slate-800/50",
          !userData.address && "md:col-span-2 lg:col-span-1"
        )}>
          <h3 className="text-sm font-medium text-slate-500 dark:text-slate-300 mb-4 flex items-center">
            <User className="w-4 h-4 mr-1.5" />
            Contato de Emergência
          </h3>
          <div className="space-y-4">
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/30 flex items-center justify-center mr-3 flex-shrink-0">
                <User className="w-5 h-5 text-primary dark:text-white" />
              </div>
              <div className="flex flex-col">
                <EditableField
                  inline
                  label="Nome do Contato"
                  value={emergencyContact}
                  fieldName={isInstructor ? "emergency_contact_name" : "emergency_contact"}
                  userId={userId}
                  className="font-medium text-slate-900 dark:text-slate-100"
                />
                <EditableField
                  inline
                  label="Relação"
                  value={emergencyRelationship}
                  fieldName="emergency_contact_relationship"
                  userId={userId}
                  className="text-xs text-slate-500 dark:text-slate-400 mt-0.5"
                />
              </div>
            </div>
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/30 flex items-center justify-center mr-3 flex-shrink-0">
                <Phone className="w-5 h-5 text-primary dark:text-white" />
              </div>
              <EditablePhoneField
                inline
                label="Telefone de Emergência"
                value={emergencyPhone || ''}
                fieldName={isInstructor ? "emergency_contact_phone" : "emergency_phone"}
                userId={userId}
                className="font-medium text-slate-900 dark:text-slate-100"
              />
            </div>
          </div>
        </div>
      )}

      {/* QR Code / Código de Check-in - apenas para estudantes */}
      {isStudent && (
        <div className={cn(
          "rounded-lg p-5 border border-slate-200 dark:border-slate-700 shadow-sm hover:shadow-md transition-shadow bg-white dark:bg-slate-800/50",
          (!userData.address && !hasEmergencyContact && !shouldShowGuardianCard) && "md:col-span-2 lg:col-span-1"
        )}>
          <h3 className="text-sm font-medium text-slate-500 dark:text-slate-300 mb-4 flex items-center">
            <QrCode className="w-4 h-4 mr-1.5" />
            Check-in
          </h3>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary/10 dark:bg-primary/30 rounded-full flex items-center justify-center">
              <QrCode className="w-5 h-5 text-primary dark:text-white" />
            </div>
            <div>
              <span className="text-xs text-slate-500 dark:text-slate-400 block">Código</span>
              <span className="text-xl font-bold text-slate-900 dark:text-slate-100">{userData.check_in_code}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 