'use client';

import { AlertTriangle } from "lucide-react";

interface ProfileErrorStateProps {
  error: string;
}

export function ProfileErrorState({ error }: ProfileErrorStateProps) {
  return (
    <div className="min-h-screen flex items-center justify-center p-8 bg-slate-50 dark:bg-slate-900">
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-8 max-w-md w-full text-center border border-slate-200 dark:border-slate-700">
        <AlertTriangle className="w-12 h-12 text-amber-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-rose-600 dark:text-rose-500 mb-2">
          Erro ao carregar perfil
        </h2>
        <p className="text-slate-600 dark:text-slate-400">
          {error || 'Não foi possível carregar as informações deste perfil.'}
        </p>
      </div>
    </div>
  );
} 