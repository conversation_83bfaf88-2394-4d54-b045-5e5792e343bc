'use client';

import { useState } from 'react';
import { Refresh<PERSON>w, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { syncUserDataFromAuth } from '../../actions';
import { cn } from '@/lib/utils';

interface ProfileSyncButtonProps {
  userId: string;
  className?: string;
}

export function ProfileSyncButton({ userId, className }: ProfileSyncButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [lastSync, setLastSync] = useState<{
    success: boolean;
    message?: string;
    updated?: boolean;
  } | null>(null);

  const handleSync = async () => {
    setIsLoading(true);
    setLastSync(null);

    try {
      const result = await syncUserDataFromAuth({ userId });
      
      setLastSync({
        success: result.success,
        message: result.success 
          ? (result as any).data?.message || 'Sincronização concluída'
          : (result as any).errors?._form || 'Erro na sincronização',
        updated: result.success ? (result as any).data?.updated : false
      });

      // Se foi atualizado, disparar evento para recarregar o perfil
      if (result.success && (result as any).data?.updated) {
        window.dispatchEvent(new CustomEvent('profile:updated', {
          detail: { 
            userId,
            fields: { 
              fullName: (result as any).data.newFullName 
            }
          }
        }));
      }
    } catch (error) {
      setLastSync({
        success: false,
        message: 'Erro inesperado durante a sincronização'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={cn('flex flex-col items-start gap-2', className)}>
      <Button
        variant="outline"
        size="sm"
        onClick={handleSync}
        disabled={isLoading}
        className="flex items-center gap-2"
      >
        <RefreshCw className={cn('w-4 h-4', isLoading && 'animate-spin')} />
        {isLoading ? 'Sincronizando...' : 'Sincronizar dados'}
      </Button>
      
      {lastSync && (
        <div className={cn(
          'flex items-center gap-2 text-sm px-3 py-2 rounded-md border',
          lastSync.success 
            ? 'text-green-700 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800'
            : 'text-red-700 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800'
        )}>
          {lastSync.success ? (
            <CheckCircle className="w-4 h-4" />
          ) : (
            <AlertCircle className="w-4 h-4" />
          )}
          <span>{lastSync.message}</span>
          {lastSync.updated && (
            <span className="text-xs text-green-600 dark:text-green-400">
              (Dados atualizados)
            </span>
          )}
        </div>
      )}
    </div>
  );
} 