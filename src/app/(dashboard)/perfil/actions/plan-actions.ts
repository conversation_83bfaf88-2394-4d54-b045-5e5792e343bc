'use server'

import { getCurrentUser } from '@/services/auth/actions/auth-actions'
import { cancelMembership } from '@/app/(dashboard)/academia/actions/membership-actions'
import { createTenantServerClient } from '@/services/supabase/server'

interface CancelPlanData {
  membershipId: string
  motivo?: string
  aplicarTaxaCancelamento?: boolean
}

interface ActionResult<T = unknown> {
  success: boolean
  data?: T
  errors?: Record<string, string>
}

interface NextPaymentData {
  payment_id: string
  due_date: string | null
  amount: number
  status: string
  payment_type: string
}

/**
 * Server action para cancelar plano de um estudante
 * Obtém automaticamente o tenant_id do usuário atual
 */
export async function cancelStudentPlan(data: CancelPlanData): Promise<ActionResult> {
  try {
    // Obter usuário atual
    const user = await getCurrentUser()
    
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    // Obter tenant_id do usuário
    const tenantId = user.app_metadata?.tenant_id

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não identificado' }
      }
    }

    // Chamar a server action de cancelamento com o tenant_id
    const result = await cancelMembership({
      membershipId: data.membershipId,
      motivo: data.motivo || 'Cancelamento solicitado pelo administrador',
      aplicarTaxaCancelamento: data.aplicarTaxaCancelamento || false
    }, tenantId)

    return result
  } catch (error) {
    console.error('Erro ao cancelar plano do estudante:', error)
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' }
    }
  }
}

/**
 * Server action para buscar a próxima data de cobrança de uma membership
 */
export async function getNextPaymentDate(membershipId: string): Promise<ActionResult<NextPaymentData | null>> {
  try {
    // Obter usuário atual
    const user = await getCurrentUser()

    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    // Obter tenant_id do usuário
    const tenantId = user.app_metadata?.tenant_id

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não identificado' }
      }
    }

    // Criar cliente Supabase
    const supabase = await createTenantServerClient()

    // Buscar o próximo pagamento pendente para a membership
    const { data, error } = await supabase
      .from('payments')
      .select('id, due_date, amount, status, payment_type')
      .eq('membership_id', membershipId)
      .eq('tenant_id', tenantId)
      .in('status', ['pending', 'awaiting_confirmation'])
      .eq('payment_type', 'recurring')
      .order('due_date', { ascending: true })
      .limit(1)
      .maybeSingle()

    if (error) {
      console.error('Erro ao buscar próximo pagamento:', error)
      return {
        success: false,
        errors: { _form: 'Erro ao buscar próxima data de cobrança' }
      }
    }

    // Se não há dados, significa que não há próximo pagamento pendente
    if (!data) {
      return {
        success: true,
        data: null
      }
    }

    // Retornar o resultado
    const nextPayment = data

    return {
      success: true,
      data: {
        payment_id: nextPayment.id,
        due_date: nextPayment.due_date,
        amount: nextPayment.amount || 0,
        status: nextPayment.status,
        payment_type: nextPayment.payment_type
      }
    }
  } catch (error) {
    console.error('Erro ao buscar próxima data de cobrança:', error)
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' }
    }
  }
}
