'use client'

import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { 
  FileText, 
  DollarSign, 
  Calendar, 
  Building,
  Upload,
  Download,
  Eye,
  Clock,
  CreditCard,
  UserCheck,
  AlertCircle,
  CheckCircle,
  Info
} from "lucide-react"
import { useEffect, useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { loadUserProfileCache } from "@/services/user/user-profile-cache"
import { CACHE_KEYS } from "@/constants/cache-keys"
import { cacheService } from "@/services/cache"
import { EditableField } from "../editable-field"
import { format } from "date-fns"
import { ptBR } from "date-fns/locale"
import { formatContractType, formatPaymentModel, formatCurrency } from "@/utils/format-utils"

interface ContratoTabProps {
  role?: string;
  userData?: any;
  userId?: string;
}

interface InstructorContractData {
  contract_type: string | null;
  payment_model: string | null;
  payment_value: string | null;
  payment_percentage: string | null;
  hire_date: string | null;
  branch_id: string | null;
  branch_name: string | null;
  status: string | null;
}

interface DocumentData {
  id: string;
  document_type: string;
  document_name: string;
  upload_date: string;
  expiry_date?: string;
  status: string;
}

interface PaymentData {
  lastPaymentAmount: number;
  lastPaymentDate: string | null;
  nextPaymentDue: string | null;
  paymentStatus: string;
  totalEarnings: number;
  averageMonthlyPayment: number;
}

// Função para buscar dados completos do instrutor incluindo informações da filial
async function fetchInstructorContractData(userId: string): Promise<InstructorContractData> {
  try {
    const response = await fetch(`/api/instructors/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Erro ao carregar dados do instrutor');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Erro ao buscar dados do instrutor:', error);
    throw error;
  }
}

export function ContratoTab({ role = 'instructor', userData: initialUserData, userId }: ContratoTabProps) {
  const isAdmin = role === 'admin';
  const isInstructor = role === 'instructor';
  
  const { data: userData, isLoading } = useQuery({
    queryKey: [CACHE_KEYS.USER_PROFILE[0], userId],
    queryFn: async () => {
      if (!userId && !initialUserData) return null;
      
      if (initialUserData) {
        return initialUserData;
      }
      
      const cachedData = userId ? cacheService.getData<any>([CACHE_KEYS.USER_PROFILE[0], userId]) : undefined;
      if (cachedData) {
        return cachedData;
      }
      
      const response = await loadUserProfileCache(userId as string);
      if (response.error || !response.data) {
        throw new Error(response.error || 'Erro ao carregar dados do perfil');
      }
      
      if (userId) {
        cacheService.setData([CACHE_KEYS.USER_PROFILE[0], userId], response.data);
      }
      
      return response.data;
    },
    staleTime: Infinity,
    gcTime: 30 * 60 * 1000,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: !!userId || !!initialUserData,
    initialData: () => {
      if (initialUserData) return initialUserData;
      return userId ? cacheService.getData<any>([CACHE_KEYS.USER_PROFILE[0], userId]) : undefined;
    },
  });

  // Query para buscar dados completos do instrutor incluindo informações da filial
  const { data: contractData, isLoading: isLoadingContract } = useQuery({
    queryKey: ['instructor-contract', userId],
    queryFn: () => fetchInstructorContractData(userId as string),
    enabled: !!userId && (userData?.role === 'instructor' || role === 'instructor'),
    staleTime: 0, // Sempre buscar dados frescos para debug
    gcTime: 10 * 60 * 1000, // 10 minutos
    refetchOnMount: true, // Forçar nova consulta ao montar
  });

  // Estado para documentos (simulado por enquanto)
  const [documents] = useState<DocumentData[]>([
    {
      id: '1',
      document_type: 'contract',
      document_name: 'Contrato de Trabalho.pdf',
      upload_date: '2024-01-15',
      expiry_date: '2024-12-31',
      status: 'active'
    },
    {
      id: '2', 
      document_type: 'certification',
      document_name: 'Certificado IBJJF.pdf',
      upload_date: '2024-02-10',
      expiry_date: '2025-02-10',
      status: 'active'
    }
  ]);

  // Estado para dados de pagamento (simulado por enquanto)
  const [paymentData] = useState<PaymentData>({
    lastPaymentAmount: 2500.00,
    lastPaymentDate: '2024-01-15',
    nextPaymentDue: '2024-02-15',
    paymentStatus: 'em_dia',
    totalEarnings: 12500.00,
    averageMonthlyPayment: 2500.00
  });

  if (isLoading || isLoadingContract) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="p-6">
        <div className="text-center text-gray-500 dark:text-gray-400">
          Dados do usuário não encontrados
        </div>
      </div>
    );
  }

  // Se é instrutor mas não conseguimos carregar dados do contrato, usar dados básicos do usuário
  const finalContractData: InstructorContractData = (contractData ?? {
    contract_type: userData.contract_type || userData.metadata?.contract_type,
    payment_model: userData.payment_model || userData.metadata?.payment_model,
    payment_value: userData.payment_value || userData.metadata?.payment_value,
    payment_percentage: userData.payment_percentage || userData.metadata?.payment_percentage,
    hire_date: userData.hire_date || userData.metadata?.hire_date,
    branch_id: userData.branch_id,
    branch_name: userData.branch_name || 'Não informada',
    status: userData.status || 'active'
  }) as InstructorContractData;

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Não informado';
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: ptBR });
    } catch {
      return 'Data inválida';
    }
  };

  const getPaymentStatusBadge = (status: string) => {
    const statusConfig = {
      'em_dia': { variant: 'default' as const, icon: CheckCircle, text: 'Em Dia' },
      'atrasado': { variant: 'destructive' as const, icon: AlertCircle, text: 'Atrasado' },
      'pendente': { variant: 'secondary' as const, icon: Clock, text: 'Pendente' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pendente;
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {config.text}
      </Badge>
    );
  };

  const getDocumentIcon = (type: string) => {
    const icons = {
      'contract': FileText,
      'certification': UserCheck,
      'insurance': Building,
      'tax': CreditCard
    };
    return icons[type as keyof typeof icons] || FileText;
  };

  return (
    <div className="p-6 space-y-6">
      {/* Informações Contratuais Básicas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6 bg-white dark:bg-slate-800">
          <div className="flex items-center mb-4">
            <FileText className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Informações Contratuais</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Tipo de Contrato</label>
              <EditableField
                label=""
                fieldName="contract_type"
                value={finalContractData.contract_type || ''}
                userId={userId}
                placeholder="Selecione o tipo de contrato"
                type="select"
                options={[
                  { value: 'clt', label: 'CLT - Carteira Assinada' },
                  { value: 'pj', label: 'PJ - Pessoa Jurídica' },
                  { value: 'autonomo', label: 'Autônomo' },
                  { value: 'parceria', label: 'Parceria/Associação' },
                  { value: 'associacao', label: 'Sócio da Academia' }
                ]}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Data de Contratação</label>
              <EditableField
                label=""
                fieldName="hire_date"
                value={finalContractData.hire_date || ''}
                userId={userId}
                placeholder="dd/mm/aaaa"
                type="date"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Filial</label>
              <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                {finalContractData.branch_name || 'Não informada'}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
              <div className="mt-1">
                <Badge variant={finalContractData.status === 'active' ? 'default' : 'secondary'}>
                  {finalContractData.status === 'active' ? 'Ativo' : 'Inativo'}
                </Badge>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-white dark:bg-slate-800">
          <div className="flex items-center mb-4">
            <DollarSign className="w-5 h-5 mr-2 text-green-600 dark:text-green-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Modelo de Pagamento</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Modelo de Remuneração</label>
              <EditableField
                label=""
                fieldName="payment_model"
                value={finalContractData.payment_model || ''}
                userId={userId}
                placeholder="Selecione o modelo de pagamento"
                type="select"
                options={[
                  { value: 'hora_aula', label: 'Por Hora/Aula' },
                  { value: 'salario_mensal', label: 'Salário Mensal' },
                  { value: 'comissao', label: 'Comissão por Resultado' },
                  { value: 'participacao_lucros', label: 'Participação nos Lucros' }
                ]}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Valor</label>
              <EditableField
                label=""
                fieldName="payment_value"
                value={finalContractData.payment_value || ''}
                userId={userId}
                placeholder="0,00"
                type="currency"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Percentual</label>
              <EditableField
                label=""
                fieldName="payment_percentage"
                value={finalContractData.payment_percentage || ''}
                userId={userId}
                placeholder="0%"
                type="percentage"
              />
            </div>
          </div>
        </Card>
      </div>

      {/* Documentos Contratuais */}
      <Card className="p-6 bg-white dark:bg-slate-800">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <FileText className="w-5 h-5 mr-2 text-purple-600 dark:text-purple-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Documentos Contratuais</h3>
          </div>
          <Button size="sm" className="flex items-center gap-2">
            <Upload className="w-4 h-4" />
            Adicionar Documento
          </Button>
        </div>
        
        <div className="space-y-3">
          {documents.length > 0 ? documents.map((doc) => {
            const Icon = getDocumentIcon(doc.document_type);
            return (
              <div key={doc.id} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <div className="flex items-center space-x-3">
                  <Icon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-gray-100">{doc.document_name}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Enviado em {formatDate(doc.upload_date)}
                      {doc.expiry_date && ` • Expira em ${formatDate(doc.expiry_date)}`}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={doc.status === 'active' ? 'default' : 'secondary'} className="text-xs">
                    {doc.status === 'active' ? 'Ativo' : 'Inativo'}
                  </Badge>
                  <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                    <Eye className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            );
          }) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <FileText className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>Nenhum documento adicionado ainda</p>
              <p className="text-sm">Clique em "Adicionar Documento" para fazer upload de contratos e certificados</p>
            </div>
          )}
        </div>
      </Card>

      {/* Informações de Pagamento */}
      <Card className="p-6 bg-white dark:bg-slate-800">
        <div className="flex items-center mb-4">
          <CreditCard className="w-5 h-5 mr-2 text-orange-600 dark:text-orange-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Informações de Pagamento</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Último Pagamento</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {formatCurrency(paymentData.lastPaymentAmount)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  {formatDate(paymentData.lastPaymentDate)}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600 dark:text-green-400 opacity-75" />
            </div>
          </div>
          
          <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Próximo Pagamento</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {formatDate(paymentData.nextPaymentDue)}
                </p>
                <div className="mt-1">
                  {getPaymentStatusBadge(paymentData.paymentStatus)}
                </div>
              </div>
              <Calendar className="w-8 h-8 text-blue-600 dark:text-blue-400 opacity-75" />
            </div>
          </div>
          
          <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Recebido</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {formatCurrency(paymentData.totalEarnings)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  Desde {formatDate(finalContractData.hire_date)}
                </p>
              </div>
              <CreditCard className="w-8 h-8 text-purple-600 dark:text-purple-400 opacity-75" />
            </div>
          </div>
          
          <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Média Mensal</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {formatCurrency(paymentData.averageMonthlyPayment)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  Últimos 6 meses
                </p>
              </div>
              <Info className="w-8 h-8 text-gray-600 dark:text-gray-400 opacity-75" />
            </div>
          </div>
        </div>

        <Separator className="my-4" />
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Info className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Histórico completo de pagamentos disponível na seção de relatórios
            </span>
          </div>
          <Button variant="outline" size="sm">
            Ver Histórico Completo
          </Button>
        </div>
      </Card>
    </div>
  );
}
