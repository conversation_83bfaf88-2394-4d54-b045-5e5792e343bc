'use client'

import { But<PERSON> } from '@/components/ui/button'
import { RotateCcw } from 'lucide-react'
import { useEditMode } from './edit-context'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import { usePermission } from '@/services/permissions'
import { useEffect } from 'react'

interface EditButtonProps {
  userId: string
  className?: string
}

export function EditButton({ userId, className }: EditButtonProps) {
  const { editHistory, undoLastEdit } = useEditMode()
  const { isAllowed } = usePermission('profile', 'edit', userId)
  
  // Debugging: monitorar o estado do histórico de edições
  useEffect(() => {
    console.log('[EDIT BUTTON] Estado atual do histórico de edições:', editHistory)
  }, [editHistory])
  
  // Se o botão não deve ser exibido, retornar null
  if (!isAllowed) return null
  
  // Verificar se há algo para desfazer
  const canUndo = editHistory && editHistory.length > 0
  
  const handleClick = () => {
    console.log('[EDIT BUTTON] Clicou em desfazer. Histórico:', editHistory)
    if (canUndo) {
      undoLastEdit()
      toast.info('Última alteração desfeita')
    } else {
      toast.info('Não há alterações para desfazer')
    }
  }
  
  return (
    <Button 
      variant="outline" 
      size="sm" 
      onClick={handleClick}
      className={cn("flex items-center gap-1", className)}
      disabled={!canUndo}
    >
      <RotateCcw className="h-4 w-4" />
      Desfazer última alteração
    </Button>
  )
} 