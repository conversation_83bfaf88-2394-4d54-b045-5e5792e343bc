'use client'

import { createContext, useState, useContext, ReactNode, useCallback, useEffect } from 'react'
import { toast } from 'sonner'

// Função auxiliar para formatar nomes de campo para exibição
const formatFieldName = (fieldName: string): string => {
  const fieldMappings: Record<string, string> = {
    fullName: 'Nome completo',
    firstName: 'Nome',
    lastName: 'Sobrenome',
    email: 'Email',
    phone: 'Telefone',
    address: 'Endereç<PERSON>',
    emergency_contact: 'Contato de emergência',
    emergency_contact_relationship: 'Relação do contato',
    emergency_phone: 'Telefone de emergência',
    birthDate: 'Data de nascimento',
    gender: 'Gê<PERSON><PERSON>'
  }

  return fieldMappings[fieldName] || `Campo ${fieldName}`
}

interface EditContextType {
  editHistory: Record<string, any>[]
  undoLastEdit: () => void
  undoSpecificEdit: (field: string, previousValue: any) => Promise<boolean>
  fieldValues: Record<string, any>
  setFieldValue: (field: string, value: any) => Promise<boolean>
  resetChanges: () => void
  getCurrentValue: (field: string) => any
  revertField: (field: string) => void
}

const EditContext = createContext<EditContextType | undefined>(undefined)

interface EditProviderProps {
  children: ReactNode
  initialData: Record<string, any>
  onFieldChange: (field: string, value: any) => Promise<boolean>
}

export function EditProvider({ children, initialData, onFieldChange }: EditProviderProps) {
  const [fieldValues, setFieldValues] = useState<Record<string, any>>({})
  const [editHistory, setEditHistory] = useState<Record<string, any>[]>([])
  const [currentData, setCurrentData] = useState<Record<string, any>>(initialData)

  const getCurrentValue = useCallback((field: string) => {
    return currentData[field] !== undefined ? currentData[field] : initialData[field]
  }, [currentData, initialData])
  
  // Função para reverter um campo específico - definida antes de setFieldValue
  const revertField = useCallback(async (field: string) => {
    // Encontrar a última edição para este campo específico
    const lastEditForField = [...editHistory].reverse().find(edit => edit.field === field)
    
    if (!lastEditForField) return
    
    const { previousValue } = lastEditForField
    
    try {
      // Atualizar dados atuais
      setCurrentData(prev => ({
        ...prev,
        [field]: previousValue
      }))
      
      // Remover este item do histórico de edições
      setEditHistory(prev => prev.filter(edit => 
        !(edit.field === field && edit.previousValue === previousValue)
      ))
      
      // Atualizar valores e dados atuais
      const newValues = { ...fieldValues }
      delete newValues[field]
      setFieldValues(newValues)
      
      // Sincronizar com o backend
      await onFieldChange(field, previousValue)
      
      console.log(`[EDIT] Campo ${field} revertido para valor anterior:`, previousValue)
    } catch (error) {
      console.error(`Erro ao reverter campo ${field}:`, error)
    }
  }, [editHistory, fieldValues, onFieldChange])
  
  // Função para desfazer uma alteração específica pelo campo e valor anterior
  const undoSpecificEdit = useCallback(async (field: string, previousValue: any) => {
    console.log(`[UNDO_SPECIFIC] Desfazendo alteração específica no campo ${field} para valor:`, previousValue)
    
    try {
      // Atualizar dados atuais diretamente
      setCurrentData(prev => ({
        ...prev,
        [field]: previousValue
      }))
      
      // Atualizar valores de campo locais
      const newValues = { ...fieldValues }
      delete newValues[field]
      setFieldValues(newValues)
      
      // Remover este item específico do histórico de edições
      setEditHistory(prev => prev.filter(edit => 
        !(edit.field === field && edit.previousValue === previousValue)
      ))
      
      // Forçar atualização no backend mesmo que o valor pareça igual
      // Isso é crucial para o desfazer funcionar corretamente
      const valueWithForce = {
        value: previousValue,
        force: true
      }
      
      // Chamar a função onFieldChange para sincronizar com o backend
      const success = await onFieldChange(field, valueWithForce)
      
      if (success) {
        toast.info('Alteração desfeita')
        return true
      } else {
        toast.error('Não foi possível desfazer a alteração')
        return false
      }
    } catch (error) {
      console.error('[UNDO_SPECIFIC] Erro ao desfazer alteração:', error)
      toast.error('Erro ao desfazer alteração. Tente novamente.')
      return false
    }
  }, [fieldValues, onFieldChange]);
  
  // Função para desfazer a última edição - definida antes de setFieldValue
  const undoLastEdit = useCallback(async () => {
    // Verificar o histórico atual no momento da chamada
    const currentHistory = editHistory
    console.log('[UNDO] Tentando desfazer última alteração. Histórico atual:', currentHistory)
    
    if (currentHistory.length === 0) {
      console.log('[UNDO] Histórico vazio, nada para desfazer')
      toast.info('Não há alterações para desfazer')
      return
    }
    
    try {
      const lastEdit = currentHistory[currentHistory.length - 1]
      const { field, previousValue } = lastEdit
      
      console.log(`[UNDO] Desfazendo última edição: campo ${field}, valor anterior:`, previousValue)
      
      // Remover a última edição do histórico imediatamente para evitar operações duplicadas
      setEditHistory(prev => {
        const newHistory = prev.slice(0, -1)
        console.log('[UNDO] Histórico após remoção:', newHistory)
        return newHistory
      })
      
      // Atualizar valores de campo locais
      const newValues = { ...fieldValues }
      delete newValues[field]
      setFieldValues(newValues)
      
      // Atualizar dados atuais diretamente
      setCurrentData(prev => ({
        ...prev,
        [field]: previousValue
      }))
      
      console.log(`[UNDO] Restaurando campo ${field} para valor anterior:`, previousValue)
      
      // Forçar atualização no backend mesmo se o valor parecer igual
      const valueWithForce = {
        value: previousValue,
        force: true
      }
      
      // Chamar a função onFieldChange para sincronizar com o backend
      const success = await onFieldChange(field, valueWithForce)
      
      if (success) {
        toast.info('Alteração desfeita')
      } else {
        // Se falhar, restaurar o histórico de edição
        console.error(`[UNDO] Falha ao sincronizar campo ${field} com valor ${previousValue}`)
        setEditHistory(prev => [...prev, lastEdit])
        toast.error('Não foi possível desfazer a alteração')
      }
    } catch (error) {
      console.error('[UNDO] Erro ao desfazer alteração:', error)
      toast.error('Erro ao desfazer alteração. Tente novamente.')
    }
  }, [editHistory, fieldValues, onFieldChange])
  
  const setFieldValue = useCallback(async (field: string, value: any) => {
    try {
      if (field === 'address_batch' && typeof value === 'object') {
        console.log(`[EDIT] Atualizando múltiplos campos de endereço:`, value);

        const addressFieldEntries: { field: string; previousValue: any }[] = [];
        const newValues = { ...fieldValues };
        const batchData: Record<string, any> = {};
        
        // Processar cada campo do lote
        for (const [addressField, addressValue] of Object.entries(value)) {
          const previousValue = currentData[addressField];
          
          // Só adicionar ao histórico e atualizar se o valor mudou
          if (addressValue !== previousValue) {
            addressFieldEntries.push({ field: addressField, previousValue });
            
            // Atualizar valores locais
            newValues[addressField] = addressValue;
            
            // Adicionar ao objeto de atualização em lote
            batchData[addressField] = addressValue;
            
            // Atualizar dados atuais
            setCurrentData(prevData => ({
              ...prevData,
              [addressField]: addressValue
            }));
          }
        }
        
        // Se não houver mudanças, retornar sucesso sem fazer nada
        if (Object.keys(batchData).length === 0) {
          console.log('[EDIT] Nenhum campo de endereço mudou, ignorando atualização');
          return true;
        }
        
        // Atualizar o histórico de edições
        setEditHistory(prev => [...prev, ...addressFieldEntries]);
        
        // Atualizar os valores de campos
        setFieldValues(newValues);
        
        // Enviar atualização em lote para o backend
        const success = await onFieldChange('address_batch', batchData);
        
        if (success) {
          toast.success('Endereço atualizado com sucesso', {
            action: {
              label: 'Desfazer',
              onClick: () => {
                // Desfazer todos os campos de endereço de uma vez
                Object.entries(batchData).forEach(([addressField, _]) => {
                  const entry = addressFieldEntries.find(e => e.field === addressField);
                  if (entry) {
                    undoSpecificEdit(entry.field, entry.previousValue);
                  }
                });
              }
            }
          });
          
          // Disparar evento personalizado para notificar outros componentes
          if (typeof window !== 'undefined') {
            const event = new CustomEvent('edit:field-updated', { 
              detail: { 
                fields: batchData,
                timestamp: Date.now()
              } 
            });
            window.dispatchEvent(event);
          }
          
          return true;
        } else {
          // Reverter alterações em caso de falha
          setEditHistory(prev => prev.filter(item => !addressFieldEntries.some(e => e.field === item.field && e.previousValue === item.previousValue)));
          setFieldValues(fieldValues);
          
          // Reverter dados atuais
          const revertedData = { ...currentData };
          Object.entries(batchData).forEach(([field, _]) => {
            const entry = addressFieldEntries.find(e => e.field === field);
            if (entry) {
              revertedData[field] = entry.previousValue;
            }
          });
          setCurrentData(revertedData);
          
          return false;
        }
      } else {
        // Obter valor atual para histórico antes da mudança
        const previousValue = currentData[field];
        
        // Verificar se o valor está realmente mudando
        if (value === previousValue) {
          console.log(`[EDIT] Valor de ${field} não mudou, ignorando atualização`);
          return true;
        }
        
        console.log(`[EDIT] Atualizando campo ${field} de "${previousValue}" para "${value}"`);
        
        // Atualizar valores de campo locais imediatamente para UI responsiva
        setFieldValues(prev => ({ ...prev, [field]: value }));
        
        // Atualizar dados atuais diretamente para refletir imediatamente
        setCurrentData(prevData => ({
          ...prevData,
          [field]: value
        }));
        
        // Registrar a edição no histórico para desfazer
        setEditHistory(prev => [...prev, { field, previousValue, timestamp: Date.now() }]);
        
        // Chamar função para sincronizar com backend
        const success = await onFieldChange(field, value);
        
        if (success) {
          const handleUndoToast = () => {
            return undoSpecificEdit(field, previousValue);
          };
          
          toast.success(`${formatFieldName(field)} atualizado`, {
            action: {
              label: 'Desfazer',
              onClick: handleUndoToast
            }
          });
          
          // Disparar evento personalizado para notificar outros componentes
          if (typeof window !== 'undefined') {
            const event = new CustomEvent('edit:field-updated', { 
              detail: { 
                field,
                value,
                timestamp: Date.now()
              } 
            });
            window.dispatchEvent(event);
          }
          
          return true;
        } else {
          // Reverter alterações em caso de falha
          setEditHistory(prev => prev.filter(item => !(item.field === field && item.previousValue === previousValue)));
          
          const newValues = { ...fieldValues };
          delete newValues[field];
          setFieldValues(newValues);
          
          // Reverter dados atuais
          setCurrentData(prevData => ({
            ...prevData,
            [field]: previousValue
          }));
          
          return false;
        }
      }
    } catch (error) {
      console.error(`[EDIT] Erro ao atualizar campo ${field}:`, error);
      toast.error(`Erro ao atualizar ${formatFieldName(field)}`);
      return false;
    }
  }, [currentData, fieldValues, onFieldChange, undoSpecificEdit]);

  // Adicionar listener para eventos de atualização de campo
  useEffect(() => {
    const handleFieldUpdated = (event: Event) => {
      const customEvent = event as CustomEvent;
      const field = customEvent.detail?.field;
      const value = customEvent.detail?.value;
      const fields = customEvent.detail?.fields;
      
      // Caso 1: Atualização de campo único
      if (field && value !== undefined) {
        console.log(`[EDIT_CONTEXT] Recebido evento de atualização para campo ${field}:`, value);
        
        // Atualizar estado local para manter consistência
        setCurrentData(prevData => ({
          ...prevData,
          [field]: value
        }));
      }
      
      // Caso 2: Atualização de múltiplos campos
      else if (fields && typeof fields === 'object') {
        console.log(`[EDIT_CONTEXT] Recebido evento de atualização para múltiplos campos:`, fields);
        
        // Atualizar estado local para todos os campos
        setCurrentData(prevData => ({
          ...prevData,
          ...fields
        }));
      }
    };
    
    window.addEventListener('edit:field-updated', handleFieldUpdated);
    
    return () => {
      window.removeEventListener('edit:field-updated', handleFieldUpdated);
    };
  }, []);

  const resetChanges = useCallback(() => {
    setFieldValues({})
    setEditHistory([])
    setCurrentData(initialData)
  }, [initialData])

  useEffect(() => {
    // Apenas atualize o currentData se initialData mudar
    // e se for uma mudança real para evitar ciclos de atualização
    const hasChanges = Object.keys(initialData).some(
      key => initialData[key] !== currentData[key]
    );
    
    if (hasChanges) {
      console.log('[EDIT] Dados iniciais atualizados no contexto de edição')
      setCurrentData(prevData => {
        // Manter os campos que foram editados localmente
        const updatedData = { ...initialData };
        
        // Preservar valores editados que ainda não foram sincronizados
        Object.keys(fieldValues).forEach(field => {
          updatedData[field] = fieldValues[field];
        });
        
        // Limpar entradas de histórico para campos que foram sincronizados externamente
        // (isso previne desfazer mudanças que já foram propagadas)
        const updatedHistory = [...editHistory].filter(edit => 
          fieldValues[edit.field] !== undefined
        );
        
        if (updatedHistory.length !== editHistory.length) {
          setEditHistory(updatedHistory);
        }
        
        return updatedData;
      });
    }
  }, [initialData, fieldValues, currentData, editHistory])

  return (
    <EditContext.Provider
      value={{
        editHistory,
        undoLastEdit,
        undoSpecificEdit,
        fieldValues,
        setFieldValue,
        resetChanges,
        getCurrentValue,
        revertField
      }}
    >
      {children}
    </EditContext.Provider>
  )
}

export function useEditMode() {
  const context = useContext(EditContext)
  if (context === undefined) {
    throw new Error('useEditMode deve ser usado dentro de um EditProvider')
  }
  return context
} 