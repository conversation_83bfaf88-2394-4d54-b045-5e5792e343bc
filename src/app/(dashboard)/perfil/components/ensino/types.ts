export interface Student {
  id: string;
  name: string;
  avatar?: string | null;
  beltColor?: string;
  degree?: number;
}

export interface ClassGroupData {
  id: string;
  name: string;
  description?: string | null;
  category?: string | null;
  is_active: boolean;
  max_capacity?: number | null;
  min_age?: number | null;
  max_age?: number | null;
  min_belt_level?: string | null;
  max_belt_level?: string | null;
  start_date?: string | null;
  end_date?: string | null;
  created_at: string;
  branch: {
    id: string;
    name: string;
  };
  totalStudents: number;
  capacityUsage?: number | null;
  students: Student[];
}

export interface EnsinoStats {
  totalClassGroups: number;
  totalStudents: number;
  averageCapacityUsage: number;
  activeClassGroups: number;
} 