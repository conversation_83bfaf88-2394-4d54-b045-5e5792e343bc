'use client'

import { TurmaInfo } from './TurmaInfo';
import { MatriculaInfo } from './MatriculaInfo';
import { HistoricoPresenca } from './HistoricoPresenca';
import { Card } from '@/components/ui/card';
import { AlertCircle, GraduationCap } from 'lucide-react';
import { useStudentEnrollment } from '@/hooks/user';

interface MatriculaTabProps {
  userId: string;
}

function MatriculaLoadingState() {
  return (
    <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
      {[1, 2, 3].map((i) => (
        <Card key={i} className="p-6 bg-white dark:bg-slate-800">
          <div className="animate-pulse">
            <div className="flex items-center gap-3 mb-4">
              <div className="h-5 w-5 bg-slate-200 dark:bg-slate-600 rounded"></div>
              <div className="h-6 w-48 bg-slate-200 dark:bg-slate-600 rounded"></div>
            </div>
            <div className="space-y-3">
              <div className="h-4 w-full bg-slate-200 dark:bg-slate-600 rounded"></div>
              <div className="h-4 w-3/4 bg-slate-200 dark:bg-slate-600 rounded"></div>
              <div className="h-4 w-1/2 bg-slate-200 dark:bg-slate-600 rounded"></div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}

function MatriculaEmptyState() {
  return (
    <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
      <Card className="p-8 bg-white dark:bg-slate-800 text-center">
        <GraduationCap className="h-12 w-12 text-slate-400 dark:text-slate-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2">
          Nenhuma matrícula encontrada
        </h3>
        <p className="text-slate-600 dark:text-slate-400">
          Este aluno ainda não possui matrículas em nenhuma turma.
        </p>
      </Card>
    </div>
  );
}

function MatriculaErrorState({ error }: { error: string }) {
  return (
    <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
      <Card className="p-6 bg-white dark:bg-slate-800 border-red-200 dark:border-red-800">
        <div className="flex items-center gap-3 text-red-600 dark:text-red-400">
          <AlertCircle className="h-5 w-5" />
          <div>
            <h3 className="font-semibold">Erro ao carregar dados</h3>
            <p className="text-sm text-red-500 dark:text-red-400 mt-1">{error}</p>
          </div>
        </div>
      </Card>
    </div>
  );
}

export function MatriculaTab({ userId }: MatriculaTabProps) {
  const { data: enrollmentData, isLoading, error } = useStudentEnrollment(userId);

  if (isLoading) {
    return <MatriculaLoadingState />;
  }

  if (error) {
    return <MatriculaErrorState error={error instanceof Error ? error.message : 'Erro desconhecido'} />;
  }

  if (!enrollmentData) {
    return <MatriculaEmptyState />;
  }

  return (
    <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
      <TurmaInfo classGroup={enrollmentData.classGroup} />
      <MatriculaInfo enrollment={enrollmentData.enrollment} />
      <HistoricoPresenca 
        attendance={enrollmentData.attendance} 
        studentId={enrollmentData.studentId}
      />
    </div>
  );
} 