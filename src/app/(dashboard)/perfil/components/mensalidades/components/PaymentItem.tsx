'use client'

import { useState } from "react"
import { Settings } from "lucide-react"
import { toast } from "sonner"
import { useUserRole } from "@/hooks/user/Permissions"
import { updatePaymentDetails, confirmPayment } from "@/services/billing/payment-actions"
import { PaymentManagementModal } from "./PaymentManagementModal"
import { formatPaymentMethodName } from '@/utils/payment-method-formatter'

import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { FileText } from "lucide-react"
import { format, isFuture, differenceInCalendarMonths } from "date-fns"
import { ptBR } from "date-fns/locale"
import clsx from "clsx"
import { PaymentItemProps, statusColor, statusText } from "../types/types"

export function PaymentItem({
  pagamento,
  showReceipt = true,
  onUpdate,
}: PaymentItemProps) {
  const { isAdmin } = useUserRole()

  type ModalPaymentData = {
    id: string
    amount: number
    currency: string
    status: string
    payment_method?: string
    paid_at?: string
    due_date?: string
    description?: string
  }

  const [showManagementModal, setShowManagementModal] = useState(false)
  const [paymentData, setPaymentData] = useState<ModalPaymentData | null>(null)

  const mapStatus = (status: string) => {
    switch (status) {
      case 'pago':
        return 'paid'
      case 'pendente':
        return 'pending'
      case 'atrasado':
        return 'overdue'
      case 'awaiting_confirmation':
        return 'awaiting_confirmation'
      default:
        return status
    }
  }

  const mapMetodoPagamento = (metodo?: string) => {
    if (!metodo) return undefined
    const m = metodo.toLowerCase()
    if (m.includes('pix')) return 'pix'
    if (m.includes('crédito')) return 'credit_card'
    if (m.includes('débito')) return 'debit_card'
    if (m.includes('transfer')) return 'bank_transfer'
    if (m.includes('dinheiro')) return 'cash'
    if (m.includes('boleto')) return 'boleto'
    return undefined
  }

  const handleManage = () => {
    setPaymentData({
      id: pagamento.id,
      amount: pagamento.valor,
      currency: 'BRL',
      status: mapStatus(pagamento.status),
      payment_method: mapMetodoPagamento(pagamento.metodoPagamento),
      paid_at: undefined, // A data de pagamento deve vir do banco, não da data de vencimento
      due_date: pagamento.data ? pagamento.data.toISOString().split('T')[0] : undefined,
      description: pagamento.descricao,
    })
    setShowManagementModal(true)
  }

  const handleSavePayment = async (updatedData: Partial<ModalPaymentData>) => {
    try {
      const result = await updatePaymentDetails({
        paymentId: pagamento.id,
        ...updatedData,
      })
      if (!result.success) {
        toast.error(result.errors?._form || 'Erro ao atualizar pagamento')
        return
      }
      toast.success('Pagamento atualizado com sucesso')
      setShowManagementModal(false)
      onUpdate?.()
    } catch {
      toast.error('Erro inesperado ao atualizar pagamento')
    }
  }

  const handleConfirmPayment = async (paymentId: string) => {
    try {
      const result = await confirmPayment({
        paymentId: paymentId,
        novoStatus: 'paid'
      })
      if (!result.success) {
        toast.error(result.errors?._form || 'Erro ao confirmar pagamento')
        return
      }
      toast.success('Pagamento confirmado com sucesso')
      setShowManagementModal(false)
      onUpdate?.()
    } catch (error) {
      toast.error('Erro inesperado ao confirmar pagamento')
    }
  }

  const today = new Date()
  let displayStatusLabel = pagamento.status === 'pendente' ? 'Pendência' : statusText[pagamento.status]

  if (pagamento.status === 'pendente' && isFuture(pagamento.data)) {
    const diff = differenceInCalendarMonths(pagamento.data, today)
    const monthName = format(pagamento.data, 'MMMM', { locale: ptBR })

    if (diff <= 1) {
      displayStatusLabel = `Próximo`
    } else {
      displayStatusLabel = `Previsto`
    }
  }

  return (
    <>
      <div className="flex items-center justify-between p-4 rounded-lg border border-gray-200 dark:border-gray-800 bg-slate-50 dark:bg-slate-700/50">
      <div className="flex items-center gap-4">
        <div className={clsx(
          "w-2 h-2 rounded-full",
          statusColor[pagamento.status]
        )} />
        <div>
          <p className="font-medium text-gray-900 dark:text-gray-100">
            {format(pagamento.data, "dd 'de' MMMM", { locale: ptBR })}
          </p>
          <div className="flex items-center gap-2">
            {pagamento.tipoLabel && (
              <>
                <span className="text-gray-400">•</span>
                <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                  {pagamento.tipoLabel}
                </span>
              </>
            )}
          </div>
        </div>
      </div>
      
      <div className="flex items-center gap-4">
        <p className="font-medium text-gray-900 dark:text-gray-100">
          R$ {pagamento.valor.toFixed(2)}
        </p>
        
        {pagamento.status === 'pendente' ? (
          <Badge variant="outline" className="text-yellow-600 border-yellow-600">
            {displayStatusLabel}
          </Badge>
        ) : pagamento.status === 'awaiting_confirmation' ? (
          <Badge variant="outline" className="text-blue-600 border-blue-600">
            {statusText[pagamento.status]}
          </Badge>
        ) : (
          showReceipt && pagamento.comprovante && (
            <Button variant="ghost" size="sm" className="gap-2">
              <FileText className="w-4 h-4" />
              Comprovante
            </Button>
          )
        )}

        {/* Botão de Gerenciar visível apenas para administradores */}
        {isAdmin && (
          <Button variant="ghost" size="sm" className="gap-2" onClick={handleManage}>
            <Settings className="w-4 h-4" />
            Gerenciar
          </Button>
        )}
      </div>
      </div>

      {/* Modal de Gerenciamento de Pagamento */}
      <PaymentManagementModal
        isOpen={showManagementModal}
        onClose={() => setShowManagementModal(false)}
        paymentData={paymentData as any}
        onSave={handleSavePayment}
        onConfirm={handleConfirmPayment}
        onUpdate={onUpdate}
      />
    </>
  )
}
