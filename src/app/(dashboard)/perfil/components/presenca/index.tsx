'use client'

import { Card } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Users, Calendar, Clock, TrendingUp } from "lucide-react"
import { format, parseISO } from "date-fns"
import { ptBR } from "date-fns/locale"
import { useQuery } from "@tanstack/react-query"
import { CACHE_KEYS } from "@/constants/cache-keys"
import { cacheService } from "@/services/cache"

type Aula = {
  id: string;
  data: Date;
  horario: string;
  professor: string;
  tipo: string;
  duracao: number;
}

type PresencaStats = {
  presencaMes: number;
  presencaTotal: number;
  totalAulas: number;
  sequenciaAtual: number;
  maiorSequencia: number;
}

interface PresencaTabProps {
  userId: string
}

export function PresencaTab({ userId }: PresencaTabProps) {

  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: CACHE_KEYS.ATTENDANCE.STATS(userId),
    queryFn: async () => {
      console.log(`[API] Buscando estatísticas de presença para o usuário ${userId}`);
      
      const cachedStats = cacheService.getData<PresencaStats>(CACHE_KEYS.ATTENDANCE.STATS(userId));
      if (cachedStats) {
        console.log(`[CACHE] Usando estatísticas de presença do cache: ${userId}`);
        return cachedStats;
      }
      
      try {
        const response = await fetch(`/api/user/${userId}/attendance/stats`);
        const statsData = await response.json();
        
        if (statsData.error) {
          throw new Error(statsData.error);
        }
        
        const formattedStats = {
          presencaMes: statsData.monthlyRate || 0,
          presencaTotal: statsData.totalRate || 0,
          totalAulas: statsData.totalClasses || 0,
          sequenciaAtual: statsData.currentStreak || 0,
          maiorSequencia: statsData.longestStreak || 0
        } as PresencaStats;
        
        cacheService.setData(CACHE_KEYS.ATTENDANCE.STATS(userId), formattedStats);
        
        return formattedStats;
      } catch (error) {
        console.error('[API] Erro ao buscar estatísticas de presença:', error);
        const cachedStats = cacheService.getData<PresencaStats>(CACHE_KEYS.ATTENDANCE.STATS(userId));
        if (cachedStats) {
          console.log(`[CACHE] Usando estatísticas de presença do cache após erro: ${userId}`);
          return cachedStats;
        }
        throw error;
      }
    },
    staleTime: Infinity, // Nunca fica stale durante a sessão
    gcTime: 30 * 60 * 1000,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: !!userId,
  });
  
  const { data: aulas, isLoading: aulasLoading } = useQuery({
    queryKey: CACHE_KEYS.ATTENDANCE.HISTORY(userId),
    queryFn: async () => {
      console.log(`[API] Buscando histórico de aulas para o usuário ${userId}`);
      
      const cachedAulas = cacheService.getData<Aula[]>(CACHE_KEYS.ATTENDANCE.HISTORY(userId));
      if (cachedAulas) {
        console.log(`[CACHE] Usando histórico de aulas do cache: ${userId}`);
        return cachedAulas;
      }

      try {
        const aulasResponse = await fetch(`/api/user/${userId}/attendance/history?limit=5`);
        const aulasData = await aulasResponse.json();
        
        if (aulasData.error) {
          throw new Error(aulasData.error);
        }
        
        const formattedAulas = aulasData.map((aula: any) => ({
          id: aula.id,
          data: parseISO(aula.checked_in_at),
          horario: format(parseISO(aula.checked_in_at), 'HH:mm'),
          professor: aula.instructor_name,
          tipo: aula.class_type,
          duracao: aula.duration
        })) as Aula[];
        
        // Armazenar no cache centralizado
        cacheService.setData(CACHE_KEYS.ATTENDANCE.HISTORY(userId), formattedAulas);
        
        return formattedAulas;
      } catch (error) {
        console.error('[API] Erro ao buscar histórico de aulas:', error);
        const cachedAulas = cacheService.getData<Aula[]>(CACHE_KEYS.ATTENDANCE.HISTORY(userId));
        if (cachedAulas) {
          console.log(`[CACHE] Usando histórico de aulas do cache após erro: ${userId}`);
          return cachedAulas;
        }
        throw error;
      }
    },
    staleTime: Infinity, // Nunca fica stale durante a sessão
    gcTime: 30 * 60 * 1000,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: !!userId,
  });
  
  const statsData = stats || {
    presencaMes: 0,
    presencaTotal: 0,
    totalAulas: 0,
    sequenciaAtual: 0,
    maiorSequencia: 0
  };
  
  const loading = statsLoading || aulasLoading;

  const renderizarDadosOuPadrao = () => {
    if (loading) {
      return (
        <Card className="p-6 bg-white dark:bg-slate-800 animate-pulse">
          <div className="h-24 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
        </Card>
      );
    }

    if (!aulas || aulas.length === 0) {
      return (
        <Card className="p-6 bg-white dark:bg-slate-800">
          <p className="text-center text-slate-500 dark:text-slate-400 py-8">
            Nenhum histórico de presença encontrado.
          </p>
        </Card>
      );
    }

    return (
      <Card className="p-6 bg-white dark:bg-slate-800">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">Últimas Aulas</h3>
        <div className="space-y-6">
          {aulas.map((aula) => (
            <div key={aula.id} className="flex items-start gap-4 pb-4 border-b last:border-0 border-gray-200 dark:border-gray-800">
              <div className="flex-shrink-0 w-16 text-center">
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {format(aula.data, "dd")}
                </p>
                <p className="text-sm text-gray-500 capitalize">
                  {format(aula.data, "MMM", { locale: ptBR })}
                </p>
              </div>
              
              <div className="flex-grow">
                <div className="flex items-center justify-between">
                  <h4 className="text-base font-medium text-gray-900 dark:text-gray-100">{aula.tipo}</h4>
                  <span className="text-sm text-gray-500">{aula.horario}</span>
                </div>
                <p className="text-sm text-gray-500 mt-1">{aula.professor}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Clock className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-500">{aula.duracao}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    )
  }

  return (
    <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
      {/* Cards de Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4 bg-white dark:bg-slate-800">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-primary/10 rounded-lg">
              <TrendingUp className="w-6 h-6 text-primary" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Presença no Mês</p>
              <div className="mt-1 flex items-baseline gap-2">
                <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{statsData.presencaMes}%</p>
                <Progress value={statsData.presencaMes} className="w-20 bg-slate-200 dark:bg-slate-700" />
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-white dark:bg-slate-800">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Users className="w-6 h-6 text-primary" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Presença Total</p>
              <div className="mt-1 flex items-baseline gap-2">
                <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{statsData.presencaTotal}%</p>
                <Progress value={statsData.presencaTotal} className="w-20 bg-slate-200 dark:bg-slate-700" />
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-white dark:bg-slate-800">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Calendar className="w-6 h-6 text-primary" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Total de Aulas</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{statsData.totalAulas}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-white dark:bg-slate-800">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Clock className="w-6 h-6 text-primary" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Sequência de Aulas</p>
              <div className="flex items-baseline gap-1">
                <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{statsData.sequenciaAtual}</p>
                <p className="text-sm text-gray-500">/ {statsData.maiorSequencia}</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Lista de Últimas Aulas */}
      {renderizarDadosOuPadrao()}

      {/* Calendário de Presença do Mês */}
      <Card className="p-6 bg-white dark:bg-slate-800">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">Calendário de Presença</h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Em breve: Visualização em calendário das presenças do mês
        </p>
      </Card>
    </div>
  )
} 