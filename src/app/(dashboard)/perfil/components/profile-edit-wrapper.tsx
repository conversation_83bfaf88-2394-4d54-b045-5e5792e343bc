'use client'

import { ReactNode, useState, useEffect, useCallback } from 'react'
import { EditProvider } from './edit-context'
import { useProfileUpdate } from '../hooks/use-profile-update'
import { formatPhoneFields } from '@/hooks/form/usePhoneFormat'
import { toast } from 'sonner'

interface ProfileEditWrapperProps {
  children: ReactNode
  userId: string
  userData: Record<string, any>
  onProfileUpdated?: (updatedFields?: Record<string, any>) => void
}

export function ProfileEditWrapper({ 
  children, 
  userId, 
  userData, 
  onProfileUpdated 
}: ProfileEditWrapperProps) {
  const { updateProfile } = useProfileUpdate()
  // Usar a função formatPhoneFields para garantir telefones formatados
  const [localUserData, setLocalUserData] = useState(() => formatPhoneFields(userData))
  
  // Função para atualizar os dados locais com base em novas informações
  const updateLocalData = useCallback((newData: Record<string, any>) => {
    setLocalUserData((prevData: Record<string, any>) => {
      // Aplicar formatação aos dados antes de atualizar
      const formattedData = formatPhoneFields({
        ...prevData,
        ...newData
      });
      
      console.log('[PROFILE-EDIT] Dados locais atualizados:', formattedData);
      return formattedData;
    });
    
    // Notificar componente pai após atualização
    if (onProfileUpdated) {
      // Passar os campos atualizados para o callback
      onProfileUpdated(newData);
    }
  }, [onProfileUpdated]);

  // Atualizar dados quando props mudarem
  useEffect(() => {
    if (userData) {
      // Sempre aplicar formatação nos dados ao atualizar
      const formattedData = formatPhoneFields(userData)
      console.log('[PROFILE-EDIT] Dados formatados na atualização:', {
        original: userData.phone,
        formatted: formattedData.formattedPhone
      })
      setLocalUserData(formattedData)
    }
  }, [userData])

  // Adicionar listener para eventos de atualização de perfil
  useEffect(() => {
    const handleProfileUpdated = (event: Event) => {
      const customEvent = event as CustomEvent;
      const updatedUserId = customEvent.detail?.userId;
      const updatedFields = customEvent.detail?.fields || null;
      
      if (updatedUserId === userId) {
        console.log('[PROFILE-EDIT] Detectada atualização de perfil via evento para userId:', userId);
        
        // Se temos os campos específicos que foram atualizados, atualizar apenas esses
        if (updatedFields && Object.keys(updatedFields).length > 0) {
          console.log('[PROFILE-EDIT] Atualizando campos específicos:', updatedFields);
          updateLocalData(updatedFields);
        } else {
          // Chamar o callback se existir
          if (onProfileUpdated) {
            console.log('[PROFILE-EDIT] Executando callback onProfileUpdated');
            onProfileUpdated();
          }
        }
      }
    };
    
    // Ouvir eventos específicos
    window.addEventListener('profile:updated', handleProfileUpdated);
    
    return () => {
      window.removeEventListener('profile:updated', handleProfileUpdated);
    };
  }, [userId, onProfileUpdated, updateLocalData]);

  const handleFieldChange = async (fieldName: string, value: any): Promise<boolean> => {
    const previousValue = localUserData[fieldName]
    const previousFormattedPhone = fieldName === 'phone' ? localUserData.formattedPhone : undefined
    const previousFormattedEmergencyPhone = fieldName === 'emergency_phone' ? localUserData.formattedEmergencyPhone : undefined
    
    // Extrair o valor real se for um objeto com force: true
    let actualValue = value
    let isForceUpdate = false
    
    if (typeof value === 'object' && value?.force === true) {
      isForceUpdate = true
      actualValue = value.value !== undefined ? value.value : value
      console.log(`[PROFILE] Valor extraído para atualização forçada: ${actualValue}`)
    }
    
    // Caso especial para atualização em lote de campos de endereço
    if (fieldName === 'address_batch' && typeof actualValue === 'object') {
      console.log('[PROFILE] Processando atualização em lote de endereço:', actualValue)
      
      // Atualizar dados locais imediatamente para manter a UI responsiva
      const updatedData = { ...localUserData }
      Object.entries(actualValue).forEach(([field, fieldValue]) => {
        updatedData[field] = fieldValue
      })
      
      setLocalUserData(updatedData)
      
      try {
        // Enviar todos os campos de endereço em uma única requisição
        const success = await updateProfile(userId, actualValue)
        
        if (success) {
          console.log('[PROFILE] Atualização em lote de endereço bem-sucedida, notificando outros componentes');
          
          // Não é necessário disparar evento manualmente, o updateProfile já faz isso
          
          // Notificar componente pai com campos específicos
          if (onProfileUpdated) {
            onProfileUpdated(actualValue);
          }
        }
        
        if (!success) {
          console.warn('[PROFILE] Falha na atualização do endereço, revertendo estado local')
          setLocalUserData(localUserData)
          toast.error('Não foi possível atualizar o endereço')
          return false
        }
        
        return true
      } catch (error) {
        console.error('Erro ao atualizar endereço:', error)
        setLocalUserData(localUserData)
        toast.error('Erro ao atualizar endereço')
        return false
      }
    }
    
    // Verificar se o valor está realmente mudando - mas APENAS se não for uma operação de desfazer
    if (actualValue === previousValue && !isForceUpdate) {
      console.log(`[PROFILE] Valor de ${fieldName} não mudou, ignorando atualização. Para forçar a atualização, use { value, force: true }.`)
      return true
    }
    
    console.log(`[PROFILE] Alterando campo ${fieldName} de "${previousValue}" para "${actualValue}"`)
    
    const updatedData = { 
      ...localUserData, 
      [fieldName]: actualValue 
    }
    
    // Garantir formatação adequada para telefones
    if (fieldName === 'phone') {
      updatedData.formattedPhone = formatPhoneFields({ phone: actualValue }).formattedPhone
      console.log('[PROFILE] Formatação para telefone:', updatedData.formattedPhone)
    } else if (fieldName === 'emergency_phone') {
      updatedData.formattedEmergencyPhone = formatPhoneFields({ emergency_phone: actualValue }).formattedEmergencyPhone
      console.log('[PROFILE] Formatação para telefone de emergência:', updatedData.formattedEmergencyPhone)
    }
    
    // Atualizar dados locais imediatamente para manter a UI responsiva
    setLocalUserData(updatedData)
    
    try {
      console.log(`[PROFILE] Atualizando campo ${fieldName} com valor:`, actualValue)
      const success = await updateProfile(userId, { [fieldName]: actualValue })
      
      if (success) {
        // Atualizar o estado local com os dados atualizados
        updateLocalData({ [fieldName]: actualValue });
        
        // Não é necessário disparar evento manualmente, o updateProfile já faz isso
        return true;
      }
      
      if (!success) {
        console.warn(`[PROFILE] Falha na atualização do campo ${fieldName}, revertendo estado local`)
        const revertedData = { 
          ...localUserData, 
          [fieldName]: previousValue 
        }
        
        if (fieldName === 'phone' && previousFormattedPhone) {
          revertedData.formattedPhone = previousFormattedPhone
        } else if (fieldName === 'emergency_phone' && previousFormattedEmergencyPhone) {
          revertedData.formattedEmergencyPhone = previousFormattedEmergencyPhone
        }
        
        setLocalUserData(revertedData)
        toast.error(`Não foi possível atualizar o campo ${fieldName}`)
        return false
      }
      
      return true
    } catch (error) {
      console.error(`Erro ao atualizar ${fieldName}:`, error)
      
      const revertedData = { 
        ...localUserData, 
        [fieldName]: previousValue 
      }
      
      if (fieldName === 'phone' && previousFormattedPhone) {
        revertedData.formattedPhone = previousFormattedPhone
      } else if (fieldName === 'emergency_phone' && previousFormattedEmergencyPhone) {
        revertedData.formattedEmergencyPhone = previousFormattedEmergencyPhone
      }
      
      setLocalUserData(revertedData)
      toast.error(`Erro ao atualizar o campo ${fieldName}`)
      return false
    }
  }
  
  return (
    <EditProvider 
      initialData={localUserData} 
      onFieldChange={handleFieldChange}
    >
      {children}
    </EditProvider>
  )
} 