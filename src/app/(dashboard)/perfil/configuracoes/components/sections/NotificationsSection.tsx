'use client'

import { useState } from 'react'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { BellIcon } from '@heroicons/react/24/outline'

interface NotificationOption {
  id: string
  label: string
  description?: string
}

const defaultOptions: NotificationOption[] = [
  {
    id: 'email_class_updates',
    label: 'Atualizações de aulas',
    description: 'Receba um e-mail quando houver alterações em suas aulas.',
  },
  {
    id: 'email_promotions',
    label: 'Promoções e ofertas',
    description: 'Fique por dentro de promoções exclusivas e novidades da academia.',
  },
  {
    id: 'push_reminders',
    label: 'Lembretes push',
    description: 'Receba lembretes no celular sobre próximas aulas e eventos da academia.',
  },
]

export default function NotificationsSection() {
  const [settings, setSettings] = useState<Record<string, boolean>>({})

  const toggleOption = (id: string) => {
    setSettings((prev) => ({ ...prev, [id]: !prev[id] }))
  }

  return (
    <section className="space-y-6">
      <h3 className="flex items-center gap-2 text-lg font-semibold">
        <BellIcon className="h-5 w-5 text-primary" /> Preferências de notificações
      </h3>

      <div className="space-y-4">
        {defaultOptions.map((option) => (
          <div
            key={option.id}
            className="flex items-center justify-between rounded-xl border border-border bg-muted/40 p-4"
          >
            <div className="space-y-1">
              <Label className="cursor-pointer" htmlFor={option.id}>
                {option.label}
              </Label>
              {option.description && (
                <p className="text-sm text-muted-foreground">
                  {option.description}
                </p>
              )}
            </div>
            <Switch
              id={option.id}
              checked={!!settings[option.id]}
              disabled
              onCheckedChange={() => toggleOption(option.id)}
            />
          </div>
        ))}
      </div>
    </section>
  )
} 