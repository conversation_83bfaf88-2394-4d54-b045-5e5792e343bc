// Helpers para usar as novas funções do banco de dados que corrigem o problema de presença
// Agora apenas alunos matriculados ANTES da aula são considerados elegíveis

import { createClient } from '@/services/supabase/server';

export interface StudentEligibility {
  student_id: string;
  enrollment_id: string;
  full_name: string;
  first_name: string;
  last_name: string;
  email: string;
  avatar_url: string | null;
  check_in_code: string | null;
  current_belt: {
    belt_color: string;
    degree: number;
    label?: string;
    stripe_color?: string | null;
    show_center_line?: boolean;
    center_line_color?: string | null;
  } | null;
  enrollment_date: string;
  effective_from_date: string;
  is_currently_paused: boolean;
  was_paused_during_class: boolean;
  pause_reason: string | null;
}

export interface AttendanceStats {
  total_eligible_students: number;
  total_present_students: number;
  total_absent_students: number;
  attendance_rate: number;
}

export interface ClassAttendanceData {
  class_id: string;
  class_name: string;
  class_start_time: string;
  class_end_time: string;
  class_status: string;
  class_group_id: string | null;
  class_group_name: string | null;
  instructor_name: string | null;
  branch_name: string | null;
  total_eligible_students: number;
  total_present_students: number;
  total_absent_students: number;
  attendance_rate: number;
  eligible_students: StudentEligibility[];
  attendance_records: any[];
}

/**
 * Busca todos os dados de presença para uma aula específica.
 * IMPORTANTE: Agora considera apenas alunos matriculados ANTES da data da aula.
 */
export async function getClassAttendanceData(classId: string): Promise<ClassAttendanceData | null> {
  const supabase = await createClient();
  
  const { data, error } = await supabase.rpc('get_class_attendance_data', {
    p_class_id: classId
  });

  if (error) {
    console.error('Erro ao buscar dados de presença:', error);
    throw new Error('Erro ao buscar dados de presença');
  }

  if (!data || data.length === 0) {
    return null;
  }

  return data[0];
}

/**
 * Busca estatísticas de presença para uma aula.
 * Considera apenas alunos elegíveis (matriculados antes da aula e não pausados).
 */
export async function getClassAttendanceStats(classId: string): Promise<AttendanceStats | null> {
  const supabase = await createClient();
  
  const { data, error } = await supabase.rpc('get_class_attendance_stats', {
    p_class_id: classId
  });

  if (error) {
    console.error('Erro ao buscar estatísticas de presença:', error);
    throw new Error('Erro ao buscar estatísticas de presença');
  }

  if (!data || data.length === 0) {
    return null;
  }

  return data[0];
}

/**
 * Busca apenas alunos elegíveis para uma aula específica.
 * Por padrão, exclui alunos que estavam pausados durante a aula.
 */
export async function getEligibleStudentsForClass(
  classId: string, 
  includePaused: boolean = false
): Promise<StudentEligibility[]> {
  const supabase = await createClient();
  
  const { data, error } = await supabase.rpc('get_eligible_students_for_class', {
    p_class_id: classId,
    p_include_paused: includePaused
  });

  if (error) {
    console.error('Erro ao buscar alunos elegíveis:', error);
    throw new Error('Erro ao buscar alunos elegíveis');
  }

  return data || [];
}

/**
 * Verifica se um aluno específico é elegível para uma aula.
 */
export async function isStudentEligibleForClass(studentId: string, classId: string) {
  const supabase = await createClient();
  
  const { data, error } = await supabase.rpc('is_student_eligible_for_class', {
    p_student_id: studentId,
    p_class_id: classId
  });

  if (error) {
    console.error('Erro ao verificar elegibilidade do aluno:', error);
    throw new Error('Erro ao verificar elegibilidade do aluno');
  }

  if (!data || data.length === 0) {
    return {
      is_eligible: false,
      reason: 'not_found',
      enrollment_date: null,
      effective_from_date: null,
      was_paused: false
    };
  }

  return data[0];
}

/**
 * Helper para comparar dados antigos vs novos (para debugging/migração)
 */
export async function compareAttendanceMethods(classId: string) {
  const supabase = await createClient();
  
  // Método novo (correto)
  const newData = await getClassAttendanceStats(classId);
  
  // Método antigo (para comparação) - todos os alunos matriculados na turma
  const { data: classInfo } = await supabase
    .from('classes')
    .select('class_group_id, start_time, name')
    .eq('id', classId)
    .single();

  if (!classInfo?.class_group_id) {
    return { newData, oldData: null, comparison: 'Aula sem turma associada' };
  }

  const { data: allEnrolled } = await supabase
    .from('class_group_enrollments')
    .select('student_id')
    .eq('class_group_id', classInfo.class_group_id)
    .eq('status', 'active');

  const oldTotalStudents = allEnrolled?.length || 0;
  const oldAttendanceRate = oldTotalStudents > 0 
    ? Math.round((newData?.total_present_students || 0) / oldTotalStudents * 100) 
    : 0;

  return {
    newData,
    oldData: {
      total_students: oldTotalStudents,
      attendance_rate: oldAttendanceRate
    },
    comparison: {
      class_name: classInfo.name,
      class_date: classInfo.start_time,
      difference_in_eligible_students: (newData?.total_eligible_students || 0) - oldTotalStudents,
      rate_difference: (newData?.attendance_rate || 0) - oldAttendanceRate
    }
  };
} 