'use client';

import { ErrorDisplay } from './ErrorDisplay';
import { useRouter } from 'next/navigation';

interface ErrorWrapperProps {
  title: string;
  message: string;
  actionType?: 'reload' | 'redirect';
  redirectPath?: string;
  actionLabel?: string;
}

export function ErrorWrapper({ 
  title, 
  message, 
  actionType,
  redirectPath = '/presenca',
  actionLabel
}: ErrorWrapperProps) {
  const router = useRouter();

  const handleAction = () => {
    if (actionType === 'reload') {
      window.location.reload();
    } else if (actionType === 'redirect') {
      router.push(redirectPath);
    }
  };

  // Se não há ação, não mostrar botão
  if (!actionType || !actionLabel) {
    return (
      <ErrorDisplay
        title={title}
        message={message}
      />
    );
  }

  return (
    <ErrorDisplay
      title={title}
      message={message}
      actionLabel={actionLabel}
      onAction={handleAction}
    />
  );
} 