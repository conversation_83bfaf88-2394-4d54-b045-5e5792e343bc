'use client';

import { QRCodeScanner } from '../../../aulas/components/attendance';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { QrCode } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface QRCodeSectionProps {
  classId: string;
  classGroupId?: string | null;
  tenantId?: string;
  isActive: boolean;
  status: 'ongoing' | 'scheduled' | 'completed';
  isVisible?: boolean;
}

export function QRCodeSection({ classId, classGroupId, tenantId, isActive, status, isVisible = true }: QRCodeSectionProps) {
  const router = useRouter();

  const handleCheckInSuccess = () => {
    // Em vez de recarregar a página inteira, apenas refrescar a rota
    router.refresh();
  };

  const handleRetryAuth = () => {
    // Redirecionar para login se há problema de autenticação
    router.push('/login');
  };

  if (!isActive) {
    const getStatusBadgeProps = (status: string) => {
      switch (status) {
        case 'completed':
          return {
            variant: 'outline' as const,
            className: 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-700'
          };
        default:
          return {
            variant: 'secondary' as const,
            className: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 border-blue-200 dark:border-blue-800'
          };
      }
    };

    const statusBadgeProps = getStatusBadgeProps(status);

    return (
      <Card className="h-fit">
        <CardContent className="pt-6 text-center">
          <QrCode className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2 text-foreground">QR Code não disponível</h3>
          <p className="text-muted-foreground mb-4">
            {status === 'completed' 
              ? 'Esta aula já foi concluída.' 
              : 'QR Code só fica disponível quando a aula está próxima ou em andamento.'
            }
          </p>
          <Badge 
            variant="outline" 
            className={statusBadgeProps.className}
          >
            {status === 'completed' ? 'Aula finalizada' : 'Aguardando início'}
          </Badge>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <QRCodeScanner 
        classId={classId}
        classGroupId={classGroupId}
        tenantId={tenantId}
        onCheckInSuccess={handleCheckInSuccess}
        onAuthError={handleRetryAuth}
        autoLoadQR={isVisible && isActive}
        className="w-full"
      />
    </div>
  );
} 