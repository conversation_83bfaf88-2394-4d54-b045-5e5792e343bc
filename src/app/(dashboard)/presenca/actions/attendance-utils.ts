import { Tables } from "@/services/supabase/types/database.types";
import type { PaginatedResult, ClassWithDetails } from "../../aulas/types";

// =============================================================================
// TIPOS E INTERFACES
// =============================================================================

export type AttendanceRecord = Tables<'attendance'> & {
  student: {
    id: string;
    user: {
      first_name: string;
      last_name: string | null;
      full_name: string | null;
      avatar_url?: string | null;
    };
    check_in_code: string | null;
  };
  class: {
    id: string;
    name: string;
    start_time: string;
    end_time: string;
  };
  checked_in_by_user: {
    first_name: string;
    last_name: string | null;
    full_name: string | null;
  };
};

export type ClassAttendanceRecord = {
  id: string;
  student_id: string;
  checked_in_at: string;
  notes: string | null;
  student: {
    id: string;
    user: {
      first_name: string;
      last_name: string | null;
      full_name: string | null;
      email: string;
      avatar_url?: string | null;
    };
    check_in_code: string | null;
    current_belt: {
      belt_color: string;
      degree: number;
      label?: string;
      stripe_color?: string | null;
      show_center_line?: boolean;
      center_line_color?: string | null;
    } | null;
  };
};

export type QRCodeData = {
  qr_code: string;
  class_id: string;
  expires_at: string;
  created_by: string;
};

export interface TrendData {
  value: number;
  isPositive: boolean;
  data: number[];
}

export interface DailyStats {
  date: string;
  total: number;
  ongoing: number;
  upcoming: number;
  completed: number;
}

export interface AttendanceStatsWithTrends {
  totalClasses: number;
  ongoingClasses: number;
  upcomingClasses: number;
  completedClasses: number;
  totalAttendances: number;
  uniqueStudents: number;
  trends: {
    total: TrendData;
    ongoing: TrendData;
    upcoming: TrendData;
    completed: TrendData;
  };
  dailyData: DailyStats[];
}

export interface AttendanceStatsResponse {
  success: boolean;
  data?: AttendanceStatsWithTrends;
  errors?: any;
}

/**
 * Verifica se uma aula está disponível para check-in
 * Considera aulas agendadas, em andamento e concluídas há menos de 24h
 */
export function isClassAvailableForCheckIn(classData: {
  status: string;
  start_time: string;
  end_time: string;
}): boolean {
  // Aulas canceladas ou reagendadas nunca estão disponíveis
  if (['cancelled', 'rescheduled'].includes(classData.status)) {
    return false;
  }

  const now = Date.now();
  const endTime = new Date(classData.end_time).getTime();
  
  // Se a aula está agendada ou em andamento, está disponível
  if (['scheduled', 'ongoing'].includes(classData.status)) {
    return true;
  }

  // Se a aula foi concluída, verificar se ainda está dentro do limite de 24h
  if (classData.status === 'completed') {
    const twentyFourHoursInMs = 24 * 60 * 60 * 1000;
    const twentyFourHoursAfterEnd = endTime + twentyFourHoursInMs;
    return now <= twentyFourHoursAfterEnd;
  }

  return false;
}

/**
 * Calcula o status real de uma aula baseado no tempo atual
 * DEPRECATED: Esta função será gradualmente removida em favor do status atualizado automaticamente pelo cron job
 * 
 * @deprecated Use o status diretamente do banco de dados, que é atualizado automaticamente pelo sistema de cron job
 */
export function calculateClassStatus(classItem: { status: string; start_time: string; end_time: string }): string {
  const now = new Date();
  const startTime = new Date(classItem.start_time);
  const endTime = new Date(classItem.end_time);
  
  if (['scheduled', 'ongoing'].includes(classItem.status)) {
    if (startTime <= now && now <= endTime) {
      return 'ongoing';
    } else if (startTime > now) {
      return 'scheduled';
    } else if (endTime < now) {
      return 'completed';
    }
  }
  
  return classItem.status;
}

/**
 * Determina o status efetivo de uma aula baseado no tempo atual e status do banco
 * Esta função unifica a lógica de determinação de status para toda a aplicação
 */
export function getClassStatus(classItem: { status: string; start_time: string; end_time: string; id?: string }): string {
  // Status finais que não mudam baseado no tempo
  if (['cancelled', 'rescheduled'].includes(classItem.status)) {
    return classItem.status;
  }
  
  const now = new Date();
  const startTime = new Date(classItem.start_time);
  const endTime = new Date(classItem.end_time);
  
  // Se a aula foi marcada como completed manualmente, manter esse status
  if (classItem.status === 'completed') {
    return 'completed';
  }
  
  // Para aulas scheduled/ongoing, determinar status baseado no tempo atual
  if (['scheduled', 'ongoing'].includes(classItem.status)) {
    if (now < startTime) {
      return 'scheduled'; // Aula ainda não começou
    } else if (now >= startTime && now <= endTime) {
      return 'ongoing'; // Aula em andamento
    } else if (now > endTime) {
      return 'completed'; // Aula terminou
    }
  }
  
  // Fallback para o status do banco de dados
  return classItem.status;
}

// Como o status da UI agora é igual ao do banco, as funções de mapeamento tornam-se identidades
export function mapStatusToFilter(status: string): string {
  return status;
}

export function mapFilterToStatus(filterStatus: string): string {
  return filterStatus;
} 