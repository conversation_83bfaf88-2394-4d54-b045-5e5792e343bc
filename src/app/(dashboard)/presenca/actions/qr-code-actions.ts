"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";
import { GenerateClassQRSchema } from "../../aulas/actions/schemas/index";
import type { QRCodeData } from './attendance-utils';
import { isClassAvailableForCheckIn } from './attendance-utils';

/**
 * Gera e armazena QR Code para uma aula
 */
export async function generateClassQR(data: unknown) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = GenerateClassQRSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const validatedData = validationResult.data;
    const supabase = await createClient();

    const { data: classData, error: classError } = await supabase
      .from("classes")
      .select("id, name, start_time, end_time, status, instructor_id")
      .eq("id", validatedData.class_id)
      .eq("tenant_id", tenantId)
      .single();

    if (classError || !classData) {
      return { success: false, errors: { class_id: "Aula não encontrada" } };
    }

    if (user.app_metadata?.role !== "admin" && classData.instructor_id !== user.id) {
      return { success: false, errors: { _form: "Sem permissão para gerar QR Code desta aula" } };
    }

    if (!isClassAvailableForCheckIn(classData)) {
      return { success: false, errors: { class_id: "Aula não está disponível para check-in" } };
    }

    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + validatedData.expires_in_minutes);

    const qrData: QRCodeData = {
      qr_code: `class_${validatedData.class_id}_${Date.now()}`,
      class_id: validatedData.class_id,
      expires_at: expiresAt.toISOString(),
      created_by: user.id,
    };

    const qrCode = Buffer.from(JSON.stringify(qrData)).toString('base64');

    await supabase
      .from("class_qr_codes")
      .update({ is_active: false })
      .eq("class_id", validatedData.class_id)
      .eq("tenant_id", tenantId)
      .eq("is_active", true);

    const { data: savedQRCode, error: saveError } = await supabase
      .from("class_qr_codes")
      .insert({
        tenant_id: tenantId,
        class_id: validatedData.class_id,
        qr_code: qrCode,
        raw_qr_data: qrData,
        expires_at: expiresAt.toISOString(),
        created_by: user.id,
        is_active: true
      })
      .select()
      .single();

    if (saveError) {
      console.error("Erro ao salvar QR Code:", saveError);
      return { success: false, errors: { _form: "Erro ao salvar QR Code no banco de dados" } };
    }

    revalidatePath("/aulas");

    return { 
      success: true, 
      data: {
        qr_code: qrCode,
        expires_at: qrData.expires_at,
        class_name: classData.name,
        class_id: classData.id,
        expires_in_minutes: validatedData.expires_in_minutes,
        stored_id: savedQRCode.id
      }
    };
  } catch (error) {
    console.error("Erro ao gerar QR Code:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Recupera QR code ativo de uma aula
 */
export async function getActiveClassQR(classId: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    const { data: classData, error: classError } = await supabase
      .from("classes")
      .select("id, name, start_time, end_time, status, instructor_id")
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .single();

    if (classError || !classData) {
      return { success: false, errors: { class_id: "Aula não encontrada" } };
    }

    if (user.app_metadata?.role !== "admin" && classData.instructor_id !== user.id) {
      return { success: false, errors: { _form: "Sem permissão para acessar QR Code desta aula" } };
    }

    const { data: qrCodeData, error: qrError } = await supabase
      .from("class_qr_codes")
      .select("*")
      .eq("class_id", classId)
      .eq("tenant_id", tenantId)
      .eq("is_active", true)
      .gt("expires_at", new Date().toISOString())
      .order("created_at", { ascending: false })
      .limit(1)
      .single();

    if (qrError && qrError.code !== 'PGRST116') {
      console.error("Erro ao buscar QR Code:", qrError);
      return { success: false, errors: { _form: "Erro ao buscar QR Code" } };
    }

    if (!qrCodeData) {
      return { 
        success: true, 
        data: null
      };
    }

    return {
      success: true,
      data: {
        qr_code: qrCodeData.qr_code,
        expires_at: qrCodeData.expires_at,
        class_name: classData.name,
        class_id: classData.id,
        stored_id: qrCodeData.id,
        raw_data: qrCodeData.raw_qr_data
      }
    };
  } catch (error) {
    console.error("Erro ao recuperar QR Code:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Desativa um QR code específico
 */
export async function deactivateClassQR(qrCodeId: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    const { data: qrCodeData, error: qrError } = await supabase
      .from("class_qr_codes")
      .select(`
        id,
        class_id,
        classes (
          instructor_id
        )
      `)
      .eq("id", qrCodeId)
      .eq("tenant_id", tenantId)
      .single();

    if (qrError || !qrCodeData) {
      return { success: false, errors: { _form: "QR Code não encontrado" } };
    }

    const classData = qrCodeData.classes as any;
    if (user.app_metadata?.role !== "admin" && classData?.instructor_id !== user.id) {
      return { success: false, errors: { _form: "Sem permissão para desativar este QR Code" } };
    }

    const { error: updateError } = await supabase
      .from("class_qr_codes")
      .update({ is_active: false })
      .eq("id", qrCodeId)
      .eq("tenant_id", tenantId);

    if (updateError) {
      console.error("Erro ao desativar QR Code:", updateError);
      return { success: false, errors: { _form: "Erro ao desativar QR Code" } };
    }

    revalidatePath("/aulas");

    return { success: true };
  } catch (error) {
    console.error("Erro ao desativar QR Code:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Lista histórico de QR codes de uma aula
 */
export async function getClassQRHistory(classId: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    const { data: classData, error: classError } = await supabase
      .from("classes")
      .select("id, name, instructor_id")
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .single();

    if (classError || !classData) {
      return { success: false, errors: { class_id: "Aula não encontrada" } };
    }

    if (user.app_metadata?.role !== "admin" && classData.instructor_id !== user.id) {
      return { success: false, errors: { _form: "Sem permissão para visualizar histórico desta aula" } };
    }

    const { data: qrHistory, error: historyError } = await supabase
      .from("class_qr_codes")
      .select(`
        id,
        qr_code,
        expires_at,
        is_active,
        created_at,
        created_by_user:users!class_qr_codes_created_by_fkey (
          first_name,
          last_name,
          full_name
        )
      `)
      .eq("class_id", classId)
      .eq("tenant_id", tenantId)
      .order("created_at", { ascending: false });

    if (historyError) {
      console.error("Erro ao buscar histórico de QR Codes:", historyError);
      return { success: false, errors: { _form: "Erro ao buscar histórico de QR Codes" } };
    }

    return {
      success: true,
      data: {
        class_name: classData.name,
        qr_codes: qrHistory || []
      }
    };
  } catch (error) {
    console.error("Erro ao buscar histórico de QR Codes:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 