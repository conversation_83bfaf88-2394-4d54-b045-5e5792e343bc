'use client';

import { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { AttendanceListFilter } from '../../aulas/actions/schemas/index';
import { mapStatusToFilter } from '../actions/attendance-utils';

// Tipos para o estado dos filtros
export interface AttendanceFiltersState {
  search: string; // Valor usado na API (com debounce)
  searchInput: string; // Valor do input (sem debounce)
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled' | 'all'; // Status da UI
  instructor_id?: string;
  branch_id?: string;
  class_group_id?: string | null;
  date_from?: string;
  date_to?: string;
  page: number;
  pageSize: number;
  isLoading: boolean;
}

// Tipos para as ações do reducer
export type AttendanceFiltersAction =
  | { type: 'SET_SEARCH_INPUT'; payload: string }
  | { type: 'SET_SEARCH'; payload: string }
  | { type: 'SET_STATUS'; payload: AttendanceFiltersState['status'] }
  | { type: 'SET_INSTRUCTOR'; payload: string | undefined }
  | { type: 'SET_BRANCH'; payload: string | undefined }
  | { type: 'SET_CLASS_GROUP'; payload: string | null | undefined }
  | { type: 'SET_DATE_RANGE'; payload: { from?: string; to?: string } }
  | { type: 'SET_PAGE'; payload: number }
  | { type: 'SET_PAGE_SIZE'; payload: number }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'RESET_FILTERS' }
  | { type: 'CLEAR_SEARCH' };

// Estado inicial dos filtros
const initialState: AttendanceFiltersState = {
  search: '',
  searchInput: '',
  status: 'all',
  instructor_id: undefined,
  branch_id: undefined,
  class_group_id: undefined,
  date_from: undefined,
  date_to: undefined,
  page: 1,
  pageSize: 20,
  isLoading: false,
};

// Reducer para gerenciar o estado dos filtros
function attendanceFiltersReducer(
  state: AttendanceFiltersState,
  action: AttendanceFiltersAction
): AttendanceFiltersState {
  switch (action.type) {
    case 'SET_SEARCH_INPUT':
      return {
        ...state,
        searchInput: action.payload,
      };
    
    case 'SET_SEARCH':
      return {
        ...state,
        search: action.payload,
        page: 1, // Reset para primeira página ao buscar
      };
    
    case 'SET_STATUS':
      return {
        ...state,
        status: action.payload,
        page: 1, // Reset para primeira página ao filtrar
      };
    
    case 'SET_INSTRUCTOR':
      return {
        ...state,
        instructor_id: action.payload,
        page: 1,
      };
    
    case 'SET_BRANCH':
      return {
        ...state,
        branch_id: action.payload,
        page: 1,
      };
    
    case 'SET_CLASS_GROUP':
      return {
        ...state,
        class_group_id: action.payload,
        page: 1,
      };
    
    case 'SET_DATE_RANGE':
      return {
        ...state,
        date_from: action.payload.from,
        date_to: action.payload.to,
        page: 1,
      };
    
    case 'SET_PAGE':
      return {
        ...state,
        page: action.payload,
      };
    
    case 'SET_PAGE_SIZE':
      return {
        ...state,
        pageSize: action.payload,
        page: 1, // Reset para primeira página ao mudar tamanho
      };
    
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    
    case 'RESET_FILTERS':
      return {
        ...initialState,
        pageSize: state.pageSize, // Manter pageSize preferido
      };
    
    case 'CLEAR_SEARCH':
      return {
        ...state,
        search: '',
        searchInput: '',
        page: 1,
      };
    
    default:
      return state;
  }
}

// Interface do contexto
interface AttendanceFiltersContextType {
  state: AttendanceFiltersState;
  dispatch: React.Dispatch<AttendanceFiltersAction>;
  // Funções helper para facilitar o uso
  setSearchInput: (search: string) => void;
  setSearch: (search: string) => void;
  setStatus: (status: AttendanceFiltersState['status']) => void;
  setInstructor: (instructorId?: string) => void;
  setBranch: (branchId?: string) => void;
  setClassGroup: (classGroupId?: string | null) => void;
  setDateRange: (from?: string, to?: string) => void;
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;
  setLoading: (loading: boolean) => void;
  resetFilters: () => void;
  clearSearch: () => void;
  // Função para converter estado em filtros para a API
  getApiFilters: () => AttendanceListFilter;
  // Verificar se há filtros ativos
  hasActiveFilters: () => boolean;
  // Lista de filtros ativos para display
  getActiveFilters: () => Array<{ key: string; label: string; value: string }>;
}

// Criar o contexto
const AttendanceFiltersContext = createContext<AttendanceFiltersContextType | null>(null);

// Provider do contexto
export function AttendanceFiltersProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(attendanceFiltersReducer, initialState);

  // Debounce para busca - 500ms de delay
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      dispatch({ type: 'SET_SEARCH', payload: state.searchInput });
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [state.searchInput]);

  // Funções helper
  const setSearchInput = (search: string) => {
    dispatch({ type: 'SET_SEARCH_INPUT', payload: search });
  };

  const setSearch = (search: string) => {
    dispatch({ type: 'SET_SEARCH', payload: search });
    dispatch({ type: 'SET_SEARCH_INPUT', payload: search });
  };

  const setStatus = (status: AttendanceFiltersState['status']) => {
    dispatch({ type: 'SET_STATUS', payload: status });
  };

  const setInstructor = (instructorId?: string) => {
    dispatch({ type: 'SET_INSTRUCTOR', payload: instructorId });
  };

  const setBranch = (branchId?: string) => {
    dispatch({ type: 'SET_BRANCH', payload: branchId });
  };

  const setClassGroup = (classGroupId?: string | null) => {
    dispatch({ type: 'SET_CLASS_GROUP', payload: classGroupId });
  };

  const setDateRange = (from?: string, to?: string) => {
    dispatch({ type: 'SET_DATE_RANGE', payload: { from, to } });
  };

  const setPage = (page: number) => {
    dispatch({ type: 'SET_PAGE', payload: page });
  };

  const setPageSize = (pageSize: number) => {
    dispatch({ type: 'SET_PAGE_SIZE', payload: pageSize });
  };

  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  const resetFilters = () => {
    dispatch({ type: 'RESET_FILTERS' });
  };

  const clearSearch = () => {
    dispatch({ type: 'CLEAR_SEARCH' });
  };

  // Converter estado em filtros para a API
  const getApiFilters = (): AttendanceListFilter => {
    const filters: AttendanceListFilter = {
      page: state.page,
      pageSize: state.pageSize,
    };

    if (state.search.trim()) {
      filters.search = state.search.trim();
    }

    // Mapear status da UI para status da API
    if (state.status !== 'all') {
      filters.status = mapStatusToFilter(state.status) as any;
    }

    if (state.instructor_id) {
      filters.instructor_id = state.instructor_id;
    }

    if (state.branch_id) {
      filters.branch_id = state.branch_id;
    }

    if (state.class_group_id !== undefined) {
      filters.class_group_id = state.class_group_id;
    }

    if (state.date_from) {
      filters.date_from = state.date_from;
    }

    if (state.date_to) {
      filters.date_to = state.date_to;
    }

    return filters;
  };

  // Verificar se há filtros ativos (excluindo paginação)
  const hasActiveFilters = (): boolean => {
    return !!(
      state.search.trim() ||
      state.status !== 'all' ||
      state.instructor_id ||
      state.branch_id ||
      state.class_group_id !== undefined ||
      state.date_from ||
      state.date_to
    );
  };

  // Lista de filtros ativos para display
  const getActiveFilters = (): Array<{ key: string; label: string; value: string }> => {
    const activeFilters: Array<{ key: string; label: string; value: string }> = [];

    if (state.search.trim()) {
      activeFilters.push({
        key: 'search',
        label: 'Busca',
        value: state.search.trim(),
      });
    }

    if (state.status !== 'all') {
      const statusLabels = {
        scheduled: 'Próximas',
        ongoing: 'Em Andamento',
        completed: 'Concluídas',
        cancelled: 'Canceladas',
      };
      activeFilters.push({
        key: 'status',
        label: 'Status',
        value: statusLabels[state.status] || state.status,
      });
    }

    if (state.instructor_id) {
      activeFilters.push({
        key: 'instructor',
        label: 'Instrutor',
        value: state.instructor_id,
      });
    }

    if (state.branch_id) {
      activeFilters.push({
        key: 'branch',
        label: 'Unidade',
        value: state.branch_id,
      });
    }

    if (state.class_group_id !== undefined) {
      activeFilters.push({
        key: 'class_group',
        label: 'Turma',
        value: state.class_group_id === null ? 'Aulas Livres' : state.class_group_id,
      });
    }

    if (state.date_from || state.date_to) {
      const dateRange = [state.date_from, state.date_to].filter(Boolean).join(' - ');
      activeFilters.push({
        key: 'date_range',
        label: 'Período',
        value: dateRange,
      });
    }

    return activeFilters;
  };

  const contextValue: AttendanceFiltersContextType = {
    state,
    dispatch,
    setSearchInput,
    setSearch,
    setStatus,
    setInstructor,
    setBranch,
    setClassGroup,
    setDateRange,
    setPage,
    setPageSize,
    setLoading,
    resetFilters,
    clearSearch,
    getApiFilters,
    hasActiveFilters,
    getActiveFilters,
  };

  return (
    <AttendanceFiltersContext.Provider value={contextValue}>
      {children}
    </AttendanceFiltersContext.Provider>
  );
}

// Hook para usar o contexto
export function useAttendanceFilters() {
  const context = useContext(AttendanceFiltersContext);
  if (!context) {
    throw new Error('useAttendanceFilters must be used within an AttendanceFiltersProvider');
  }
  return context;
} 