'use client';

import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';

interface AttendanceListPaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  isLoading?: boolean;
  onPageChange: (page: number) => void;
}

export function AttendanceListPagination({ 
  currentPage, 
  totalPages, 
  totalItems, 
  itemsPerPage,
  isLoading = false,
  onPageChange 
}: AttendanceListPaginationProps) {
  
  // Não mostrar paginação se só há uma página ou menos
  if (totalPages <= 1) {
    return null;
  }

  const hasNext = currentPage < totalPages;
  const hasPrev = currentPage > 1;

  // Calcular range de itens sendo exibidos
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  // Gerar range de páginas para mostrar
  const generatePageNumbers = () => {
    const pages: (number | string)[] = [];
    const delta = 2; // Quantas páginas mostrar ao redor da atual

    if (totalPages <= 7) {
      // Se temos 7 ou menos páginas, mostrar todas
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Lógica mais complexa para muitas páginas
      if (currentPage <= 4) {
        // Início: mostrar 1-5 ... last
        for (let i = 1; i <= 5; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 3) {
        // Final: mostrar 1 ... (totalPages-4)-totalPages
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // Meio: mostrar 1 ... (current-1)-(current+1) ... last
        pages.push(1);
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const pageNumbers = generatePageNumbers();

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage && !isLoading) {
      onPageChange(page);
    }
  };

  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 px-2 py-4">
      {/* Informações dos itens */}
      <div className="text-sm text-slate-600 dark:text-gray-400">
        Mostrando <span className="font-medium text-slate-900 dark:text-gray-100">{startItem}</span> a{' '}
        <span className="font-medium text-slate-900 dark:text-gray-100">{endItem}</span> de{' '}
        <span className="font-medium text-slate-900 dark:text-gray-100">{totalItems}</span> aulas
      </div>
      
      {/* Controles de navegação */}
      <div className="flex items-center space-x-2">
        {/* Botão Anterior */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={!hasPrev || isLoading}
          className="gap-1 h-9"
        >
          <ChevronLeft className="h-4 w-4" />
          <span className="hidden sm:inline">Anterior</span>
        </Button>
        
        {/* Números das páginas */}
        <div className="flex items-center space-x-1">
          {pageNumbers.map((page, index) => {
            if (page === '...') {
              return (
                <div
                  key={`ellipsis-${index}`}
                  className="flex items-center justify-center w-9 h-9 text-slate-400 dark:text-gray-500"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </div>
              );
            }
            
            const pageNumber = page as number;
            const isCurrentPage = pageNumber === currentPage;
            
            return (
              <Button
                key={pageNumber}
                variant={isCurrentPage ? "default" : "outline"}
                size="sm"
                onClick={() => handlePageChange(pageNumber)}
                disabled={isLoading}
                className={`w-9 h-9 p-0 ${
                  isCurrentPage 
                    ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                    : 'hover:bg-slate-50 dark:hover:bg-gray-800'
                }`}
              >
                {pageNumber}
              </Button>
            );
          })}
        </div>
        
        {/* Botão Próximo */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={!hasNext || isLoading}
          className="gap-1 h-9"
        >
          <span className="hidden sm:inline">Próximo</span>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
      
      {/* Indicador de loading quando necessário */}
      {isLoading && (
        <div className="text-xs text-slate-500 dark:text-gray-400 italic">
          Carregando...
        </div>
      )}
    </div>
  );
} 