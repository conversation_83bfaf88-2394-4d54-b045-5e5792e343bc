'use client';

import { useState } from 'react';
import { AttendanceTable } from './AttendanceTable';
import { AttendanceListPagination } from './AttendanceListPagination';
import { useAttendanceFilters } from './AttendanceFiltersContext';
import { useAttendanceClasses } from '../../aulas/hooks/use-attendance-classes';
import { exportAttendanceList } from '../actions/attendance-queries';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

// Hook para gerenciar exportações - reutilizando do QuickActionBar
function useAttendanceExport() {
  const [isExporting, setIsExporting] = useState(false);

  const exportClassAttendance = async (classId: string) => {
    setIsExporting(true);
    try {
      const result = await exportAttendanceList(classId);
      
      if (result.success && result.data) {
        // Criar o blob CSV
        const blob = new Blob([result.data.csvContent], { type: 'text/csv;charset=utf-8;' });
        
        // Criar link temporário para download
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', result.data.filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        toast.success("Exportação concluída", {
          description: `Arquivo ${result.data.filename} baixado com sucesso.`,
        });
      } else {
        toast.error("Erro na exportação", {
          description: result.errors?._form || "Erro desconhecido ao exportar dados.",
        });
      }
    } catch (error) {
      console.error('Erro na exportação:', error);
      toast.error("Erro na exportação", {
        description: "Erro interno do sistema. Tente novamente.",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return {
    isExporting,
    exportClassAttendance
  };
}

export function AttendanceListSection() {
  const { 
    state: filterState, 
    getApiFilters, 
    setPage 
  } = useAttendanceFilters();

  const router = useRouter();
  const { isExporting, exportClassAttendance } = useAttendanceExport();

  // Buscar aulas usando React Query
  const {
    data: queryResult,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
  } = useAttendanceClasses(getApiFilters());

  // Extrair dados e paginação do resultado da query
  const classes = queryResult?.data || [];
  const pagination = queryResult?.pagination || {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  };

  // Função para lidar com mudança de página
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Função para navegar para a página de detalhes da aula na aba de presenças
  const handleViewDetails = (classId: string) => {
    router.push(`/presenca/${classId}?tab=lista`);
  };

  // Preparar mensagem de erro para exibição
  const errorMessage = isError 
    ? error instanceof Error 
      ? error.message 
      : 'Erro ao carregar aulas'
    : null;

  return (
    <div className="space-y-4">
      {/* Título da seção */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-slate-900 dark:text-gray-100">
          Listagem de Aulas
        </h2>
        <div className="flex items-center gap-3">
          {/* Indicador de loading quando fazendo refetch */}
          {isFetching && !isLoading && (
            <div className="text-sm text-blue-600 dark:text-blue-400 flex items-center gap-2 bg-blue-50 dark:bg-blue-900/20 px-3 py-1.5 rounded-full border border-blue-200 dark:border-blue-800">
              <div className="w-4 h-4 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
              Atualizando dados...
            </div>
          )}
          
          {/* Contador de resultados */}
          <div className="text-sm text-slate-500 dark:text-gray-400">
            {pagination.total > 0 && (
              `${pagination.total} ${pagination.total === 1 ? 'aula encontrada' : 'aulas encontradas'}`
            )}
          </div>
        </div>
      </div>

      {/* Tabela de aulas */}
      <AttendanceTable
        classes={classes}
        isLoading={isLoading || isFetching || isExporting}
        error={errorMessage}
        onViewDetails={handleViewDetails}
        onExportClass={exportClassAttendance}
      />

      {/* Paginação */}
      {pagination.total > 0 && (
        <AttendanceListPagination
          currentPage={pagination.page}
          totalPages={pagination.totalPages}
          totalItems={pagination.total}
          itemsPerPage={pagination.limit}
          onPageChange={handlePageChange}
          isLoading={isLoading || isFetching}
        />
      )}
    </div>
  );
} 