'use client';

import { BarChart3 } from 'lucide-react';

interface AttendanceTabNavigationProps {
  children: React.ReactNode;
}

export function AttendanceTabNavigation({ children }: AttendanceTabNavigationProps) {
  return (
    <div className="space-y-6">
      {/* <div className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
        <div className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-slate-600 dark:text-gray-400" />
          <h1 className="text-lg font-semibold text-slate-900 dark:text-gray-100">Dashboard de Presença</h1>
        </div>
      </div> */}
      {children}
    </div>
  );
} 