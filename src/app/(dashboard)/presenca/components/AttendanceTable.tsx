'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Clock,
  Users,
  MapPin,
  Eye,
  Calendar,
  Play,
  CheckCircle,
  AlertCircle,
  Database,
  Filter,
  Download,
  Unlock
} from 'lucide-react';
import Link from 'next/link';
import type { ClassWithDetails } from '../../aulas/types';

// Função para formatar data/hora no timezone de Brasília
function formatDateTimeBrasilia(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleString('pt-BR', {
    timeZone: 'America/Sao_Paulo',
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

interface AttendanceTableProps {
  classes: ClassWithDetails[];
  isLoading?: boolean;
  error?: string | null;
  onViewDetails?: (classId: string) => void;
  onExportClass?: (classId: string) => void;
}

export function AttendanceTable({ 
  classes = [], 
  isLoading = false, 
  error = null,
  onViewDetails,
  onExportClass 
}: AttendanceTableProps) {
  
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'ongoing':
        return {
          label: 'Em andamento',
          color: 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800',
          icon: <Play className="h-3 w-3" />,
        };
      case 'scheduled':
        return {
          label: 'Agendada',
          color: 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800',
          icon: <Clock className="h-3 w-3" />,
        };
      case 'completed':
        return {
          label: 'Concluída',
          color: 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-400 border-gray-200 dark:border-gray-600',
          icon: <CheckCircle className="h-3 w-3" />,
        };
      case 'cancelled':
        return {
          label: 'Cancelada',
          color: 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border-red-200 dark:border-red-800',
          icon: <AlertCircle className="h-3 w-3" />,
        };
      default:
        return {
          label: 'Agendada',
          color: 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800',
          icon: <Clock className="h-3 w-3" />,
        };
    }
  };

  const getInstructorInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getAttendanceDisplay = (count: number, maxCapacity: number | null, isOpenClass: boolean = false) => {
    if (isOpenClass || maxCapacity === null) {
      return (
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-slate-400 dark:text-gray-500" />
          <span className="font-medium text-slate-900 dark:text-gray-100">{count}</span>
          <Badge variant="outline" className="text-xs bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800">
            <Unlock className="h-3 w-3 mr-1" />
            Livre
          </Badge>
        </div>
      );
    }

    const percentage = maxCapacity > 0 ? Math.round((count / maxCapacity) * 100) : 0;
    const colorClass = percentage >= 90 
      ? 'text-red-600 dark:text-red-400' 
      : percentage >= 70 
        ? 'text-orange-600 dark:text-orange-400' 
        : 'text-green-600 dark:text-green-400';

    return (
      <div className="flex items-center gap-2">
        <Users className="h-4 w-4 text-slate-400 dark:text-gray-500" />
        <span className={`font-medium ${colorClass}`}>
          {count}/{maxCapacity}
        </span>
        <span className="text-xs text-slate-500 dark:text-gray-400">
          ({percentage}%)
        </span>
      </div>
    );
  };

  // Loading state
  if (isLoading) {
    return (
      <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-slate-900 dark:text-gray-100">
            <Filter className="h-5 w-5" />
            Listagem de Aulas
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Desktop Skeleton */}
          <div className="hidden lg:block">
            <div className="space-y-3">
              {/* Header Skeleton */}
              <div className="grid grid-cols-7 gap-4 pb-3 border-b border-slate-200 dark:border-gray-700">
                <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
              
              {/* Rows Skeleton */}
              {[...Array(5)].map((_, i) => (
                <div key={i} className="grid grid-cols-7 gap-4 py-4 animate-pulse">
                  {/* Aula */}
                  <div className="space-y-2">
                    <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded w-3/4"></div>
                    <div className="h-3 bg-slate-200 dark:bg-gray-700 rounded w-1/2"></div>
                  </div>
                  
                  {/* Instrutor */}
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-slate-200 dark:bg-gray-700 rounded-full"></div>
                    <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded w-20"></div>
                  </div>
                  
                  {/* Data/Hora */}
                  <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded w-24"></div>
                  
                  {/* Status */}
                  <div className="h-6 bg-slate-200 dark:bg-gray-700 rounded w-20"></div>
                  
                  {/* Presenças */}
                  <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded w-16"></div>
                  
                  {/* Filial */}
                  <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded w-20"></div>
                  
                  {/* Ações */}
                  <div className="h-8 bg-slate-200 dark:bg-gray-700 rounded w-16"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Mobile Skeleton */}
          <div className="lg:hidden space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="border border-slate-200 dark:border-gray-700 rounded-lg p-4 animate-pulse">
                <div className="flex items-start justify-between mb-3">
                  <div className="space-y-2 flex-1">
                    <div className="h-5 bg-slate-200 dark:bg-gray-700 rounded w-3/4"></div>
                    <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded w-1/2"></div>
                  </div>
                  <div className="h-6 bg-slate-200 dark:bg-gray-700 rounded w-20"></div>
                </div>
                
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-8 h-8 bg-slate-200 dark:bg-gray-700 rounded-full"></div>
                  <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded w-24"></div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded w-20"></div>
                  <div className="h-8 bg-slate-200 dark:bg-gray-700 rounded w-20"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-slate-900 dark:text-gray-100">
            <Filter className="h-5 w-5" />
            Listagem de Aulas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-red-500 dark:text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-900 dark:text-gray-100 mb-2">
              Erro ao carregar aulas
            </h3>
            <p className="text-slate-600 dark:text-gray-400 mb-4">
              {error}
            </p>
            <Button 
              variant="outline" 
              onClick={() => window.location.reload()}
            >
              Tentar novamente
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Empty state
  if (classes.length === 0) {
    return (
      <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-slate-900 dark:text-gray-100">
            <Filter className="h-5 w-5" />
            Listagem de Aulas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <Database className="h-12 w-12 text-slate-400 dark:text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-900 dark:text-gray-100 mb-2">
              Nenhuma aula encontrada
            </h3>
            <p className="text-slate-600 dark:text-gray-400">
              Tente ajustar os filtros para encontrar aulas.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-slate-900 dark:text-gray-100">
          <Filter className="h-5 w-5" />
          Listagem de Aulas
          <Badge variant="secondary" className="ml-auto">
            {classes.length} {classes.length === 1 ? 'aula' : 'aulas'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Desktop Table */}
        <div className="hidden lg:block">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[300px]">Aula</TableHead>
                <TableHead className="w-[200px]">Instrutor</TableHead>
                <TableHead className="w-[150px]">Data/Hora</TableHead>
                <TableHead className="w-[120px]">Status</TableHead>
                <TableHead className="w-[120px]">Presenças</TableHead>
                <TableHead className="w-[150px]">Filial</TableHead>
                <TableHead className="w-[100px]">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {classes.map((classItem, index) => {
                const statusConfig = getStatusConfig(classItem.status);
                const isOpenClass = !classItem.class_group_id;
                
                return (
                  <TableRow key={classItem.id} className="hover:bg-slate-50 dark:hover:bg-gray-800/50">
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          {isOpenClass && (
                            <Unlock className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                          )}
                          <Link 
                            href={`/presenca/${classItem.id}?tab=lista`}
                            className="font-medium text-slate-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 cursor-pointer"
                          >
                            {classItem.name}
                          </Link>
                        </div>
                        {isOpenClass && (
                          <Badge variant="outline" className="text-xs bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800">
                            Aula Livre
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={classItem.instructor?.avatar_url || undefined} alt={classItem.instructor?.full_name || undefined} />
                          <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs font-medium">
                            {getInstructorInitials(classItem.instructor?.full_name || 'IN')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-slate-900 dark:text-gray-100 text-sm">
                            {classItem.instructor?.full_name || 'Instrutor'}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-gray-400">
                        <Calendar className="h-4 w-4" />
                        <span>
                          {formatDateTimeBrasilia(classItem.start_time)}
                        </span>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={`${statusConfig.color} text-xs font-medium flex items-center gap-1 w-fit`}
                      >
                        {statusConfig.icon}
                        {statusConfig.label}
                      </Badge>
                    </TableCell>
                    
                    <TableCell>
                      {getAttendanceDisplay(classItem._count?.attendance || 0, classItem.max_capacity, isOpenClass)}
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-gray-400">
                        <MapPin className="h-4 w-4" />
                        <span>{classItem.branch?.name || 'Academia'}</span>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onViewDetails?.(classItem.id)}
                          className="h-8 px-3 text-slate-600 dark:text-gray-400 hover:text-slate-900 dark:hover:text-gray-100"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Ver
                        </Button>
                        {onExportClass && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => onExportClass(classItem.id)}
                            className="h-8 px-2 text-slate-600 dark:text-gray-400 hover:text-slate-900 dark:hover:text-gray-100"
                            title="Exportar lista de presença"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>

        {/* Mobile Cards */}
        <div className="lg:hidden space-y-4">
          {classes.map((classItem, index) => {
            const statusConfig = getStatusConfig(classItem.status);
            const isOpenClass = !classItem.class_group_id;
            
            return (
              <Card key={classItem.id} className="bg-slate-50 dark:bg-gray-800 border border-slate-200 dark:border-gray-700">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    {/* Header */}
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          {isOpenClass && (
                            <Unlock className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                          )}
                          <Link 
                            href={`/presenca/${classItem.id}?tab=lista`}
                            className="font-medium text-slate-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 cursor-pointer"
                          >
                            {classItem.name}
                          </Link>
                        </div>
                        {isOpenClass && (
                          <Badge variant="outline" className="text-xs bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800">
                            Aula Livre
                          </Badge>
                        )}
                      </div>
                      <Badge 
                        variant="outline" 
                        className={`${statusConfig.color} text-xs font-medium flex items-center gap-1`}
                      >
                        {statusConfig.icon}
                        {statusConfig.label}
                      </Badge>
                    </div>

                    {/* Instructor */}
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={classItem.instructor?.avatar_url || undefined} alt={classItem.instructor?.full_name || undefined} />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs font-medium">
                          {getInstructorInitials(classItem.instructor?.full_name || 'IN')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-slate-900 dark:text-gray-100 text-sm">
                          {classItem.instructor?.full_name || 'Instrutor'}
                        </p>
                      </div>
                    </div>

                    {/* Details */}
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-gray-400">
                        <Calendar className="h-4 w-4" />
                        <span>
                          {formatDateTimeBrasilia(classItem.start_time)}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-gray-400">
                        <MapPin className="h-4 w-4" />
                        <span>{classItem.branch?.name || 'Academia'}</span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        {getAttendanceDisplay(classItem._count?.attendance || 0, classItem.max_capacity, isOpenClass)}
                        
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => onViewDetails?.(classItem.id)}
                            className="h-8 px-3 text-slate-600 dark:text-gray-400 hover:text-slate-900 dark:hover:text-gray-100"
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Ver detalhes
                          </Button>
                          {onExportClass && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => onExportClass(classItem.id)}
                              className="h-8 px-2 text-slate-600 dark:text-gray-400 hover:text-slate-900 dark:hover:text-gray-100"
                              title="Exportar lista de presença"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
} 