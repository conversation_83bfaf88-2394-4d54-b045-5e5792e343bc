'use client';

import { useState, useEffect, useTransition } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Calendar, 
  Search, 
  Filter, 
  Download, 
  RefreshCw,
  Plus,
  ChevronDown,
  BarChart3,
  X,
  Clock,
  Users
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { getAttendanceStats } from '../actions/attendance-actions';
import { exportAttendanceList } from '../actions/attendance-queries';
import { toast } from 'sonner';
import { useAttendanceFilters } from './AttendanceFiltersContext';

interface FilterOption {
  id: string;
  label: string;
  icon: React.ReactNode;
  count?: number;
  active?: boolean;
}

interface AttendanceStats {
  totalClasses: number;
  ongoingClasses: number;
  upcomingClasses: number;
  completedClasses: number;
  totalAttendances: number;
  uniqueStudents: number;
}

function useAttendanceExport() {
  const [isExporting, setIsExporting] = useState(false);

  const exportClassAttendance = async (classId: string) => {
    setIsExporting(true);
    try {
      const result = await exportAttendanceList(classId);
      
      if (result.success && result.data) {
        // Criar o blob CSV
        const blob = new Blob([result.data.csvContent], { type: 'text/csv;charset=utf-8;' });
        
        // Criar link temporário para download
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', result.data.filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        toast.success("Exportação concluída", {
          description: `Arquivo ${result.data.filename} baixado com sucesso.`,
        });
      } else {
        toast.error("Erro na exportação", {
          description: result.errors?._form || "Erro desconhecido ao exportar dados.",
        });
      }
    } catch (error) {
      console.error('Erro na exportação:', error);
      toast.error("Erro na exportação", {
        description: "Erro interno do sistema. Tente novamente.",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const exportAllAttendance = async () => {
    setIsExporting(true);
    try {
      // TODO: Implementar exportação geral quando necessário
      toast.info("Em desenvolvimento", {
        description: "Exportação geral de presenças será implementada em breve.",
      });
    } catch (error) {
      console.error('Erro na exportação geral:', error);
      toast.error("Erro na exportação", {
        description: "Erro interno do sistema. Tente novamente.",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return {
    isExporting,
    exportClassAttendance,
    exportAllAttendance
  };
}

export function QuickActionBar() {
  const { 
    state: filterState, 
    setSearchInput, 
    setStatus, 
    clearSearch,
    resetFilters,
    hasActiveFilters,
    getActiveFilters
  } = useAttendanceFilters();

  const { isExporting, exportAllAttendance } = useAttendanceExport();

  const [stats, setStats] = useState<AttendanceStats>({
    totalClasses: 0,
    ongoingClasses: 0,
    upcomingClasses: 0,
    completedClasses: 0,
    totalAttendances: 0,
    uniqueStudents: 0,
  });
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isPending, startTransition] = useTransition();

  // Carregar estatísticas ao montar o componente
  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    startTransition(async () => {
      try {
        const result = await getAttendanceStats();
        if (result.success && result.data) {
          setStats(result.data);
        } else {
          console.error('Erro ao carregar estatísticas:', result.errors);
        }
      } catch (error) {
        console.error('Erro ao carregar estatísticas:', error);
      }
    });
  };

  const filterOptions: FilterOption[] = [
    {
      id: 'all',
      label: 'Todas as Aulas',
      icon: <BarChart3 className="h-4 w-4" />,
      count: stats.totalClasses,
      active: filterState.status === 'all'
    },
    {
      id: 'ongoing',
      label: 'Em Andamento',
      icon: <Clock className="h-4 w-4" />,
      count: stats.ongoingClasses,
      active: filterState.status === 'ongoing'
    },
    {
      id: 'scheduled',
      label: 'Próximas',
      icon: <Calendar className="h-4 w-4" />,
      count: stats.upcomingClasses,
      active: filterState.status === 'scheduled'
    },
    {
      id: 'completed',
      label: 'Concluídas',
      icon: <Users className="h-4 w-4" />,
      count: stats.completedClasses,
      active: filterState.status === 'completed'
    }
  ];

  const quickActions: Array<{
    id: string;
    label: string;
    icon: React.ReactNode;
    href?: string;
    onClick?: () => void;
    primary?: boolean;
    disabled?: boolean;
  }> = [
    {
      id: 'new-class',
      label: 'Nova Aula Livre',
      icon: <Plus className="h-4 w-4" />,
      href: '/aulas/livres/nova',
      primary: true
    },
    {
      id: 'export',
      label: 'Exportar',
      icon: <Download className={`h-4 w-4 ${isExporting ? 'animate-pulse' : ''}`} />,
      onClick: () => handleExport(),
      disabled: isExporting
    },
    {
      id: 'refresh',
      label: 'Atualizar',
      icon: <RefreshCw className={`h-4 w-4 ${isPending ? 'animate-spin' : ''}`} />,
      onClick: () => handleRefresh(),
      disabled: isPending
    }
  ];

  const handleFilterToggle = (filterId: string) => {
    if (filterId === 'all') {
      setStatus('all');
    } else {
      setStatus(filterId as any);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchInput(value);
  };

  const handleExport = () => {
    exportAllAttendance();
  };

  const handleRefresh = () => {
    loadStats();
    toast.success("Dados atualizados", {
      description: "As estatísticas foram atualizadas com sucesso.",
    });
  };

  const clearAllFilters = () => {
    resetFilters();
  };

  // Obter filtros ativos para exibição
  const activeFilters = getActiveFilters();

  return (
    <motion.div 
      className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 rounded-xl p-4 shadow-sm"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
        
        {/* Seção de Busca */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 dark:text-gray-500" />
            <Input
              placeholder="Buscar aulas, instrutores ou alunos..."
              value={filterState.searchInput}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10 pr-4 h-10 bg-slate-50 dark:bg-gray-800 border-slate-200 dark:border-gray-600 focus:bg-white dark:focus:bg-gray-700 transition-colors text-slate-900 dark:text-gray-100"
            />
            {filterState.searchInput && (
              <motion.button
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                onClick={clearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 hover:bg-slate-100 dark:hover:bg-gray-700 rounded-full transition-colors"
              >
                <X className="h-3 w-3 text-slate-400 dark:text-gray-500" />
              </motion.button>
            )}
          </div>
        </div>

        {/* Filtros e Ações */}
        <div className="flex items-center gap-2 flex-wrap">
          
          {/* Filtros Rápidos */}
          <div className="hidden md:flex items-center gap-1 bg-slate-50 dark:bg-gray-800 rounded-lg p-1">
            {filterOptions.map((option) => (
              <motion.button
                key={option.id}
                onClick={() => handleFilterToggle(option.id)}
                className={`
                  flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-all
                  ${option.active 
                    ? 'bg-white dark:bg-gray-700 text-slate-900 dark:text-gray-100 shadow-sm' 
                    : 'text-slate-500 dark:text-gray-400 hover:text-slate-700 dark:hover:text-gray-300 hover:bg-white/50 dark:hover:bg-gray-700/50'
                  }
                `}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                disabled={isPending}
              >
                {option.icon}
                <span>{option.label}</span>
                {option.count !== undefined && (
                  <span className={`
                    text-xs px-1.5 py-0.5 rounded-full
                    ${option.active 
                      ? 'bg-slate-100 dark:bg-gray-600 text-slate-600 dark:text-gray-300' 
                      : 'bg-slate-200 dark:bg-gray-600 text-slate-500 dark:text-gray-400'
                    }
                  `}>
                    {option.count}
                  </span>
                )}
              </motion.button>
            ))}
          </div>

          {/* Filtro Dropdown Mobile */}
          <div className="md:hidden relative">
            <Button
              variant="outline"
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="h-10 border-slate-200 dark:border-gray-600 bg-slate-50 dark:bg-gray-800 text-slate-700 dark:text-gray-300"
              disabled={isPending}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filtros
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>

            <AnimatePresence>
              {isFilterOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                  className="absolute top-12 left-0 bg-white dark:bg-gray-800 border border-slate-200 dark:border-gray-600 rounded-lg shadow-lg p-2 min-w-[200px] z-10"
                >
                  {filterOptions.map((option) => (
                    <button
                      key={option.id}
                      onClick={() => {
                        handleFilterToggle(option.id);
                        setIsFilterOpen(false);
                      }}
                      className={`
                        w-full flex items-center gap-2 px-3 py-2 rounded-md text-sm transition-colors
                        ${option.active 
                          ? 'bg-slate-100 dark:bg-gray-700 text-slate-900 dark:text-gray-100' 
                          : 'text-slate-600 dark:text-gray-300 hover:bg-slate-50 dark:hover:bg-gray-700'
                        }
                      `}
                    >
                      {option.icon}
                      <span className="flex-1 text-left">{option.label}</span>
                      {option.count !== undefined && (
                        <span className="text-xs bg-slate-200 dark:bg-gray-600 text-slate-600 dark:text-gray-300 px-1.5 py-0.5 rounded-full">
                          {option.count}
                        </span>
                      )}
                    </button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Ações Rápidas */}
          <div className="flex items-center gap-2">
            {quickActions.map((action) => (
              <motion.div key={action.id} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                {action.href ? (
                  <Link href={action.href}>
                    <Button
                      variant={action.primary ? "default" : "ghost"}
                      size="sm"
                      className={`
                        h-10 ${action.primary ? 'bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-600 dark:hover:bg-blue-700' : ''}
                      `}
                    >
                      {action.icon}
                      <span className="ml-2 hidden sm:inline">{action.label}</span>
                    </Button>
                  </Link>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={action.onClick}
                    disabled={action.disabled}
                    className="h-10 text-slate-600 dark:text-gray-400 hover:text-slate-900 dark:hover:text-gray-100 hover:bg-slate-50 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {action.icon}
                    <span className="ml-2 hidden sm:inline">{action.label}</span>
                  </Button>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Filtros Ativos */}
      <AnimatePresence>
        {hasActiveFilters() && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="mt-3 pt-3 border-t border-slate-100 dark:border-gray-700"
          >
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-sm text-slate-500 dark:text-gray-400">Filtros ativos:</span>
              
              {activeFilters.map((filter) => (
                <motion.div
                  key={filter.key}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className={`
                    flex items-center gap-1 px-2 py-1 rounded-md text-xs
                    ${filter.key === 'search' 
                      ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400'
                      : 'bg-slate-100 dark:bg-gray-700 text-slate-700 dark:text-gray-300'
                    }
                  `}
                >
                  {filter.key === 'search' && <Search className="h-3 w-3" />}
                  <span>{filter.label}: {filter.value}</span>
                  <button
                    onClick={() => {
                      if (filter.key === 'search') {
                        clearSearch();
                      } else if (filter.key === 'status') {
                        setStatus('all');
                      }
                      // Outros filtros serão implementados nas próximas tarefas
                    }}
                    className={`
                      rounded-full p-0.5 transition-colors
                      ${filter.key === 'search' 
                        ? 'hover:bg-blue-100 dark:hover:bg-blue-800/50'
                        : 'hover:bg-slate-200 dark:hover:bg-gray-600'
                      }
                    `}
                  >
                    <X className="h-2.5 w-2.5" />
                  </button>
                </motion.div>
              ))}

              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="h-6 px-2 text-xs text-slate-500 dark:text-gray-400 hover:text-slate-700 dark:hover:text-gray-300"
              >
                Limpar tudo
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
} 