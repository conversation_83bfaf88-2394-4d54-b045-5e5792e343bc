'use client';

import { motion, useMotionValue, useTransform, animate } from 'framer-motion';
import { useEffect } from 'react';

interface RadialProgressProps {
  value: number;
  maxValue: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  showValue?: boolean;
  duration?: number;
  className?: string;
}

export function RadialProgress({
  value,
  maxValue,
  size = 80,
  strokeWidth = 8,
  color = '#3b82f6',
  backgroundColor = '#e5e7eb',
  showValue = true,
  duration = 1.2,
  className = ''
}: RadialProgressProps) {
  const progress = Math.min((value / maxValue) * 100, 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const center = size / 2;

  // Motion values para animação
  const animatedProgress = useMotionValue(0);
  const pathLength = useTransform(animatedProgress, [0, 100], [circumference, 0]);

  useEffect(() => {
    const controls = animate(animatedProgress, progress, {
      duration,
      ease: 'easeOut',
    });

    return controls.stop;
  }, [progress, animatedProgress, duration]);

  return (
    <div className={`relative inline-flex items-center justify-center ${className}`}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        <defs>
          <linearGradient id={`gradient-${color.replace('#', '')}`} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor={color} />
            <stop offset="100%" stopColor={`${color}CC`} />
          </linearGradient>
        </defs>
        
        {/* Background circle */}
        <circle
          cx={center}
          cy={center}
          r={radius}
          fill="none"
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
        />
        
        {/* Progress circle */}
        <motion.circle
          cx={center}
          cy={center}
          r={radius}
          fill="none"
          stroke={`url(#gradient-${color.replace('#', '')})`}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeDasharray={circumference}
          strokeDashoffset={pathLength}
          initial={{ strokeDashoffset: circumference }}
        />
      </svg>
      
      {showValue && (
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: duration * 0.5 }}
        >
          <span className="text-sm font-semibold text-slate-700 dark:text-gray-300">
            {Math.round(progress)}%
          </span>
        </motion.div>
      )}
    </div>
  );
} 