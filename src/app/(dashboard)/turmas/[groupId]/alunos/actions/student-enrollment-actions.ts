'use server';

import { createAdminClient } from '@/services/supabase/server';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import { z } from 'zod';

// Schema para filtros de estudantes
const StudentEnrollmentFiltersSchema = z.object({
  search: z.string().optional(),
  belt_color: z.string().optional(),
  status: z.enum(['active', 'inactive']).optional(),
  branch_id: z.string().uuid().optional(),
  has_payment_pending: z.boolean().optional(),
});

// Schema para paginação
const PaginationSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(20),
});

// Schema completo para busca de estudantes
const GetStudentsForEnrollmentSchema = z.object({
  classGroupId: z.string().uuid('ID da turma é obrigatório'),
  filters: StudentEnrollmentFiltersSchema.default({}),
  pagination: PaginationSchema.default({ page: 1, pageSize: 20 }),
});

export interface StudentForEnrollment {
  id: string;
  user_id: string;
  user: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
    avatar_url?: string;
  };
  current_belt?: {
    belt_color: string;
    degree: number;
    label?: string | null;
    stripe_color?: string | null;
    show_center_line?: boolean | null;
    center_line_color?: string | null;
  };
  belt?: string; // Mantido para compatibilidade
  is_already_enrolled: boolean;
  branch_id?: string;
  status: 'active' | 'inactive';
}

export interface StudentsForEnrollmentResult {
  students: StudentForEnrollment[];
  totalCount: number;
  hasNextPage: boolean;
  currentPage: number;
  pageSize: number;
  stats: {
    total: number;
    available: number;
    alreadyEnrolled: number;
  };
}

/**
 * Server action para buscar estudantes disponíveis para matrícula em uma turma
 * 
 * Aplica os seguintes filtros:
 * 1. Validação de faixa etária (min_age e max_age da turma)
 * 2. Status do estudante (ativo/inativo)
 * 3. Remoção de estudantes já matriculados
 * 4. Busca por nome ou email
 * 5. Filtro por cor da faixa
 * 6. Paginação
 */
export async function getStudentsForEnrollment(data: unknown) {
  try {
    // Validar autenticação
    const user = await getCurrentUser();
    if (!user) {
      return { 
        success: false, 
        errors: { _form: 'Usuário não autenticado' } 
      };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { 
        success: false, 
        errors: { _form: 'Tenant não identificado' } 
      };
    }

    // Validar dados de entrada
    const validationResult = GetStudentsForEnrollmentSchema.safeParse(data);
    if (!validationResult.success) {
      return { 
        success: false, 
        errors: validationResult.error.format() 
      };
    }

    const { classGroupId, filters, pagination } = validationResult.data;
    const supabase = await createAdminClient();

    // Verificar se a turma existe e pertence ao tenant
    const { data: classGroup, error: classGroupError } = await supabase
      .from('class_groups')
      .select('id, name, max_capacity, is_active, min_age, max_age')
      .eq('id', classGroupId)
      .eq('tenant_id', tenantId)
      .single();

    if (classGroupError || !classGroup) {
      return { 
        success: false, 
        errors: { _form: 'Turma não encontrada' } 
      };
    }

    if (!classGroup.is_active) {
      return { 
        success: false, 
        errors: { _form: 'Turma não está ativa' } 
      };
    }

    // Buscar estudantes já matriculados na turma
    const { data: enrolledStudents, error: enrolledError } = await supabase
      .from('class_group_enrollments')
      .select('student_id')
      .eq('class_group_id', classGroupId)
      .eq('status', 'active')
      .eq('tenant_id', tenantId);

    if (enrolledError) {
      return { 
        success: false, 
        errors: { _form: 'Falha ao verificar estudantes matriculados' } 
      };
    }

    const enrolledStudentIds = enrolledStudents?.map(e => e.student_id) || [];

    // Buscar todos os estudantes do tenant
    let studentsQuery = supabase
      .from('students')
      .select(`
        id,
        user_id,
        branch_id,
        current_belt_id,
        tenant_id,
        birth_date,
        users (
          id,
          full_name,
          email,
          phone,
          avatar_url,
          status
        )
      `)
      .eq('tenant_id', tenantId)
      .is('deleted_at', null);

    // Aplicar filtro de branch se especificado
    if (filters.branch_id) {
      studentsQuery = studentsQuery.eq('branch_id', filters.branch_id);
    }

    const { data: studentsResult, error: studentsQueryError } = await studentsQuery;

    if (studentsQueryError) {
      return { 
        success: false, 
        errors: { _form: 'Falha ao carregar estudantes' } 
      };
    }

    if (!studentsResult) {
      return {
        success: true,
        data: {
          students: [],
          totalCount: 0,
          hasNextPage: false,
          currentPage: pagination.page,
          pageSize: pagination.pageSize,
          stats: { total: 0, available: 0, alreadyEnrolled: 0 }
        }
      };
    }

    // Mapear estudantes com detalhes da faixa via RPC
    const studentIds = studentsResult.map(s => s.id);
    const beltsDataMap = new Map<string, any>();

    if (studentIds.length > 0) {
      const beltPromises = studentIds.map(async (sid) => {
        const { data: beltDetails } = await supabase.rpc('get_student_current_belt_details', { student_id_param: sid });
        if (beltDetails && beltDetails.length > 0) {
          beltsDataMap.set(sid, beltDetails[0]);
        }
      });
      await Promise.all(beltPromises);
    }

    // Mapear resultados
    let mappedStudents: StudentForEnrollment[] = studentsResult.map(student => {
      const user = Array.isArray(student.users) ? student.users[0] : student.users;
      
      if (!user) {
        return null;
      }
      
      const belt = beltsDataMap.get(student.id);
      const currentBelt = belt ? {
        belt_color: belt.belt_color,
        degree: belt.degree,
        label: belt.label,
        stripe_color: belt.stripe_color,
        show_center_line: belt.show_center_line,
        center_line_color: belt.center_line_color,
      } : undefined;

      return {
        id: student.id,
        user_id: student.user_id,
        user: {
          id: user.id || '',
          full_name: user.full_name || '',
          email: user.email || '',
          phone: user.phone,
          avatar_url: user.avatar_url
        },
        current_belt: currentBelt,
        belt: belt ? belt.belt_color : undefined, // Compatibilidade
        is_already_enrolled: enrolledStudentIds.includes(student.id),
        branch_id: student.branch_id,
        status: user.status as 'active' | 'inactive' || 'active'
      };
    }).filter(Boolean) as StudentForEnrollment[];

    // Função para calcular idade
    const calculateAge = (birthDate: string): number => {
      if (!birthDate) return 0;
      const today = new Date();
      const birth = new Date(birthDate);
      let age = today.getFullYear() - birth.getFullYear();
      const monthDiff = today.getMonth() - birth.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
      }
      
      return age;
    };

    // Aplicar filtros no lado servidor
    
    // 1. Filtrar por faixa etária da turma
    if (classGroup.min_age !== null || classGroup.max_age !== null) {
      mappedStudents = mappedStudents.filter(student => {
        const studentData = studentsResult.find(s => s.id === student.id);
        if (!studentData?.birth_date) return false;
        
        const age = calculateAge(studentData.birth_date);
        
        // Verificar idade mínima
        if (classGroup.min_age !== null && age < classGroup.min_age) {
          return false;
        }
        
        // Verificar idade máxima
        if (classGroup.max_age !== null && age > classGroup.max_age) {
          return false;
        }
        
        return true;
      });
    }
    
    // 2. Filtrar por status (padrão: apenas ativos)
    if (filters.status) {
      mappedStudents = mappedStudents.filter(student => student.status === filters.status);
    } else {
      mappedStudents = mappedStudents.filter(student => student.status === 'active');
    }

    // 3. Remover estudantes já matriculados
    const availableStudents = mappedStudents.filter(student => !student.is_already_enrolled);
    const alreadyEnrolledCount = mappedStudents.length - availableStudents.length;

    // 4. Aplicar filtro de busca
    let filteredStudents = availableStudents;
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredStudents = availableStudents.filter(student => 
        student.user.full_name.toLowerCase().includes(searchTerm) ||
        student.user.email.toLowerCase().includes(searchTerm)
      );
    }

    // 5. Aplicar filtro de faixa
    if (filters.belt_color) {
      const colorFilter = filters.belt_color.toLowerCase();
      filteredStudents = filteredStudents.filter(student => 
        (student.current_belt?.belt_color || '').toLowerCase() === colorFilter
      );
    }

    // 6. Aplicar paginação
    const totalCount = filteredStudents.length;
    const from = (pagination.page - 1) * pagination.pageSize;
    const to = from + pagination.pageSize;
    const paginatedStudents = filteredStudents.slice(from, to);
    const hasNextPage = to < totalCount;

    // Calcular estatísticas
    const stats = {
      total: mappedStudents.length,
      available: availableStudents.length,
      alreadyEnrolled: alreadyEnrolledCount
    };

    return {
      success: true,
      data: {
        students: paginatedStudents,
        totalCount,
        hasNextPage,
        currentPage: pagination.page,
        pageSize: pagination.pageSize,
        stats
      }
    };

  } catch (error) {
    console.error('Erro ao buscar estudantes para matrícula:', error);
    return { 
      success: false, 
      errors: { _form: 'Erro interno do servidor' } 
    };
  }
}

/**
 * Server action para obter informações de capacidade da turma
 */
export async function getClassGroupCapacityInfo(classGroupId: string) {
  try {
    // Validar autenticação
    const user = await getCurrentUser();
    if (!user) {
      return { 
        success: false, 
        errors: { _form: 'Usuário não autenticado' } 
      };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { 
        success: false, 
        errors: { _form: 'Tenant não identificado' } 
      };
    }

    if (!classGroupId || typeof classGroupId !== 'string') {
      return { 
        success: false, 
        errors: { _form: 'ID da turma inválido' } 
      };
    }

    const supabase = await createAdminClient();

    // Buscar informações da turma
    const { data: classGroup, error: classGroupError } = await supabase
      .from('class_groups')
      .select('id, name, max_capacity')
      .eq('id', classGroupId)
      .eq('tenant_id', tenantId)
      .single();

    if (classGroupError || !classGroup) {
      return { 
        success: false, 
        errors: { _form: 'Turma não encontrada' } 
      };
    }

    // Contar matrículas ativas
    const { count: currentEnrollmentCount, error: countError } = await supabase
      .from('class_group_enrollments')
      .select('*', { count: 'exact', head: true })
      .eq('class_group_id', classGroupId)
      .eq('status', 'active')
      .eq('tenant_id', tenantId);

    if (countError) {
      return { 
        success: false, 
        errors: { _form: 'Erro ao contar matrículas' } 
      };
    }

    const enrollmentCount = currentEnrollmentCount || 0;
    const maxCapacity = classGroup.max_capacity;
    const availableSpots = maxCapacity ? maxCapacity - enrollmentCount : null;
    const isFull = maxCapacity ? enrollmentCount >= maxCapacity : false;

    return {
      success: true,
      data: {
        current_enrollment_count: enrollmentCount,
        max_capacity: maxCapacity,
        available_spots: availableSpots,
        is_full: isFull
      }
    };

  } catch (error) {
    console.error('Erro ao obter informações de capacidade:', error);
    return { 
      success: false, 
      errors: { _form: 'Erro interno do servidor' } 
    };
  }
}

// Schemas para validação das ações de pausa/reativação
const PauseEnrollmentSchema = z.object({
  enrollment_id: z.string().uuid('ID da matrícula é obrigatório'),
  reason: z.string().optional(),
  notes: z.string().optional()
});

const ResumeEnrollmentSchema = z.object({
  enrollment_id: z.string().uuid('ID da matrícula é obrigatório'),
  reason: z.string().optional(),
  notes: z.string().optional()
});

/**
 * Server action para pausar uma matrícula
 * 
 * Esta ação:
 * 1. Valida se o usuário tem permissão
 * 2. Verifica se a matrícula existe e está ativa
 * 3. Verifica se não há pausa ativa para esta matrícula
 * 4. Cria um registro de pausa na tabela enrollment_pauses
 */
export async function pauseEnrollment(data: unknown) {
  try {
    // Validar autenticação
    const user = await getCurrentUser();
    if (!user) {
      return { 
        success: false, 
        errors: { _form: 'Usuário não autenticado' } 
      };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { 
        success: false, 
        errors: { _form: 'Tenant não identificado' } 
      };
    }

    // Validar dados de entrada
    const validationResult = PauseEnrollmentSchema.safeParse(data);
    if (!validationResult.success) {
      return { 
        success: false, 
        errors: validationResult.error.format() 
      };
    }

    const { enrollment_id: enrollmentId, reason, notes } = validationResult.data;
    
    // Se reason não foi fornecido, usar um valor padrão
    const pauseReason = reason || 'Pausa solicitada';

    const supabase = await createAdminClient();

    // Verificar se a matrícula existe e pertence ao tenant
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('class_group_enrollments')
      .select(`
        id,
        tenant_id,
        class_group_id,
        student_id,
        status,
        students (
          users (
            full_name,
            email
          )
        ),
        class_groups (
          name
        )
      `)
      .eq('id', enrollmentId)
      .eq('tenant_id', tenantId)
      .single();

    if (enrollmentError || !enrollment) {
      return { 
        success: false, 
        errors: { _form: 'Matrícula não encontrada' } 
      };
    }

    // Verificar se a matrícula está ativa
    if (enrollment.status !== 'active') {
      return { 
        success: false, 
        errors: { _form: 'Apenas matrículas ativas podem ser pausadas' } 
      };
    }

    // Verificar se já existe uma pausa ativa para esta matrícula
    const { data: activePause, error: pauseCheckError } = await supabase
      .from('enrollment_pauses')
      .select('id')
      .eq('enrollment_id', enrollmentId)
      .eq('tenant_id', tenantId)
      .is('resumed_at', null)
      .single();

    if (pauseCheckError && pauseCheckError.code !== 'PGRST116') {
      return { 
        success: false, 
        errors: { _form: 'Erro ao verificar pausas existentes' } 
      };
    }

    if (activePause) {
      return { 
        success: false, 
        errors: { _form: 'Esta matrícula já está pausada' } 
      };
    }

    // Criar registro de pausa
    const { data: pauseRecord, error: pauseError } = await supabase
      .from('enrollment_pauses')
      .insert({
        enrollment_id: enrollmentId,
        tenant_id: tenantId,
        reason: pauseReason,
        notes: notes || null,
        created_by: user.id
      })
      .select()
      .single();

    if (pauseError) {
      console.error('Erro ao criar pausa:', pauseError);
      return { 
        success: false, 
        errors: { _form: 'Erro ao pausar matrícula' } 
      };
    }

    return {
      success: true,
      data: {
        pauseId: pauseRecord.id,
        enrollmentId: enrollmentId,
        pausedAt: pauseRecord.paused_at,
        reason: pauseReason,
        studentName: (enrollment.students as any)?.users?.full_name || 'Estudante',
        className: (enrollment.class_groups as any)?.name || 'Turma'
      }
    };

  } catch (error) {
    console.error('Erro ao pausar matrícula:', error);
    return { 
      success: false, 
      errors: { _form: 'Erro interno do servidor' } 
    };
  }
}

/**
 * Server action para reativar uma matrícula pausada
 * 
 * Esta ação:
 * 1. Valida se o usuário tem permissão
 * 2. Verifica se existe uma pausa ativa para a matrícula
 * 3. Atualiza o registro de pausa com a data de retomada
 */
export async function resumeEnrollment(data: unknown) {
  try {
    // Validar autenticação
    const user = await getCurrentUser();
    if (!user) {
      return { 
        success: false, 
        errors: { _form: 'Usuário não autenticado' } 
      };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { 
        success: false, 
        errors: { _form: 'Tenant não identificado' } 
      };
    }

    // Validar dados de entrada
    const validationResult = ResumeEnrollmentSchema.safeParse(data);
    if (!validationResult.success) {
      return { 
        success: false, 
        errors: validationResult.error.format() 
      };
    }

    const { enrollment_id: enrollmentId, reason, notes } = validationResult.data;
    const supabase = await createAdminClient();

    // Verificar se a matrícula existe
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('class_group_enrollments')
      .select(`
        id,
        tenant_id,
        status,
        students (
          users (
            full_name,
            email
          )
        ),
        class_groups (
          name
        )
      `)
      .eq('id', enrollmentId)
      .eq('tenant_id', tenantId)
      .single();

    if (enrollmentError || !enrollment) {
      return { 
        success: false, 
        errors: { _form: 'Matrícula não encontrada' } 
      };
    }

    // Buscar pausa ativa para esta matrícula
    const { data: activePause, error: pauseError } = await supabase
      .from('enrollment_pauses')
      .select('id, paused_at, reason')
      .eq('enrollment_id', enrollmentId)
      .eq('tenant_id', tenantId)
      .is('resumed_at', null)
      .single();

    if (pauseError || !activePause) {
      return { 
        success: false, 
        errors: { _form: 'Não há pausa ativa para esta matrícula' } 
      };
    }

    // Atualizar registro de pausa com data de retomada
    const { data: updatedPause, error: updateError } = await supabase
      .from('enrollment_pauses')
      .update({
        resumed_at: new Date().toISOString(),
        notes: notes || reason || null
      })
      .eq('id', activePause.id)
      .select()
      .single();

    if (updateError) {
      console.error('Erro ao reativar matrícula:', updateError);
      return { 
        success: false, 
        errors: { _form: 'Erro ao reativar matrícula' } 
      };
    }

    return {
      success: true,
      data: {
        pauseId: activePause.id,
        enrollmentId: enrollmentId,
        pausedAt: activePause.paused_at,
        resumedAt: updatedPause.resumed_at,
        reason: activePause.reason,
        studentName: (enrollment.students as any)?.users?.full_name || 'Estudante',
        className: (enrollment.class_groups as any)?.name || 'Turma'
      }
    };

  } catch (error) {
    console.error('Erro ao reativar matrícula:', error);
    return { 
        success: false, 
        errors: { _form: 'Erro interno do servidor' } 
      };
  }
}

/**
 * Server action para verificar se uma matrícula está pausada
 */
export async function checkEnrollmentPauseStatus(enrollmentId: string) {
  try {
    // Validar autenticação
    const user = await getCurrentUser();
    if (!user) {
      return { 
        success: false, 
        errors: { _form: 'Usuário não autenticado' } 
      };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { 
        success: false, 
        errors: { _form: 'Tenant não identificado' } 
      };
    }

    if (!enrollmentId || typeof enrollmentId !== 'string') {
      return { 
        success: false, 
        errors: { _form: 'ID da matrícula inválido' } 
      };
    }

    const supabase = await createAdminClient();

    // Buscar pausa ativa
    const { data: activePause, error: pauseError } = await supabase
      .from('enrollment_pauses')
      .select('id, paused_at, reason, notes')
      .eq('enrollment_id', enrollmentId)
      .eq('tenant_id', tenantId)
      .is('resumed_at', null)
      .single();

    if (pauseError && pauseError.code !== 'PGRST116') {
      return { 
        success: false, 
        errors: { _form: 'Erro ao verificar status de pausa' } 
      };
    }

    return {
      success: true,
      data: {
        isPaused: !!activePause,
        pauseDetails: activePause || null
      }
    };

  } catch (error) {
    console.error('Erro ao verificar status de pausa:', error);
    return { 
      success: false, 
      errors: { _form: 'Erro interno do servidor' } 
    };
  }
} 