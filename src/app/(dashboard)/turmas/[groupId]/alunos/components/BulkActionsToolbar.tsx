'use client';

import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { Button } from '@/components/ui/button';
import { Pause, Play, Trash2, X } from 'lucide-react';

interface BulkActionsToolbarProps {
  selectedCount: number;
  onClearSelection: () => void;
  onPause: () => void;
  onResume: () => void;
  onUnenroll: () => void;
  isLoading?: boolean;
  pauseDisabled?: boolean;
  resumeDisabled?: boolean;
}

export function BulkActionsToolbar({
  selectedCount,
  onClearSelection,
  onPause,
  onResume,
  onUnenroll,
  isLoading = false,
  pauseDisabled = false,
  resumeDisabled = false,
}: BulkActionsToolbarProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted || selectedCount === 0) {
    return null;
  }

  const toolbar = (
    <div className="fixed bottom-4 left-1/2 -translate-x-1/2 z-50">
      <div className="flex items-center gap-4 bg-background border shadow-2xl rounded-lg p-3">
        <div className="flex items-center gap-2">
          <span className="bg-primary text-primary-foreground rounded-full h-7 w-7 flex items-center justify-center text-sm font-bold">
            {selectedCount}
          </span>
          <span className="text-sm font-medium">
            {selectedCount === 1 ? 'aluno selecionado' : 'alunos selecionados'}
          </span>
        </div>
        
        <div className="h-6 border-l border-border" />

        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={onPause}
            disabled={isLoading || pauseDisabled}
            aria-label="Pausar matrículas selecionadas"
          >
            <Pause className="h-4 w-4 mr-2" />
            Pausar
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onResume}
            disabled={isLoading || resumeDisabled}
            aria-label="Reativar matrículas selecionadas"
          >
            <Play className="h-4 w-4 mr-2" />
            Reativar
          </Button>
          <Button
            size="sm"
            variant="destructive"
            onClick={onUnenroll}
            disabled={isLoading}
            aria-label="Cancelar matrículas selecionadas"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Cancelar Matrícula
          </Button>
        </div>

        <div className="h-6 border-l border-border" />

        <Button
          size="icon"
          variant="ghost"
          onClick={onClearSelection}
          disabled={isLoading}
          aria-label="Limpar seleção"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  return createPortal(toolbar, document.body);
} 