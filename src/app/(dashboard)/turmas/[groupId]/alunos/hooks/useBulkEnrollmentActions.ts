'use client';

import { useState, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useBulkEnrollmentPause } from './useEnrollmentPause';
import { unenrollMultipleStudents } from '../../../actions/class-group/unenroll-multiple-students';
import { CACHE_KEYS } from '@/constants/cache-keys';
import type { Student } from '@/components/students/types';

export function useBulkEnrollmentActions(classGroupId: string, students: Student[]) {
  const queryClient = useQueryClient();
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const { 
    pauseMultiple, 
    resumeMultiple, 
    isLoading: isPauseLoading 
  } = useBulkEnrollmentPause(classGroupId);

  const getSelectedStudents = useCallback((selectedIds: string[]) => {
    return students.filter(s => selectedIds.includes(s.id));
  }, [students]);

  const invalidateAndRefresh = useCallback(async () => {
    await queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.ENROLLMENT.CLASS_GROUP_ENROLLMENTS(classGroupId)
    });
  }, [queryClient, classGroupId]);

  const pauseSelected = useCallback(async (selectedIds: string[]) => {
    const selected = getSelectedStudents(selectedIds);
    if (selected.length === 0) return;

    const enrollmentsToPause = selected.map(s => ({
      id: s.id,
      studentName: s.user.full_name,
    }));
    
    await pauseMultiple(enrollmentsToPause);
    await invalidateAndRefresh();
  }, [getSelectedStudents, pauseMultiple, invalidateAndRefresh]);

  const resumeSelected = useCallback(async (selectedIds: string[]) => {
    const selected = getSelectedStudents(selectedIds);
    if (selected.length === 0) return;

    const enrollmentsToResume = selected.map(s => ({
      id: s.id,
      studentName: s.user.full_name,
    }));
    
    await resumeMultiple(enrollmentsToResume);
    await invalidateAndRefresh();
  }, [getSelectedStudents, resumeMultiple, invalidateAndRefresh]);

  const unenrollSelected = useCallback(async (selectedIds: string[]) => {
    if (selectedIds.length === 0) return;
    
    setIsActionLoading(true);
    setErrors({});

    const result = await unenrollMultipleStudents({ enrollmentIds: selectedIds });

    if (result.success && result.data) {
      toast.success(`${result.data.successCount} matrículas foram canceladas com sucesso.`);
    } else {
      const errorCount = selectedIds.length - (result.data?.successCount || 0);
      toast.error(`Falha ao cancelar ${errorCount} ${errorCount === 1 ? 'matrícula' : 'matrículas'}.`);
      // Optionally set detailed errors
      const detailedErrors: Record<string, string> = {};
      result.data?.results.forEach(r => {
        if (!r.success) detailedErrors[r.enrollmentId] = r.error || 'Erro desconhecido';
      });
      setErrors(detailedErrors);
    }
    
    await invalidateAndRefresh();
    setIsActionLoading(false);

    return result;
  }, [invalidateAndRefresh]);

  return {
    isLoading: isPauseLoading || isActionLoading,
    errors,
    pauseSelected,
    resumeSelected,
    unenrollSelected,
  };
} 