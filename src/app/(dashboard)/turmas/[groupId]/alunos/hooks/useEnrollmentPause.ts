import { useState, useCallback, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { 
  pauseEnrollment, 
  resumeEnrollment,
  checkEnrollmentPauseStatus 
} from '../actions/student-enrollment-actions';
import { CACHE_KEYS } from '@/constants/cache-keys';

export interface PauseInfo {
  id: string;
  enrollment_id?: string;
  paused_at: string;
  resumed_at?: string | null;
  reason?: string | null;
  notes?: string | null;
  created_by?: string;
}

export interface EnrollmentPauseState {
  isLoading: boolean;
  error: string | null;
  isPaused: boolean;
  pauseInfo?: PauseInfo | null;
}

export interface UseEnrollmentPauseOptions {
  enrollmentId: string;
  studentName: string;
  classGroupId: string;
  enabled?: boolean;
}

export function useEnrollmentPause({
  enrollmentId,
  studentName,
  classGroupId,
  enabled = true
}: UseEnrollmentPauseOptions) {
  const queryClient = useQueryClient();
  const [actionState, setActionState] = useState<{
    isLoading: boolean;
    error: string | null;
  }>({
    isLoading: false,
    error: null
  });

  // Query para verificar status de pausa
  const {
    data: pauseStatus,
    isLoading: isLoadingStatus,
    error: statusError,
    refetch: refetchStatus
  } = useQuery({
    queryKey: CACHE_KEYS.ENROLLMENT.PAUSE_STATUS(enrollmentId),
    queryFn: async () => {
      const result = await checkEnrollmentPauseStatus(enrollmentId);
      if (!result.success) {
        throw new Error((result.errors as any)?._form || 'Erro ao verificar status de pausa');
      }
      return result.data;
    },
    enabled: enabled && !!enrollmentId,
    staleTime: 30 * 1000, // 30 segundos
    refetchOnWindowFocus: false,
  });

  const invalidateRelatedQueries = useCallback(async () => {
    await Promise.all([
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.ENROLLMENT.CLASS_GROUP_ENROLLMENTS(classGroupId)
      }),
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.ENROLLMENT.CAPACITY_CHECK(classGroupId)
      }),
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.ENROLLMENT.PAUSE_STATUS(enrollmentId)
      })
    ]);
  }, [queryClient, classGroupId, enrollmentId]);

  const pause = useCallback(async (reason?: string, notes?: string) => {
    setActionState({ isLoading: true, error: null });

    try {
      const result = await pauseEnrollment({
        enrollment_id: enrollmentId,
        reason,
        notes
      });

      if (result.success) {
        const message = `Matrícula de ${studentName} pausada com sucesso`;
        toast.success(message);
        await invalidateRelatedQueries();
        await refetchStatus();
        return { success: true, data: result.data };
      } else {
        const errorMessage = (result.errors as any)?._form || 'Erro ao pausar matrícula';
        setActionState({ isLoading: false, error: errorMessage });
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (error) {
      const errorMessage = 'Erro inesperado ao pausar matrícula';
      setActionState({ isLoading: false, error: errorMessage });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setActionState(prev => ({ ...prev, isLoading: false }));
    }
  }, [enrollmentId, studentName, invalidateRelatedQueries, refetchStatus]);

  const resume = useCallback(async (reason?: string) => {
    setActionState({ isLoading: true, error: null });

    try {
      const result = await resumeEnrollment({
        enrollment_id: enrollmentId,
        reason
      });

      if (result.success) {
        const message = `Matrícula de ${studentName} reativada com sucesso`;
        toast.success(message);
        await invalidateRelatedQueries();
        await refetchStatus();
        return { success: true, data: result.data };
      } else {
        const errorMessage = (result.errors as any)?._form || 'Erro ao reativar matrícula';
        setActionState({ isLoading: false, error: errorMessage });
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (error) {
      const errorMessage = 'Erro inesperado ao reativar matrícula';
      setActionState({ isLoading: false, error: errorMessage });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setActionState(prev => ({ ...prev, isLoading: false }));
    }
  }, [enrollmentId, studentName, invalidateRelatedQueries, refetchStatus]);

  const clearError = useCallback(() => {
    setActionState(prev => ({ ...prev, error: null }));
  }, []);

  // Preparar estado combinado
  const combinedState: EnrollmentPauseState = {
    isLoading: isLoadingStatus || actionState.isLoading,
    error: (statusError as any)?.message || actionState.error,
    isPaused: pauseStatus?.isPaused || false,
    pauseInfo: pauseStatus?.pauseDetails || null
  };

  return {
    // Estado atual
    state: combinedState,
    
    // Dados específicos
    isPaused: combinedState.isPaused,
    pauseInfo: combinedState.pauseInfo,
    isLoading: combinedState.isLoading,
    error: combinedState.error,
    
    // Ações
    pause,
    resume,
    refresh: refetchStatus,
    clearError,
    
    // Utilitários
    canPause: !combinedState.isPaused && !combinedState.isLoading,
    canResume: combinedState.isPaused && !combinedState.isLoading,
    
    // Informações derivadas
    pausedSince: combinedState.pauseInfo?.paused_at ? new Date(combinedState.pauseInfo.paused_at) : null,
    pauseReason: combinedState.pauseInfo?.reason || null,
    pauseNotes: combinedState.pauseInfo?.notes || null,
  };
}

/**
 * Hook simplificado para apenas verificar se uma matrícula está pausada
 */
export function useEnrollmentPauseStatus(enrollmentId: string, enabled = true) {
  const { isPaused, pauseInfo, isLoading, error } = useEnrollmentPause({
    enrollmentId,
    studentName: '', // Não necessário para verificação apenas
    classGroupId: '', // Não necessário para verificação apenas
    enabled
  });

  return {
    isPaused,
    pauseInfo,
    isLoading,
    error,
    pausedSince: pauseInfo?.paused_at ? new Date(pauseInfo.paused_at) : null,
    pauseReason: pauseInfo?.reason || null
  };
}

/**
 * Hook para gerenciar múltiplas pausas de matrículas
 */
export function useBulkEnrollmentPause(classGroupId: string) {
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const pauseMultiple = useCallback(async (
    enrollments: Array<{ id: string; studentName: string }>,
    reason?: string,
    notes?: string
  ) => {
    setIsLoading(true);
    setErrors({});
    
    const results = [];
    const newErrors: Record<string, string> = {};

    for (const enrollment of enrollments) {
      try {
        const result = await pauseEnrollment({
          enrollment_id: enrollment.id,
          reason,
          notes
        });

        if (result.success) {
          results.push({ enrollmentId: enrollment.id, success: true });
          toast.success(`Matrícula de ${enrollment.studentName} pausada`);
        } else {
          const errorMessage = (result.errors as any)?._form || 'Erro ao pausar matrícula';
          newErrors[enrollment.id] = errorMessage;
          results.push({ enrollmentId: enrollment.id, success: false, error: errorMessage });
        }
      } catch (error) {
        const errorMessage = 'Erro inesperado';
        newErrors[enrollment.id] = errorMessage;
        results.push({ enrollmentId: enrollment.id, success: false, error: errorMessage });
      }
    }

    setErrors(newErrors);
    setIsLoading(false);

    // Invalidar consultas relacionadas
    await queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.ENROLLMENT.CLASS_GROUP_ENROLLMENTS(classGroupId)
    });

    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    if (successCount === totalCount) {
      toast.success(`${successCount} matrículas pausadas com sucesso`);
    } else if (successCount > 0) {
      toast.warning(`${successCount}/${totalCount} matrículas pausadas. Verifique os erros.`);
    } else {
      toast.error('Erro ao pausar matrículas');
    }

    return { results, successCount, totalCount, errors: newErrors };
  }, [classGroupId, queryClient]);

  const resumeMultiple = useCallback(async (
    enrollments: Array<{ id: string; studentName: string }>,
    reason?: string
  ) => {
    setIsLoading(true);
    setErrors({});
    
    const results = [];
    const newErrors: Record<string, string> = {};

    for (const enrollment of enrollments) {
      try {
        const result = await resumeEnrollment({
          enrollment_id: enrollment.id,
          reason
        });

        if (result.success) {
          results.push({ enrollmentId: enrollment.id, success: true });
          toast.success(`Matrícula de ${enrollment.studentName} reativada`);
        } else {
          const errorMessage = (result.errors as any)?._form || 'Erro ao reativar matrícula';
          newErrors[enrollment.id] = errorMessage;
          results.push({ enrollmentId: enrollment.id, success: false, error: errorMessage });
        }
      } catch (error) {
        const errorMessage = 'Erro inesperado';
        newErrors[enrollment.id] = errorMessage;
        results.push({ enrollmentId: enrollment.id, success: false, error: errorMessage });
      }
    }

    setErrors(newErrors);
    setIsLoading(false);

    // Invalidar consultas relacionadas
    await queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.ENROLLMENT.CLASS_GROUP_ENROLLMENTS(classGroupId)
    });

    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    if (successCount === totalCount) {
      toast.success(`${successCount} matrículas reativadas com sucesso`);
    } else if (successCount > 0) {
      toast.warning(`${successCount}/${totalCount} matrículas reativadas. Verifique os erros.`);
    } else {
      toast.error('Erro ao reativar matrículas');
    }

    return { results, successCount, totalCount, errors: newErrors };
  }, [classGroupId, queryClient]);

  return {
    isLoading,
    errors,
    pauseMultiple,
    resumeMultiple,
    clearErrors: () => setErrors({})
  };
} 