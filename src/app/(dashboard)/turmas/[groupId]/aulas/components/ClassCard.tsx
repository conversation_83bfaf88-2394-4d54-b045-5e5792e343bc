'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  Eye, 
  Edit, 
  X, 
  ChevronRight 
} from 'lucide-react';
import { ClassItem } from '../types';
import { useRouter } from 'next/navigation';

interface ClassCardProps {
  classItem: ClassItem;
  groupId?: string;
  onEdit?: (classId: string) => void;
  onCancel?: (classId: string) => void;
  isLoading?: boolean;
}

export function ClassCard({ classItem, groupId, onEdit, onCancel, isLoading = false }: ClassCardProps) {
  const router = useRouter();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      timeZone: 'America/Sao_Paulo',
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('pt-BR', {
      timeZone: 'America/Sao_Paulo',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDuration = () => {
    const start = new Date(classItem.startTime);
    const end = new Date(classItem.endTime);
    const diff = end.getTime() - start.getTime();
    const minutes = Math.round(diff / (1000 * 60));
    
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`;
    }
    return `${minutes}min`;
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'scheduled': 'Agendada',
      'ongoing': 'Em Andamento',
      'completed': 'Concluída',
      'cancelled': 'Cancelada',
    };
    return labels[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'scheduled': 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
      'ongoing': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
      'completed': 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400',
      'cancelled': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    };
    return colors[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
  };

  const getInstructorInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  const handleViewAttendance = () => {
    router.push(`/presenca/${classItem.id}`);
  };

  const handleEdit = () => {
    // Se groupId estiver disponível, navegar diretamente para a página de edição
    if (groupId) {
      router.push(`/turmas/${groupId}/aulas/editar/${classItem.id}`);
    } else if (onEdit) {
      // Fallback para o comportamento anterior
      onEdit(classItem.id);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel(classItem.id);
    }
  };

  const canEdit = classItem.status !== 'completed' && classItem.status !== 'cancelled';
  const canCancel = classItem.status === 'scheduled';

  if (isLoading) {
    return (
      <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm">
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1 space-y-2">
                <div className="h-5 bg-slate-200 dark:bg-gray-700 rounded w-2/3"></div>
                <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
              <div className="h-6 bg-slate-200 dark:bg-gray-700 rounded w-20"></div>
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded w-full"></div>
              <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded w-3/4"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200 group">
      <CardContent className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
              {classItem.name}
            </h3>
            {classItem.description && (
              <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                {classItem.description}
              </p>
            )}
          </div>
          
          <Badge className={getStatusColor(classItem.status)}>
            {getStatusLabel(classItem.status)}
          </Badge>
        </div>

        {/* Informações da Aula */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>{formatDate(classItem.startTime)}</span>
            </div>
            
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>
                {formatTime(classItem.startTime)} - {formatTime(classItem.endTime)}
                <span className="text-xs ml-1">({getDuration()})</span>
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <MapPin className="h-4 w-4" />
              <span>{classItem.branch?.name || 'Local não definido'}</span>
            </div>

            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Users className="h-4 w-4" />
              <span>
                {classItem.attendanceCount} aluno{classItem.attendanceCount !== 1 ? 's' : ''}
                {classItem.maxCapacity && ` / ${classItem.maxCapacity}`}
              </span>
            </div>
          </div>
        </div>

        {/* Instrutor */}
        {classItem.instructor ? (
          <div className="flex items-center space-x-3 mb-4 p-3 bg-slate-50 dark:bg-gray-800/50 rounded-lg">
            <Avatar className="h-8 w-8">
              <AvatarImage src={classItem.instructor.avatar} alt={classItem.instructor.name} />
              <AvatarFallback className="text-xs">
                {getInstructorInitials(classItem.instructor.name)}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {classItem.instructor.name}
              </p>
              <p className="text-xs text-muted-foreground">Instrutor</p>
            </div>
          </div>
        ) : (
          <div className="flex items-center space-x-3 mb-4 p-3 bg-slate-50 dark:bg-gray-800/50 rounded-lg">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">
                ?
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                Instrutor não definido
              </p>
              <p className="text-xs text-muted-foreground">Instrutor</p>
            </div>
          </div>
        )}

        {/* Ações */}
        <div className="flex items-center justify-between pt-4 border-t border-slate-200 dark:border-gray-700">
          <Button
            variant="outline"
            size="sm"
            onClick={handleViewAttendance}
            className="flex items-center space-x-1"
          >
            <Eye className="h-4 w-4" />
            <span>Ver Presença</span>
          </Button>

          <div className="flex items-center space-x-2">
            {canEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleEdit}
                className="flex items-center space-x-1"
              >
                <Edit className="h-4 w-4" />
                <span className="hidden sm:inline">Editar</span>
              </Button>
            )}

            {canCancel && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
                className="flex items-center space-x-1 text-red-600 hover:text-red-700 hover:border-red-300"
              >
                <X className="h-4 w-4" />
                <span className="hidden sm:inline">Cancelar</span>
              </Button>
            )}

            <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 