'use client';

import { Search, RotateCcw, Plus, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useClassFilters } from '../contexts/ClassFiltersContext';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { useParams } from 'next/navigation';

interface QuickActionBarProps {
  onRefresh?: () => void;
  isLoading?: boolean;
  isClassesLoading?: boolean;
  createClassButton?: React.ReactNode;
  groupName?: string;
}

export function QuickActionBar({ 
  onRefresh, 
  isLoading = false, 
  isClassesLoading = false,
  createClassButton,
  groupName = ''
}: QuickActionBarProps) {
  const params = useParams();
  const groupId = params?.groupId as string;
  
  const { 
    filters,
    setSearchInput, 
    clearSearch,
    resetFilters,
    hasActiveFilters,
    getActiveFilters
  } = useClassFilters();

  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    }
  };

  const handleResetFilters = () => {
    resetFilters();
  };

  const handleSearchChange = (value: string) => {
    setSearchInput(value);
  };

  // Obter filtros ativos para exibição
  const activeFilters = getActiveFilters();

  return (
    <motion.div 
      className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 rounded-xl p-4 shadow-sm"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
        
        {/* Seção de Busca */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 dark:text-gray-500" />
            <Input
              placeholder={`Buscar aulas${groupName ? ` de ${groupName}` : ''}...`}
              value={filters.searchInput || ''}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10 pr-10 h-10 bg-slate-50 dark:bg-gray-800 border-slate-200 dark:border-gray-600 focus:bg-white dark:focus:bg-gray-700 transition-colors text-slate-900 dark:text-gray-100"
              disabled={isLoading}
            />
            
            {/* Área dos ícones à direita */}
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
              {/* Indicador de loading da pesquisa */}
              {isClassesLoading && (
                <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
              )}
              
              {/* Botão limpar pesquisa */}
              {filters.searchInput && !isClassesLoading && (
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  onClick={clearSearch}
                  className="flex items-center justify-center w-6 h-6 hover:bg-slate-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                  disabled={isLoading}
                  aria-label="Limpar pesquisa"
                >
                  <X className="h-3 w-3 text-slate-400 dark:text-gray-500" />
                </motion.button>
              )}
            </div>
          </div>
        </div>

        {/* Ações Rápidas */}
        <div className="flex items-center gap-2">        
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-10 text-slate-600 dark:text-gray-400 hover:text-slate-900 dark:hover:text-gray-100 hover:bg-slate-50 dark:hover:bg-gray-800 disabled:opacity-50"
            >
              <RotateCcw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline">Atualizar</span>
            </Button>
          </motion.div>
          
          {createClassButton || (
            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                asChild
                disabled={isLoading}
                className="h-10 bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Link href={`/turmas/${groupId}/aulas/nova`}>
                  <Plus className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Nova Aula</span>
                  <span className="sm:hidden">Nova</span>
                </Link>
              </Button>
            </motion.div>
          )}
        </div>
      </div>

      {/* Filtros Ativos - Apenas exibir se houver pesquisa ativa */}
      <AnimatePresence>
        {filters.searchInput && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="mt-3 pt-3 border-t border-slate-100 dark:border-gray-700"
          >
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-sm text-slate-500 dark:text-gray-400">Pesquisando por:</span>
              
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="flex items-center gap-1 px-2 py-1 rounded-md text-xs bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
              >
                <Search className="h-3 w-3" />
                <span>"{filters.searchInput}"</span>
                <button
                  onClick={clearSearch}
                  className="rounded-full p-0.5 transition-colors hover:bg-blue-100 dark:hover:bg-blue-800/50"
                  disabled={isLoading}
                >
                  <X className="h-2.5 w-2.5" />
                </button>
              </motion.div>
              
              {hasActiveFilters() && (
                <motion.button
                  onClick={handleResetFilters}
                  className="text-xs text-slate-500 dark:text-gray-400 hover:text-slate-700 dark:hover:text-gray-300 underline ml-2"
                  whileHover={{ scale: 1.05 }}
                  disabled={isLoading}
                >
                  Limpar todos os filtros
                </motion.button>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
} 