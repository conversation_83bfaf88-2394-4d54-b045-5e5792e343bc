'use client';

import { useCallback, useMemo, useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useParams } from 'next/navigation';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Clock, Info } from 'lucide-react';

// Imports dos componentes e hooks do formulário original
import { editClassSchema, EditClassFormData } from './schema';
import { EditClassFormProps } from './types';
import { updateClassInGroup } from '../../../actions/update-class';
import type { ClassGroup } from '../../../nova/components/types';

// Reutilizar componentes do formulário de criação
import { 
  useAutoSave, 
  useKeyboardShortcuts, 
  useFormValidation,
  useFormState,
  useFormHandlers
} from '../../../nova/components/hooks';

// Componentes reutilizáveis
import { FormStatusBars } from '../../../nova/components/FormStatusBars';
import { ValidationHelp } from '../../../nova/components/ValidationHelp';
import { BasicInfoSection } from '../../../nova/components/sections/BasicInfoSection';
import { ConfigSection } from '../../../nova/components/sections/ConfigSection';
import { ScheduleSection } from '../../../nova/components/sections/ScheduleSection';
import { NotesSection } from '../../../nova/components/sections/NotesSection';
import { DebugPanel } from '../../../nova/components/DebugPanel';

// Componentes específicos para edição
import { EditFormActions } from './EditFormActions';
import { EditSuccessView } from './EditSuccessView';
import { EditRestrictionsCard } from './EditRestrictionsCard';

// Helper para converter data ISO para datetime-local
function formatDateTimeLocal(isoString: string): string {
  const date = new Date(isoString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day}T${hours}:${minutes}`;
}

export function EditClassForm({ classGroup, classData, instructors, branches }: EditClassFormProps) {
  const params = useParams();
  const groupId = params.groupId as string;
  const classId = params.classId as string;

  // Verificar se a aula pode ser editada
  const canEdit = useMemo(() => {
    return classData.status !== 'completed' && !classData.attendance_recorded;
  }, [classData.status, classData.attendance_recorded]);

  // Configuração do formulário com valores iniciais da aula
  const form = useForm<EditClassFormData>({
    resolver: zodResolver(editClassSchema),
    defaultValues: {
      name: classData.name || '',
      description: classData.description || '',
      instructor_id: classData.instructor_id || '',
      branch_id: classData.branch_id || '',
      start_time: formatDateTimeLocal(classData.start_time),
      end_time: formatDateTimeLocal(classData.end_time),
      max_capacity: classData.max_capacity || undefined,
      notes: classData.notes || '',
    },
    mode: 'onChange',
  });

  const { handleSubmit, register, watch, setValue, setError, formState: { errors, isValid, isDirty } } = form;
  const formValues = watch();

  // Garantir que os campos sejam marcados como dirty quando carregados com dados iniciais
  useEffect(() => {
    // Força uma validação e marcação como "dirty" para os campos iniciais
    const timer = setTimeout(() => {
      setValue('name', classData.name || '', { shouldDirty: true, shouldTouch: true, shouldValidate: true });
      setValue('description', classData.description || '', { shouldDirty: true, shouldTouch: true, shouldValidate: true });
      setValue('instructor_id', classData.instructor_id || '', { shouldDirty: true, shouldTouch: true, shouldValidate: true });
      setValue('branch_id', classData.branch_id || '', { shouldDirty: true, shouldTouch: true, shouldValidate: true });
      setValue('start_time', formatDateTimeLocal(classData.start_time), { shouldDirty: true, shouldTouch: true, shouldValidate: true });
      setValue('end_time', formatDateTimeLocal(classData.end_time), { shouldDirty: true, shouldTouch: true, shouldValidate: true });
      setValue('max_capacity', classData.max_capacity || undefined, { shouldDirty: true, shouldTouch: true, shouldValidate: true });
      setValue('notes', classData.notes || '', { shouldDirty: true, shouldTouch: true, shouldValidate: true });
    }, 100);

    return () => clearTimeout(timer);
  }, [setValue, classData]);

  // Hooks personalizados (reutilizados do formulário de criação)
  const { isCheckingConflicts, hasTimeConflict, checkTimeConflicts, getErrorMessage } = useFormValidation();
  
  const { lastSaved, isAutoSaving, clearDraft, getDraft } = useAutoSave(formValues, isValid, false);
  
  const { 
    formProgress, 
    showValidationHelp, 
    setShowValidationHelp, 
    hasDraftLoaded, 
    handleClearDraft 
  } = useFormState({ form, getDraft, clearDraft });
  
  const { handleStartTimeChange, duration } = useFormHandlers({ form, checkTimeConflicts });

  // Hook personalizado para submissão (adaptado para edição)
  const { 
    isPending, 
    submitError, 
    showSuccess, 
    onSubmit, 
    handleCancel 
  } = useEditFormSubmission({ 
    classId, 
    groupId, 
    setError, 
    clearDraft,
    form
  });

  // Keyboard shortcuts
  const handleSaveShortcut = useCallback(() => {
    if (isValid && isDirty && !isPending && canEdit) {
      handleSubmit(onSubmit)();
    }
  }, [isValid, isDirty, isPending, canEdit, handleSubmit, onSubmit]);

  useKeyboardShortcuts(handleSaveShortcut, handleCancel, !showSuccess && canEdit);

  if (showSuccess) {
    return <EditSuccessView classData={classData} groupId={groupId} />;
  }

  if (!canEdit) {
    return (
      <EditRestrictionsCard 
        status={classData.status}
        attendanceRecorded={classData.attendance_recorded}
        className={classData.name}
      />
    );
  }

  return (
    <div className="space-y-6">
      <FormStatusBars 
        formProgress={formProgress}
        isAutoSaving={isAutoSaving}
        lastSaved={lastSaved}
      />

      {/* Card de informações sobre edição */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Você está editando a aula <strong>{classData.name}</strong>. 
          As alterações serão aplicadas imediatamente após salvar.
        </AlertDescription>
      </Alert>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {submitError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        <ValidationHelp 
          errors={errors}
          showValidationHelp={showValidationHelp}
          setShowValidationHelp={setShowValidationHelp}
          getErrorMessage={getErrorMessage}
        />

        <BasicInfoSection 
          register={register}
          errors={errors}
          formValues={formValues}
          classGroup={classGroup as ClassGroup}
          getErrorMessage={getErrorMessage}
        />

        <ConfigSection 
          watch={watch}
          setValue={setValue}
          errors={errors}
          classGroup={classGroup as ClassGroup}
          instructors={instructors}
          branches={branches}
          getErrorMessage={getErrorMessage}
        />

        <ScheduleSection 
          watch={watch}
          setValue={setValue}
          errors={errors}
          isCheckingConflicts={isCheckingConflicts}
          hasTimeConflict={hasTimeConflict}
          duration={duration}
          onStartTimeChange={handleStartTimeChange}
          getErrorMessage={getErrorMessage}
        />

        <NotesSection 
          register={register}
          errors={errors}
          formValues={formValues}
          getErrorMessage={getErrorMessage}
        />

        <EditFormActions 
          isPending={isPending}
          isValid={isValid}
          isDirty={isDirty}
          hasTimeConflict={hasTimeConflict}
          errors={errors}
          getErrorMessage={getErrorMessage}
          onCancel={handleCancel}
          classData={classData}
          groupId={groupId}
        />
      </form>

      {/* Debug Panel - só aparece em desenvolvimento */}
      {process.env.NODE_ENV === 'development' && (
        <DebugPanel
          form={form}
          errors={errors}
          isCheckingConflicts={isCheckingConflicts}
          hasTimeConflict={hasTimeConflict}
          duration={duration}
          formProgress={formProgress}
          isAutoSaving={isAutoSaving}
          lastSaved={lastSaved}
          isPending={isPending}
          hasDraftLoaded={hasDraftLoaded}
        />
      )}
    </div>
  );
}

// Hook personalizado para submissão de edição
function useEditFormSubmission({ 
  classId, 
  groupId, 
  setError, 
  clearDraft,
  form
}: {
  classId: string;
  groupId: string;
  setError: any;
  clearDraft: () => void;
  form: any;
}) {
  const [isPending, setIsPending] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const onSubmit = useCallback(async (data: EditClassFormData) => {
    setIsPending(true);
    setSubmitError(null);

    try {
      const result = await updateClassInGroup(classId, groupId, data);

      if (result.success) {
        clearDraft();
        setShowSuccess(true);
      } else {
        if (result.errors) {
          // Definir erros específicos nos campos
          Object.entries(result.errors).forEach(([key, value]) => {
            if (key !== '_form' && typeof value === 'string') {
              setError(key, { type: 'server', message: value });
            }
          });

          // Erro geral do formulário
          if ('_form' in result.errors && result.errors._form) {
            setSubmitError(result.errors._form as string);
          }
        }
      }
    } catch (error) {
      console.error('Erro ao atualizar aula:', error);
      setSubmitError('Erro inesperado ao atualizar aula');
    } finally {
      setIsPending(false);
    }
  }, [classId, groupId, setError, clearDraft]);

  const handleCancel = useCallback(() => {
    // Resetar o formulário aos valores originais
    form.reset();
    
    // Limpar rascunho se existir
    clearDraft();
    
    // Limpar erros de submissão
    setSubmitError(null);
  }, [form, clearDraft]);

  return {
    isPending,
    submitError,
    showSuccess,
    onSubmit,
    handleCancel,
  };
} 