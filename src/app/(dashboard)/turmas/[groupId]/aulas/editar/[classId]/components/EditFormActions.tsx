'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle, Save, RotateCcw, Loader2 } from 'lucide-react';
import Link from 'next/link';
import type { ClassData } from './types';

interface EditFormActionsProps {
  isPending: boolean;
  isValid: boolean;
  isDirty: boolean;
  hasTimeConflict: boolean;
  errors: any;
  getErrorMessage: (key: string) => string | undefined;
  onCancel: () => void;
  classData: ClassData;
  groupId: string;
}

export function EditFormActions({
  isPending,
  isValid,
  isDirty,
  hasTimeConflict,
  errors,
  getErrorMessage,
  onCancel,
  classData,
  groupId,
}: EditFormActionsProps) {
  const hasErrors = Object.keys(errors).length > 0;
  const canSubmit = isValid && isDirty && !hasTimeConflict && !isPending;

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
          {/* Status do formulário */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {hasErrors && (
              <>
                <AlertCircle className="h-4 w-4 text-destructive" />
                <span className="text-destructive">
                  {Object.keys(errors).length} erro(s) encontrado(s)
                </span>
              </>
            )}
            
            {!hasErrors && isDirty && (
              <span className="text-amber-600 dark:text-amber-400">
                Alterações não salvas
              </span>
            )}
            
            {!hasErrors && !isDirty && (
              <span className="text-green-600 dark:text-green-400">
                Nenhuma alteração detectada
              </span>
            )}
          </div>

          {/* Ações */}
          <div className="flex gap-3 w-full sm:w-auto">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isPending || !isDirty}
              className="flex-1 sm:flex-none"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Desfazer Alterações
            </Button>

            <Link
              href={`/turmas/${groupId}/aulas`}
              className="flex-1 sm:flex-none"
            >
              <Button 
                variant="ghost"
                disabled={isPending}
                className="w-full"
              >
                Voltar para Aulas
              </Button>
            </Link>

            <Button
              type="submit"
              disabled={!canSubmit}
              className="flex-1 sm:flex-none"
            >
              {isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Salvando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar Alterações
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Informações adicionais sobre a edição */}
        {isDirty && (
          <div className="mt-4 p-3 bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800 rounded-md">
            <p className="text-sm text-amber-800 dark:text-amber-200">
              <strong>Atenção:</strong> As alterações serão aplicadas imediatamente após salvar.
              {classData.status === 'scheduled' && (
                <span className="block mt-1">
                  Esta aula está agendada e pode ter alunos inscritos.
                </span>
              )}
            </p>
          </div>
        )}

        {/* Informações sobre conflitos de horário */}
        {hasTimeConflict && (
          <div className="mt-4 p-3 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-md">
            <p className="text-sm text-red-800 dark:text-red-200">
              <strong>Conflito de horário detectado!</strong> 
              O instrutor já possui uma aula agendada neste horário.
            </p>
          </div>
        )}

        {/* Dicas de teclado */}
        <div className="mt-4 text-xs text-muted-foreground">
          <div className="flex flex-wrap gap-4">
            <span>
              <kbd className="px-1.5 py-0.5 text-xs font-mono bg-muted rounded">Ctrl+S</kbd> 
              {' '}para salvar
            </span>
            <span>
              <kbd className="px-1.5 py-0.5 text-xs font-mono bg-muted rounded">Esc</kbd> 
              {' '}para desfazer alterações
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 