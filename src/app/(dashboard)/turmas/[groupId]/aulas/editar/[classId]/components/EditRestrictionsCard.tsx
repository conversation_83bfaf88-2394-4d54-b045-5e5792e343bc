'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Lock, AlertTriangle, ArrowLeft, Eye } from 'lucide-react';
import Link from 'next/link';

interface EditRestrictionsCardProps {
  status: string;
  attendanceRecorded: boolean;
  className: string;
}

export function EditRestrictionsCard({ 
  status, 
  attendanceRecorded, 
  className 
}: EditRestrictionsCardProps) {
  const getRestrictionInfo = () => {
    if (status === 'completed') {
      return {
        title: 'Aula Concluída',
        description: 'Esta aula já foi concluída e não pode mais ser editada.',
        icon: <Lock className="h-5 w-5" />,
        variant: 'default' as const,
        reasons: [
          'A aula já aconteceu e foi marcada como concluída',
          'Dados históricos devem ser preservados para auditoria',
          'Alterações podem afetar relatórios já gerados'
        ]
      };
    }
    
    if (attendanceRecorded) {
      return {
        title: 'Presença Registrada',
        description: 'Esta aula possui presenças registradas e não pode ser editada.',
        icon: <AlertTriangle className="h-5 w-5" />,
        variant: 'destructive' as const,
        reasons: [
          'Já existem presenças registradas para esta aula',
          'Editar poderia causar inconsistências nos dados',
          'Para alterações, contate um administrador'
        ]
      };
    }

    return {
      title: 'Edição Restrita',
      description: 'Esta aula não pode ser editada no momento.',
      icon: <Lock className="h-5 w-5" />,
      variant: 'default' as const,
      reasons: [
        'Status atual da aula não permite edição',
        'Verifique as regras de negócio aplicáveis'
      ]
    };
  };

  const restrictionInfo = getRestrictionInfo();

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-muted rounded-lg">
              {restrictionInfo.icon}
            </div>
            <div>
              <CardTitle className="text-lg">
                {restrictionInfo.title}
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Aula: <strong>{className}</strong>
              </p>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          <Alert variant={restrictionInfo.variant}>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {restrictionInfo.description}
            </AlertDescription>
          </Alert>

          {/* Motivos da restrição */}
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-3">Por que não posso editar?</h4>
            <ul className="space-y-2">
              {restrictionInfo.reasons.map((reason, index) => (
                <li key={index} className="flex items-start gap-2 text-sm">
                  <span className="w-1.5 h-1.5 bg-current rounded-full mt-2 flex-shrink-0" />
                  {reason}
                </li>
              ))}
            </ul>
          </div>

          {/* Alternativas */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-900 mb-2">
              O que você pode fazer:
            </h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Visualizar os detalhes da aula</li>
              <li>• Criar uma nova aula baseada nesta</li>
              {status !== 'completed' && (
                <li>• Contatar um administrador para alterações especiais</li>
              )}
              <li>• Verificar outras aulas da turma</li>
            </ul>
          </div>

          {/* Informações do status atual */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Status atual:</span>
              <p className="font-medium capitalize">
                {status === 'completed' ? 'Concluída' : 
                 status === 'cancelled' ? 'Cancelada' :
                 status === 'ongoing' ? 'Em andamento' : 
                 status}
              </p>
            </div>
            <div>
              <span className="text-muted-foreground">Presença registrada:</span>
              <p className="font-medium">
                {attendanceRecorded ? 'Sim' : 'Não'}
              </p>
            </div>
          </div>

          {/* Ações disponíveis */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button asChild className="flex-1">
              <Link href="../">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar para Aulas
              </Link>
            </Button>
            
            <Button variant="outline" asChild className="flex-1">
              <Link href={`../../nova`}>
                Criar Nova Aula
              </Link>
            </Button>
            
            <Button variant="ghost" asChild className="flex-1">
              <Link href="../">
                <Eye className="h-4 w-4 mr-2" />
                Ver Detalhes
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 