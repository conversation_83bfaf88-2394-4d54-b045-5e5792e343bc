import type { EditClassFormData } from './schema';

// Tipo para os dados da aula existente
export interface ClassData {
  id: string;
  name: string;
  description?: string | null;
  instructor_id: string;
  branch_id: string;
  start_time: string;
  end_time: string;
  max_capacity?: number | null;
  notes?: string | null;
  status: string;
  attendance_recorded: boolean;
  class_group_id: string;
  instructor?: {
    id: string;
    first_name: string;
    last_name: string;
    full_name?: string;
  };
  branch?: {
    id: string;
    name: string;
  };
  class_group?: {
    id: string;
    name: string;
    category: string;
  };
}

// Tipo para informações da turma
export interface ClassGroup {
  id: string;
  name: string;
  description?: string | null;
  category: string | null;
  is_active: boolean;
  max_capacity?: number | null;
  instructor_id?: string | null;
  branch_id?: string | null;
  instructor?: {
    id: string;
    first_name: string;
    last_name: string;
    full_name?: string;
  };
  branch?: {
    id: string;
    name: string;
  };
  recurrence_pattern?: string | null;
  _count?: {
    enrollments?: number;
    waitlist?: number;
    classes?: number;
  };
}

// Tipo para instrutores disponíveis
export interface Instructor {
  id: string;
  name: string;
}

// Tipo para filiais disponíveis
export interface Branch {
  id: string;
  name: string;
}

// Props do componente EditClassForm
export interface EditClassFormProps {
  classGroup: ClassGroup;
  classData: ClassData;
  instructors: Instructor[];
  branches: Branch[];
}

// Tipo para resultado da submissão
export interface SubmissionResult {
  success: boolean;
  data?: any;
  message?: string;
  errors?: Record<string, string | string[]>;
}

export type { EditClassFormData }; 