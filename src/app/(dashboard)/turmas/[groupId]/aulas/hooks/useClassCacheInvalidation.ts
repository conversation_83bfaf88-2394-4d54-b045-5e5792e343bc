'use client';

import { useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { CACHE_KEYS } from '@/constants/cache-keys';

export function useClassCacheInvalidation(groupId: string) {
  const queryClient = useQueryClient();

  const invalidateClassData = useCallback(() => {
    // Invalidar todas as queries relacionadas às aulas da turma
    queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.CLASS_GROUPS.CLASSES(groupId, {}),
    });
    queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.CLASS_GROUPS.CLASS_STATS(groupId),
    });
  }, [queryClient, groupId]);

  const invalidateGroupInfo = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.CLASS_GROUPS.INFO(groupId),
    });
  }, [queryClient, groupId]);

  const invalidateInstructors = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.CLASS_GROUPS.INSTRUCTORS(groupId),
    });
  }, [queryClient, groupId]);

  const invalidateAll = useCallback(() => {
    invalidateClassData();
    invalidateGroupInfo();
    invalidateInstructors();
  }, [invalidateClassData, invalidateGroupInfo, invalidateInstructors]);

  const removeFromCache = useCallback((queryKeyPattern: any[]) => {
    queryClient.removeQueries({
      queryKey: queryKeyPattern,
    });
  }, [queryClient]);

  const prefetchClasses = useCallback((filters: Record<string, any>) => {
    queryClient.prefetchQuery({
      queryKey: CACHE_KEYS.CLASS_GROUPS.CLASSES(groupId, filters),
      queryFn: () => {
        // Aqui você pode importar a action se necessário
        // Para manter simples, vamos usar apenas invalidateQueries
        return Promise.resolve(null);
      },
      staleTime: 30 * 1000, // 30 segundos
    });
  }, [queryClient, groupId]);

  return {
    invalidateClassData,
    invalidateGroupInfo,
    invalidateInstructors,
    invalidateAll,
    removeFromCache,
    prefetchClasses,
  };
} 