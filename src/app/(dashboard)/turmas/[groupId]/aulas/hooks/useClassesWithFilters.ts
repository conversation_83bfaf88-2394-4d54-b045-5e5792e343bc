'use client';

import { useClassFilters } from '../contexts/ClassFiltersContext';

/**
 * Hook que integra automaticamente os filtros do Context com a busca de dados
 * Facilita o uso ao não precisar passar manualmente os filtros
 */
export function useClassesWithFilters() {
  const { filters, updateFilters, resetFilters } = useClassFilters();

  return {
    filters,
    updateFilters,
    resetFilters,
  };
} 