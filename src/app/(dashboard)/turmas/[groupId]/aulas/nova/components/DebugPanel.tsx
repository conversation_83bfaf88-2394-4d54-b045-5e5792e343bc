'use client';

import { useState } from 'react';
import { FieldErrors, UseFormReturn } from 'react-hook-form';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Code2, 
  ChevronDown, 
  ChevronUp, 
  Bug, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Clock,
  Database,
  Eye,
  EyeOff,
  Globe
} from 'lucide-react';
import { CreateClassFormData, createClassSchema } from './schema';
import { datetimeLocalToBrasiliaISO } from '@/utils/timezone-utils';

interface DebugPanelProps {
  form: UseFormReturn<CreateClassFormData>;
  errors: FieldErrors<CreateClassFormData>;
  isCheckingConflicts?: boolean;
  hasTimeConflict?: boolean;
  duration?: { text: string; minutes: number; } | null;
  formProgress?: number;
  isAutoSaving?: boolean;
  lastSaved?: Date | null;
  isPending?: boolean;
  hasDraftLoaded?: boolean;
}

export function DebugPanel({
  form,
  errors,
  isCheckingConflicts = false,
  hasTimeConflict = false,
  duration,
  formProgress = 0,
  isAutoSaving = false,
  lastSaved,
  isPending = false,
  hasDraftLoaded = false
}: DebugPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showRawData, setShowRawData] = useState(false);
  
  const { formState, watch } = form;
  const formValues = watch();
  
  // Verificar se formValues é um objeto válido e tipá-lo adequadamente
  const safeFormValues: Partial<CreateClassFormData> = formValues && typeof formValues === 'object' ? formValues : {};
  
  const {
    isDirty,
    isValid,
    isSubmitting,
    isValidating,
    touchedFields,
    dirtyFields,
    errors: formErrors
  } = formState;

  const getValidationStatus = () => {
    if (Object.keys(errors).length > 0) return 'error';
    if (isValidating) return 'validating';
    if (isValid) return 'success';
    return 'pending';
  };

  const validationStatus = getValidationStatus();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'validating':
        return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'validating':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatFieldName = (fieldName: string) => {
    const fieldMap: Record<string, string> = {
      name: 'Nome',
      description: 'Descrição',
      instructor_id: 'Instrutor',
      branch_id: 'Filial',
      start_time: 'Horário de Início',
      end_time: 'Horário de Fim',
      max_capacity: 'Capacidade Máxima',
      notes: 'Observações'
    };
    return fieldMap[fieldName] || fieldName;
  };

  // Debug da conversão de timezone
  const getTimezoneDebugInfo = () => {
    const startTime = safeFormValues.start_time || '';
    const endTime = safeFormValues.end_time || '';
    
    if (!startTime && !endTime) {
      return null;
    }
    
    return {
      startTime: {
        original: startTime,
        converted: startTime ? datetimeLocalToBrasiliaISO(startTime) : '',
        isISOFormat: startTime.includes('Z') || startTime.includes('+') || startTime.includes('-', 19)
      },
      endTime: {
        original: endTime,
        converted: endTime ? datetimeLocalToBrasiliaISO(endTime) : '',
        isISOFormat: endTime.includes('Z') || endTime.includes('+') || endTime.includes('-', 19)
      },
      currentBrasiliaTime: new Date().toLocaleString('pt-BR', {
        timeZone: 'America/Sao_Paulo',
        dateStyle: 'short',
        timeStyle: 'medium'
      }),
      currentUTCTime: new Date().toISOString()
    };
  };

  const timezoneInfo = getTimezoneDebugInfo();

  if (!isExpanded) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsExpanded(true)}
          size="sm"
          variant="outline"
          className="shadow-lg bg-background/95 backdrop-blur border-border/50 hover:bg-accent/50"
        >
          <Bug className="h-4 w-4 mr-2" />
          Debug
          {Object.keys(errors).length > 0 && (
            <Badge 
              variant="destructive" 
              className="ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {Object.keys(errors).length}
            </Badge>
          )}
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-w-[calc(100vw-2rem)]">
      <Card className="shadow-2xl bg-background/95 backdrop-blur border-border/50">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Code2 className="h-5 w-5 text-muted-foreground" />
              <CardTitle className="text-base font-semibold">
                Debug Panel
              </CardTitle>
              {getStatusIcon(validationStatus)}
            </div>
            <div className="flex items-center gap-2">
              <Button
                onClick={() => setShowRawData(!showRawData)}
                size="sm"
                variant="ghost"
                className="h-7 w-7 p-0"
              >
                {showRawData ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
              </Button>
              <Button
                onClick={() => setIsExpanded(false)}
                size="sm"
                variant="ghost"
                className="h-7 w-7 p-0"
              >
                <ChevronDown className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4 max-h-96 overflow-y-auto">
          {/* Debug Timezone */}
          {timezoneInfo && (
            <>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <Globe className="h-4 w-4" />
                  Debug Timezone
                </div>
                <div className="p-2 bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800 rounded text-xs">
                  <div className="space-y-2">
                    <div>
                      <div className="font-medium text-blue-800 dark:text-blue-300">Horário de Início:</div>
                      <div className="text-blue-700 dark:text-blue-400 pl-2">
                        <div>• Original: <code className="bg-blue-100 dark:bg-blue-900/20 px-1 rounded">{timezoneInfo.startTime.original || 'vazio'}</code></div>
                        <div>• Convertido: <code className="bg-blue-100 dark:bg-blue-900/20 px-1 rounded">{timezoneInfo.startTime.converted || 'vazio'}</code></div>
                        <div>• É ISO: {timezoneInfo.startTime.isISOFormat ? 'Sim' : 'Não'}</div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="font-medium text-blue-800 dark:text-blue-300">Horário de Fim:</div>
                      <div className="text-blue-700 dark:text-blue-400 pl-2">
                        <div>• Original: <code className="bg-blue-100 dark:bg-blue-900/20 px-1 rounded">{timezoneInfo.endTime.original || 'vazio'}</code></div>
                        <div>• Convertido: <code className="bg-blue-100 dark:bg-blue-900/20 px-1 rounded">{timezoneInfo.endTime.converted || 'vazio'}</code></div>
                        <div>• É ISO: {timezoneInfo.endTime.isISOFormat ? 'Sim' : 'Não'}</div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="font-medium text-blue-800 dark:text-blue-300">Referência de Tempo:</div>
                      <div className="text-blue-700 dark:text-blue-400 pl-2">
                        <div>• Brasília agora: <code className="bg-blue-100 dark:bg-blue-900/20 px-1 rounded">{timezoneInfo.currentBrasiliaTime}</code></div>
                        <div>• UTC agora: <code className="bg-blue-100 dark:bg-blue-900/20 px-1 rounded">{timezoneInfo.currentUTCTime}</code></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <Separator />
            </>
          )}

          {/* Status Geral */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <Database className="h-4 w-4" />
              Status do Formulário
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <Badge className={getStatusColor('success')} variant="outline">
                Válido: {isValid ? 'Sim' : 'Não'}
              </Badge>
              <Badge className={getStatusColor(isDirty ? 'success' : 'pending')} variant="outline">
                Modificado: {isDirty ? 'Sim' : 'Não'}
              </Badge>
              <Badge className={getStatusColor(isSubmitting ? 'validating' : 'pending')} variant="outline">
                Enviando: {isSubmitting ? 'Sim' : 'Não'}
              </Badge>
              <Badge className={getStatusColor(isValidating ? 'validating' : 'pending')} variant="outline">
                Validando: {isValidating ? 'Sim' : 'Não'}
              </Badge>
            </div>
            
            {/* Debug adicional da validação Zod */}
            <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800 rounded text-xs">
              <div className="font-medium text-blue-800 dark:text-blue-300 mb-1">
                Debug Zod Validation:
              </div>
              <div className="space-y-0.5 text-blue-700 dark:text-blue-400">
                <div>• Total errors: {Object.keys(errors).length}</div>
                <div>• Error details: {(() => {
                  try {
                    // Criar uma versão serializable dos erros
                    const serializableErrors: Record<string, any> = {};
                    Object.entries(errors).forEach(([key, error]) => {
                      if (error) {
                        serializableErrors[key] = {
                          message: error.message || 'Invalid',
                          type: error.type || 'validation'
                        };
                      }
                    });
                    return JSON.stringify(serializableErrors, null, 2);
                  } catch (e) {
                    return 'Error serializing errors';
                  }
                })()}</div>
                <div>• Form valid: {isValid ? 'true' : 'false'}</div>
                <div>• Form dirty: {isDirty ? 'true' : 'false'}</div>
                <div>• Mode: onChange</div>
                <div>• Resolver: zodResolver</div>
              </div>
            </div>
            
            {/* Teste manual do Schema Zod */}
            <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/10 border border-green-200 dark:border-green-800 rounded text-xs">
              <div className="font-medium text-green-800 dark:text-green-300 mb-1">
                Zod Manual Test:
              </div>
              <div className="space-y-0.5 text-green-700 dark:text-green-400">
                {(() => {
                  try {
                    // Criar uma versão limpa dos formValues sem referências circulares
                    const cleanFormValues = {
                      name: safeFormValues.name || '',
                      description: safeFormValues.description || '',
                      instructor_id: safeFormValues.instructor_id || '',
                      branch_id: safeFormValues.branch_id || '',
                      start_time: safeFormValues.start_time || '',
                      end_time: safeFormValues.end_time || '',
                      max_capacity: safeFormValues.max_capacity || undefined,
                      notes: safeFormValues.notes || ''
                    };
                    
                    const result = createClassSchema.safeParse(cleanFormValues);
                    return (
                      <>
                        <div>• Manual validation: {result.success ? 'SUCCESS' : 'FAILED'}</div>
                        {!result.success && (
                          <div>• Manual errors: {JSON.stringify(result.error.format(), null, 2)}</div>
                        )}
                        <div>• Input data: {JSON.stringify(cleanFormValues, null, 2)}</div>
                      </>
                    );
                  } catch (error) {
                    return <div>• Manual test error: {String(error)}</div>;
                  }
                })()}
              </div>
            </div>
          </div>

          <Separator />

          {/* Progresso e AutoSave */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <Clock className="h-4 w-4" />
              Progresso e Estado
            </div>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Progresso:</span>
                <span className="font-mono">{formProgress.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Auto-salvando:</span>
                <Badge className={getStatusColor(isAutoSaving ? 'validating' : 'pending')} variant="outline">
                  {isAutoSaving ? 'Sim' : 'Não'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Último save:</span>
                <span className="font-mono text-xs">
                  {lastSaved ? lastSaved.toLocaleTimeString() : 'Nunca'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Draft carregado:</span>
                <Badge className={getStatusColor(hasDraftLoaded ? 'success' : 'pending')} variant="outline">
                  {hasDraftLoaded ? 'Sim' : 'Não'}
                </Badge>
              </div>
            </div>
          </div>

          <Separator />

          {/* Validações e Conflitos */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <AlertTriangle className="h-4 w-4" />
              Validações
            </div>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Erros:</span>
                <Badge className={getStatusColor(Object.keys(errors).length > 0 ? 'error' : 'success')} variant="outline">
                  {Object.keys(errors).length}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Verificando conflitos:</span>
                <Badge className={getStatusColor(isCheckingConflicts ? 'validating' : 'pending')} variant="outline">
                  {isCheckingConflicts ? 'Sim' : 'Não'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Conflito de horário:</span>
                <Badge className={getStatusColor(hasTimeConflict ? 'error' : 'success')} variant="outline">
                  {hasTimeConflict ? 'Sim' : 'Não'}
                </Badge>
              </div>
              {duration && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Duração:</span>
                  <span className="font-mono">{duration.text}</span>
                </div>
              )}
            </div>
          </div>

          {/* Lista de Erros */}
          {Object.keys(errors).length > 0 && (
            <>
              <Separator />
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-red-600 dark:text-red-400">
                  <XCircle className="h-4 w-4" />
                  Erros de Validação
                </div>
                <div className="space-y-1">
                  {Object.entries(errors).map(([field, error]) => (
                    <div key={field} className="p-2 rounded bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800">
                      <div className="flex justify-between items-start gap-2">
                        <span className="text-xs font-medium text-red-700 dark:text-red-300">
                          {formatFieldName(field)}
                        </span>
                      </div>
                      <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                        {error?.message || 'Erro desconhecido'}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Campos Modificados */}
          {Object.keys(dirtyFields).length > 0 && (
            <>
              <Separator />
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <CheckCircle className="h-4 w-4 text-blue-500" />
                  Campos Modificados
                </div>
                <div className="flex flex-wrap gap-1">
                  {Object.keys(dirtyFields).map((field) => (
                    <Badge key={field} variant="outline" className="text-xs">
                      {formatFieldName(field)}
                    </Badge>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Dados Brutos (quando ativado) */}
          {showRawData && (
            <>
              <Separator />
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <Code2 className="h-4 w-4" />
                  Dados do Formulário
                </div>
                <div className="bg-muted/50 p-2 rounded text-xs font-mono overflow-auto max-h-32">
                  <pre className="whitespace-pre-wrap break-words">
                    {(() => {
                      try {
                        return JSON.stringify(safeFormValues, null, 2);
                      } catch (e) {
                        return 'Error serializing form values';
                      }
                    })()}
                  </pre>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <Database className="h-4 w-4" />
                  Estado do React Hook Form
                </div>
                <div className="space-y-1 text-xs">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Submit Count:</span>
                    <span className="font-mono">{formState.submitCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Campos tocados:</span>
                    <span className="font-mono">{Object.keys(touchedFields).length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Default Values:</span>
                    <span className="font-mono">
                      {Object.keys(formState.defaultValues || {}).length} campos
                    </span>
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 