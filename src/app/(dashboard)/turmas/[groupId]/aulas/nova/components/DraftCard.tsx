'use client';

import { CheckCircle2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface DraftCardProps {
  hasDraftLoaded: boolean;
  onClearDraft: () => void;
}

export function DraftCard({ hasDraftLoaded, onClearDraft }: DraftCardProps) {
  if (!hasDraftLoaded) return null;

  return (
    <Card className="border-green-200 bg-green-50 dark:bg-green-950/20 dark:border-green-800">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <CheckCircle2 className="h-5 w-5 text-green-600 dark:text-green-400 flex-shrink-0 mt-0.5" />
          <div className="space-y-1">
            <h4 className="text-sm font-medium text-green-900 dark:text-green-100">
              Rascunho Recuperado
            </h4>
            <p className="text-sm text-green-700 dark:text-green-300">
              Seus dados salvos foram carregados automaticamente. Continue preenchendo o formulário.
            </p>
          </div>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onClearDraft}
            className="text-green-700 dark:text-green-300 hover:text-green-900 dark:hover:text-green-100"
          >
            Limpar
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 