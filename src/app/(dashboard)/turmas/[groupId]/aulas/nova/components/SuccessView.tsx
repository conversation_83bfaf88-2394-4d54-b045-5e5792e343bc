'use client';

import { CheckCircle2, Loader2 } from 'lucide-react';

export function SuccessView() {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20 mb-4">
        <CheckCircle2 className="h-8 w-8 text-green-600 dark:text-green-400" />
      </div>
      <h3 className="text-lg font-semibold text-foreground mb-2">
        Aula criada com sucesso!
      </h3>
      <p className="text-muted-foreground mb-4">
        Redirecionando para a lista de aulas...
      </p>
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>Aguarde...</span>
      </div>
    </div>
  );
} 