'use client';

import { Info } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { CreateClassFormData } from './schema';
import { ClassGroup, Instructor } from './types';

interface SuggestionsCardProps {
  formValues: CreateClassFormData;
  classGroup: ClassGroup;
  instructors: Instructor[];
}

export function SuggestionsCard({ formValues, classGroup, instructors }: SuggestionsCardProps) {
  const getSuggestions = () => {
    const suggestions = [];
    
    if (!formValues.name && formValues.instructor_id) {
      const instructor = instructors.find(i => i.id === formValues.instructor_id);
      if (instructor) {
        suggestions.push(`Sugestão: "${instructor.name.split(' ')[0]} - ${classGroup.name}"`);
      }
    }
    
    if (formValues.start_time) {
      const start = new Date(formValues.start_time);
      const dayOfWeek = start.toLocaleDateString('pt-BR', { weekday: 'long' });
      suggestions.push(`Aula agendada para ${dayOfWeek}`);
    }

    return suggestions;
  };

  const suggestions = getSuggestions();

  if (suggestions.length === 0) return null;

  return (
    <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950/20 dark:border-blue-800">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
          <div className="space-y-1">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
              Sugestões
            </h4>
            {suggestions.map((suggestion, index) => (
              <p key={index} className="text-sm text-blue-700 dark:text-blue-300">
                {suggestion}
              </p>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 