'use client';

import { AlertCircle, Info } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { FieldErrors } from 'react-hook-form';
import { CreateClassFormData } from './schema';

interface ValidationHelpProps {
  errors: FieldErrors<CreateClassFormData>;
  showValidationHelp: boolean;
  setShowValidationHelp: (show: boolean) => void;
  getErrorMessage: (error: any) => string;
}

export function ValidationHelp({ 
  errors, 
  showValidationHelp, 
  setShowValidationHelp, 
  getErrorMessage 
}: ValidationHelpProps) {
  const errorCount = Object.keys(errors).length;

  if (errorCount === 0) return null;

  return (
    <>
      <div className="flex items-center justify-between p-3 bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg">
        <div className="flex items-center gap-2">
          <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
          <span className="text-sm text-amber-800 dark:text-amber-200">
            {errorCount} erro(s) de validação encontrado(s)
          </span>
        </div>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => setShowValidationHelp(!showValidationHelp)}
        >
          {showValidationHelp ? 'Ocultar' : 'Ver detalhes'}
        </Button>
      </div>

      {showValidationHelp && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <strong>Erros encontrados:</strong>
              <ul className="list-disc pl-4 space-y-1">
                {Object.entries(errors).map(([field, error]) => (
                  <li key={field} className="text-sm">
                    <strong>{field}:</strong> {getErrorMessage(error)}
                  </li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </>
  );
} 