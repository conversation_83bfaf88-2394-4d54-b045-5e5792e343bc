import { useEffect, useMemo, useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { CreateClassFormData } from '../schema';
import { calculateDuration } from '../utils/formUtils';

interface UseFormHandlersProps {
  form: UseFormReturn<CreateClassFormData>;
  checkTimeConflicts: (startTime: string, endTime: string, instructorId: string) => void;
}

export function useFormHandlers({ form, checkTimeConflicts }: UseFormHandlersProps) {
  const { watch, setValue } = form;

  // Atualizar start_time quando mudar (sem definir end_time automaticamente)
  const handleStartTimeChange = useCallback((newStartTime: string) => {
    setValue('start_time', newStartTime);
  }, [setValue]);

  // Monitorar mudanças de horário e instrutor para verificar conflitos
  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name === 'start_time' || name === 'end_time' || name === 'instructor_id') {
        const { start_time, end_time, instructor_id } = value;
        if (start_time && end_time && instructor_id) {
          checkTimeConflicts(start_time, end_time, instructor_id);
        }
      }
    });
    
    return () => subscription.unsubscribe();
  }, [watch, checkTimeConflicts]);

  // Calcular duração da aula em tempo real
  const duration = useMemo(() => {
    return calculateDuration(watch('start_time'), watch('end_time'));
  }, [watch('start_time'), watch('end_time')]);

  return {
    handleStartTimeChange,
    duration,
  };
} 