import { useState, useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { toast } from 'sonner';
import { CreateClassFormData } from '../schema';
import { calculateFormProgress } from '../utils/formUtils';

interface UseFormStateProps {
  form: UseFormReturn<CreateClassFormData>;
  getDraft: () => any;
  clearDraft: () => void;
}

export function useFormState({ form, getDraft, clearDraft }: UseFormStateProps) {
  const [formProgress, setFormProgress] = useState(0);
  const [showValidationHelp, setShowValidationHelp] = useState(false);
  const [hasDraftLoaded, setHasDraftLoaded] = useState(false);

  const { reset, watch } = form;
  const formValues = watch();

  // Carregar draft salvo na inicialização
  useEffect(() => {
    const draft = getDraft();
    if (draft && typeof draft === 'object') {
      try {
        const validDraftData: Partial<CreateClassFormData> = {};
        
        Object.entries(draft).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            (validDraftData as any)[key] = value;
          }
        });
        
        if (Object.keys(validDraftData).length > 0) {
          reset({
            ...form.getValues(),
            ...validDraftData
          });
          
          setHasDraftLoaded(true);
          
          toast.info('Rascunho recuperado', {
            description: 'Seus dados salvos foram carregados automaticamente.',
            action: {
              label: 'Limpar',
              onClick: () => {
                clearDraft();
                setHasDraftLoaded(false);
                reset();
                toast.success('Rascunho removido');
              }
            }
          });
        }
      } catch (error) {
        console.error('Erro ao carregar draft:', error);
        clearDraft();
      }
    }
  }, [getDraft, clearDraft, reset, form]);

  // Calcular progresso do formulário
  useEffect(() => {
    const progress = calculateFormProgress(formValues);
    setFormProgress(progress);
  }, [formValues]);

  const handleClearDraft = () => {
    clearDraft();
    setHasDraftLoaded(false);
    reset();
    toast.success('Rascunho removido');
  };

  return {
    formProgress,
    showValidationHelp,
    setShowValidationHelp,
    hasDraftLoaded,
    handleClearDraft,
  };
} 