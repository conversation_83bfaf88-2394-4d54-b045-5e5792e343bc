import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { UseFormSetError } from 'react-hook-form';
import { toast } from 'sonner';
import { createClass } from '../../../../../../aulas/actions/create-class-actions';
import { CreateClassFormData } from '../schema';
import { ClassGroup } from '../types';
import { handleNetworkError } from '../utils/formUtils';

interface UseFormSubmissionProps {
  classGroup: ClassGroup;
  setError: UseFormSetError<CreateClassFormData>;
  clearDraft: () => void;
}

export function useFormSubmission({ classGroup, setError, clearDraft }: UseFormSubmissionProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const onSubmit = async (data: CreateClassFormData) => {
    setSubmitError(null);

    startTransition(async () => {
      try {
        // Pequeno delay para dispositivos móveis
        if (typeof window !== 'undefined' && window.innerWidth < 768) {
          await new Promise(resolve => setTimeout(resolve, 300));
        }

        const result = await createClass({
          ...data,
          class_group_id: classGroup.id,
          max_capacity: data.max_capacity ?? undefined,
          description: data.description || undefined,
          notes: data.notes || undefined,
        });

        if (!result.success) {
          if (result.errors) {
            Object.entries(result.errors).forEach(([field, error]) => {
              if (field === '_form') {
                setSubmitError(error as string);
              } else {
                // Extrair mensagem de erro do formato Zod
                let errorMessage: string;
                
                if (typeof error === 'string') {
                  errorMessage = error;
                } else if (Array.isArray(error)) {
                  errorMessage = error[0];
                } else if (error && typeof error === 'object' && '_errors' in error) {
                  errorMessage = (error._errors as string[])[0] || 'Erro de validação';
                } else {
                  errorMessage = 'Erro de validação';
                }
                
                setError(field as any, { 
                  type: 'server', 
                  message: errorMessage 
                });
              }
            });
          } else {
            setSubmitError('Erro desconhecido ao criar a aula.');
          }
          return;
        }

        clearDraft();
        setShowSuccess(true);
        
        toast.success('Aula criada com sucesso!', {
          description: 'Redirecionando para a lista de aulas...'
        });

        setTimeout(() => {
          router.push(`/turmas/${classGroup.id}/aulas`);
        }, 2000);

      } catch (error) {
        handleNetworkError(error, setSubmitError);
      }
    });
  };

  const handleCancel = () => {
    router.back();
  };

  return {
    isPending,
    submitError,
    showSuccess,
    onSubmit,
    handleCancel,
  };
} 