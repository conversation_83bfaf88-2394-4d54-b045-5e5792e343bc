'use client';

import { AlertCircle, Users } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { FieldErrors, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { CreateClassFormData } from '../schema';
import { ClassGroup, Instructor, Branch } from '../types';

interface ConfigSectionProps {
  watch: UseFormWatch<CreateClassFormData>;
  setValue: UseFormSetValue<CreateClassFormData>;
  errors: FieldErrors<CreateClassFormData>;
  classGroup: ClassGroup;
  instructors: Instructor[];
  branches: Branch[];
  getErrorMessage: (error: any) => string;
}

export function ConfigSection({ 
  watch, 
  setValue, 
  errors, 
  classGroup, 
  instructors, 
  branches, 
  getErrorMessage 
}: ConfigSectionProps) {
  return (
    <Card className={`transition-all duration-200 ${(errors.instructor_id || errors.branch_id) ? 'ring-2 ring-destructive/20' : ''}`}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Users className="h-5 w-5" />
          Configurações
          {(errors.instructor_id || errors.branch_id) && <AlertCircle className="h-4 w-4 text-destructive" />}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="instructor_id">Instrutor *</Label>
            <Select 
              value={watch('instructor_id')} 
              onValueChange={(value) => setValue('instructor_id', value, { shouldDirty: true, shouldTouch: true, shouldValidate: true })}
            >
              <SelectTrigger className={errors.instructor_id ? 'border-destructive' : ''}>
                <SelectValue placeholder="Selecione um instrutor" />
              </SelectTrigger>
              <SelectContent>
                {instructors.map((instructor) => (
                  <SelectItem key={instructor.id} value={instructor.id}>
                    {instructor.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.instructor_id && (
              <p className="text-sm text-destructive">{getErrorMessage(errors.instructor_id)}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="branch_id">Filial *</Label>
            <Select 
              value={watch('branch_id')} 
              onValueChange={(value) => setValue('branch_id', value, { shouldDirty: true, shouldTouch: true, shouldValidate: true })}
            >
              <SelectTrigger className={errors.branch_id ? 'border-destructive' : ''}>
                <SelectValue placeholder="Selecione uma filial" />
              </SelectTrigger>
              <SelectContent>
                {branches.map((branch) => (
                  <SelectItem key={branch.id} value={branch.id}>
                    <div className="flex flex-col">
                      <span>{branch.name}</span>
                      {branch.address && (
                        <span className="text-xs text-muted-foreground">{branch.address}</span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.branch_id && (
              <p className="text-sm text-destructive">{getErrorMessage(errors.branch_id)}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 