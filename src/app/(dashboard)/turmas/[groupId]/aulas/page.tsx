'use client';

import { Suspense, useEffect } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ClassFiltersProvider } from './contexts/ClassFiltersContext';
import { ClassQueryProvider } from './contexts/ClassQueryContext';
import { useGroupClassData } from './hooks/useGroupClassData';
import { useClassActions } from './hooks/useClassActions';
import { BookOpen } from 'lucide-react';
import { usePageTitle } from '@/contexts/PageTitleContext';

// Componentes
import { StatsSection } from './components/StatsSection';
import { QuickActionBar } from './components/QuickActionBar';
import { GroupInfoSection } from './components/GroupInfoSection';
import { ClassFiltersBar } from './components/ClassFiltersBar';
import { ClassesListSection } from './components/ClassesListSection';
import { CancelClassDialog } from './components/CancelClassDialog';

// Loading Components
function PageLoadingSkeleton() {
  return (
    <div className="min-h-screen">
      <div className="w-full p-4 space-y-6">
        
        {/* Back Button Skeleton */}
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-32" />
        </div>
        
        {/* Stats */}
        <div className="grid gap-4 md:grid-cols-5">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16 mb-2" />
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-12 mb-1" />
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-20" />
              </div>
            </div>
          ))}
        </div>
        
        {/* Group Info */}
        <div className="animate-pulse">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4" />
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-full" />
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32" />
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24" />
              </div>
            </div>
          </div>
        </div>
        
        {/* Quick Actions */}
        <div className="animate-pulse">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
            <div className="flex justify-between items-center">
              <div className="flex space-x-2">
                <div className="h-10 w-28 bg-gray-200 dark:bg-gray-700 rounded" />
                <div className="h-10 w-20 bg-gray-200 dark:bg-gray-700 rounded" />
              </div>
              <div className="h-10 w-32 bg-gray-200 dark:bg-gray-700 rounded" />
            </div>
          </div>
        </div>
        
        {/* Filters */}
        <div className="animate-pulse">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border">
            <div className="grid gap-4 md:grid-cols-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20" />
                  <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded" />
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Classes List */}
        <div className="animate-pulse">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6" />
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-48 bg-gray-200 dark:bg-gray-700 rounded-lg" />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Componente principal do conteúdo da página
function ClassGroupContent() {
  // Configurar título dinâmico no header
  const {
    setPageTitle,
    setPageSubtitle,
    setPageIcon,
  } = usePageTitle();

  useEffect(() => {
    setPageTitle('Aulas da Turma');
    setPageSubtitle('Gerencie as aulas e cronograma da turma');
    setPageIcon(<BookOpen className="h-6 w-6 text-primary" />);

    return () => {
      // Limpar ao desmontar
      setPageTitle(null);
      setPageSubtitle(null);
      setPageIcon(null);
    };
  }, [setPageTitle, setPageSubtitle, setPageIcon]);

  const params = useParams();
  const searchParams = useSearchParams();
  const groupId = params?.groupId as string;

  const {
    groupInfo,
    classStats,
    classesData,
    instructors,
    isLoading,
    isClassesLoading,
    error,
    refetch,
    refetchClasses
  } = useGroupClassData(groupId);

  const {
    handleEditClass,
    handleViewAttendance,
    handleCancelClass,
    cancelDialog,
    closeCancelDialog,
    confirmCancelClass,
    isActionLoading,
    isCancelling
  } = useClassActions({ 
    groupId, 
    onRefresh: refetchClasses 
  });

  // Função helper para formatar data e hora para o dialog
  const formatClassDateTime = (classItem: any) => {
    const date = new Date(classItem.startTime).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
    const time = new Date(classItem.startTime).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
    });
    return { date, time };
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Erro ao carregar dados
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error}
          </p>
          <button
            onClick={refetch}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return <PageLoadingSkeleton />;
  }

  return (
    <div className="min-h-screen">
      <div className="w-full p-4 space-y-6">
        {/* Botão de Voltar - Minimalista */}
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            asChild
            className="text-muted-foreground hover:text-foreground -ml-2 h-8"
          >
            <Link href={`/turmas/${groupId}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              <span className="text-sm">Voltar para turma</span>
            </Link>
          </Button>
        </div>

        {/* Estatísticas */}
        {classStats && <StatsSection stats={classStats} isLoading={isLoading} />}

        {/* Informações da Turma */}
        {groupInfo && <GroupInfoSection groupInfo={groupInfo} isLoading={isLoading} />}

        {/* Barra de Ações Rápidas */}
        <QuickActionBar 
          onRefresh={refetchClasses} 
          isLoading={isLoading}
          isClassesLoading={isClassesLoading}
          groupName={groupInfo?.name}
        />

        {/* Filtros */}
        <ClassFiltersBar 
          groupId={groupId}
          isLoading={isLoading}
        />

        {/* Lista de Aulas */}
        {classesData && (
          <ClassesListSection
            classesData={classesData}
            groupId={groupId}
            onEdit={handleEditClass}
            onCancel={(classId) => {
              const classItem = classesData.data.find(c => c.id === classId);
              if (classItem) {
                const { date, time } = formatClassDateTime(classItem);
                handleCancelClass(classId, classItem.name, date, time);
              }
            }}
            isLoading={isClassesLoading}
          />
        )}

        {/* Dialog de Cancelamento */}
        <CancelClassDialog
          open={cancelDialog.isOpen}
          onOpenChange={closeCancelDialog}
          onConfirm={confirmCancelClass}
          className={cancelDialog.className || undefined}
          classDate={cancelDialog.classDate || undefined}
          classTime={cancelDialog.classTime || undefined}
          isLoading={isCancelling}
        />
      </div>
    </div>
  );
}

export default function ClassGroupClassesPage() {
  return (
    <ClassQueryProvider>
      <ClassFiltersProvider>
        <Suspense fallback={<PageLoadingSkeleton />}>
          <ClassGroupContent />
        </Suspense>
      </ClassFiltersProvider>
    </ClassQueryProvider>
  );
} 