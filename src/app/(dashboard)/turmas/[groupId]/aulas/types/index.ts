export interface ClassStats {
  totalClasses: number;
  scheduledClasses: number;
  ongoingClasses: number;
  completedClasses: number;
  cancelledClasses: number;
}

export interface ClassFilters {
  status?: 'scheduled' | 'ongoing' | 'completed' | 'cancelled' | null;
  searchInput?: string;
  dateFrom?: string;
  dateTo?: string;
  instructorId?: string;
  page?: number;
  limit?: number;
}

export interface GroupClassInfo {
  id: string;
  name: string;
  description?: string;
  category: string;
  isActive: boolean;
  maxCapacity?: number;
  instructor: {
    id: string;
    name: string;
    avatar?: string;
  };
  branch: {
    id: string;
    name: string;
  };
}

export interface ClassItem {
  id: string;
  name: string;
  description?: string;
  startTime: string;
  endTime: string;
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
  maxCapacity?: number;
  attendanceCount: number;
  instructor: {
    id: string;
    name: string;
    avatar?: string;
  } | null;
  branch: {
    id: string;
    name: string;
  } | null;
}

export interface ClassesResponse {
  data: ClassItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ClassFiltersContextValue {
  filters: ClassFilters;
  queryFilters: ClassFilters;
  updateFilters: (newFilters: Partial<ClassFilters>) => void;
  resetFilters: () => void;
  setSearchInput: (searchInput: string) => void;
  clearSearch: () => void;
  setStatus: (status: ClassFilters['status']) => void;
  hasActiveFilters: () => boolean;
  getActiveFilters: () => Array<{ key: string; label: string; value: string }>;
} 