'use client';

import { useEffect, useState } from 'react';
import { ClassGroupWithDetails } from '@/app/(dashboard)/aulas/types';
import { useClassGroup } from './ClassGroupContext';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertCircle, Clock, Users, BarChart3 } from 'lucide-react';
import { getClassesByGroup } from '@/app/(dashboard)/aulas/actions';

interface ClassGroupIntegrationTestProps {
  classGroup: ClassGroupWithDetails;
}

export function ClassGroupIntegrationTest({ classGroup }: ClassGroupIntegrationTestProps) {
  const { state, setActiveTab } = useClassGroup();
  const [classStats, setClassStats] = useState<{
    total: number;
    completed: number;
    scheduled: number;
    cancelled: number;
  } | null>(null);
  const [testResults, setTestResults] = useState<{
    contextLoaded: boolean;
    dataIntegrity: boolean;
    componentsReady: boolean;
    apiConnections: boolean;
    statisticsAccuracy: boolean;
  }>({
    contextLoaded: false,
    dataIntegrity: false,
    componentsReady: false,
    apiConnections: false,
    statisticsAccuracy: false,
  });

  useEffect(() => {
    const loadClassStats = async () => {
      try {
        const result = await getClassesByGroup(classGroup.id);
        if (result.success && 'data' in result && result.data) {
          let classesData: any[] = [];
          
          if (Array.isArray(result.data)) {
            classesData = result.data;
          } else if (result.data.data && Array.isArray(result.data.data)) {
            classesData = result.data.data;
          }
          
          const stats = {
            total: classesData.length,
            completed: classesData.filter(c => c.status === 'completed').length,
            scheduled: classesData.filter(c => c.status === 'scheduled').length,
            cancelled: classesData.filter(c => c.status === 'cancelled').length,
          };
          
          setClassStats(stats);
        }
      } catch (error) {
        console.error('Erro ao carregar estatísticas de aulas:', error);
      }
    };

    if (classGroup?.id) {
      loadClassStats();
    }
  }, [classGroup?.id]);

  useEffect(() => {
    // Teste 1: Verificar se o contexto foi carregado
    const contextLoaded = !!state && !!state.classGroup;
    
    // Teste 2: Verificar integridade dos dados
    const dataIntegrity = !!(
      classGroup?.id &&
      classGroup?.name &&
      classGroup?.instructor &&
      classGroup?.branch
    );
    
    // Teste 3: Verificar se os componentes estão prontos
    const componentsReady = !!(
      state?.activeTab &&
      typeof setActiveTab === 'function'
    );
    
    // Teste 4: Verificar conexões com APIs (simulado)
    const apiConnections = !state.error;
    
    // Teste 5: Verificar precisão das estatísticas
    const statisticsAccuracy = classStats !== null && classStats.total > 0;
    
    setTestResults({
      contextLoaded,
      dataIntegrity,
      componentsReady,
      apiConnections,
      statisticsAccuracy,
    });
  }, [state, classGroup, classStats]);

  const getTestIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <AlertCircle className="h-4 w-4 text-red-600" />
    );
  };

  const getTestBadge = (passed: boolean) => {
    return (
      <Badge variant={passed ? "default" : "destructive"}>
        {passed ? "Passou" : "Falhou"}
      </Badge>
    );
  };

  const allTestsPassed = Object.values(testResults).every(Boolean);

  return (
    <Card className="border-2 border-dashed border-gray-300 dark:border-gray-600">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Teste de Integração dos Componentes
          {getTestBadge(allTestsPassed)}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Resumo dos testes */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="flex items-center gap-2">
            {getTestIcon(testResults.contextLoaded)}
            <span className="text-sm">Contexto</span>
          </div>
          <div className="flex items-center gap-2">
            {getTestIcon(testResults.dataIntegrity)}
            <span className="text-sm">Dados</span>
          </div>
          <div className="flex items-center gap-2">
            {getTestIcon(testResults.componentsReady)}
            <span className="text-sm">Componentes</span>
          </div>
          <div className="flex items-center gap-2">
            {getTestIcon(testResults.apiConnections)}
            <span className="text-sm">APIs</span>
          </div>
          <div className="flex items-center gap-2">
            {getTestIcon(testResults.statisticsAccuracy)}
            <span className="text-sm">Estatísticas</span>
          </div>
        </div>

        {/* Detalhes dos testes */}
        <div className="space-y-2 text-sm">
          <div className="flex items-center justify-between">
            <span>Context Provider carregado:</span>
            {getTestBadge(testResults.contextLoaded)}
          </div>
          <div className="flex items-center justify-between">
            <span>Integridade dos dados da turma:</span>
            {getTestBadge(testResults.dataIntegrity)}
          </div>
          <div className="flex items-center justify-between">
            <span>Componentes prontos para uso:</span>
            {getTestBadge(testResults.componentsReady)}
          </div>
          <div className="flex items-center justify-between">
            <span>Conexões com APIs funcionando:</span>
            {getTestBadge(testResults.apiConnections)}
          </div>
          <div className="flex items-center justify-between">
            <span>Estatísticas de aulas precisas:</span>
            {getTestBadge(testResults.statisticsAccuracy)}
          </div>
        </div>

        {/* Estatísticas de aulas detalhadas */}
        {classStats && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 className="font-medium mb-2 flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Estatísticas de Aulas Detectadas
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
              <div>Total: <Badge variant="outline">{classStats.total}</Badge></div>
              <div>Concluídas: <Badge variant="outline" className="text-green-600">{classStats.completed}</Badge></div>
              <div>Agendadas: <Badge variant="outline" className="text-blue-600">{classStats.scheduled}</Badge></div>
              <div>Canceladas: <Badge variant="outline" className="text-red-600">{classStats.cancelled}</Badge></div>
            </div>
            {classStats.completed > 0 && (
              <p className="text-xs text-green-600 mt-2">
                ✅ Aulas concluídas detectadas corretamente!
              </p>
            )}
          </div>
        )}

        {/* Informações do estado atual */}
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <Users className="h-4 w-4" />
            Estado Atual do Sistema
          </h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>Aba Ativa: <Badge variant="outline">{state?.activeTab || 'N/A'}</Badge></div>
            <div>Loading: <Badge variant="outline">{state?.isLoading ? 'Sim' : 'Não'}</Badge></div>
            <div>Turma ID: <code className="text-xs bg-gray-200 dark:bg-gray-700 px-1 rounded">{classGroup?.id?.slice(0, 8)}...</code></div>
            <div>Última Atualização: <Badge variant="outline">{state?.lastRefresh ? new Date(state.lastRefresh).toLocaleTimeString() : 'N/A'}</Badge></div>
          </div>
        </div>

        {/* Status geral */}
        <div className={`p-3 rounded-lg ${allTestsPassed ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'}`}>
          <div className="flex items-center gap-2">
            {getTestIcon(allTestsPassed)}
            <span className="font-medium">
              {allTestsPassed 
                ? '✅ Todos os componentes estão integrados e funcionando corretamente!' 
                : '❌ Alguns componentes apresentam problemas de integração.'
              }
            </span>
          </div>
          {classStats && classStats.completed > 0 && (
            <p className="text-xs text-green-600 mt-1">
              Problema das estatísticas de aulas concluídas foi resolvido!
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 