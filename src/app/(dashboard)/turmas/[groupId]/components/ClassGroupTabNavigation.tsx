'use client';

import { motion } from 'framer-motion';
import { 
  BarChart3, 
  Calendar, 
  Clock, 
  TrendingUp 
} from 'lucide-react';
import { useClassGroup } from './ClassGroupContext';

interface ClassGroupTabNavigationProps {
  overviewContent: React.ReactNode;
  timelineContent: React.ReactNode;
  statisticsContent: React.ReactNode;
  calendarContent: React.ReactNode;
  defaultTab?: 'overview' | 'timeline' | 'statistics' | 'calendar';
}

export function ClassGroupTabNavigation({
  overviewContent,
  timelineContent,
  statisticsContent,
  calendarContent,
  defaultTab = 'overview'
}: ClassGroupTabNavigationProps) {
  // Usar o contexto para gerenciar a aba ativa
  const { state, setActiveTab } = useClassGroup();
  const activeTab = state.activeTab;

  const tabs = [
    {
      id: 'overview' as const,
      label: 'Visão Geral',
      shortLabel: 'Geral',
      icon: <TrendingUp className="h-4 w-4" />,
      content: overviewContent,
    },
    {
      id: 'timeline' as const,
      label: 'Timeline',
      shortLabel: 'Timeline',
      icon: <Clock className="h-4 w-4" />,
      content: timelineContent,
    },
    {
      id: 'statistics' as const,
      label: 'Estatísticas',
      shortLabel: 'Stats',
      icon: <BarChart3 className="h-4 w-4" />,
      content: statisticsContent,
    },
    {
      id: 'calendar' as const,
      label: 'Calendário',
      shortLabel: 'Agenda',
      icon: <Calendar className="h-4 w-4" />,
      content: calendarContent,
    },
  ];

  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;

  return (
    <div className="space-y-6">
      {/* Navegação das abas */}
      <div className="border-b border-slate-200 dark:border-gray-700">
        <nav className="flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                transition-colors duration-200
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-gray-400 dark:hover:text-gray-300'
                }
              `}
            >
              {tab.icon}
              <span className="hidden sm:inline">{tab.label}</span>
              <span className="sm:hidden">{tab.shortLabel}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Conteúdo da aba ativa */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
        className="min-h-[400px]"
      >
        {activeTabContent}
      </motion.div>
    </div>
  );
} 