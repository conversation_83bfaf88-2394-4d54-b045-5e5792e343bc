'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { AlertCircle, Info } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { FormFieldWrapperProps } from './types';

export default function FormFieldWrapper({ 
  children, 
  error, 
  label, 
  required = false,
  hint 
}: FormFieldWrapperProps) {
  return (
    <div className="space-y-2">
      <Label className="text-sm font-medium text-foreground">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      {children}
      
      {/* Feedback Visual */}
      <AnimatePresence mode="wait">
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            className="flex items-center gap-2 text-red-600 text-xs bg-red-50 dark:bg-red-950 p-2 rounded-md border border-red-200 dark:border-red-800"
          >
            <AlertCircle className="h-3 w-3 flex-shrink-0" />
            <span>{error}</span>
          </motion.div>
        )}
        
        {!error && hint && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            className="flex items-center gap-2 text-blue-600 text-xs bg-blue-50 dark:bg-blue-950 p-2 rounded-md border border-blue-200 dark:border-blue-800"
          >
            <Info className="h-3 w-3 flex-shrink-0" />
            <span>{hint}</span>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
} 