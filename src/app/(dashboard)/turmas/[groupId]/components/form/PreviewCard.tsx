'use client';

import { motion } from 'framer-motion';
import { CheckCircle2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { PreviewCardProps } from './types';

export default function PreviewCard({ 
  title, 
  current, 
  new: newValue, 
  icon: Icon,
  formatter
}: PreviewCardProps) {
  const hasChanged = current !== newValue;
  const formatValue = formatter || ((value: any) => String(value || 'Não definido'));
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
      className={`relative overflow-hidden rounded-xl border-2 transition-all duration-300 ${
        hasChanged 
          ? 'border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 dark:border-blue-700 dark:from-blue-950 dark:to-blue-900 shadow-lg' 
          : 'border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100 dark:border-gray-700 dark:from-gray-800 dark:to-gray-900'
      }`}
    >
      {hasChanged && (
        <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-blue-500" />
      )}
      
      <div className="p-5">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${
              hasChanged 
                ? 'bg-blue-100 dark:bg-blue-800' 
                : 'bg-gray-100 dark:bg-gray-700'
            }`}>
              <Icon className={`h-4 w-4 ${
                hasChanged 
                  ? 'text-blue-600 dark:text-blue-400' 
                  : 'text-gray-600 dark:text-gray-400'
              }`} />
            </div>
            <span className="font-semibold text-sm text-foreground">{title}</span>
          </div>
          
          {hasChanged && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.1 }}
            >
              <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700 dark:bg-blue-800 dark:text-blue-300">
                <CheckCircle2 className="h-3 w-3 mr-1" />
                Modificado
              </Badge>
            </motion.div>
          )}
        </div>
        
        <div className="space-y-3">
          {/* Valor Atual */}
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 w-2 h-2 rounded-full bg-gray-400 mt-2" />
            <div className="flex-1">
              <div className="text-xs font-medium text-muted-foreground mb-1">VALOR ATUAL</div>
              <div className={`text-sm font-medium p-2 rounded-md ${
                hasChanged 
                  ? 'bg-red-50 text-red-700 line-through dark:bg-red-950 dark:text-red-300' 
                  : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
              }`}>
                {formatValue(current)}
              </div>
            </div>
          </div>
          
          {/* Valor Novo (apenas se houver mudança) */}
          {hasChanged && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.15 }}
              className="flex items-start gap-3"
            >
              <div className="flex-shrink-0 w-2 h-2 rounded-full bg-green-500 mt-2" />
              <div className="flex-1">
                <div className="text-xs font-medium text-muted-foreground mb-1">NOVO VALOR</div>
                <div className="text-sm font-semibold p-2 rounded-md bg-green-50 text-green-700 dark:bg-green-950 dark:text-green-300 border border-green-200 dark:border-green-800">
                  {formatValue(newValue)}
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </motion.div>
  );
} 