import { 
  FileText, 
  Users, 
  MapPin, 
  Award, 
  Calendar,
  Repeat
} from 'lucide-react';

// Categorias disponíveis para turmas
export const categories = [
  { value: 'kids', label: 'Infantil' },
  { value: 'teens', label: 'Juvenil' },
  { value: 'adults', label: 'Adulto' },
  { value: 'seniors', label: 'Senior' }
];

// Níveis de faixa disponíveis
export const beltLevels = [
  { value: 'white', label: 'Branca' },
  { value: 'blue', label: 'Azul' },
  { value: 'purple', label: 'Roxa' },
  { value: 'brown', label: 'Marrom' },
  { value: 'black', label: 'Preta' }
];

// Configuração das abas do formulário
export const tabs = [
  {
    id: 'basic',
    label: 'Informações Básicas',
    shortLabel: 'Básico',
    icon: FileText,
    description: 'Nome, categoria e descrição'
  },
  {
    id: 'capacity',
    label: 'Capacidade e Lista',
    shortLabel: 'Capacidade',
    icon: Users,
    description: 'Vagas e lista de espera'
  },
  {
    id: 'location',
    label: 'Instrutor e Local',
    shortLabel: 'Local',
    icon: MapPin,
    description: 'Instrutor responsável e filial'
  },
  {
    id: 'requirements',
    label: 'Requisitos',
    shortLabel: 'Requisitos',
    icon: Award,
    description: 'Idade e nível de faixa'
  },
  {
    id: 'schedule',
    label: 'Cronograma e Status',
    shortLabel: 'Cronograma',
    icon: Calendar,
    description: 'Datas, status e configurações'
  },
  // {
  //   id: 'recurrence',
  //   label: 'Recorrência',
  //   shortLabel: 'Recorrência',
  //   icon: Repeat,
  //   description: 'Configuração de repetição das aulas'
  // }
] as const; 