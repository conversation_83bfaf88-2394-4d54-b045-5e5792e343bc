import { useState, useCallback, useMemo, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';
import { createFormSchema, type FormValues } from '../validation';
import { ClassGroup } from '../types';
import { normalizeDateString } from '../utils';
import { useClassGroup } from '../../ClassGroupContext';

interface UseEditClassGroupFormParams {
  initialData: ClassGroup;
  onSuccess?: () => void;
}

export const useEditClassGroupForm = ({
  initialData,
  onSuccess,
}: UseEditClassGroupFormParams) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const [userInteracted, setUserInteracted] = useState(false);
  const { updateClassGroup, isUpdating } = useClassGroup();
  
  // Efeito para atualizar o estado após uma edição bem-sucedida
  useEffect(() => {
    if (isUpdating === false && isSubmitting === true) {
      // A atualização foi concluída
      setIsSubmitting(false);
      
      // Resetar modo de preview
      setPreviewMode(false);
      
      // Chamar callback de sucesso se existir
      if (onSuccess) {
        onSuccess();
      }
    }
  }, [isUpdating, isSubmitting, onSuccess]);

  // Função para normalizar valores para comparação
  const normalizeValue = useCallback((value: any, fieldName?: string): any => {
    // Tratamento especial para max_capacity
    if (fieldName === 'max_capacity') {
      if (value === null || value === undefined || value === '') {
        return null;
      }
      
      if (typeof value === 'string') {
        const trimmed = value.trim();
        if (trimmed === '') return null;
        const parsed = Number(trimmed);
        return isNaN(parsed) ? null : parsed;
      }
      
      return typeof value === 'number' ? value : null;
    }
    
    if (value === null || value === undefined) return '';
    if (typeof value === 'string') {
      const trimmed = value.trim();
      return trimmed === '' ? '' : trimmed;
    }
    if (typeof value === 'number') {
      return isNaN(value) ? 0 : value;
    }
    if (typeof value === 'boolean') {
      return value;
    }
    return value;
  }, []);

  // Função para preparar defaultValues
  const prepareDefaultValues = useCallback((data: ClassGroup): FormValues => {
    // Determinar se a capacidade é ilimitada baseado no valor de max_capacity
    const isUnlimitedCapacity = data.max_capacity === null || data.max_capacity === undefined;
    
    return {
      name: normalizeValue(data.name, 'name') || '',
      description: normalizeValue(data.description, 'description') || '',
      category: normalizeValue(data.category, 'category') || '',
      unlimited_capacity: isUnlimitedCapacity,
      max_capacity: isUnlimitedCapacity ? null : data.max_capacity,
      allow_waitlist: Boolean(data.allow_waitlist),
      instructor_id: normalizeValue(data.instructor_id, 'instructor_id') || '',
      branch_id: normalizeValue(data.branch_id, 'branch_id') || '',
      min_age: data.min_age,
      max_age: data.max_age,
      min_belt_level: data.min_belt_level || '',
      max_belt_level: data.max_belt_level || '',
      is_active: data.is_active !== undefined ? Boolean(data.is_active) : true,
      start_date: normalizeDateString(data.start_date) || '',
      end_date: normalizeDateString(data.end_date) || '',
      recurrence_pattern: data.recurrence_pattern === null ? undefined : data.recurrence_pattern
    };
  }, [normalizeValue]);

  // Criar schema dinâmico baseado nos dados originais
  const formSchema = useMemo(() => createFormSchema(initialData), [initialData.id]);

  // Resolver memoizado para evitar re-criações
  const resolver = useMemo(() => zodResolver(formSchema), [formSchema]);

  // Default values memoizados
  const defaultValues = useMemo(() => prepareDefaultValues(initialData), [prepareDefaultValues, initialData]);

  // Configuração do React Hook Form com validação mínima
  const form = useForm<FormValues>({
    resolver,
    defaultValues,
    mode: 'onSubmit',
    reValidateMode: 'onBlur'
  });

  const { 
    handleSubmit, 
    formState: { errors, isValid, dirtyFields, isValidating }, 
    watch,
    getValues,
    trigger,
    reset,
    setError
  } = form;

  // Usar uma referência estável para watchedValues
  const watchedValues = watch();

  // Monitorar dirtyFields para definir userInteracted automaticamente
  useEffect(() => {
    if (Object.keys(dirtyFields).length > 0 && !userInteracted) {
      setUserInteracted(true);
    }
  }, [dirtyFields, userInteracted]);

  // Atualizar userInteracted quando qualquer campo for modificado
  const checkUserInteraction = useCallback(() => {
    if (Object.keys(dirtyFields).length > 0 || !userInteracted) {
      setUserInteracted(true);
    }
  }, [dirtyFields, userInteracted]);

  // Detectar mudanças no formulário
  const hasChanges = useMemo(() => {
    // Se o usuário não interagiu com o formulário ainda, não mostrar alterações
    if (!userInteracted) return false;
    
    const currentValues = getValues();
    
    // Função de comparação mais simples e robusta
    const isValueChanged = (fieldName: string, currentValue: any, defaultValue: any): boolean => {
      if (fieldName === 'max_capacity') {
        // Para max_capacity, comparar diretamente os valores
        // Tratar undefined, null e '' como null
        const normalizedCurrent = currentValue === undefined || currentValue === '' ? null : currentValue;
        const normalizedDefault = defaultValue === undefined || defaultValue === '' ? null : defaultValue;
        
        // Se ambos são null, não há mudança
        if (normalizedCurrent === null && normalizedDefault === null) return false;
        
        // Se um é null e outro não, há mudança
        if ((normalizedCurrent === null) !== (normalizedDefault === null)) return true;
        
        // Se ambos são números, comparar diretamente
        if (typeof normalizedCurrent === 'number' && typeof normalizedDefault === 'number') {
          return normalizedCurrent !== normalizedDefault;
        }
        
        // Para strings que representam números, converter e comparar
        if (typeof normalizedCurrent === 'string' && typeof normalizedDefault === 'string') {
          const currentNum = Number(normalizedCurrent);
          const defaultNum = Number(normalizedDefault);
          if (!isNaN(currentNum) && !isNaN(defaultNum)) {
            return currentNum !== defaultNum;
          }
        }
        
        // Para casos mistos (string vs number), converter ambos para number
        const currentAsNumber = Number(normalizedCurrent);
        const defaultAsNumber = Number(normalizedDefault);
        if (!isNaN(currentAsNumber) && !isNaN(defaultAsNumber)) {
          return currentAsNumber !== defaultAsNumber;
        }
        
        // Fallback: comparação de string
        return String(normalizedCurrent) !== String(normalizedDefault);
      }
      
      if (fieldName === 'unlimited_capacity' || fieldName === 'allow_waitlist' || fieldName === 'is_active') {
        // Para campos booleanos, comparar como booleanos
        return Boolean(currentValue) !== Boolean(defaultValue);
      }
      
      if (fieldName === 'min_age' || fieldName === 'max_age') {
        // Para campos de idade, tratar null, undefined e '' como null
        const normalizedCurrent = currentValue === undefined || currentValue === '' ? null : currentValue;
        const normalizedDefault = defaultValue === undefined || defaultValue === '' ? null : defaultValue;
        
        if (normalizedCurrent === null && normalizedDefault === null) return false;
        if ((normalizedCurrent === null) !== (normalizedDefault === null)) return true;
        
        return Number(normalizedCurrent) !== Number(normalizedDefault);
      }
      
      // Para strings, comparar após trim
      const currentStr = String(currentValue || '').trim();
      const defaultStr = String(defaultValue || '').trim();
      return currentStr !== defaultStr;
    };

    // Campos a serem comparados - contém apenas campos realmente editáveis pelo usuário
    const fieldsToCompare = [
      'name', 'description', 'category', 'unlimited_capacity', 'max_capacity', 'allow_waitlist',
      'instructor_id', 'branch_id', 'min_age', 'max_age', 'min_belt_level',
      'max_belt_level', 'is_active', 'start_date', 'end_date'
    ];

    // Verificar se algum campo mudou
    for (const field of fieldsToCompare) {
      const currentValue = currentValues[field as keyof FormValues];
      const defaultValue = defaultValues[field as keyof FormValues];
      
      if (isValueChanged(field, currentValue, defaultValue)) {
        return true;
      }
    }

    // Verificar recurrence_pattern separadamente (comparação mais complexa)
    const currentPattern = currentValues.recurrence_pattern;
    const defaultPattern = defaultValues.recurrence_pattern;
    
    // Comparar tratando undefined e null como equivalentes
    const normalizePattern = (pattern: any) => {
      if (pattern === undefined || pattern === null) return null;
      return pattern;
    };
    
    const normalizedCurrent = normalizePattern(currentPattern);
    const normalizedDefault = normalizePattern(defaultPattern);
    
    if (normalizedCurrent !== normalizedDefault) {
      const currentPatternStr = JSON.stringify(normalizedCurrent);
      const defaultPatternStr = JSON.stringify(normalizedDefault);
      return currentPatternStr !== defaultPatternStr;
    }

    return false;
  }, [watchedValues, defaultValues, getValues, userInteracted]);

  // Função para detectar dirtyFields manualmente
  const manualDirtyFields = useMemo(() => {
    // Se o React Hook Form já detectou campos modificados, usar esses
    if (Object.keys(dirtyFields).length > 0) {
      return dirtyFields;
    }

    // Se o usuário não interagiu ainda, retornar objeto vazio
    if (!userInteracted) return {};

    // Comparar valores manualmente
    const currentValues = getValues();
    const manual: Record<string, boolean> = {};

    // Só fazer comparações manuais em campos relevantes
    const relevantFields = [
      'name', 'description', 'category', 'unlimited_capacity', 'max_capacity', 'allow_waitlist',
      'instructor_id', 'branch_id', 'min_age', 'max_age', 'min_belt_level',
      'max_belt_level', 'is_active', 'start_date', 'end_date', 'recurrence_pattern'
    ];

    relevantFields.forEach(field => {
      const current = normalizeValue(currentValues[field as keyof FormValues], field);
      const defaultVal = normalizeValue(defaultValues[field as keyof FormValues], field);
      
      // Comparação especial para max_capacity (pode ser null)
      if (field === 'max_capacity') {
        if (current !== defaultVal) {
          manual[field] = true;
        }
      } else if (field === 'unlimited_capacity') {
        // Comparação especial para unlimited_capacity (booleano)
        const currentBool = Boolean(currentValues[field as keyof FormValues]);
        const defaultBool = Boolean(defaultValues[field as keyof FormValues]);
        if (currentBool !== defaultBool) {
          manual[field] = true;
        }
      } else {
        // Converter para string para comparação segura
        const currentStr = current?.toString() || '';
        const defaultStr = defaultVal?.toString() || '';
        
        if (currentStr !== defaultStr) {
          manual[field] = true;
        }
      }
    });

    return manual;
  }, [dirtyFields, watchedValues, defaultValues, normalizeValue, getValues, userInteracted]);

  // Função de submissão
  const handleFormSubmit = async (data: FormValues) => {
    // Proteção contra submissões duplicadas
    if (isSubmitting) {
      console.warn('Tentativa de submissão duplicada bloqueada');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Validar antes de enviar
      const isFormValid = await trigger();
      if (!isFormValid) {
        console.error('Formulário inválido após trigger:', errors);
        
        // Identificar erros específicos para mensagens mais claras
        const errorMessages = Object.entries(errors)
          .filter(([_, value]) => value?.message)
          .map(([key, value]) => `${key}: ${value?.message}`);
        
        if (errorMessages.length > 0) {
          toast.error(`Erros de validação: ${errorMessages.join('; ')}`);
        } else {
          toast.error('Verifique os campos obrigatórios');
        }
        
        setIsSubmitting(false);
        return;
      }

      // Preparar dados para o Server Action com conversões adequadas
      const updateData = {
        id: initialData.id,
        name: data.name,
        description: data.description,
        category: data.category,
        max_capacity: data.unlimited_capacity ? null : (data.max_capacity !== null ? Number(data.max_capacity) : null),
        allow_waitlist: data.allow_waitlist,
        instructor_id: data.instructor_id,
        branch_id: data.branch_id,
        min_age: data.min_age ? Number(data.min_age) : null,
        max_age: data.max_age ? Number(data.max_age) : null,
        min_belt_level: data.min_belt_level,
        max_belt_level: data.max_belt_level,
        is_active: data.is_active,
        start_date: data.start_date ? `${data.start_date}T00:00:00.000Z` : undefined,
        end_date: data.end_date ? `${data.end_date}T00:00:00.000Z` : undefined,
        metadata: {}
      };

      // Chamar a função de atualização do contexto (que já gerencia loading states)
      const result = await updateClassGroup(updateData);
      
      if (!result?.success) {
        if (result?.errors) {
          Object.entries(result.errors).forEach(([field, error]) => {
            if (field === '_form') {
              toast.error(error as string);
            } else {
              setError(field as any, {
                type: 'server',
                message: error as string,
              });
            }
          });
        } else {
          toast.error('Erro ao atualizar turma');
        }
        setIsSubmitting(false);
      } else {
        // Sucesso é tratado no useEffect que monitora isUpdating
        toast.success('Turma atualizada com sucesso!');
      }
    } catch (error) {
      console.error('Erro ao processar formulário:', error);
      toast.error('Erro ao processar formulário. Tente novamente.');
      setIsSubmitting(false);
    }
  };

  // Função para achatar dirtyFields
  const flattenDirtyFields = (dirtyFields: any): Record<string, boolean> => {
    const flattened: Record<string, boolean> = {};
    
    Object.keys(dirtyFields).forEach(key => {
      if (typeof dirtyFields[key] === 'object' && dirtyFields[key] !== null) {
        // Se é um objeto aninhado, achatar
        Object.keys(dirtyFields[key]).forEach(nestedKey => {
          flattened[`${key}.${nestedKey}`] = true;
        });
      } else {
        flattened[key] = dirtyFields[key];
      }
    });
    
    return flattened;
  };

  return {
    form,
    isSubmitting,
    previewMode,
    setPreviewMode,
    hasChanges,
    userInteracted,
    checkUserInteraction,
    manualDirtyFields,
    flattenDirtyFields,
    watchedValues,
    onSubmit: handleSubmit(handleFormSubmit),
    reset
  };
}; 