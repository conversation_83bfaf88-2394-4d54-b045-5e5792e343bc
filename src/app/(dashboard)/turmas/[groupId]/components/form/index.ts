// Componentes do formulário
export { default as ClassGroupEditForm } from './ClassGroupEditForm';
export { default as FormFieldWrapper } from './FormFieldWrapper';
export { default as PreviewCard } from './PreviewCard';
export { default as BasicInfoSection } from './sections/BasicInfoSection';
export { default as CapacitySection } from './sections/CapacitySection';
export { default as LocationSection } from './sections/LocationSection';
export { default as RequirementsSection } from './sections/RequirementsSection';
export { default as PreviewSection } from './sections/PreviewSection';

// Hooks
export { useBeltValidation } from './hooks/useBeltValidation';

// Tipos e constantes
export * from './types';
export * from './constants';
export * from './validation';
export * from './utils';

export { default as ScheduleSection } from './sections/ScheduleSection'; 