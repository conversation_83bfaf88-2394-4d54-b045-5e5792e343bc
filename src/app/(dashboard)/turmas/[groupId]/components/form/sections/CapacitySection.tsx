'use client';

import { useCallback } from 'react';
import { Users, Info, AlertTriangle, Infinity } from 'lucide-react';
import { UseFormRegister, FieldErrors, UseFormSetValue } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import FormFieldWrapper from '../FormFieldWrapper';
import { FormValues } from '../validation';

interface CapacitySectionProps {
  register: UseFormRegister<FormValues>;
  errors: FieldErrors<FormValues>;
  setValue: UseFormSetValue<FormValues>;
  watchedValues: FormValues;
  checkUserInteraction?: () => void;
}

export default function CapacitySection({
  register,
  errors,
  setValue,
  watchedValues,
  checkUserInteraction
}: CapacitySectionProps) {
  
  // Verificar se há um erro específico relacionado à capacidade mínima
  const hasCapacityError = errors.max_capacity?.message?.includes('capacidade mínima');
  
  // Handler para lista de espera - memoizado para evitar recriações
  const handleWaitlistChange = useCallback((checked: boolean) => {
    setValue('allow_waitlist', checked, { shouldDirty: true });
    checkUserInteraction?.();
  }, [setValue, checkUserInteraction]);
  
  // Handler para capacidade ilimitada - memoizado para evitar recriações
  const handleUnlimitedCapacityChange = useCallback((checked: boolean) => {
    setValue('unlimited_capacity', checked, { shouldDirty: true });
    
    // Se marcar capacidade ilimitada, garantir que max_capacity seja null
    if (checked) {
      setValue('max_capacity', null, { shouldDirty: true, shouldValidate: true });
    }
    
    checkUserInteraction?.();
  }, [setValue, checkUserInteraction]);
  
  // Limpar campo de capacidade - memoizado para evitar recriações
  const handleClearCapacity = useCallback(() => {
    setValue('max_capacity', null, { shouldDirty: true });
  }, [setValue]);

  // Handler para alteração de capacidade máxima - memoizado para evitar recriações
  const handleCapacityChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    
    // Se capacidade ilimitada estiver marcada, não permitir alteração
    if (watchedValues.unlimited_capacity) return;
    
    // Converter para número ou null
    const numValue = value === '' ? null : Number(value);
    const finalValue = isNaN(numValue as number) ? null : numValue;
    
    setValue('max_capacity', finalValue, { shouldDirty: true, shouldValidate: true });
    checkUserInteraction?.();
  }, [setValue, checkUserInteraction, watchedValues.unlimited_capacity]);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Capacidade e Lista de Espera
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Checkbox para capacidade ilimitada */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="unlimited_capacity"
            checked={watchedValues.unlimited_capacity || false}
            onCheckedChange={handleUnlimitedCapacityChange}
          />
          <Label htmlFor="unlimited_capacity" className="text-sm font-medium flex items-center gap-2">
            <Infinity className="h-4 w-4" />
            Capacidade ilimitada
          </Label>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormFieldWrapper 
            label="Capacidade Máxima" 
            error={errors.max_capacity?.message}
            hint={watchedValues.unlimited_capacity 
              ? "Capacidade ilimitada selecionada - este campo será ignorado"
              : "Defina o número máximo de alunos para esta turma"
            }
          >
            <div className="flex items-center gap-2">
              <Input
                {...register('max_capacity', { 
                  setValueAs: (value) => {
                    // Se capacidade ilimitada estiver marcada, sempre retornar null
                    if (watchedValues.unlimited_capacity) return null;
                    
                    // Se o valor está vazio, retornar null
                    if (value === '' || value === null || value === undefined) return null;
                    
                    // Converter para número
                    const parsed = Number(value);
                    return isNaN(parsed) ? null : parsed;
                  }
                })}
                type="number"
                inputMode="numeric"
                pattern="[0-9]*"
                placeholder={watchedValues.unlimited_capacity ? "Ilimitado" : "Ex: 30"}
                disabled={watchedValues.unlimited_capacity}
                onChange={handleCapacityChange}
                className={`${errors.max_capacity ? 'border-red-500' : ''} ${
                  watchedValues.unlimited_capacity ? 'bg-muted text-muted-foreground' : ''
                }`}
              />
              {watchedValues.max_capacity && !watchedValues.unlimited_capacity && (
                <button 
                  type="button"
                  onClick={handleClearCapacity}
                  className="text-xs text-muted-foreground hover:text-foreground"
                >
                  Limpar
                </button>
              )}
            </div>
          </FormFieldWrapper>

          <div className="space-y-2">
            <Label className="text-sm font-medium">Permitir Lista de Espera</Label>
            <div className="flex items-center gap-2">
              <Switch
                checked={watchedValues.allow_waitlist || false}
                onCheckedChange={handleWaitlistChange}
              />
              <span className="text-sm text-muted-foreground">
                {watchedValues.allow_waitlist 
                  ? 'Estudantes podem entrar na lista de espera' 
                  : 'Não permitir lista de espera'}
              </span>
            </div>
          </div>
        </div>

        {/* Alertas baseados na configuração */}
        {!watchedValues.allow_waitlist && !watchedValues.unlimited_capacity && watchedValues.max_capacity && watchedValues.max_capacity < 5 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Quando a lista de espera está desabilitada, a capacidade mínima deve ser de 5 alunos.
            </AlertDescription>
          </Alert>
        )}

        {watchedValues.unlimited_capacity && (
          <Alert>
            <Infinity className="h-4 w-4" />
            <AlertDescription>
              Com capacidade ilimitada, não há limite para o número de estudantes que podem se inscrever na turma.
            </AlertDescription>
          </Alert>
        )}

        {!watchedValues.unlimited_capacity && !watchedValues.max_capacity && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Defina uma capacidade máxima ou marque a opção de capacidade ilimitada.
            </AlertDescription>
          </Alert>
        )}

        {watchedValues.allow_waitlist && !watchedValues.unlimited_capacity && watchedValues.max_capacity && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Com a lista de espera habilitada, estudantes poderão se inscrever mesmo quando a capacidade máxima ({watchedValues.max_capacity} alunos) for atingida.
            </AlertDescription>
          </Alert>
        )}

        {watchedValues.allow_waitlist && watchedValues.unlimited_capacity && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Com capacidade ilimitada e lista de espera habilitada, todos os estudantes podem se inscrever livremente.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
} 