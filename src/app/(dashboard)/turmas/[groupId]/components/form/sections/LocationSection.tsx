'use client';

import { MapPin } from 'lucide-react';
import { UseFormRegister, FieldErrors, UseFormSetValue } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import FormFieldWrapper from '../FormFieldWrapper';
import { FormValues } from '../validation';
import { Instructor, Branch } from '../types';

interface LocationSectionProps {
  register: UseFormRegister<FormValues>;
  errors: FieldErrors<FormValues>;
  setValue: UseFormSetValue<FormValues>;
  watchedValues: FormValues;
  instructors: Instructor[];
  branches: Branch[];
}

export default function LocationSection({
  register,
  errors,
  setValue,
  watchedValues,
  instructors,
  branches
}: LocationSectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Instrutor e Local
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormFieldWrapper label="Instrutor" required error={errors.instructor_id?.message}>
            <Select
              value={watchedValues.instructor_id}
              onValueChange={(value) => setValue('instructor_id', value, { shouldValidate: true })}
            >
              <SelectTrigger className={errors.instructor_id ? 'border-red-500' : ''}>
                <SelectValue placeholder="Selecione um instrutor" />
              </SelectTrigger>
              <SelectContent>
                {instructors.map(instructor => (
                  <SelectItem key={instructor.id} value={instructor.id}>
                    {instructor.full_name || `${instructor.first_name} ${instructor.last_name || ''}`.trim()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormFieldWrapper>

          <FormFieldWrapper label="Filial" required error={errors.branch_id?.message}>
            <Select
              value={watchedValues.branch_id}
              onValueChange={(value) => setValue('branch_id', value, { shouldValidate: true })}
            >
              <SelectTrigger className={errors.branch_id ? 'border-red-500' : ''}>
                <SelectValue placeholder="Selecione uma filial" />
              </SelectTrigger>
              <SelectContent>
                {branches.map(branch => (
                  <SelectItem key={branch.id} value={branch.id}>
                    {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormFieldWrapper>
        </div>
      </CardContent>
    </Card>
  );
} 