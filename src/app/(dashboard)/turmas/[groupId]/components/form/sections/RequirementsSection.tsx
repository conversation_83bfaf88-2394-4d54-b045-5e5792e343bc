'use client';

import { AlertTriangle } from 'lucide-react';
import { UseFormRegister, FieldErrors, UseFormSetValue, UseFormSetError, UseFormClearErrors } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import FormFieldWrapper from '../FormFieldWrapper';
import { FormValues } from '../validation';
import { beltLevels } from '../constants';
import { useBeltValidation } from '../hooks/useBeltValidation';

// Helper para transformar valor do input em number ou null
const transformAgeValue = (value: string): number | null => {
  if (!value || value.trim() === '') return null;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? null : parsed;
};

interface RequirementsSectionProps {
  register: UseFormRegister<FormValues>;
  errors: FieldErrors<FormValues>;
  setValue: UseFormSetValue<FormValues>;
  setError: UseFormSetError<FormValues>;
  clearErrors: UseFormClearErrors<FormValues>;
  watchedValues: FormValues;
}

export default function RequirementsSection({
  register,
  errors,
  setValue,
  setError,
  clearErrors,
  watchedValues
}: RequirementsSectionProps) {
  // Hook personalizado para validação das faixas
  useBeltValidation({
    minBelt: watchedValues.min_belt_level || '',
    maxBelt: watchedValues.max_belt_level || '',
    setError,
    clearErrors
  });

  // Handlers para os campos de idade que fazem a transformação correta
  const handleMinAgeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const transformedValue = transformAgeValue(e.target.value);
    setValue('min_age', transformedValue, { shouldValidate: true, shouldDirty: true });
  };

  const handleMaxAgeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const transformedValue = transformAgeValue(e.target.value);
    setValue('max_age', transformedValue, { shouldValidate: true, shouldDirty: true });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5" />
          Critérios de Elegibilidade
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <FormFieldWrapper 
            label="Idade Mínima" 
            required 
            error={errors.min_age?.message}
            hint="Idade mínima para participar da turma"
          >
            <Input
              type="number"
              inputMode="numeric"
              pattern="[0-9]*"
              min="0"
              max="100"
              placeholder="Deixe em branco para não limitar"
              value={watchedValues.min_age === null ? '' : watchedValues.min_age || ''}
              onChange={handleMinAgeChange}
              className={errors.min_age ? 'border-red-500' : ''}
            />
          </FormFieldWrapper>

          <FormFieldWrapper 
            label="Idade Máxima" 
            required 
            error={errors.max_age?.message}
            hint="Idade máxima para participar da turma"
          >
            <Input
              type="number"
              inputMode="numeric"
              pattern="[0-9]*"
              min="0"
              max="100"
              placeholder="Deixe em branco para não limitar"
              value={watchedValues.max_age === null ? '' : watchedValues.max_age || ''}
              onChange={handleMaxAgeChange}
              className={errors.max_age ? 'border-red-500' : ''}
            />
          </FormFieldWrapper>

          <FormFieldWrapper 
            label="Faixa Mínima" 
            required 
            error={errors.min_belt_level?.message}
            hint="Nível mínimo de faixa exigido"
          >
            <Select
              value={watchedValues.min_belt_level || ''}
              onValueChange={(value) => setValue('min_belt_level', value, { shouldValidate: true })}
            >
              <SelectTrigger className={errors.min_belt_level ? 'border-red-500' : ''}>
                <SelectValue placeholder="Selecione a faixa mínima" />
              </SelectTrigger>
              <SelectContent>
                {beltLevels.map(belt => (
                  <SelectItem key={belt.value} value={belt.value}>
                    {belt.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormFieldWrapper>

          <FormFieldWrapper 
            label="Faixa Máxima" 
            required 
            error={errors.max_belt_level?.message}
            hint="Nível máximo de faixa aceito"
          >
            <Select
              value={watchedValues.max_belt_level || ''}
              onValueChange={(value) => setValue('max_belt_level', value, { shouldValidate: true })}
            >
              <SelectTrigger className={errors.max_belt_level ? 'border-red-500' : ''}>
                <SelectValue placeholder="Selecione a faixa máxima" />
              </SelectTrigger>
              <SelectContent>
                {beltLevels.map(belt => (
                  <SelectItem key={belt.value} value={belt.value}>
                    {belt.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormFieldWrapper>
        </div>
      </CardContent>
    </Card>
  );
} 