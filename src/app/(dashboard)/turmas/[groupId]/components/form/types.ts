// Tipos para o formulário de edição de turma
export interface Instructor {
  id: string;
  first_name: string;
  last_name: string | null;
  full_name?: string | null;
}

export interface Branch {
  id: string;
  name: string;
}

export interface ClassGroup {
  id: string;
  name: string;
  description: string | null;
  category: string | null;
  max_capacity: number | null;
  allow_waitlist?: boolean;
  instructor_id: string;
  branch_id: string;
  min_age: number | null;
  max_age: number | null;
  min_belt_level: string | null;
  max_belt_level: string | null;
  is_active: boolean;
  start_date: string;
  end_date: string | null;
  recurrence_pattern?: any;
  instructor?: Instructor;
  branch?: Branch;
}

export interface ClassGroupEditFormProps {
  initialData: ClassGroup;
  instructors: Instructor[];
  branches: Branch[];
  onSuccess?: () => void;
}

export interface FormFieldWrapperProps {
  children: React.ReactNode;
  error?: string;
  label: string;
  required?: boolean;
  hint?: string;
}

export interface PreviewCardProps {
  title: string;
  current: any;
  new: any;
  icon: any;
  formatter?: (value: any) => string;
}

export interface TabConfig {
  id: string;
  label: string;
  shortLabel: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

// Re-exportar interface compartilhada para manter compatibilidade
export type { QuickStat, QuickAction } from '../../../../../../types/shared';

// Re-exportar FormValues do validation.ts para conveniência
export type { FormValues } from './validation'; 