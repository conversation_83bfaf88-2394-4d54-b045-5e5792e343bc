import { z } from 'zod';
import { isDateTodayOrFuture, isEndDateAfterStartDate, getTodayDateString } from './utils';

// Função para verificar se a data original já passou
function isOriginalDateInPast(originalDate: string): boolean {
  if (!originalDate) return false;
  
  try {
    const todayString = getTodayDateString();
    const originalDateString = originalDate.split('T')[0]; // Pegar apenas a parte da data
    
    // Se a data original é anterior a hoje, já passou
    return originalDateString < todayString;
  } catch (error) {
    console.error('Erro ao verificar data original:', error);
    return false;
  }
}

// Helper para transformar string em número ou null
const stringToNumberOrNull = (value: string | number | null | undefined) => {
  if (value === null || value === undefined || value === '') return null;
  if (typeof value === 'number') return value;
  const parsed = Number(value);
  return isNaN(parsed) ? null : parsed;
};

// Função que cria o schema de validação dinâmico baseado nos dados originais
export function createFormSchema(originalData?: { start_date?: string }) {
  const originalStartDate = originalData?.start_date;
  const isStartDateInPast = originalStartDate ? isOriginalDateInPast(originalStartDate) : false;

  return z.object({
    name: z.string()
      .min(2, 'Nome deve ter pelo menos 2 caracteres')
      .max(100, 'Nome deve ter no máximo 100 caracteres')
      .regex(/^[a-zA-ZÀ-ÿ0-9\s\-]+$/, 'Nome contém caracteres inválidos'),
    description: z.union([
      z.string()
        .min(10, 'Descrição deve ter pelo menos 10 caracteres')
        .max(500, 'Descrição deve ter no máximo 500 caracteres'),
      z.undefined(),
      z.null().transform(() => undefined)
    ]).optional(),
    category: z.string().min(1, 'Categoria é obrigatória'),
    unlimited_capacity: z.boolean(),
    max_capacity: z.union([
      z.number().int('Capacidade deve ser um número inteiro').min(1, 'Capacidade deve ser pelo menos 1'),
      z.string().transform((val) => {
        if (val === '' || val === null || val === undefined) return null;
        const parsed = Number(val);
        return isNaN(parsed) ? null : parsed;
      }).nullable(),
      z.null(),
      z.undefined().transform(() => null)
    ]).nullable(),
    allow_waitlist: z.boolean(),
    instructor_id: z.string().min(1, 'Instrutor é obrigatório'),
    branch_id: z.string().min(1, 'Filial é obrigatória'),
    min_age: z.union([
      z.number()
        .min(0, 'Idade mínima deve ser positiva')
        .max(100, 'Idade mínima inválida')
        .int('Idade deve ser um número inteiro')
        .nullable(),
      z.string()
        .transform(stringToNumberOrNull)
        .nullable()
    ]),
    max_age: z.union([
      z.number()
        .min(0, 'Idade máxima deve ser positiva')
        .max(100, 'Idade máxima inválida')
        .int('Idade deve ser um número inteiro')
        .nullable(),
      z.string()
        .transform(stringToNumberOrNull)
        .nullable()
    ]),
    min_belt_level: z.string()
      .optional()
      .refine(value => !value || ['white', 'blue', 'purple', 'brown', 'black'].includes(value), 'Faixa mínima inválida'),
    max_belt_level: z.string()
      .optional()
      .refine(value => !value || ['white', 'blue', 'purple', 'brown', 'black'].includes(value), 'Faixa máxima inválida'),
    is_active: z.boolean(),
    start_date: z.string()
      .min(1, 'Data de início é obrigatória')
      .refine(date => {
        const parsedDate = new Date(date);
        return !isNaN(parsedDate.getTime());
      }, 'Data de início inválida')
      .refine(date => {
        // Se a data original já passou, não permitir alteração da data de início
        if (isStartDateInPast && originalStartDate) {
          const originalDateString = originalStartDate.split('T')[0];
          const currentDateString = date.split('T')[0];
          
          // Só permite se a data não foi alterada
          return originalDateString === currentDateString;
        }
        return true;
      }, 'Não é possível alterar a data de início de uma turma que já começou')
      .refine(date => {
        // Validação normal para datas futuras (apenas se a data original não passou)
        if (isStartDateInPast) return true; // Pular validação de data futura se já passou
        
        return isDateTodayOrFuture(date);
      }, 'A data de início deve ser hoje ou uma data futura'),
    end_date: z.union([z.string().min(1), z.literal('')])
      .optional()
      .refine(date => {
        if (!date || date === '') return true;
        const parsedDate = new Date(date);
        return !isNaN(parsedDate.getTime());
      }, 'Data de término inválida'),
    recurrence_pattern: z.any().optional()
  })
  .refine(data => {
    // Validação: idade mínima não pode ser maior que máxima
    if (data.min_age !== null && data.max_age !== null && data.min_age > data.max_age) {
      return false;
    }
    return true;
  }, {
    message: 'Idade mínima não pode ser maior que a máxima',
    path: ['min_age']
  })
  .refine(data => {
    // Validação: data de término deve ser após data de início
    if (!data.end_date || data.end_date === '') return true;
    
    return isEndDateAfterStartDate(data.start_date, data.end_date);
  }, {
    message: 'Data de término deve ser posterior à data de início',
    path: ['end_date']
  })
  .refine(data => {
    // Validação: se capacidade ilimitada estiver marcada, max_capacity deve ser null
    if (data.unlimited_capacity) {
      return data.max_capacity === null || data.max_capacity === undefined;
    }
    return true;
  }, {
    message: 'Capacidade deve ser vazia quando capacidade ilimitada está marcada',
    path: ['max_capacity']
  })
  .refine(data => {
    // Validação: se capacidade não é ilimitada, max_capacity deve ser fornecida e válida
    if (!data.unlimited_capacity) {
      return data.max_capacity !== null && 
             data.max_capacity !== undefined && 
             typeof data.max_capacity === 'number' && 
             data.max_capacity >= 1;
    }
    return true;
  }, {
    message: 'Capacidade máxima é obrigatória quando capacidade ilimitada não está marcada',
    path: ['max_capacity']
  })
  .refine(data => {
    // Validação: quando lista de espera está desabilitada e capacidade não é ilimitada, deve ser pelo menos 5
    if (!data.allow_waitlist && !data.unlimited_capacity && data.max_capacity !== null) {
      return data.max_capacity >= 5;
    }
    return true;
  }, {
    message: 'Quando a lista de espera está desabilitada, a capacidade mínima deve ser de 5 alunos',
    path: ['max_capacity']
  });
}

// Schema padrão para compatibilidade
export const formSchema = createFormSchema();

export type FormValues = z.infer<ReturnType<typeof createFormSchema>>; 