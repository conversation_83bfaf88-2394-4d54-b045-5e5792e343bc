// Componente principal de navegação
export { ClassGroupTabNavigation } from './ClassGroupTabNavigation';

// Contexto e estado
export { ClassGroupProvider, useClassGroup } from './ClassGroupContext';

// Componentes funcionais
export { ClassGroupOverview } from './ClassGroupOverview';
export { ClassGroupCalendar } from './ClassGroupCalendar';
export { ClassGroupTimeline } from './ClassGroupTimeline';
export { ClassGroupStatsSection } from './ClassGroupStatsSection';
export { ClassGroupQuickActionBar } from './ClassGroupQuickActionBar';
export { ClassGroupIntegrationTest } from './ClassGroupIntegrationTest';

// Formulário de edição
export { default as ClassGroupEditForm } from './form/ClassGroupEditForm';

export * from './ClassGroupContext';
export * from './ClassGroupTabNavigation';
export * from './ClassGroupOverview';
export * from './ClassGroupTimeline';
export * from './ClassGroupStatsSection';
export * from './ClassGroupCalendar';
export * from './ClassGroupQuickActionBar';
export * from './ClassGroupDetailsClient'; 