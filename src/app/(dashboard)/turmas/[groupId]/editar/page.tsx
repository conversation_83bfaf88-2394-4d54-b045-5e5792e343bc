import { Metadata } from "next";
import { Suspense } from "react";
import { notFound } from "next/navigation";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { requireAuth } from "@/services/auth/server";
import { getClassGroupById } from "../../actions/class-group";
import { getFormData } from "../../../aulas/actions";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ClassGroupEditForm, ClassGroupProvider } from "../components";

export const metadata: Metadata = {
  title: "Editar Turma | ApexSAAS",
  description: "Editar informações da turma de aulas"
};

interface Props {
  params: Promise<{
    groupId: string;
  }>;
}

async function getClassGroupData(groupId: string) {
  const result = await getClassGroupById(groupId);
  
  if (!result.success || !('data' in result) || !result.data) {
    notFound();
  }
  
  return result.data;
}

export default async function EditarTurmaPage({ params }: Props) {
  // Verificar autenticação
  await requireAuth();
  
  // Aguardar resolução dos params
  const resolvedParams = await params;
  
  // Buscar dados da turma e do formulário
  const [classGroup, formDataResult] = await Promise.all([
    getClassGroupData(resolvedParams.groupId),
    getFormData()
  ]);
  
  const instructors = formDataResult.success ? formDataResult.data.instructors : [];
  const branches = formDataResult.success ? formDataResult.data.branches : [];

  const LoadingSkeleton = () => (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <Skeleton className="h-6 w-48 mb-2" />
              <Skeleton className="h-4 w-72" />
            </div>
            <div className="flex gap-2">
              <Skeleton className="h-9 w-20" />
              <Skeleton className="h-9 w-24" />
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Skeleton para formulário */}
      {[1, 2, 3].map((i) => (
        <Card key={i}>
          <CardContent className="p-6">
            <Skeleton className="h-6 w-40 mb-4" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            asChild
            className="h-8 w-8 p-0"
          >
            <Link href={`/turmas/${resolvedParams.groupId}`}>
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">Voltar</span>
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Editar Turma</h1>
            <p className="text-muted-foreground mt-2">
              Atualize as informações da turma "{classGroup.name}" de forma segura e eficiente.
            </p>
          </div>
        </div>
      </div>
      
      <ClassGroupProvider initialClassGroup={classGroup}>
        <Suspense fallback={<LoadingSkeleton />}>
          <ClassGroupEditForm 
            initialData={{
              ...classGroup,
              is_active: classGroup.is_active ?? true,
              start_date: classGroup.start_date ?? ''
            }}
            instructors={instructors}
            branches={branches}
          />
        </Suspense>
      </ClassGroupProvider>
    </div>
  );
} 