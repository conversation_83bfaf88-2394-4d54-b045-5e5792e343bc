import { Suspense } from 'react';
import { notFound, redirect } from 'next/navigation';
import { getClassGroupById } from '../actions/class-group';
import { ClassGroupProvider } from './components';

interface ClassGroupLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    groupId: string;
  }>;
}

async function ClassGroupLayoutContent({ 
  children, 
  groupId 
}: { 
  children: React.ReactNode; 
  groupId: string; 
}) {
  console.log('🏗️ ClassGroupLayout: Buscando turma:', groupId);
  
  try {
    const result = await getClassGroupById(groupId);
    
    if (!result.success || !('data' in result) || !result.data) {
      console.log('❌ ClassGroupLayout: Turma não encontrada, redirecionando para lista:', groupId);
      redirect('/turmas');
    }

    const classGroup = result.data;
    console.log('✅ ClassGroupLayout: Turma encontrada:', classGroup.name);

    return (
      <ClassGroupProvider initialClassGroup={classGroup}>
        {children}
      </ClassGroupProvider>
    );
  } catch (error) {
    console.error('💥 ClassGroupLayout: Erro ao buscar turma:', error);
    console.log('🔄 ClassGroupLayout: Redirecionando para lista devido ao erro');
    redirect('/turmas');
  }
}

function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2" />
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2" />
      </div>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-48" />
          </div>
        ))}
      </div>
    </div>
  );
}

export default async function ClassGroupLayout({ 
  children, 
  params 
}: ClassGroupLayoutProps) {
  const resolvedParams = await params;
  
  return (
    <Suspense fallback={<LoadingSkeleton />}>
      <ClassGroupLayoutContent groupId={resolvedParams.groupId}>
        {children}
      </ClassGroupLayoutContent>
    </Suspense>
  );
} 