"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/services/supabase/server";
import { CreateClassGroupFormSchema } from "../schemas/class-group";
import { validateUserAuthentication, validateInstructor, validateBranch } from "./shared/validation-helpers";

/**
 * Cria uma nova turma
 */
export async function createClassGroup(data: unknown) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    // Primeiro validar com o schema de formulário
    const formValidationResult = CreateClassGroupFormSchema.safeParse(data);
    if (!formValidationResult.success) {
      return { success: false, errors: formValidationResult.error.format() };
    }

    const formData = formValidationResult.data;

    // Preparar dados para inserção no banco
    const insertData = {
      branch_id: formData.branch_id,
      instructor_id: formData.instructor_id,
      name: formData.name,
      description: formData.description,
      category: formData.category,
      min_age: formData.min_age,
      max_age: formData.max_age,
      min_belt_level: formData.min_belt_level,
      max_belt_level: formData.max_belt_level,
      // Se unlimited_capacity for true, max_capacity deve ser null
      max_capacity: formData.unlimited_capacity ? null : formData.max_capacity,
      allow_waitlist: formData.allow_waitlist,
      is_active: formData.is_active,
      start_date: formData.start_date ? formData.start_date.split('T')[0] : undefined,
      end_date: formData.end_date ? formData.end_date.split('T')[0] : undefined,
      recurrence_pattern: formData.recurrence_pattern,
      metadata: formData.metadata,
    };

    // Validar instrutor
    const instructorValidation = await validateInstructor(insertData.instructor_id, tenantId);
    if (!instructorValidation.success) {
      return instructorValidation;
    }

    // Validar filial
    const branchValidation = await validateBranch(insertData.branch_id, tenantId);
    if (!branchValidation.success) {
      return branchValidation;
    }

    const supabase = await createAdminClient();

    // Criar a turma
    const { data: classGroup, error } = await supabase
      .from("class_groups")
      .insert({
        ...insertData,
        tenant_id: tenantId,
      })
      .select()
      .single();

    if (error) {
      console.error("Erro ao criar turma:", error);
      return { success: false, errors: { _form: "Erro ao criar turma" } };
    }

    revalidatePath("/aulas");
    return { success: true, data: classGroup };
  } catch (error) {
    console.error("Erro ao criar turma:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 