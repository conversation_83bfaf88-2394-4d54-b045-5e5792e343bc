"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/services/supabase/server";
import { z } from "zod";
import { validateUserAuthentication, validateClassGroup } from "./shared/validation-helpers";
import { enrollStudent } from "./enroll-student";

const ProcessWaitlistSchema = z.object({
  class_group_id: z.string().uuid({ message: 'ID da turma é obrigatório' }),
  spots_available: z.number().min(1, { message: 'Número de vagas deve ser maior que 0' }),
});

/**
 * Processa a lista de espera de uma turma quando há vagas disponíveis
 */
export async function processWaitlist(data: unknown) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    const validationResult = ProcessWaitlistSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const { class_group_id, spots_available } = validationResult.data;
    const supabase = await createAdminClient();

    // Verificar se a turma existe
    const classGroupValidation = await validateClassGroup(class_group_id, tenantId);
    if (!classGroupValidation.success) {
      return classGroupValidation;
    }

    // Buscar alunos na lista de espera ordenados por posição
    const { data: waitlistEntries, error: waitlistError } = await supabase
      .from("class_waitlist")
      .select(`
        id,
        student_id,
        position,
        students!students_user_id_fkey!inner(user_id)
      `)
      .eq("class_group_id", class_group_id)
      .eq("tenant_id", tenantId)
      .eq("status", "waiting")
      .order("position", { ascending: true })
      .limit(spots_available);

    if (waitlistError) {
      console.error("Erro ao buscar lista de espera:", waitlistError);
      return { success: false, errors: { _form: "Erro ao buscar lista de espera" } };
    }

    if (!waitlistEntries || waitlistEntries.length === 0) {
      return { success: false, errors: { _form: "Nenhum aluno na lista de espera" } };
    }

    const enrolledStudents = [];
    const failedEnrollments = [];

    // Processar cada aluno da lista de espera
    for (const entry of waitlistEntries) {
      try {
        // Tentar matricular o aluno
        const enrollmentResult = await enrollStudent({
          class_group_id,
          student_id: entry.student_id,
          notes: "Matriculado automaticamente da lista de espera"
        });

        if (enrollmentResult.success) {
          // Remover da lista de espera
          await supabase
            .from("class_waitlist")
            .update({ status: "enrolled" })
            .eq("id", entry.id);

          enrolledStudents.push(entry);
        } else {
          failedEnrollments.push({
            student_id: entry.student_id,
            error: enrollmentResult.errors?._form || "Erro desconhecido"
          });
        }
      } catch (error) {
        console.error(`Erro ao processar aluno ${entry.student_id}:`, error);
        failedEnrollments.push({
          student_id: entry.student_id,
          error: "Erro interno"
        });
      }
    }

    revalidatePath("/aulas");

    if (enrolledStudents.length > 0) {
      return {
        success: true,
        data: {
          enrolled_count: enrolledStudents.length,
          failed_count: failedEnrollments.length,
          enrolled_students: enrolledStudents,
          failed_enrollments: failedEnrollments
        },
        message: `${enrolledStudents.length} alunos matriculados da lista de espera`
      };
    } else {
      return {
        success: false,
        errors: { _form: "Nenhum aluno pôde ser matriculado da lista de espera" },
        data: { failed_enrollments: failedEnrollments }
      };
    }
  } catch (error) {
    console.error("Erro ao processar lista de espera:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 