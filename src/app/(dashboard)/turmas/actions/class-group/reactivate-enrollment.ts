"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/services/supabase/server";
import { z } from "zod";
import { validateUserAuthentication, validateClassGroup } from "./shared/validation-helpers";

const ReactivateEnrollmentSchema = z.object({
  enrollment_id: z.string().uuid({ message: 'ID da matrícula é obrigatório' }),
  reason: z.string().max(500).optional(),
});

/**
 * Reativa a matrícula de um aluno em uma turma
 */
export async function reactivateEnrollment(data: unknown) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    const validationResult = ReactivateEnrollmentSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const { enrollment_id, reason } = validationResult.data;
    const supabase = await createAdminClient();

    // Verificar se a matrícula existe e está pausada/suspensa
    const { data: enrollment, error: enrollmentError } = await supabase
      .from("class_group_enrollments")
      .select(`
        id,
        status,
        class_group_id,
        student_id,
        students!students_user_id_fkey!inner(user_id),
        class_groups!inner(name, max_capacity, is_active)
      `)
      .eq("id", enrollment_id)
      .eq("tenant_id", tenantId)
      .single();

    if (enrollmentError || !enrollment) {
      return { success: false, errors: { _form: "Matrícula não encontrada" } };
    }

    if (enrollment.status === "active") {
      return { success: false, errors: { _form: "Matrícula já está ativa" } };
    }

    if (enrollment.status !== "suspended") {
      return { success: false, errors: { _form: "Apenas matrículas pausadas podem ser reativadas" } };
    }

    // Verificar se a turma ainda está ativa
    const classGroup = (enrollment.class_groups as any);
    if (!classGroup.is_active) {
      return { success: false, errors: { _form: "Turma não está mais ativa" } };
    }

    // Verificar capacidade se a turma tem limite (não contar alunos pausados)
    if (classGroup.max_capacity) {
      // Buscar matrículas ativas
      const { data: activeEnrollments, error: enrollmentsError } = await supabase
        .from("class_group_enrollments")
        .select("id")
        .eq("class_group_id", enrollment.class_group_id)
        .eq("status", "active")
        .eq("tenant_id", tenantId);

      if (enrollmentsError) {
        console.error("Erro ao verificar capacidade:", enrollmentsError);
        return { success: false, errors: { _form: "Erro ao verificar capacidade da turma" } };
      }

      if (activeEnrollments && activeEnrollments.length > 0) {
        // Verificar quantas dessas matrículas estão pausadas
        const enrollmentIds = activeEnrollments.map(e => e.id);
        
        const { count: pausedCount } = await supabase
          .from("enrollment_pauses")
          .select("*", { count: "exact", head: true })
          .in("enrollment_id", enrollmentIds)
          .eq("tenant_id", tenantId)
          .is("resumed_at", null);

        // Calcular matrículas realmente ativas (não pausadas)
        const realActiveCount = activeEnrollments.length - (pausedCount || 0);

        if (realActiveCount >= classGroup.max_capacity) {
          return { success: false, errors: { _form: "Turma já atingiu a capacidade máxima" } };
        }
      }
    }

    // Reativar a matrícula
    const { error } = await supabase
      .from("class_group_enrollments")
      .update({
        status: "active",
        notes: reason ? `Reativada: ${reason}` : "Matrícula reativada",
        updated_at: new Date().toISOString(),
      })
      .eq("id", enrollment_id)
      .eq("tenant_id", tenantId);

    if (error) {
      console.error("Erro ao reativar matrícula:", error);
      return { success: false, errors: { _form: "Erro ao reativar matrícula" } };
    }

    revalidatePath("/aulas");
    revalidatePath(`/perfil/${(enrollment.students as any).user_id}`);
    
    return { 
      success: true, 
      message: `Matrícula reativada com sucesso na turma ${classGroup.name}` 
    };
  } catch (error) {
    console.error("Erro ao reativar matrícula:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 