import { createAdminClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";

/**
 * Obtém o usuário atual e valida autenticação
 */
export async function validateUserAuthentication() {
  const user = await getCurrentUser();
  if (!user) {
    return { success: false, errors: { _form: "Usuário não autenticado" } };
  }

  const tenantId = user.app_metadata?.tenant_id;
  if (!tenantId) {
    return { success: false, errors: { _form: "Tenant não identificado" } };
  }

  return { success: true, user, tenantId };
}

/**
 * Valida se um instrutor existe e está ativo
 */
export async function validateInstructor(instructorId: string, tenantId: string) {
  const supabase = await createAdminClient();
  
  const { data: instructor, error } = await supabase
    .from("users")
    .select("id, role, status")
    .eq("id", instructorId)
    .eq("tenant_id", tenantId)
    .single();

  if (error || !instructor || instructor.role !== "instructor" || instructor.status !== "active") {
    return { 
      success: false, 
      errors: { instructor_id: "Instrutor não encontrado ou inativo" } 
    };
  }

  return { success: true, instructor };
}

/**
 * Valida se uma filial existe
 */
export async function validateBranch(branchId: string, tenantId: string) {
  const supabase = await createAdminClient();
  
  const { data: branch, error } = await supabase
    .from("branches")
    .select("id")
    .eq("id", branchId)
    .eq("tenant_id", tenantId)
    .single();

  if (error || !branch) {
    return { 
      success: false, 
      errors: { branch_id: "Filial não encontrada" } 
    };
  }

  return { success: true, branch };
}

/**
 * Valida se uma turma existe e pertence ao tenant
 */
export async function validateClassGroup(classGroupId: string, tenantId: string) {
  const supabase = await createAdminClient();
  
  const { data: classGroup, error } = await supabase
    .from("class_groups")
    .select("*")
    .eq("id", classGroupId)
    .eq("tenant_id", tenantId)
    .single();

  if (error || !classGroup) {
    return { 
      success: false, 
      errors: { _form: "Turma não encontrada" } 
    };
  }

  return { success: true, classGroup };
}

/**
 * Valida se um aluno existe
 */
export async function validateStudent(studentId: string, tenantId: string) {
  const supabase = await createAdminClient();
  
  const { data: student, error } = await supabase
    .from("students")
    .select("id")
    .eq("id", studentId)
    .eq("tenant_id", tenantId)
    .single();

  if (error || !student) {
    return { 
      success: false, 
      errors: { student_id: "Aluno não encontrado" } 
    };
  }

  return { success: true, student };
} 