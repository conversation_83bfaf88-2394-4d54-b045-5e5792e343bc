"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/services/supabase/server";
import { TransferStudentsSchema } from "../schemas/class-group-enrollment";
import { validateUserAuthentication } from "./shared/validation-helpers";

/**
 * Transfere alunos entre turmas
 */
export async function transferStudents(data: unknown) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    const validationResult = TransferStudentsSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const { from_class_group_id, to_class_group_id, student_ids, notes } = validationResult.data;
    const supabase = await createAdminClient();

    // Verificar se as turmas existem
    const { data: classGroups, error: classGroupsError } = await supabase
      .from("class_groups")
      .select("id, max_capacity, is_active")
      .in("id", [from_class_group_id, to_class_group_id])
      .eq("tenant_id", tenantId);

    if (classGroupsError || !classGroups || classGroups.length !== 2) {
      return { success: false, errors: { _form: "Uma ou ambas as turmas não foram encontradas" } };
    }

    const destinationGroup = classGroups.find(g => g.id === to_class_group_id);
    if (!destinationGroup?.is_active) {
      return { success: false, errors: { to_class_group_id: "Turma de destino está inativa" } };
    }

    // Verificar capacidade da turma de destino
    if (destinationGroup.max_capacity) {
      const { count: currentEnrollments } = await supabase
        .from("class_group_enrollments")
        .select("*", { count: "exact", head: true })
        .eq("class_group_id", to_class_group_id)
        .eq("status", "active");

      if (currentEnrollments) {
        const availableSpots = destinationGroup.max_capacity - currentEnrollments;
        if (student_ids.length > availableSpots) {
          return { 
            success: false, 
            errors: { _form: `Apenas ${availableSpots} vagas disponíveis na turma de destino` } 
          };
        }
      }
    }

    // Verificar se os alunos estão matriculados na turma de origem
    const { data: currentEnrollments, error: enrollmentError } = await supabase
      .from("class_group_enrollments")
      .select("id, student_id")
      .eq("class_group_id", from_class_group_id)
      .eq("status", "active")
      .in("student_id", student_ids);

    if (enrollmentError || !currentEnrollments || currentEnrollments.length !== student_ids.length) {
      return { success: false, errors: { _form: "Nem todos os alunos estão matriculados na turma de origem" } };
    }

    // Executar transferência em transação
    const { error: updateError } = await supabase
      .from("class_group_enrollments")
      .update({ 
        status: "transferred",
        updated_at: new Date().toISOString(),
      })
      .eq("class_group_id", from_class_group_id)
      .in("student_id", student_ids)
      .eq("status", "active");

    if (updateError) {
      console.error("Erro ao atualizar matrículas de origem:", updateError);
      return { success: false, errors: { _form: "Erro ao transferir alunos" } };
    }

    // Criar novas matrículas
    const newEnrollments = student_ids.map((student_id: string) => ({
      class_group_id: to_class_group_id,
      student_id,
      tenant_id: tenantId,
      enrollment_date: new Date().toISOString(),
      status: "active" as const,
      notes,
    }));

    const { data: transfers, error: insertError } = await supabase
      .from("class_group_enrollments")
      .insert(newEnrollments)
      .select();

    if (insertError) {
      console.error("Erro ao criar novas matrículas:", insertError);
      // Reverter mudanças
      await supabase
        .from("class_group_enrollments")
        .update({ status: "active" })
        .eq("class_group_id", from_class_group_id)
        .in("student_id", student_ids)
        .eq("status", "transferred");
      
      return { success: false, errors: { _form: "Erro ao transferir alunos" } };
    }

    revalidatePath("/aulas");
    return { 
      success: true, 
      data: transfers, 
      message: `${student_ids.length} alunos transferidos com sucesso` 
    };
  } catch (error) {
    console.error("Erro ao transferir alunos:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 