'use server';

import { z } from 'zod';
import { createAdminClient } from '@/services/supabase/server';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';

const UnenrollMultipleStudentsSchema = z.object({
  enrollmentIds: z.array(z.string().uuid()).min(1, 'Pelo menos uma matrícula deve ser selecionada'),
  reason: z.string().optional(),
});

interface UnenrollmentResult {
  success: boolean;
  enrollmentId: string;
  error?: string;
}

export async function unenrollMultipleStudents(data: unknown) {
  // 1. Validação de autenticação e tenant
  const user = await getCurrentUser();
  if (!user) {
    return { success: false, errors: { _form: 'Usuário não autenticado' } };
  }
  const tenantId = user.app_metadata?.tenant_id;
  if (!tenantId) {
    return { success: false, errors: { _form: 'Tenant não identificado' } };
  }

  // 2. Validação de dados de entrada
  const validationResult = UnenrollMultipleStudentsSchema.safeParse(data);
  if (!validationResult.success) {
    return { success: false, errors: validationResult.error.format() };
  }

  const { enrollmentIds, reason } = validationResult.data;
  const supabase = await createAdminClient();
  
  const results: UnenrollmentResult[] = [];

  // 3. Processar cada matrícula
  for (const enrollmentId of enrollmentIds) {
    try {
      // Verificar se a matrícula pertence ao tenant
      const { data: enrollment, error: fetchError } = await supabase
        .from('class_group_enrollments')
        .select('id, status')
        .eq('id', enrollmentId)
        .eq('tenant_id', tenantId)
        .single();

      if (fetchError || !enrollment) {
        results.push({ success: false, enrollmentId, error: 'Matrícula não encontrada' });
        continue;
      }

      // Deletar a matrícula
      const { error: deleteError } = await supabase
        .from('class_group_enrollments')
        .delete()
        .eq('id', enrollmentId);

      if (deleteError) {
        results.push({ success: false, enrollmentId, error: 'Falha ao deletar matrícula' });
      } else {
        results.push({ success: true, enrollmentId });
      }
    } catch (error) {
      results.push({ success: false, enrollmentId, error: 'Erro inesperado' });
    }
  }
  
  const successCount = results.filter(r => r.success).length;
  const hasErrors = results.some(r => !r.success);

  // 4. Retornar resultado
  return {
    success: !hasErrors,
    data: {
      results,
      successCount,
      totalCount: enrollmentIds.length,
    }
  };
} 