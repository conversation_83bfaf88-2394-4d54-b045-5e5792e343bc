"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/services/supabase/server";
import { z } from "zod";
import { validateUserAuthentication } from "./shared/validation-helpers";

const UnenrollStudentSchema = z.object({
  enrollment_id: z.string().uuid({ message: 'ID da matrícula é obrigatório' }),
  reason: z.string().max(500).optional(),
});

/**
 * Remove completamente um aluno de uma turma
 * Esta ação deleta o registro de matrícula permanentemente
 */
export async function unenrollStudent(data: unknown) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    const validationResult = UnenrollStudentSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const { enrollment_id, reason } = validationResult.data;
    const supabase = await createAdminClient();

    // Verificar se a matrícula existe
    const { data: enrollment, error: enrollmentError } = await supabase
      .from("class_group_enrollments")
      .select(`
        id,
        status,
        class_group_id,
        student_id,
        students!students_user_id_fkey!inner(user_id),
        class_groups!inner(name)
      `)
      .eq("id", enrollment_id)
      .eq("tenant_id", tenantId)
      .single();

    if (enrollmentError || !enrollment) {
      return { success: false, errors: { _form: "Matrícula não encontrada" } };
    }

    if (enrollment.status !== "active") {
      return { success: false, errors: { _form: "Matrícula não está ativa" } };
    }

    // Registrar o motivo do cancelamento em uma nota antes de deletar (opcional)
    if (reason) {
      await supabase
        .from("class_group_enrollments")
        .update({
          notes: `Cancelada: ${reason}`,
          updated_at: new Date().toISOString(),
        })
        .eq("id", enrollment_id)
        .eq("tenant_id", tenantId);
    }

    // Remover completamente a matrícula
    const { error } = await supabase
      .from("class_group_enrollments")
      .delete()
      .eq("id", enrollment_id)
      .eq("tenant_id", tenantId);

    if (error) {
      console.error("Erro ao remover matrícula:", error);
      return { success: false, errors: { _form: "Erro ao remover matrícula" } };
    }

    revalidatePath("/aulas");
    revalidatePath(`/perfil/${(enrollment.students as any).user_id}`);
    
    return { 
      success: true, 
      message: `Aluno removido da turma ${(enrollment.class_groups as any).name} com sucesso` 
    };
  } catch (error) {
    console.error("Erro ao remover matrícula:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 