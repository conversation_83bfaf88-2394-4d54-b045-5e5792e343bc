"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/services/supabase/server";
import { z } from "zod";
import { validateUserAuthentication, validateClassGroup } from "./shared/validation-helpers";

const UpdateEnrollmentStatusSchema = z.object({
  enrollment_id: z.string().uuid({ message: 'ID da matrícula é obrigatório' }),
  status: z.enum(['active', 'paused', 'cancelled'], { message: 'Status inválido' }),
  notes: z.string().optional(),
});

function mapStatusToDatabase(interfaceStatus: 'active' | 'paused' | 'cancelled'): 'active' | 'inactive' | 'paused' {
  const statusMap = {
    active: 'active',
    paused: 'paused', 
    cancelled: 'inactive'
  } as const;

  return statusMap[interfaceStatus];
}

/**
 * Atualiza o status de uma matrícula
 */
export async function updateEnrollmentStatus(data: unknown) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    const validationResult = UpdateEnrollmentStatusSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const { enrollment_id, status, notes } = validationResult.data;
    const supabase = await createAdminClient();

    // Verificar se a matrícula existe e pertence ao tenant
    const { data: enrollment, error: checkError } = await supabase
      .from("class_group_enrollments")
      .select("id, class_group_id, student_id, status")
      .eq("id", enrollment_id)
      .eq("tenant_id", tenantId)
      .single();

    if (checkError || !enrollment) {
      return { success: false, errors: { _form: "Matrícula não encontrada" } };
    }

    // Verificar se a turma existe
    const classGroupValidation = await validateClassGroup(enrollment.class_group_id, tenantId);
    if (!classGroupValidation.success) {
      return classGroupValidation;
    }

    // Mapear o status da interface para o status do banco
    const databaseStatus = mapStatusToDatabase(status);

    // Atualizar o status da matrícula
    const { error: updateError } = await supabase
      .from("class_group_enrollments")
      .update({
        status: databaseStatus,
        notes,
        updated_at: new Date().toISOString()
      })
      .eq("id", enrollment_id);

    if (updateError) {
      console.error("Erro ao atualizar status da matrícula:", updateError);
      return { success: false, errors: { _form: "Erro ao atualizar status da matrícula" } };
    }

    revalidatePath("/aulas");

    const statusMessages = {
      active: "Matrícula ativada",
      paused: "Matrícula pausada",
      cancelled: "Matrícula cancelada"
    };

    return {
      success: true,
      message: statusMessages[status] + " com sucesso"
    };
  } catch (error) {
    console.error("Erro ao atualizar status da matrícula:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 