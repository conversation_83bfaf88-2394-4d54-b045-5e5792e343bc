import { z } from 'zod';
import { 
  ClassGroupCategoryEnum, 
  BeltColorEnum 
} from './enums';
import { 
  RecurrencePatternSchema, 
  PaginationSchema,
  SortOrderSchema,
  SearchFilterSchema,
  validateDateRange,
  validateAgeRange
} from './shared';

// Schema base para Class Group
const BaseClassGroupSchema = z.object({
  id: z.string().uuid().optional(),
  tenant_id: z.string().uuid(),
  branch_id: z.string().uuid(),
  instructor_id: z.string().uuid(),
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(100),
  description: z.string().max(500).optional(),
  category: ClassGroupCategoryEnum.optional(),
  min_age: z.number().min(3).max(80).nullable(),
  max_age: z.number().min(3).max(80).nullable(),
  min_belt_level: BeltColorEnum.optional(),
  max_belt_level: BeltColorEnum.optional(),
  max_capacity: z.number().min(1).max(100).optional(),
  allow_waitlist: z.boolean().default(true).optional(),
  start_date: z.string().datetime().optional(),
  end_date: z.string().datetime().optional(),
  recurrence_pattern: RecurrencePatternSchema.optional(),
  is_active: z.boolean().default(true),
  metadata: z.record(z.any()).optional(),
});

// Schema completo com validações cruzadas
export const ClassGroupSchema = BaseClassGroupSchema.refine(data => {
  return validateAgeRange(data.min_age, data.max_age);
}, {
  message: 'Idade máxima deve ser maior ou igual à idade mínima',
  path: ['max_age']
}).refine(data => {
  return validateDateRange(data.start_date, data.end_date);
}, {
  message: 'Data final deve ser posterior à data inicial',
  path: ['end_date']
});

// Schema para criação (sem campos auto-gerados)
export const CreateClassGroupSchema = BaseClassGroupSchema.omit({ 
  id: true,
  tenant_id: true 
}).extend({
  unlimited_capacity: z.boolean().default(false),
}).refine(data => {
  return validateAgeRange(data.min_age, data.max_age);
}, {
  message: 'Idade máxima deve ser maior ou igual à idade mínima',
  path: ['max_age']
}).refine(data => {
  return validateDateRange(data.start_date, data.end_date);
}, {
  message: 'Data final deve ser posterior à data inicial',
  path: ['end_date']
}).refine(data => {
  // Validação: se capacidade ilimitada estiver marcada, max_capacity deve ser null/undefined
  if (data.unlimited_capacity) {
    return data.max_capacity === null || data.max_capacity === undefined;
  }
  return true;
}, {
  message: 'Capacidade deve ser vazia quando capacidade ilimitada está marcada',
  path: ['max_capacity']
}).refine(data => {
  // Validação: se capacidade não é ilimitada, max_capacity deve ser fornecida e válida
  if (!data.unlimited_capacity) {
    return data.max_capacity !== null && 
           data.max_capacity !== undefined && 
           typeof data.max_capacity === 'number' && 
           data.max_capacity >= 1;
  }
  return true;
}, {
  message: 'Capacidade máxima é obrigatória quando capacidade ilimitada não está marcada',
  path: ['max_capacity']
}).refine(data => {
  // Validação: quando lista de espera está desabilitada e capacidade não é ilimitada, deve ser pelo menos 5
  if (!data.allow_waitlist && !data.unlimited_capacity && data.max_capacity !== null && data.max_capacity !== undefined) {
    return data.max_capacity >= 5;
  }
  return true;
}, {
  message: 'Quando a lista de espera está desabilitada, a capacidade mínima deve ser de 5 alunos',
  path: ['max_capacity']
});

// Schema para formulário (aceita strings e converte para numbers)
export const CreateClassGroupFormSchema = z.object({
  branch_id: z.string().uuid(),
  instructor_id: z.string().uuid(),
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(100),
  description: z.string().max(500).optional(),
  category: ClassGroupCategoryEnum.optional(),
  unlimited_capacity: z.boolean().default(false),
  min_age: z.union([
    z.number().int('Idade deve ser um número inteiro').min(0).max(100),
    z.string().transform((val) => 
      val && val.trim() !== '' ? parseInt(val) : null
    ),
    z.null(),
    z.undefined().transform(() => null)
  ]).nullable(),
  max_age: z.union([
    z.number().int('Idade deve ser um número inteiro').min(0).max(100),
    z.string().transform((val) => 
      val && val.trim() !== '' ? parseInt(val) : null
    ),
    z.null(),
    z.undefined().transform(() => null)
  ]).nullable(),
  min_belt_level: BeltColorEnum.optional(),
  max_belt_level: BeltColorEnum.optional(),
  max_capacity: z.union([
    z.number().int('Capacidade deve ser um número inteiro').min(1, 'Capacidade deve ser pelo menos 1'),
    z.string().transform((val) => {
      if (val === '' || val === null || val === undefined) return null;
      const parsed = Number(val);
      return isNaN(parsed) ? null : parsed;
    }).nullable(),
    z.null(),
    z.undefined().transform(() => null)
  ]).nullable(),
  allow_waitlist: z.boolean().default(true).optional(),
  start_date: z.string().datetime().optional(),
  end_date: z.string().datetime().optional(),
  recurrence_pattern: RecurrencePatternSchema.optional(),
  is_active: z.boolean().default(true),
  metadata: z.record(z.any()).optional(),
}).refine(data => {
  return validateAgeRange(data.min_age, data.max_age);
}, {
  message: 'Idade máxima deve ser maior ou igual à idade mínima',
  path: ['max_age']
}).refine(data => {
  return validateDateRange(data.start_date, data.end_date);
}, {
  message: 'Data final deve ser posterior à data inicial',
  path: ['end_date']
}).refine(data => {
  // Validação: se capacidade ilimitada estiver marcada, max_capacity deve ser null
  if (data.unlimited_capacity) {
    return data.max_capacity === null || data.max_capacity === undefined;
  }
  return true;
}, {
  message: 'Capacidade deve ser vazia quando capacidade ilimitada está marcada',
  path: ['max_capacity']
}).refine(data => {
  // Validação: se capacidade não é ilimitada, max_capacity deve ser fornecida e válida
  if (!data.unlimited_capacity) {
    return data.max_capacity !== null && 
           data.max_capacity !== undefined && 
           typeof data.max_capacity === 'number' && 
           data.max_capacity >= 1;
  }
  return true;
}, {
  message: 'Capacidade máxima é obrigatória quando capacidade ilimitada não está marcada',
  path: ['max_capacity']
}).refine(data => {
  // Validação: quando lista de espera está desabilitada e capacidade não é ilimitada, deve ser pelo menos 5
  if (!data.allow_waitlist && !data.unlimited_capacity && data.max_capacity !== null && data.max_capacity !== undefined) {
    return data.max_capacity >= 5;
  }
  return true;
}, {
  message: 'Quando a lista de espera está desabilitada, a capacidade mínima deve ser de 5 alunos',
  path: ['max_capacity']
});

// Schema para atualização (campos parciais)
export const UpdateClassGroupSchema = BaseClassGroupSchema.partial().omit({ 
  id: true,
  tenant_id: true,
  branch_id: true 
});

// Schema para formulário de atualização (aceita strings e converte para numbers)
export const UpdateClassGroupFormSchema = z.object({
  id: z.string().uuid(),
  instructor_id: z.string().uuid().optional(),
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(100).optional(),
  description: z.string().max(500).optional(),
  category: ClassGroupCategoryEnum.optional(),
  unlimited_capacity: z.boolean().optional(),
  min_age: z.union([
    z.number().int('Idade deve ser um número inteiro').min(0).max(100),
    z.string().transform((val) => 
      val && val.trim() !== '' ? parseInt(val) : null
    ),
    z.null(),
    z.undefined().transform(() => null)
  ]).nullable().optional(),
  max_age: z.union([
    z.number().int('Idade deve ser um número inteiro').min(0).max(100),
    z.string().transform((val) => 
      val && val.trim() !== '' ? parseInt(val) : null
    ),
    z.null(),
    z.undefined().transform(() => null)
  ]).nullable().optional(),
  min_belt_level: BeltColorEnum.optional(),
  max_belt_level: BeltColorEnum.optional(),
  max_capacity: z.union([
    z.number().int('Capacidade deve ser um número inteiro').min(1, 'Capacidade deve ser pelo menos 1'),
    z.string().transform((val) => {
      if (val === '' || val === null || val === undefined) return null;
      const parsed = Number(val);
      return isNaN(parsed) ? null : parsed;
    }).nullable(),
    z.null(),
    z.undefined().transform(() => null)
  ]).nullable().optional(),
  allow_waitlist: z.boolean().optional(),
  start_date: z.string().datetime().optional(),
  end_date: z.string().datetime().optional(),
  recurrence_pattern: RecurrencePatternSchema.optional(),
  is_active: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
}).refine(data => {
  return validateAgeRange(data.min_age, data.max_age);
}, {
  message: 'Idade máxima deve ser maior ou igual à idade mínima',
  path: ['max_age']
}).refine(data => {
  return validateDateRange(data.start_date, data.end_date);
}, {
  message: 'Data final deve ser posterior à data inicial',
  path: ['end_date']
}).refine(data => {
  // Validação: se capacidade ilimitada estiver marcada, max_capacity deve ser null
  if (data.unlimited_capacity) {
    return data.max_capacity === null || data.max_capacity === undefined;
  }
  return true;
}, {
  message: 'Capacidade deve ser vazia quando capacidade ilimitada está marcada',
  path: ['max_capacity']
}).refine(data => {
  // Validação: se capacidade não é ilimitada e unlimited_capacity estiver definida, max_capacity deve ser válida
  if (data.unlimited_capacity === false && data.max_capacity !== undefined) {
    return data.max_capacity !== null && 
           typeof data.max_capacity === 'number' && 
           data.max_capacity >= 1;
  }
  return true;
}, {
  message: 'Capacidade máxima é obrigatória quando capacidade ilimitada não está marcada',
  path: ['max_capacity']
}).refine(data => {
  // Validação: quando lista de espera está desabilitada e capacidade não é ilimitada, deve ser pelo menos 5
  if (data.allow_waitlist === false && data.unlimited_capacity === false && data.max_capacity !== null && data.max_capacity !== undefined) {
    return data.max_capacity >= 5;
  }
  return true;
}, {
  message: 'Quando a lista de espera está desabilitada, a capacidade mínima deve ser de 5 alunos',
  path: ['max_capacity']
});

// Schema para filtros de busca
export const ClassGroupFilterSchema = SearchFilterSchema.extend({
  instructor_id: z.string().uuid().optional(),
  branch_id: z.string().uuid().optional(),
  category: ClassGroupCategoryEnum.optional(),
  is_active: z.boolean().optional(),
  min_belt_level: BeltColorEnum.optional(),
  max_belt_level: BeltColorEnum.optional(),
  has_availability: z.boolean().optional(),
  start_date_from: z.string().datetime().optional(),
  start_date_to: z.string().datetime().optional(),
}).merge(PaginationSchema).extend({
  sort_by: z.enum(['name', 'created_at', 'start_date', 'instructor_name']).default('name'),
  sort_order: SortOrderSchema,
});

// Schema para mudança de status
export const ChangeClassGroupStatusSchema = z.object({
  id: z.string().uuid({ message: 'ID da turma é obrigatório' }),
  is_active: z.boolean(),
});

// Schema para estatísticas
export const ClassGroupStatsRequestSchema = z.object({
  class_group_id: z.string().uuid().optional(),
  period_start: z.string().datetime().optional(),
  period_end: z.string().datetime().optional(),
});

export const ClassGroupStatsSchema = z.object({
  total_groups: z.number(),
  active_groups: z.number(),
  inactive_groups: z.number(),
  total_enrollments: z.number(),
  total_waitlist: z.number(),
  average_capacity_usage: z.number(),
  groups_by_category: z.record(z.number()),
});

// Tipos TypeScript inferidos
export type ClassGroup = z.infer<typeof ClassGroupSchema>;
export type CreateClassGroup = z.infer<typeof CreateClassGroupSchema>;
export type UpdateClassGroup = z.infer<typeof UpdateClassGroupSchema>;
export type CreateClassGroupForm = z.infer<typeof CreateClassGroupFormSchema>;
export type UpdateClassGroupForm = z.infer<typeof UpdateClassGroupFormSchema>;
export type ClassGroupFilter = z.infer<typeof ClassGroupFilterSchema>;
export type ChangeClassGroupStatus = z.infer<typeof ChangeClassGroupStatusSchema>;
export type ClassGroupStatsRequest = z.infer<typeof ClassGroupStatsRequestSchema>;
export type ClassGroupStats = z.infer<typeof ClassGroupStatsSchema>; 