import { z } from 'zod';
import { ClassWaitlistStatusEnum } from './enums';
import { PaginationSchema, SortOrderSchema, SearchFilterSchema } from './shared';

// Schema base para Class Waitlist
const BaseClassWaitlistSchema = z.object({
  id: z.string().uuid().optional(),
  tenant_id: z.string().uuid(),
  class_group_id: z.string().uuid(),
  student_id: z.string().uuid(),
  position: z.number().positive(),
  added_date: z.string().datetime().optional(),
  notified_date: z.string().datetime().optional(),
  status: ClassWaitlistStatusEnum.default('waiting'),
  notes: z.string().max(500).optional(),
  metadata: z.record(z.any()).optional(),
});

// Schema completo
export const ClassWaitlistSchema = BaseClassWaitlistSchema;

// Schema para criação (posição será calculada automaticamente)
export const CreateClassWaitlistSchema = BaseClassWaitlistSchema.omit({ 
  id: true,
  tenant_id: true,
  position: true
});

// Schema para atualização
export const UpdateClassWaitlistSchema = BaseClassWaitlistSchema.partial().omit({ 
  id: true,
  tenant_id: true,
  class_group_id: true,
  student_id: true,
  position: true 
});

// Schema para adicionar à lista de espera
export const AddToWaitlistSchema = z.object({
  class_group_id: z.string().uuid({ message: 'ID da turma é obrigatório' }),
  student_id: z.string().uuid({ message: 'ID do aluno é obrigatório' }),
  notes: z.string().max(500).optional(),
});

// Schema para notificar pessoas na lista de espera
export const NotifyWaitlistSchema = z.object({
  waitlist_ids: z.array(z.string().uuid()).min(1, { message: 'Selecione pelo menos uma pessoa' }),
  message: z.string().max(1000).optional(),
});

// Schema para mover posição na lista
export const MoveWaitlistPositionSchema = z.object({
  id: z.string().uuid({ message: 'ID da lista de espera é obrigatório' }),
  new_position: z.number().positive({ message: 'Nova posição deve ser positiva' }),
});

// Schema para filtros de lista de espera
export const ClassWaitlistFilterSchema = SearchFilterSchema.extend({
  class_group_id: z.string().uuid().optional(),
  student_id: z.string().uuid().optional(),
  status: ClassWaitlistStatusEnum.optional(),
  added_date_from: z.string().datetime().optional(),
  added_date_to: z.string().datetime().optional(),
}).merge(PaginationSchema).extend({
  sort_by: z.enum(['position', 'added_date', 'student_name', 'status']).default('position'),
  sort_order: SortOrderSchema,
});

// Tipos inferidos
export type ClassWaitlist = z.infer<typeof ClassWaitlistSchema>;
export type CreateClassWaitlist = z.infer<typeof CreateClassWaitlistSchema>;
export type UpdateClassWaitlist = z.infer<typeof UpdateClassWaitlistSchema>;
export type AddToWaitlist = z.infer<typeof AddToWaitlistSchema>;
export type NotifyWaitlist = z.infer<typeof NotifyWaitlistSchema>;
export type MoveWaitlistPosition = z.infer<typeof MoveWaitlistPositionSchema>;
export type ClassWaitlistFilter = z.infer<typeof ClassWaitlistFilterSchema>; 