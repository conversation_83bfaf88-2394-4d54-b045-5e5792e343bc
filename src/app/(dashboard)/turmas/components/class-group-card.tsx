'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  TooltipContent,
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Users, 
  MapPin, 
  Calendar,
  MoreHorizontal,
  UserPlus,
  Eye,
  Edit,
  GraduationCap
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ClassGroupWithDetails } from '../../aulas/types';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import Link from 'next/link';
import { BeltDisplay, beltColorTranslation } from '@/components/belt';

interface ClassGroupCardProps {
  classGroup: ClassGroupWithDetails;
}

export function ClassGroupCard({ classGroup }: ClassGroupCardProps) {
  const capacityPercentage = classGroup.capacity_usage_percentage || 0;
  const enrollmentCount = classGroup.current_enrollment_count || 0;
  const maxCapacity = classGroup.max_capacity || 0;
  const waitlistCount = classGroup._count.waitlist || 0;

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-green-500' : 'bg-red-500';
  };

  const getCategoryColor = (category: string | null) => {
    switch (category) {
      case 'kids': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'teens': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';
      case 'adults': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'seniors': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getCategoryLabel = (category: string | null) => {
    switch (category) {
      case 'kids': return 'Infantil';
      case 'teens': return 'Adolescente';
      case 'adults': return 'Adulto';
      case 'seniors': return 'Sênior';
      default: return 'Geral';
    }
  };

  return (
    <Card className="h-full transition-all duration-200 hover:shadow-md">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1 flex-1">
            <CardTitle className="text-lg font-semibold line-clamp-1">
              <Link 
                href={`/turmas/${classGroup.id}`}
                className="hover:text-primary transition-colors cursor-pointer"
              >
                {classGroup.name}
              </Link>
            </CardTitle>
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${getStatusColor(classGroup.is_active ?? false)}`} />
              <span className="text-xs text-muted-foreground">
                {classGroup.is_active ? 'Ativa' : 'Inativa'}
              </span>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem asChild>
                <Link href={`/turmas/${classGroup.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  Ver detalhes
                </Link>
              </DropdownMenuItem>
              
              <DropdownMenuItem asChild>
                <Link href={`/turmas/${classGroup.id}/alunos`}>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Gerenciar matrículas
                </Link>
              </DropdownMenuItem>
              
              <DropdownMenuItem asChild>
                <Link href={`/turmas/${classGroup.id}/editar`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Editar turma
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Badge de categoria */}
        {classGroup.category && (
          <Badge 
            variant="secondary" 
            className={getCategoryColor(classGroup.category)}
          >
            {getCategoryLabel(classGroup.category)}
          </Badge>
        )}

        {/* Descrição */}
        {classGroup.description && (
          <p className="text-sm text-muted-foreground line-clamp-2">
            {classGroup.description}
          </p>
        )}

        {/* Instructor */}
        {classGroup.instructor && (
          <div className="flex items-center gap-2 text-sm">
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
            <span>{classGroup.instructor.full_name}</span>
          </div>
        )}

        {/* Branch */}
        {classGroup.branch && (
          <div className="flex items-center gap-2 text-sm">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <span>{classGroup.branch.name}</span>
          </div>
        )}

        {/* Capacidade */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              Matrículas
            </span>
            <span className="font-medium">
              {enrollmentCount}{maxCapacity > 0 && `/${maxCapacity}`}
            </span>
          </div>
          
          {maxCapacity > 0 && (
            <div className="w-full bg-muted rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  capacityPercentage >= 90 
                    ? 'bg-red-500' 
                    : capacityPercentage >= 75 
                      ? 'bg-orange-500' 
                      : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(capacityPercentage, 100)}%` }}
              />
            </div>
          )}
        </div>

        {/* Lista de espera */}
        {waitlistCount > 0 && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Lista de espera</span>
            <Badge variant="outline" className="text-xs">
              {waitlistCount}
            </Badge>
          </div>
        )}

        {/* Faixas */}
        {classGroup.min_belt_level !== null && classGroup.max_belt_level !== null && 
         Number(classGroup.min_belt_level) >= 0 && Number(classGroup.max_belt_level) >= 0 && (
          <TooltipProvider>
            <TooltipRoot>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-muted-foreground">Faixas:</span>
                  <div className="flex items-center gap-1">
                    <BeltDisplay 
                      belt={Object.keys(beltColorTranslation)[Number(classGroup.min_belt_level)] as keyof typeof beltColorTranslation}
                      stripes={0}
                      size="sm"
                    />
                    <span className="text-xs text-muted-foreground">até</span>
                    <BeltDisplay 
                      belt={Object.keys(beltColorTranslation)[Number(classGroup.max_belt_level)] as keyof typeof beltColorTranslation}
                      stripes={0}
                      size="sm"
                    />
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  De {beltColorTranslation[Object.keys(beltColorTranslation)[Number(classGroup.min_belt_level)] as keyof typeof beltColorTranslation]} 
                  até {beltColorTranslation[Object.keys(beltColorTranslation)[Number(classGroup.max_belt_level)] as keyof typeof beltColorTranslation]}
                </p>
              </TooltipContent>
            </TooltipRoot>
          </TooltipProvider>
        )}

        {/* Data de início */}
        {classGroup.start_date && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>
              Iniciada {formatDistanceToNow(new Date(classGroup.start_date), { 
                addSuffix: true, 
                locale: ptBR 
              })}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 