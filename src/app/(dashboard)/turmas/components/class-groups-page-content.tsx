'use client';

import { useClassGroups } from '@/hooks/turmas/use-class-groups';
import { useBranches } from '@/hooks/turmas/use-branches';
import { useInstructorsWithClassGroups } from '@/hooks/turmas/use-instructors-with-class-groups';
import type { ClassGroupFilters as ClassGroupFiltersType, ClassGroupPagination } from '@/hooks/turmas/use-class-groups';

import { ClassGroupFilters } from '../../aulas/components/filters/class-group-filters';
import { ListSkeleton } from '../../aulas/components/list';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ClassGroupsList } from './class-groups-list';

export function ClassGroupsPageContent() {
  // Hook para buscar filiais
  const { data: branches = [], isLoading: isLoadingBranches, error: branchesError } = useBranches();
  
  // Hook para buscar instrutores que têm turmas
  const { data: instructorsData = [], isLoading: isLoadingInstructors, error: instructorsError } = useInstructorsWithClassGroups();

  // Configuração inicial sem filtros aplicados
  const initialFilters: ClassGroupFiltersType = {};
  const initialPagination: ClassGroupPagination = {
    page: 1,
    limit: 12,
    sort_by: 'name',
    sort_order: 'asc',
  };

  const {
    classGroups,
    pagination,
    isLoading,
    isFetching,
    error,
    hasActiveFilters,
    refetch,
    filters,
    setFilters,
    clearFilters,
    stats,
    goToPage
  } = useClassGroups(initialFilters, initialPagination);

  // Transformar dados dos instrutores para o formato esperado pelo filtro
  const instructors = instructorsData.map(instructor => ({
    id: instructor.id,
    name: instructor.full_name
  }));

  // Loading state inicial - mostra skeleton apenas no primeiro carregamento
  if (isLoading) {
    return (
      <div className="space-y-4">
        <Card className="p-4">
          <div className="h-20 bg-muted animate-pulse rounded" />
        </Card>
        <ListSkeleton />
      </div>
    );
  }

  // Error state para filiais
  if (branchesError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>
            Erro ao carregar filiais: {branchesError instanceof Error ? branchesError.message : 'Erro desconhecido'}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="ml-4"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Tentar novamente
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  // Error state para instrutores
  if (instructorsError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>
            Erro ao carregar instrutores: {instructorsError instanceof Error ? instructorsError.message : 'Erro desconhecido'}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="ml-4"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Tentar novamente
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filtros - sempre visíveis, não afetados por loading states */}
      <Card className="p-4">
        <ClassGroupFilters
          instructors={instructors}
          branches={branches}
          filters={filters}
          onFiltersChange={setFilters}
          onClearFilters={clearFilters}
        />
      </Card>

      {/* Estatísticas - sempre visíveis quando há dados */}
      {stats.total > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <p className="text-xs text-muted-foreground">Total</p>
          </Card>
          <Card className="p-4">
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            <p className="text-xs text-muted-foreground">Ativas</p>
          </Card>
          <Card className="p-4">
            <div className="text-2xl font-bold text-orange-600">{stats.withAvailability}</div>
            <p className="text-xs text-muted-foreground">Com vagas</p>
          </Card>
          <Card className="p-4">
            <div className="text-2xl font-bold text-red-600">{stats.full}</div>
            <p className="text-xs text-muted-foreground">Lotadas</p>
          </Card>
        </div>
      )}

      {/* Lista de turmas - sem Suspense, usando estados de loading controlados */}
      <Card className="p-4">
        <ClassGroupsList
          classGroups={classGroups}
          pagination={pagination}
          isLoading={isLoading}
          isFetching={isFetching}
          error={error as Error | null}
          hasActiveFilters={hasActiveFilters}
          refetch={refetch}
          goToPage={goToPage}
          onClearFilters={clearFilters}
        />
      </Card>
    </div>
  );
} 