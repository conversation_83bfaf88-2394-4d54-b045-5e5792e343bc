'use client';

import { useClassGroups } from '@/hooks/turmas/use-class-groups';
import { useBranches } from '@/hooks/turmas/use-branches';
import type { ClassGroupFilters, ClassGroupPagination } from '@/hooks/turmas/use-class-groups';
import type { ClassGroupWithDetails, PaginatedResult } from '../../aulas/types';

import { ClassGroupFilters } from '../../aulas/components/filters/class-group-filters';
import { ClassGroupCard } from './class-group-card';
import { EmptyState } from '../../aulas/components/list/empty-state';
import { ListSkeleton } from '../../aulas/components/list';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, RefreshCw, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useState, useMemo } from 'react';
import { ViewToggle, ViewMode } from '../../aulas/components/list/view-toggle';
import { ClassGroupsTable } from '../../aulas/components/list/class-groups-table';

// Mock data para instrutores - será substituído por hook real
const mockInstructors = [
  { id: '1', name: 'Professor João' },
  { id: '2', name: 'Professor Maria' },
  { id: '3', name: 'Professor Carlos' },
];

// Componente de paginação simples
interface SimplePaginationProps {
  currentPage: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  onPageChange: (page: number) => void;
}

function SimplePagination({ currentPage, totalPages, hasNext, hasPrev, onPageChange }: SimplePaginationProps) {
  if (totalPages <= 1) {
    return null;
  }

  return (
    <div className="flex items-center justify-between px-2">
      <div className="flex-1 text-sm text-muted-foreground">
        Página {currentPage} de {totalPages}
      </div>
      
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={!hasPrev}
          className="gap-1"
        >
          <ChevronLeft className="h-4 w-4" />
          Anterior
        </Button>
        
        <div className="flex items-center space-x-1">
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            let pageNumber;
            
            if (totalPages <= 5) {
              pageNumber = i + 1;
            } else if (currentPage <= 3) {
              pageNumber = i + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNumber = totalPages - 4 + i;
            } else {
              pageNumber = currentPage - 2 + i;
            }
            
            return (
              <Button
                key={pageNumber}
                variant={currentPage === pageNumber ? "default" : "outline"}
                size="sm"
                onClick={() => onPageChange(pageNumber)}
                className="w-9 h-9 p-0"
              >
                {pageNumber}
              </Button>
            );
          })}
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={!hasNext}
          className="gap-1"
        >
          Próximo
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

export function ClassGroupsPageWrapper() {
  const [viewMode, setViewMode] = useState<ViewMode>('card');

  // Hook para buscar filiais
  const { data: branches = [], isLoading: isLoadingBranches, error: branchesError } = useBranches();

  // Configuração inicial sem filtros aplicados
  const initialFilters: ClassGroupFilters = {};
  const initialPagination: ClassGroupPagination = {
    page: 1,
    limit: 12,
    sort_by: 'name',
    sort_order: 'asc',
  };

  const {
    classGroups,
    pagination,
    isLoading,
    isFetching,
    error,
    hasActiveFilters,
    refetch,
    filters,
    setFilters,
    clearFilters,
    stats,
    goToPage
  } = useClassGroups(initialFilters, initialPagination);

  // Loading state inicial
  if (isLoading) {
    return (
      <div className="space-y-4">
        <Card className="p-4">
          <div className="h-20 bg-muted animate-pulse rounded" />
        </Card>
        <ListSkeleton />
      </div>
    );
  }

  // Error state
  if (error || branchesError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>
            {branchesError 
              ? `Erro ao carregar filiais: ${branchesError instanceof Error ? branchesError.message : 'Erro desconhecido'}`
              : error instanceof Error ? error.message : 'Erro ao carregar turmas'
            }
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isFetching}
            className="ml-4"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isFetching ? 'animate-spin' : ''}`} />
            Tentar novamente
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filtros */}
      <Card className="p-4">
        <ClassGroupFilters
          instructors={mockInstructors}
          branches={branches}
          filters={filters}
          onFiltersChange={setFilters}
          onClearFilters={clearFilters}
        />
      </Card>

      {/* Estatísticas */}
      {stats.total > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <p className="text-xs text-muted-foreground">Total</p>
          </Card>
          <Card className="p-4">
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            <p className="text-xs text-muted-foreground">Ativas</p>
          </Card>
          <Card className="p-4">
            <div className="text-2xl font-bold text-orange-600">{stats.withAvailability}</div>
            <p className="text-xs text-muted-foreground">Com vagas</p>
          </Card>
          <Card className="p-4">
            <div className="text-2xl font-bold text-red-600">{stats.full}</div>
            <p className="text-xs text-muted-foreground">Lotadas</p>
          </Card>
        </div>
      )}

      {/* Lista de turmas */}
      <Card className="p-4">
        <div className="space-y-4">
          {/* Cabeçalho da lista */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                Mostrando {classGroups.length} de {pagination?.total || 0} grupos de aulas
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                disabled={isFetching}
                className="gap-2"
                aria-label="Atualizar lista"
              >
                <RefreshCw 
                  className={`h-4 w-4 transition-transform ${isFetching ? 'animate-spin' : ''}`}
                />
                {isFetching ? 'Atualizando...' : 'Atualizar'}
              </Button>
            </div>
            
            <ViewToggle 
              mode={viewMode} 
              onModeChange={setViewMode} 
            />
          </div>

          {/* Conteúdo da lista */}
          {classGroups.length === 0 ? (
            <EmptyState hasFilters={hasActiveFilters} />
          ) : (
            <>
              {viewMode === 'card' ? (
                <div className={`grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 ${isFetching ? 'opacity-50 pointer-events-none' : ''}`}>
                  {classGroups.map((classGroup) => (
                    <ClassGroupCard 
                      key={classGroup.id} 
                      classGroup={classGroup}
                    />
                  ))}
                </div>
              ) : (
                <div className={isFetching ? 'opacity-50 pointer-events-none' : ''}>
                  <ClassGroupsTable 
                    groups={classGroups}
                  />
                </div>
              )}

              {/* Paginação */}
              {pagination && pagination.totalPages > 1 && (
                <SimplePagination
                  currentPage={pagination.page}
                  totalPages={pagination.totalPages}
                  hasNext={pagination.hasNext}
                  hasPrev={pagination.hasPrev}
                  onPageChange={goToPage}
                />
              )}
            </>
          )}
        </div>
      </Card>
    </div>
  );
} 