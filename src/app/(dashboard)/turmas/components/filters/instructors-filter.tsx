'use client';

import { useState } from 'react';
import { ChevronDown, Users } from 'lucide-react';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { UserAvatar } from '@/components/ui/user-avatar';
import { BeltDisplay } from '@/components/belt';
import { Skeleton } from '@/components/ui/skeleton';
import { useInstructorsWithClassGroups } from '@/hooks/turmas/use-instructors-with-class-groups';
import { cn } from '@/lib/utils';

interface InstructorsFilterProps {
  selectedInstructorId?: string;
  onChange: (instructorId: string | undefined) => void;
  className?: string;
}

export function InstructorsFilter({ 
  selectedInstructorId, 
  onChange, 
  className 
}: InstructorsFilterProps) {
  const [open, setOpen] = useState(false);
  const { data: instructors = [], isLoading, error } = useInstructorsWithClassGroups();

  const selectedInstructor = instructors.find(instructor => instructor.id === selectedInstructorId);

  const handleSelect = (instructorId: string) => {
    if (instructorId === selectedInstructorId) {
      onChange(undefined);
    } else {
      onChange(instructorId);
    }
    setOpen(false);
  };

  if (error) {
    return (
      <Button 
        variant="outline" 
        disabled
        className={cn("h-10 justify-start text-muted-foreground", className)}
      >
        <Users className="mr-2 h-4 w-4" />
        Erro ao carregar instrutores
      </Button>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("h-10 justify-between", className)}
          disabled={isLoading}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <Users className="h-4 w-4 flex-shrink-0" />
            {isLoading ? (
              <span className="text-muted-foreground">Carregando...</span>
            ) : selectedInstructor ? (
              <div className="flex items-center gap-2 min-w-0">
                <UserAvatar
                  src={selectedInstructor.avatar_url}
                  name={selectedInstructor.full_name}
                  size="sm"
                  className="flex-shrink-0"
                />
                <span className="truncate">
                  {selectedInstructor.full_name}
                </span>
                <BeltDisplay
                  belt={selectedInstructor.belt_color as any}
                  stripes={Math.max(0, (selectedInstructor.degree || 1) - 1)}
                  size="sm"
                  className="flex-shrink-0"
                />
              </div>
            ) : (
              <span className="text-muted-foreground">Filtrar por instrutor...</span>
            )}
          </div>
          <ChevronDown className="ml-2 h-4 w-4 opacity-50 flex-shrink-0" />
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-80 p-0" align="start">
        <Command>
          <CommandInput 
            placeholder="Buscar instrutor..." 
            className="h-9"
          />
          <CommandList>
            <CommandEmpty>
              {isLoading ? (
                <div className="py-6 space-y-2">
                  <Skeleton className="h-4 w-32 mx-auto" />
                  <Skeleton className="h-4 w-24 mx-auto" />
                </div>
              ) : (
                "Nenhum instrutor encontrado."
              )}
            </CommandEmpty>
            
            {!isLoading && instructors.length > 0 && (
              <CommandGroup>
                {instructors.map((instructor) => {
                  const isSelected = instructor.id === selectedInstructorId;
                  
                  return (
                    <CommandItem
                      key={instructor.id}
                      value={`${instructor.first_name} ${instructor.last_name}`}
                      onSelect={() => handleSelect(instructor.id)}
                      className={cn(
                        'flex items-center gap-3 py-3 px-2 cursor-pointer',
                        isSelected ? 'bg-accent' : ''
                      )}
                    >
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <UserAvatar
                          src={instructor.avatar_url}
                          name={instructor.full_name}
                          size="md"
                          className="flex-shrink-0"
                        />
                        
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">
                            {instructor.full_name}
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <BeltDisplay
                              belt={instructor.belt_color as any}
                              stripes={Math.max(0, (instructor.degree || 1) - 1)}
                              size="sm"
                              showTranslation
                            />
                          </div>
                        </div>
                      </div>
                      
                      {isSelected && (
                        <div className="flex h-4 w-4 items-center justify-center rounded-sm bg-primary text-primary-foreground">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            width="12"
                            height="12"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="3"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <polyline points="20 6 9 17 4 12" />
                          </svg>
                        </div>
                      )}
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
} 