'use client';

import React, { useEffect } from 'react';
import { Form } from '@/components/ui/form';
import { useTenantTheme } from '@/hooks/tenant/use-tenant-theme';
import { useClassGroupForm } from './hooks/use-class-group-form';
import {
  FormHeader,
  BasicInfoSection,
  InstructorBranchSection,
  ParticipationCriteriaSection,
  CapacityPeriodSection,
  RecurrencePatternSection,
  FormFooter,
} from './components';
import type { ClassGroupFormProps } from './types/class-group-form-types';

export default function ClassGroupForm({
  initialData,
  onSuccess,
  onCancel,
  instructors = [],
  branches = [],
}: ClassGroupFormProps) {
  const { primaryColor } = useTenantTheme();
  
  const {
    form,
    isSubmitting,
    showRecurrenceDetails,
    setShowRecurrenceDetails,
    onSubmit,
    handleCancel,
  } = useClassGroupForm({
    onSuccess,
    onCancel,
  });

  const isEditMode = false;

  // Monitorar mudanças no padrão de recorrência
  const recurrencePattern = form.watch('recurrence_pattern');
  useEffect(() => {
    setShowRecurrenceDetails(!!recurrencePattern);
  }, [recurrencePattern, setShowRecurrenceDetails]);

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className="space-y-6">
        <FormHeader
          isEditMode={isEditMode}
          isSubmitting={isSubmitting}
          primaryColor={primaryColor ?? undefined}
          onCancel={handleCancel}
        />

        <BasicInfoSection
          form={form}
          isEditMode={isEditMode}
          primaryColor={primaryColor ?? undefined}
        />

        <InstructorBranchSection
          form={form}
          isEditMode={isEditMode}
          primaryColor={primaryColor ?? undefined}
          instructors={instructors}
          branches={branches}
          initialData={initialData}
        />

        <ParticipationCriteriaSection
          form={form}
          isEditMode={isEditMode}
          primaryColor={primaryColor ?? undefined}
        />

        <CapacityPeriodSection
          form={form}
          isEditMode={isEditMode}
          primaryColor={primaryColor ?? undefined}
        />

        <RecurrencePatternSection
          form={form}
          isEditMode={isEditMode}
          primaryColor={primaryColor ?? undefined}
          showRecurrenceDetails={showRecurrenceDetails}
          setShowRecurrenceDetails={setShowRecurrenceDetails}
        />

        <FormFooter
          isEditMode={isEditMode}
          isSubmitting={isSubmitting}
          primaryColor={primaryColor ?? undefined}
          formErrors={form.formState.errors}
          onCancel={handleCancel}
        />
      </form>
    </Form>
  );
}