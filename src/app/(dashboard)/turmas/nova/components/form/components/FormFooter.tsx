import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';

interface FormFooterProps {
  isEditMode: boolean;
  isSubmitting: boolean;
  primaryColor?: string;
  formErrors?: any;
  onCancel: () => void;
}

export const FormFooter = ({
  isEditMode,
  isSubmitting,
  primaryColor,
  formErrors,
  onCancel,
}: FormFooterProps) => {
  return (
    <>
      {formErrors?._form && (
        <div className="flex items-center gap-2 p-4 border border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-900/50 rounded-lg">
          <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
          <p className="text-sm text-red-600 dark:text-red-400">
            {formErrors._form.message}
          </p>
        </div>
      )}

      <div className="flex flex-col-reverse sm:flex-row gap-2 sm:justify-end">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
          className="sm:w-auto"
        >
          Cancelar
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
          className="sm:w-auto bg-primary text-primary-foreground"
          style={primaryColor ? { backgroundColor: primaryColor } : undefined}
        >
          {isSubmitting 
            ? (isEditMode ? 'Atualizando...' : 'Criando...') 
            : (isEditMode ? 'Atualizar Grupo' : 'Criar Grupo')
          }
        </Button>
      </div>
    </>
  );
}; 