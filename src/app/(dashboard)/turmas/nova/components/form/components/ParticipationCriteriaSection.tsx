import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { GraduationCap } from 'lucide-react';
import { BELT_LEVELS } from '../constants/class-group-constants';
import type { FormSectionProps } from '../types/class-group-form-types';

// Helper para transformar valor do input em number ou null
const transformAgeValue = (value: string): number | null => {
  if (!value || value.trim() === '') return null;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? null : parsed;
};

export const ParticipationCriteriaSection = ({ form }: FormSectionProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <GraduationCap className="w-5 h-5" />
          Critérios de Participação
        </CardTitle>
        <CardDescription>
          Definição dos requisitos de idade e graduação
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <FormLabel>Faixa Etária</FormLabel>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="min_age"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Idade Mínima</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Deixe em branco para não limitar"
                      min={0}
                      max={100}
                      value={field.value === null ? '' : field.value || ''}
                      onChange={(e) => {
                        const transformedValue = transformAgeValue(e.target.value);
                        field.onChange(transformedValue);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="max_age"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Idade Máxima</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Deixe em branco para não limitar"
                      min={0}
                      max={100}
                      value={field.value === null ? '' : field.value || ''}
                      onChange={(e) => {
                        const transformedValue = transformAgeValue(e.target.value);
                        field.onChange(transformedValue);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormDescription>
            Deixe em branco para não restringir por idade
          </FormDescription>
        </div>

        <Separator />

        <div className="space-y-2">
          <FormLabel>Nível de Graduação</FormLabel>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="min_belt_level"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Graduação Mínima</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {BELT_LEVELS.map((belt) => (
                        <SelectItem key={belt.value} value={belt.value}>
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full border"
                              style={{ backgroundColor: belt.color }}
                            />
                            {belt.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="max_belt_level"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Graduação Máxima</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {BELT_LEVELS.map((belt) => (
                        <SelectItem key={belt.value} value={belt.value}>
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full border"
                              style={{ backgroundColor: belt.color }}
                            />
                            {belt.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormDescription>
            Deixe em branco para aceitar todas as graduações
          </FormDescription>
        </div>
      </CardContent>
    </Card>
  );
}; 