import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { 
  CreateClassGroupFormSchema, 
  type CreateClassGroupForm
} from '../../../../actions/schemas/class-group';
import { 
  createClassGroup
} from '../../../../actions/class-group';

interface UseClassGroupFormParams {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const useClassGroupForm = ({
  onSuccess,
  onCancel,
}: UseClassGroupFormParams) => {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showRecurrenceDetails, setShowRecurrenceDetails] = useState(false);

  const form = useForm<any>({
    resolver: zodResolver(CreateClassGroupFormSchema),
    defaultValues: {
      name: '',
      description: '',
      category: undefined,
      instructor_id: '',
      branch_id: '',
      min_age: null,
      max_age: null,
      min_belt_level: undefined,
      max_belt_level: undefined,
      unlimited_capacity: false,
      max_capacity: null,
      allow_waitlist: true,
      is_active: true,
      start_date: undefined,
      end_date: undefined,
      recurrence_pattern: undefined,
    },
  });

  const onSubmit = async (data: any) => {
    setIsSubmitting(true);
    
    try {
      const result = await createClassGroup(data as CreateClassGroupForm);

      if (result.success) {
        toast.success('Grupo de aulas criado com sucesso!');
        
        if (onSuccess) {
          onSuccess();
        } else {
          router.push('/turmas');
        }
      } else {
        if (result.errors) {
          Object.entries(result.errors).forEach(([field, error]) => {
            if (field === '_form') {
              toast.error(error as string);
            } else {
              form.setError(field as any, {
                type: 'server',
                message: Array.isArray(error) ? error[0] : error as string,
              });
            }
          });
        }
      }
    } catch (error) {
      console.error('Erro ao criar grupo de aulas:', error);
      toast.error('Erro interno do servidor');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.back();
    }
  };

  return {
    form,
    isSubmitting,
    showRecurrenceDetails,
    setShowRecurrenceDetails,
    onSubmit: form.handleSubmit(onSubmit),
    handleCancel,
  };
}; 