import type { ClassGroupWithDetails } from '../../../../../aulas/types';

export interface ClassGroupFormProps {
  initialData?: ClassGroupWithDetails;
  onSuccess?: () => void;
  onCancel?: () => void;
  instructors?: Array<{
    id: string;
    first_name: string;
    last_name: string | null;
    full_name: string | null;
  }>;
  branches?: Array<{
    id: string;
    name: string;
  }>;
}

export interface FormSectionProps {
  form: any;
  isEditMode: boolean;
  primaryColor?: string;
}

export interface InstructorBranchSectionProps extends FormSectionProps {
  instructors: ClassGroupFormProps['instructors'];
  branches: ClassGroupFormProps['branches'];
  initialData?: ClassGroupWithDetails;
}

export interface RecurrencePatternSectionProps extends FormSectionProps {
  showRecurrenceDetails: boolean;
  setShowRecurrenceDetails: (show: boolean) => void;
}

export interface FormHeaderProps {
  isEditMode: boolean;
  isSubmitting: boolean;
  primaryColor?: string;
  onCancel: () => void;
}

export interface DaysOfWeekSelectorProps {
  form: any;
  primaryColor?: string;
} 