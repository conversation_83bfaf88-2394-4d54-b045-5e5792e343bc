'use client';

import { useEffect, ReactNode } from 'react';
import { usePageTitle } from '@/contexts/PageTitleContext';

interface NovaTurmaClientWrapperProps {
  children: ReactNode;
}

export default function NovaTurmaClientWrapper({ children }: NovaTurmaClientWrapperProps) {
  const { setPageTitle } = usePageTitle();

  useEffect(() => {
    setPageTitle('Nova Turma');
    return () => setPageTitle(null);
  }, [setPageTitle]);
  
  return <>{children}</>;
} 