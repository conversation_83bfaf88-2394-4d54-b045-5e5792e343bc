import { Metada<PERSON> } from "next";
import { Suspense } from "react";
import { requireAuth } from "@/services/auth/server";
import ClassGroupForm from "./components/form/class-group-form";
import { getFormData } from "../../aulas/actions/form-data-actions";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { PageHeader } from "@/components/layout/page-header";
import { Users, GraduationCap, Calendar } from "lucide-react";
import NovaTurmaClientWrapper from "./components/nova-turma-client-wrapper";

export const metadata: Metadata = {
  title: "Nova Turma | ApexSAAS",
  description: "Criar uma nova turma de aulas"
};

export default async function NovaTurmaPage() {
  // Verificar autenticação
  await requireAuth();
  
  // Buscar dados do formulário
  const result = await getFormData();
  const instructors = result.success ? result.data.instructors : [];
  const branches = result.success ? result.data.branches : [];

  const LoadingSkeleton = () => (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <Card className="bg-gradient-to-r from-background via-muted/30 to-background border-0 shadow-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Skeleton className="h-12 w-12 rounded-xl" />
              <div className="space-y-2">
                <Skeleton className="h-7 w-48" />
                <Skeleton className="h-4 w-64" />
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Form Skeleton */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
            <Skeleton className="h-24 w-full" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <NovaTurmaClientWrapper>
      <div className="space-y-6">
        {/* Page Header com volta para listagem */}
        <PageHeader 
          backHref="/aulas/turmas"
          backLabel="Voltar para Turmas"
          className="from-background via-muted/20 to-background"
        />

        {/* Hero Section */}
        <Card className="from-background via-muted/30 to-background border-0 shadow-sm">
          <CardHeader className="pb-4">
            <div className="flex items-start gap-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-primary/10 ring-1 ring-primary/20">
                <Users className="h-6 w-6 text-primary" />
              </div>
              <div className="flex-1 space-y-1">
                <CardTitle className="text-2xl font-bold tracking-tight from-foreground to-foreground/70 bg-clip-text">
                  Criar Nova Turma
                </CardTitle>
                <p className="text-muted-foreground">
                  Configure uma nova turma de aulas com horários e critérios específicos para organizar melhor suas atividades.
                </p>
              </div>
            </div>
            
            {/* Features Grid */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-border/50">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20">
                  <GraduationCap className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <div className="text-sm font-medium">Critérios Flexíveis</div>
                  <div className="text-xs text-muted-foreground">Idade e graduação</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-border/50">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/20">
                  <Calendar className="h-4 w-4 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <div className="text-sm font-medium">Agendamento</div>
                  <div className="text-xs text-muted-foreground">Recorrência automática</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-border/50">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/20">
                  <Users className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <div className="text-sm font-medium">Capacidade</div>
                  <div className="text-xs text-muted-foreground">Controle de vagas</div>
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>
        
        {/* Form Container */}
        <Suspense fallback={<LoadingSkeleton />}>
          <div className="rounded-lg  bg-card shadow-sm">
            <ClassGroupForm 
              instructors={instructors}
              branches={branches}
            />
          </div>
        </Suspense>
      </div>
    </NovaTurmaClientWrapper>
  );
} 