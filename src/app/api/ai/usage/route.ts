import { NextRequest, NextResponse } from 'next/server';
import { checkApiAuth } from '@/services/auth/actions/api-auth-actions';
import { AIChatUsageLimitService } from '@/services/ai/usage-limit-service';
import { createAuthClient } from '@/services/supabase/server/auth-client';

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticação
    const authCheck = await checkApiAuth();
    if (!authCheck.authenticated || !authCheck.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    const userId = authCheck.user.id;
    
    // Buscar tenant_id do usuário
    const { supabase } = await createAuthClient();
    const { data: userProfile, error: userError } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', userId)
      .single();

    if (userError || !userProfile?.tenant_id) {
      return NextResponse.json(
        { error: 'Usuário não possui tenant associado.' },
        { status: 400 }
      );
    }

    // Obter estatísticas de uso
    const usageStats = await AIChatUsageLimitService.getUserUsageStats(userId);

    return NextResponse.json({
      success: true,
      usageStats,
    });

  } catch (error) {
    console.error('Erro ao buscar estatísticas de uso:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 