import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/services/supabase/server'
import { z } from 'zod'

const resetPasswordConfirmSchema = z.object({
  token_hash: z.string().min(1, 'Token é obrigatório'),
  password: z.string().min(6, 'A senha deve ter pelo menos 6 caracteres'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validar dados de entrada
    const validationResult = resetPasswordConfirmSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Dados inválidos',
          details: validationResult.error.format()
        }, 
        { status: 400 }
      )
    }

    const { token_hash, password } = validationResult.data
    const supabase = await createClient()

    // Verificar e usar o token para atualizar a senha
    const { data, error } = await supabase.auth.verifyOtp({
      token_hash,
      type: 'recovery',
    })

    if (error) {
      console.error('Erro ao verificar token:', error)
      
      // Mensagens de erro mais específicas
      let errorMessage = 'Token inválido ou expirado'
      if (error.message.includes('expired')) {
        errorMessage = 'Token expirado. Solicite um novo link de redefinição de senha.'
      } else if (error.message.includes('invalid')) {
        errorMessage = 'Token inválido. Verifique o link recebido por email.'
      }
      
      return NextResponse.json(
        { 
          success: false,
          error: errorMessage
        }, 
        { status: 400 }
      )
    }

    if (!data.user) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Usuário não encontrado'
        }, 
        { status: 404 }
      )
    }

    // Atualizar a senha do usuário
    const { error: updateError } = await supabase.auth.updateUser({
      password
    })

    if (updateError) {
      console.error('Erro ao atualizar senha:', updateError)
      return NextResponse.json(
        { 
          success: false,
          error: 'Erro ao atualizar senha. Tente novamente.'
        }, 
        { status: 500 }
      )
    }

    // Fazer logout para forçar novo login com a nova senha
    await supabase.auth.signOut()

    return NextResponse.json({ 
      success: true,
      message: 'Senha atualizada com sucesso'
    })
    
  } catch (error) {
    console.error('Erro ao processar confirmação de reset de senha:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : String(error)
      }, 
      { status: 500 }
    )
  }
}
