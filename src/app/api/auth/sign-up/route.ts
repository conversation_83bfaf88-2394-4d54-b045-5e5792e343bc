import { signUpAction } from '@/services/auth/actions/index';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const result = await signUpAction(formData);
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error('Erro durante o cadastro:', error);
    return NextResponse.json(
      { 
        error: 'Falha ao processar o cadastro',
        error_code: 'server_error',
        details: error instanceof Error ? error.message : String(error)
      }, 
      { status: 500 }
    );
  }
} 