import { NextRequest, NextResponse } from 'next/server';
import { processClassReminders } from '@/services/billing/notification-service';
import { CronLockManager } from '@/services/classes/cron-lock-manager';

// Configuração para runtime do Next.js
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const maxDuration = 300; // 5 minutos de timeout

/**
 * Chave secreta para proteger o endpoint do cron job
 * Deve ser definida no .env como CRON_SECRET_KEY
 */
const CRON_SECRET_KEY = process.env.CRON_SECRET_KEY;

/**
 * Contador de execuções para estatísticas
 */
let executionCount = 0;

/**
 * Valida se a requisição está autorizada
 */
function validateCronRequest(request: NextRequest): { isValid: boolean; error?: string } {
  // Verificar se a chave secreta está configurada
  if (!CRON_SECRET_KEY) {
    console.error('🚨 CRON_SECRET_KEY não configurada');
    return { isValid: false, error: 'Cron job não configurado' };
  }

  // Verificar header de autorização
  const authHeader = request.headers.get('authorization');
  const providedKey = authHeader?.replace('Bearer ', '');

  if (!providedKey || providedKey !== CRON_SECRET_KEY) {
    console.error('🚨 Tentativa de acesso não autorizada ao cron job de lembretes de aula');
    return { isValid: false, error: 'Não autorizado' };
  }

  return { isValid: true };
}

/**
 * Endpoint principal do cron job para enviar lembretes de aula
 * Método: POST
 * Header: Authorization: Bearer <CRON_SECRET_KEY>
 * 
 * Execução programada: A cada 30 minutos
 * Função: 
 * - Enviar lembretes 24 horas antes da aula
 * - Enviar lembretes 2 horas antes da aula
 * - Enviar lembretes 30 minutos antes da aula
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(7);
  
  console.log(`🥋 [${requestId}] Iniciando cron job de lembretes de aula...`);

  try {
    // Validar requisição
    const validation = validateCronRequest(request);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: validation.error,
          timestamp: new Date().toISOString(),
          requestId
        },
        { status: validation.error === 'Não autorizado' ? 401 : 409 }
      );
    }

    // Criar gerenciador de lock
    const lockManager = new CronLockManager('class-reminders', 5); // 5 minutos de timeout

    // Executar com lock automático
    const lockResult = await lockManager.executeWithLock(requestId, async () => {
      executionCount++;
      console.log(`🚀 [${requestId}] Execução #${executionCount} autorizada`);

      // Executar processamento de lembretes de aula
      return await processClassReminders();
    });

    const executionTime = Date.now() - startTime;

    // Verificar se conseguiu adquirir o lock
    if (!lockResult.lockAcquired) {
      console.warn(`⚠️ [${requestId}] Lock não adquirido: ${lockResult.error}`);
      return NextResponse.json({
        success: false,
        error: lockResult.error || 'Não foi possível adquirir lock',
        lockAcquired: false,
        timestamp: new Date().toISOString(),
        requestId
      }, { status: 409 });
    }

    // Verificar se a execução foi bem-sucedida
    if (!lockResult.success || !lockResult.data) {
      console.error(`❌ [${requestId}] Erro durante execução: ${lockResult.error}`);
      return NextResponse.json({
        success: false,
        error: lockResult.error || 'Erro durante execução',
        lockAcquired: true,
        executionTimeMs: executionTime,
        requestId,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

    const result = lockResult.data;

    // Log do resultado
    console.log(`✅ [${requestId}] Cron job de lembretes de aula concluído em ${executionTime}ms:`);
    console.log(`  - Total processados: ${result.data?.totalProcessed}`);
    console.log(`  - Lembretes preparados: ${result.data?.remindersSent}`);
    console.log(`  - Notificações enviadas: ${result.data?.notificationsSent || 0}`);
    console.log(`  - Erros de envio: ${result.data?.notificationErrors || 0}`);
    console.log(`  - Total de erros: ${result.data?.errors.length || 0}`);

    // Verificar se houve erros
    if (result.data?.errors && result.data.errors.length > 0) {
      console.error(`❌ [${requestId}] Erros durante execução:`, result.data.errors);
    }

    // Log detalhado dos lembretes por tipo
    const remindersByType = result.data?.reminders?.reduce((acc: Record<string, number>, reminder: { type: string }) => {
      acc[reminder.type] = (acc[reminder.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    console.log(`📊 [${requestId}] Lembretes por tipo:`, remindersByType);

    return NextResponse.json({
      success: result.success,
      data: {
        ...result,
        remindersByType,
        executionId: requestId,
        cronExecutionCount: executionCount,
        apiExecutionTimeMs: executionTime,
        lockAcquired: true
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const executionTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    
    console.error(`💥 [${requestId}] Erro crítico no cron job de lembretes de aula:`, error);

    return NextResponse.json({
      success: false,
      error: 'Erro interno do cron job',
      details: process.env.NODE_ENV === 'development' ? errorMessage : undefined,
      executionTimeMs: executionTime,
      requestId,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Endpoint para verificar status do cron job
 * Método: GET
 */
export async function GET(request: NextRequest) {
  // Verificar autorização (mesma chave do POST)
  const authHeader = request.headers.get('authorization');
  const providedKey = authHeader?.replace('Bearer ', '');

  if (!CRON_SECRET_KEY || !providedKey || providedKey !== CRON_SECRET_KEY) {
    return NextResponse.json(
      { error: 'Não autorizado' },
      { status: 401 }
    );
  }

  try {
    // Verificar quantas aulas precisam de lembrete
    const { createClient } = await import('@/services/supabase/server');
    const supabase = await createClient();

    // Trabalhar diretamente com UTC (dados do banco estão em UTC)
    const now = new Date();

    // Calcular janela de tempo para lembretes (próximas 24 horas em UTC)
    const in24Hours = new Date(now.getTime() + (24 * 60 * 60 * 1000));

    const { data: classesForReminder, error } = await supabase
      .from('classes')
      .select(`
        id, 
        name, 
        start_time, 
        class_group_id,
        class_groups!inner(
          name,
          class_group_enrollments!inner(
            student_id,
            status
          )
        )
      `)
      .eq('status', 'scheduled')
      .is('deleted_at', null)
      .eq('class_groups.class_group_enrollments.status', 'active')
      .gte('start_time', now.toISOString())
      .lte('start_time', in24Hours.toISOString());

    if (error) {
      throw error;
    }

    // Categorizar por tipo de lembrete
    const reminderCategories = {
      in_24_hours: 0,
      in_2_hours: 0,
      in_30_minutes: 0
    };

    classesForReminder?.forEach(classItem => {
      const classTime = new Date(classItem.start_time);
      const timeDiff = classTime.getTime() - now.getTime();
      const hoursUntilClass = timeDiff / (1000 * 60 * 60);

      // Categorizar baseado nas janelas de lembrete (±15 minutos)
      if (Math.abs(hoursUntilClass - 0.5) <= 0.25) {
        reminderCategories.in_30_minutes++;
      } else if (Math.abs(hoursUntilClass - 2) <= 0.25) {
        reminderCategories.in_2_hours++;
      } else if (Math.abs(hoursUntilClass - 24) <= 0.25) {
        reminderCategories.in_24_hours++;
      }
    });

    // Verificar locks ativos
    const lockManager = new CronLockManager('class-reminders');
    const lockStatus = await lockManager.hasActiveLock();

    return NextResponse.json({
      status: 'healthy',
      isRunning: lockStatus.hasLock,
      runningBy: lockStatus.lockedBy || null,
      lockExpiresAt: lockStatus.expiresAt || null,
      executionCount,
      classesNeedingReminders: {
        total: classesForReminder?.length || 0,
        in_24_hours: reminderCategories.in_24_hours,
        in_2_hours: reminderCategories.in_2_hours,
        in_30_minutes: reminderCategories.in_30_minutes
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao verificar status do cron job de lembretes de aula:', error);
    return NextResponse.json({
      status: 'error',
      error: 'Erro ao verificar status',
      executionCount,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Bloquear outros métodos HTTP
 */
export async function PUT() {
  return NextResponse.json({ error: 'Método não permitido' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Método não permitido' }, { status: 405 });
}

export async function PATCH() {
  return NextResponse.json({ error: 'Método não permitido' }, { status: 405 });
}
