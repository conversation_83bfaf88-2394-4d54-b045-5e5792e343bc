import { NextRequest, NextResponse } from 'next/server';
import { processRecurringPaymentsWithRetry } from '@/services/billing/recurring-payments-service';
import { CronLockManager } from '@/services/classes/cron-lock-manager';

// Configuração para runtime do Next.js
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const maxDuration = 300; // 5 minutos de timeout

/**
 * Chave secreta para proteger o endpoint do cron job
 * Deve ser definida no .env como CRON_SECRET_KEY
 */
const CRON_SECRET_KEY = process.env.CRON_SECRET_KEY;

/**
 * Contador de execuções para estatísticas
 */
let executionCount = 0;

/**
 * Valida se a requisição está autorizada
 */
function validateCronRequest(request: NextRequest): { isValid: boolean; error?: string } {
  // Verificar se a chave secreta está configurada
  if (!CRON_SECRET_KEY) {
    console.error('🚨 CRON_SECRET_KEY não configurada');
    return { isValid: false, error: 'Cron job não configurado' };
  }

  // Verificar header de autorização
  const authHeader = request.headers.get('authorization');
  const providedKey = authHeader?.replace('Bearer ', '');

  if (!providedKey || providedKey !== CRON_SECRET_KEY) {
    console.error('🚨 Tentativa de acesso não autorizada ao cron job de pagamentos recorrentes');
    return { isValid: false, error: 'Não autorizado' };
  }

  return { isValid: true };
}

/**
 * Endpoint principal do cron job para processar pagamentos recorrentes pendentes
 * Método: POST
 * Header: Authorization: Bearer <CRON_SECRET_KEY>
 * 
 * Execução programada: Diário às 00:30
 * Função: 
 * - Processar pagamentos recorrentes que deveriam ter sido criados
 * - Criar pagamentos para ciclos que já venceram
 * - Corrigir pagamentos não criados devido a pagamentos antecipados
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(7);
  
  console.log(`🔄 [${requestId}] Iniciando cron job de processamento de pagamentos recorrentes...`);

  try {
    // Validar requisição
    const validation = validateCronRequest(request);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: validation.error,
          timestamp: new Date().toISOString(),
          requestId
        },
        { status: validation.error === 'Não autorizado' ? 401 : 409 }
      );
    }

    // Criar gerenciador de lock
    const lockManager = new CronLockManager('process-recurring-payments', 5); // 5 minutos de timeout

    // Executar com lock automático
    const lockResult = await lockManager.executeWithLock(requestId, async () => {
      executionCount++;
      console.log(`🚀 [${requestId}] Execução #${executionCount} autorizada`);

      // Executar processamento de pagamentos recorrentes com retry
      return await processRecurringPaymentsWithRetry();
    });

    const executionTime = Date.now() - startTime;

    // Verificar resultado do lock
    if (!lockResult.success) {
      console.warn(`⚠️ [${requestId}] ${lockResult.error}`);
      return NextResponse.json(
        {
          success: false,
          error: lockResult.error,
          timestamp: new Date().toISOString(),
          requestId,
          executionTime
        },
        { status: 409 } // Conflict - outro processo já está executando
      );
    }

    // Verificar resultado do processamento
    if (!lockResult.data?.success) {
      console.error(`❌ [${requestId}] Erro no processamento: ${lockResult.data?.error}`);
      return NextResponse.json(
        {
          success: false,
          error: lockResult.data?.error || 'Erro no processamento',
          timestamp: new Date().toISOString(),
          requestId,
          executionTime
        },
        { status: 500 }
      );
    }

    // Sucesso
    const result = lockResult.data.data;
    console.log(`✅ [${requestId}] Cron job concluído em ${executionTime}ms:`);
    console.log(`  - Total analisados: ${result?.totalAnalyzed || 0}`);
    console.log(`  - Processados: ${result?.processedCount || 0}`);
    console.log(`  - Erros: ${result?.errorCount || 0}`);

    return NextResponse.json({
      success: true,
      message: 'Pagamentos recorrentes processados com sucesso',
      data: {
        totalAnalyzed: result?.totalAnalyzed || 0,
        processedCount: result?.processedCount || 0,
        errorCount: result?.errorCount || 0,
        processedAt: result?.processedAt,
        executionTime,
        requestId
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const executionTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    
    console.error(`💥 [${requestId}] Erro crítico no cron job:`, error);
    
    return NextResponse.json(
      {
        success: false,
        error: `Erro crítico: ${errorMessage}`,
        timestamp: new Date().toISOString(),
        requestId,
        executionTime
      },
      { status: 500 }
    );
  }
}

/**
 * Endpoint para verificar status do cron job
 * Método: GET
 */
export async function GET(request: NextRequest) {
  // Verificar autorização (mesma chave do POST)
  const authHeader = request.headers.get('authorization');
  const providedKey = authHeader?.replace('Bearer ', '');

  if (!CRON_SECRET_KEY || !providedKey || providedKey !== CRON_SECRET_KEY) {
    return NextResponse.json(
      { error: 'Não autorizado' },
      { status: 401 }
    );
  }

  try {
    // Verificar quantos pagamentos precisam ser processados
    const { checkPendingRecurringPayments } = await import('@/services/billing/recurring-payments-service');
    const pendingResult = await checkPendingRecurringPayments();

    if (!pendingResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: pendingResult.error,
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      );
    }

    const pendingCount = pendingResult.data?.pendingCount || 0;
    const pendingPayments = pendingResult.data?.pendingPayments || [];

    // Verificar se há lock ativo
    const lockManager = new CronLockManager('process-recurring-payments', 5);
    const lockStatus = await lockManager.hasActiveLock();

    return NextResponse.json({
      success: true,
      status: {
        isRunning: lockStatus.hasLock,
        lockedBy: lockStatus.lockedBy,
        expiresAt: lockStatus.expiresAt,
        executionCount,
        pendingPayments: {
          count: pendingCount,
          payments: pendingPayments.slice(0, 5) // Mostrar apenas os primeiros 5
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    
    return NextResponse.json(
      {
        success: false,
        error: `Erro ao verificar status: ${errorMessage}`,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
