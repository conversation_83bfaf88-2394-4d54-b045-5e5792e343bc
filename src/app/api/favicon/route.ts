import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/services/supabase/server';
import { TenantExtractorServer } from '@/services/tenant/tenant-extractor-server';

// Cache no navegador por no máximo 1 hora e obrigar revalidação após esse período.
const CACHE_CONTROL = 'public, max-age=3600, s-maxage=3600, stale-while-revalidate=2592000';
const DEFAULT_FAVICON_URL = '/favicon.ico';

export const revalidate = 86400;
export const dynamic = 'force-dynamic';
export const runtime = 'edge';

const MEMORY_CACHE = new Map();
const CACHE_TTL = 3600000;

export async function GET(request: NextRequest) {
  const timeoutController = new AbortController();
  const timeoutId = setTimeout(() => {
    timeoutController.abort();
  }, 10000); 
  
  try {
    const host = request.headers.get('host') || '';
    const url = request.url;
    const urlObj = new URL(url);
    const searchParams = urlObj.searchParams;
    let tenantFromParam = searchParams.get('tenant');
    const versionParam = searchParams.get('v');
    
    let tenantSlug = tenantFromParam;
    
    if (!tenantSlug) {
      if (host.includes('.localhost')) {
        tenantSlug = host.split('.')[0];
      } else if (host.includes('.')) {
        tenantSlug = host.split('.')[0];
      } else {
        try {
          const hostname = urlObj.hostname;
          if (hostname.includes('.')) {
            tenantSlug = hostname.split('.')[0];
          }
        } catch (urlError) {
        }
        
        if (!tenantSlug) {
          const extractor = new TenantExtractorServer();
          tenantSlug = await extractor.extractTenantSlug();
        }
      }
    }
    
    if (!tenantSlug) {
      return redirectToDefaultFavicon(request);
    }
    
    const referer = request.headers.get('referer');
    const isRevalidation = referer && (referer.includes('/home') || referer.includes('/perfil/'));
    const ifNoneMatch = request.headers.get('if-none-match');

    const supabase = await createAdminClient();

    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .select('*')
      .eq('slug', tenantSlug)
      .single();

    if (tenantError) {
      return redirectToDefaultFavicon(request);
    }
    
    if (!tenant) {
      return redirectToDefaultFavicon(request);
    }

    const versionMarker = tenant.updated_at ? new Date(tenant.updated_at).getTime() : '0';
    const cacheKey = `favicon_${tenantSlug}_${versionMarker}`;

    const cachedData = MEMORY_CACHE.get(cacheKey);

    if (cachedData && ifNoneMatch && ifNoneMatch.includes(cachedData.etag) && !isRevalidation) {
      clearTimeout(timeoutId);
      return new NextResponse(null, {
        status: 304,
        headers: {
          'Cache-Control': CACHE_CONTROL,
          'ETag': cachedData.etag,
          'Expires': new Date(Date.now() + 86400000).toUTCString(),
          'Vary': 'Accept-Encoding'
        }
      });
    }
    
    if (cachedData && (Date.now() - cachedData.timestamp < CACHE_TTL)) {
      clearTimeout(timeoutId);
      
      if (cachedData.type === 'redirect') {
        return createRedirectResponse(cachedData.url, cachedData.etag || `"favicon-${tenantSlug}"`);
      } else if (cachedData.type === 'buffer') {
        return createBufferResponse(
          cachedData.data, 
          cachedData.contentType || 'image/png', 
          cachedData.etag || `"favicon-${tenantSlug}"`
        );
      }
    }
    
    if (tenant.favicon_url) {
      clearTimeout(timeoutId);
      
      const etag = `"favicon-${tenantSlug}-${versionMarker}"`;
      
      MEMORY_CACHE.set(cacheKey, {
        type: 'redirect',
        url: tenant.favicon_url,
        timestamp: Date.now(),
        etag
      });
      
      return createRedirectResponse(tenant.favicon_url, etag);
    } 
    else if (tenant.favicon_storage_path && tenant.favicon_storage_path.length > 0) {
      let storagePath = tenant.favicon_storage_path;
      
      if (storagePath.startsWith('http')) {
        try {
          const urlObj = new URL(storagePath);
          const pathParts = urlObj.pathname.split('/');
          
          if (urlObj.pathname.includes('/storage/v1/object/sign/')) {

            const signIndex = pathParts.indexOf('sign');
            if (signIndex !== -1 && signIndex + 1 < pathParts.length) {
              const remainingParts = pathParts.slice(signIndex + 1);
              storagePath = remainingParts.join('/');
            }
          } else {
            const fileName = pathParts[pathParts.length - 1];
            
            if (fileName && fileName !== 'object' && fileName !== 'sign') {
              storagePath = fileName;  
            } else if (urlObj.searchParams.has('token')) {
              const objectPath = new URLSearchParams(urlObj.search).get('token')?.split('/')[0];
              if (objectPath) {
                storagePath = objectPath;
              }
            }
          }
        } catch (urlError) {
        }
      }
      
      if (!storagePath.includes('/') && 
          (storagePath.endsWith('.png') || 
           storagePath.endsWith('.jpg') || 
           storagePath.endsWith('.ico'))) {
        storagePath = `logos/${storagePath}`;
      }
      
      try {
        const { data, error } = await supabase.storage
          .from('tenant-assets') 
          .download(storagePath);

        if (error) {
          const { data: altData, error: altError } = await supabase.storage
            .from('logos') 
            .download(storagePath.replace('logos/', ''));
            
          if (altError || !altData) {
            if (altError.message?.includes('timeout') || 
                altError.message?.includes('size') || 
                altError.message?.includes('large')) {
              if (tenant.logo_url) {
                clearTimeout(timeoutId);
                
                const etag = `"favicon-${tenantSlug}-${versionMarker}"`;
                
                MEMORY_CACHE.set(cacheKey, {
                  type: 'redirect',
                  url: tenant.logo_url,
                  timestamp: Date.now(),
                  etag
                });
                
                return createRedirectResponse(tenant.logo_url, etag);
              }
            }
          } else {
            const sizeMB = altData.size / (1024 * 1024);
            
            if (sizeMB > 2) {
              if (tenant.logo_url) {
                clearTimeout(timeoutId);
                
                const etag = `"favicon-${tenantSlug}-${versionMarker}"`;
                
                MEMORY_CACHE.set(cacheKey, {
                  type: 'redirect',
                  url: tenant.logo_url,
                  timestamp: Date.now(),
                  etag
                });
                
                return createRedirectResponse(tenant.logo_url, etag);
              }
            }
            
            const arrayBuffer = await altData.arrayBuffer();
            clearTimeout(timeoutId);
            
            const etag = `"favicon-${tenantSlug}-${versionMarker}"`;
            
            MEMORY_CACHE.set(cacheKey, {
              type: 'buffer',
              data: arrayBuffer,
              contentType: 'image/png',
              timestamp: Date.now(),
              etag
            });
            
            return createBufferResponse(arrayBuffer, 'image/png', etag);
          }
        } else {
          const sizeMB = data.size / (1024 * 1024);
          
          if (sizeMB > 2) {
            if (tenant.logo_url) {
              clearTimeout(timeoutId);
              
              const etag = `"favicon-${tenantSlug}-${versionMarker}"`;
              
              MEMORY_CACHE.set(cacheKey, {
                type: 'redirect',
                url: tenant.logo_url,
                timestamp: Date.now(),
                etag
              });
              
              return createRedirectResponse(tenant.logo_url, etag);
            }
          }
          
          const arrayBuffer = await data.arrayBuffer();
          clearTimeout(timeoutId);
          
          const etag = `"favicon-${tenantSlug}-${versionMarker}"`;
          
          MEMORY_CACHE.set(cacheKey, {
            type: 'buffer',
            data: arrayBuffer,
            contentType: 'image/png',
            timestamp: Date.now(),
            etag
          });
          
          return createBufferResponse(arrayBuffer, 'image/png', etag);
        }
      } catch (storageError: any) {
        if (storageError.name === 'AbortError' || 
            (typeof storageError.message === 'string' && 
             (storageError.message.includes('timeout') || 
              storageError.message.includes('large')))) {
          if (tenant.logo_url) {
            clearTimeout(timeoutId);
            
            const etag = `"favicon-${tenantSlug}-${versionMarker}"`;
            
            MEMORY_CACHE.set(cacheKey, {
              type: 'redirect',
              url: tenant.logo_url,
              timestamp: Date.now(),
              etag
            });
            
            return createRedirectResponse(tenant.logo_url, etag);
          }
        }
      }
    }
    
    if (tenant.logo_url) {
      clearTimeout(timeoutId);
      
      const etag = `"favicon-${tenantSlug}-${versionMarker}"`;
      
      MEMORY_CACHE.set(cacheKey, {
        type: 'redirect',
        url: tenant.logo_url,
        timestamp: Date.now(),
        etag
      });
      
      return createRedirectResponse(tenant.logo_url, etag);
    }
    
    clearTimeout(timeoutId);
    return redirectToDefaultFavicon(request);
  } catch (error) {
    clearTimeout(timeoutId);
    return redirectToDefaultFavicon(request);
  } finally {
    clearTimeout(timeoutId);
  }
}

function redirectToDefaultFavicon(request: NextRequest) {
  // Constrói a URL absoluta para o favicon localizado em `/public/favicon.ico`
  const absoluteUrl = new URL('/favicon.ico', request.url).toString();

  return NextResponse.redirect(absoluteUrl, {
    headers: {
      'Cache-Control': CACHE_CONTROL,
      'ETag': '"default-favicon"',
      'Expires': new Date(Date.now() + 86400000).toUTCString(),
      'Vary': 'Accept-Encoding',
      'Link': '<' + absoluteUrl + '>; rel=preload; as=image'
    },
    status: 302
  });
}

function createRedirectResponse(url: string, etag: string) {
  return NextResponse.redirect(url, {
    headers: {
      'Cache-Control': CACHE_CONTROL,
      'ETag': etag,
      'Expires': new Date(Date.now() + 86400000).toUTCString(),
      'Vary': 'Accept-Encoding',
      'Link': '<' + url + '>; rel=preload; as=image'
    },
    status: 302
  });
}

function createBufferResponse(buffer: ArrayBuffer, contentType: string, etag: string) {
  return new NextResponse(buffer, {
    headers: {
      'Content-Type': contentType,
      'Cache-Control': CACHE_CONTROL,
      'ETag': etag,
      'Expires': new Date(Date.now() + 86400000).toUTCString(),
      'Vary': 'Accept-Encoding'
    }
  });
}