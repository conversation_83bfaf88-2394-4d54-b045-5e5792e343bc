import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";
import { z } from 'zod';

const instructorIdSchema = z.string().uuid({
  message: "ID de instrutor inválido"
});

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ instructorId: string }> }
) {
  try {
    const supabase = await createClient();
    const { instructorId } = await params;
    
    const validatedParams = instructorIdSchema.safeParse(instructorId);
    if (!validatedParams.success) {
      return NextResponse.json(
        { error: 'ID de instrutor inválido' },
        { status: 400 }
      );
    }
    
    const validatedInstructorId = validatedParams.data;
    
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Não autenticado' },
        { status: 401 }
      );
    }

    // Primeiro, buscar dados do instrutor
    const { data: instructorData, error: instructorError } = await supabase
      .from('instructors')
      .select('*')
      .eq('user_id', validatedInstructorId)
      .single();

    if (instructorError) {
      console.error('Erro ao buscar dados do instrutor:', instructorError);
      return NextResponse.json(
        { error: 'Instrutor não encontrado' },
        { status: 404 }
      );
    }

    if (!instructorData) {
      return NextResponse.json(
        { error: 'Instrutor não encontrado' },
        { status: 404 }
      );
    }

    // Depois, buscar dados do usuário
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        branch_id,
        first_name,
        last_name,
        role
      `)
      .eq('id', validatedInstructorId)
      .single();

    if (userError) {
      console.error('Erro ao buscar dados do usuário:', userError);
      return NextResponse.json(
        { error: 'Dados do usuário não encontrados' },
        { status: 404 }
      );
    }

    // Buscar dados da filial separadamente
    let branchName = 'Não informada';
    if (userData.branch_id) {
      const { data: branchData, error: branchError } = await supabase
        .from('branches')
        .select('name')
        .eq('id', userData.branch_id)
        .single();
      
      if (branchError) {
        console.error('Erro ao buscar filial:', branchError);
      } else if (branchData?.name) {
        branchName = branchData.name;
      }
    }

    const formattedData = {
      contract_type: instructorData.contract_type,
      payment_model: instructorData.payment_model,
      payment_value: instructorData.payment_value?.toString(),
      payment_percentage: instructorData.payment_percentage?.toString(),
      hire_date: instructorData.hire_date,
      branch_id: userData.branch_id,
      branch_name: branchName,
      status: instructorData.status || 'active',
      id: instructorData.id,
      specialties: instructorData.specialties,
      certification_level: instructorData.certification_level,
      experience_years: instructorData.experience_years,
      bio: instructorData.bio,
    };

    const isOwnProfile = currentUser.id === userData.id;
    const { data: currentUserData } = await supabase
      .from('users')
      .select('role')
      .eq('id', currentUser.id)
      .single();
    
    const isAdmin = currentUserData?.role === 'admin';

    if (!isAdmin && !isOwnProfile) {
      return NextResponse.json(
        { error: 'Permissão negada' },
        { status: 403 }
      );
    }

    const response = NextResponse.json(formattedData);
    
    response.headers.set('Cache-Control', 'private, max-age=300, s-maxage=60');
    
    return response;
  } catch (error) {
    console.error('Erro ao processar requisição:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 