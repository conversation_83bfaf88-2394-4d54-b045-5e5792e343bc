import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/services/supabase/server';
import { usePermission } from '@/services/permissions/hooks/use-permission';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ instructorId: string }> }
) {
  try {
    const { instructorId } = await params;
    const supabase = await createClient();  
    
    // Verificar permissão
    const canUpdate = await usePermission('instructors', 'edit');
    if (!canUpdate) {
      return NextResponse.json(
        { error: 'Sem permissão para atualizar instrutores' },
        { status: 403 }
      );
    }

    // Buscar o instrutor e o status atual do usuário
    const { data: instructor, error: fetchError } = await supabase
      .from('instructors')
      .select(`
        id,
        user_id,
        users (
          id,
          status
        )
      `)
      .eq('id', instructorId)
      .single();

    if (fetchError || !instructor) {
      console.error('Erro ao buscar instrutor:', fetchError);
      return NextResponse.json(
        { error: 'Instrutor não encontrado' },
        { status: 404 }
      );
    }

    const currentStatus = (instructor.users as any)?.status || 'active';
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    
    // Atualizar o status na tabela users (campo centralizado)
    const { error: updateError } = await supabase
      .from('users')
      .update({ 
        status: newStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', instructor.user_id);

    if (updateError) {
      console.error('Erro ao atualizar status do usuário/instrutor:', updateError);
      return NextResponse.json(
        { error: 'Falha ao atualizar status do instrutor' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Instrutor ${newStatus === 'active' ? 'ativado' : 'desativado'} com sucesso`,
      status: newStatus
    });
  } catch (error) {
    console.error('Erro ao alternar status do instrutor:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 