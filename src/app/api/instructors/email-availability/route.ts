import { NextResponse } from 'next/server';
import { checkEmailAvailability } from '@/app/(dashboard)/instrutores/novo/actions/check-email-availability';

export async function POST(req: Request) {
  try {
    const { email } = await req.json();
    const result = await checkEmailAvailability(email || '');
    return NextResponse.json(result);
  } catch (error) {
    console.error('Erro na rota email-availability:', error);
    return NextResponse.json({ available: false, error: 'Erro inesperado' }, { status: 500 });
  }
} 