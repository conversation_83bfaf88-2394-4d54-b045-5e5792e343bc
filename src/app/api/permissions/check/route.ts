import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/services/supabase/server';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import { ActionType, ResourceType } from '@/services/permissions/types/permission-types';
import { getPermissionService } from '@/services/permissions/service';

/**
 * API para verificar permissões do usuário
 * GET /api/permissions/check?resource=user&action=list
 * GET /api/permissions/check?resource=profile&action=view&targetId={userId}
 */
export async function GET(request: NextRequest) {
  // Verificar se o usuário está autenticado
  const user = await getCurrentUser();
  
  if (!user) {
    return NextResponse.json(
      { 
        granted: false, 
        reason: 'Usuário não autenticado'
      },
      { status: 401 }
    );
  }
  
  try {
    // Buscar parâmetros da query
    const searchParams = request.nextUrl.searchParams;
    const resource = searchParams.get('resource') as ResourceType;
    const action = searchParams.get('action') as ActionType;
    const targetId = searchParams.get('targetId');
    
    if (!resource || !action) {
      return NextResponse.json(
        { 
          granted: false, 
          reason: 'Parâmetros resource e action são obrigatórios'
        },
        { status: 400 }
      );
    }
    
    console.log(`[API Permissions] Verificando permissão: usuário=${user.id}, resource=${resource}, action=${action}, target=${targetId}`);
    
    // Usar o sistema de políticas padrão para verificar permissões
    const permissionService = getPermissionService();
    const result = await permissionService.hasPermission(
      user.id,
      resource,
      action,
      targetId || undefined
    );
    
    console.log(`[API Permissions] Resultado: granted=${result.granted}, reason=${result.reason}`);
    
    if (!result.granted) {
      return NextResponse.json(
        { 
          granted: false, 
          reason: result.reason || 'Acesso negado'
        },
        { status: 403 }
      );
    }
    
    // Se chegou até aqui, o usuário tem permissão
    return NextResponse.json({ granted: true });
  } catch (error) {
    console.error('Erro ao verificar permissão do usuário:', error);
    return NextResponse.json(
      { 
        granted: false, 
        reason: 'Erro interno ao verificar permissão'
      },
      { status: 500 }
    );
  }
} 