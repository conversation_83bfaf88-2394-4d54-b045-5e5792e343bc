import { NextRequest, NextResponse } from 'next/server';
import { getAttendanceByStudent } from '@/app/(dashboard)/presenca/actions/attendance-actions';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const result = await getAttendanceByStudent(body);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Erro na API de histórico de presença:', error);
    return NextResponse.json(
      { success: false, errors: { _form: 'Erro interno do servidor' } },
      { status: 500 }
    );
  }
} 