import { NextResponse } from 'next/server';
import { getTenantData } from '@/services/tenant/tenant-service';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ slug: string }> }
) {
  const { slug } = await params;
  
  try {
    const tenant = await getTenantData(slug);
    
    if (!tenant?.description) {
      return NextResponse.json([]);
    }

    try {
      const carouselData = JSON.parse(tenant.description);
      if (Array.isArray(carouselData) && carouselData.length > 0) {
        return NextResponse.json(carouselData);
      }
    } catch (error) {
      console.error('Erro ao fazer parse da descrição do tenant:', error);
    }

    return NextResponse.json([]);
  } catch (error) {
    console.error('Erro ao obter dados do carrossel:', error);
    return NextResponse.json([]);
  }
}