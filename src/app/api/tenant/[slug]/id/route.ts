import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/services/supabase/types/database.types';
import { getSupabaseConfig } from '@/config/supabase';
import { SupabaseErrorHandler } from '@/utils/supabase-error-handler';

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  const slug = params.slug;
  
  if (!slug) {
    return NextResponse.json({ id: null }, { status: 400 });
  }
  
  try {
    // Criar cliente Supabase
    const config = getSupabaseConfig();
    const supabase = createClient<Database>(
      config.url,
      config.anonKey
    );
    
    // Buscar tenant pelo slug
    const { data, error } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', slug)
      .single();
    
    if (error || !data) {
      return NextResponse.json({ id: null }, { status: 404 });
    }
    
    // Retornar o ID do tenant
    return NextResponse.json({ id: data.id });
  } catch (error) {
    const analysis = SupabaseErrorHandler.analyzeError(error, 'tenant-id-api');
    console.error('🚨 API - Erro ao buscar ID do tenant:', analysis.debugInfo);
    return NextResponse.json({ id: null }, { status: 500 });
  }
} 