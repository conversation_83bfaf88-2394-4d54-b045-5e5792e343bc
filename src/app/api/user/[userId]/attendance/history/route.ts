import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/services/supabase/server";
import { protectProfileAccess } from "@/services/user/profile-access";

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const supabase = await createClient();
    
    const { userId } = await params;

    if (!userId) {
      return NextResponse.json(
        { error: "ID de usuário inválido" },
        { status: 400 }
      );
    }

    const accessResult = await protectProfileAccess(userId);
    if (accessResult) {
      return NextResponse.json(
        { error: "Não autorizado a acessar este perfil" },
        { status: 403 }
      );
    }

    const { data: sessionData } = await supabase.auth.getSession();
    if (!sessionData?.session) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      );
    }

    // Primeiro buscar o student_id correspondente ao user_id
    const { data: student, error: studentError } = await supabase
      .from("students")
      .select("id")
      .eq("user_id", userId)
      .single();

    if (studentError) {
      console.error("Erro ao buscar dados do estudante:", studentError);
      return NextResponse.json(
        { error: "Estudante não encontrado" },
        { status: 404 }
      );
    }

    const studentId = student.id;

    const searchParams = request.nextUrl.searchParams;
    let limit = parseInt(searchParams.get('limit') || '5', 10);
    
    const MAX_LIMIT = 50;
    const MIN_LIMIT = 1;
    
    if (isNaN(limit) || limit < MIN_LIMIT) {
      limit = 5; 
    } else if (limit > MAX_LIMIT) {
      limit = MAX_LIMIT;
    }

    const { data, error } = await supabase
      .from("attendance")
      .select(`
        id,
        checked_in_at,
        notes,
        class_id,
        checked_in_by
      `)
      .eq("student_id", studentId)
      .order('checked_in_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Erro ao buscar histórico de presença:", error);
      return NextResponse.json(
        { error: "Erro ao buscar histórico de presença" },
        { status: 500 }
      );
    }

    // Buscar dados das aulas e instrutores separadamente
    const processedData = await Promise.all(data.map(async (record) => {
      // Buscar dados da aula
      const { data: classData } = await supabase
        .from("classes")
        .select(`
          id,
          name,
          description,
          start_time,
          end_time,
          instructor_id
        `)
        .eq("id", record.class_id)
        .single();

      // Buscar dados do instrutor
      const { data: instructorData } = await supabase
        .from("users")
        .select("first_name, last_name, full_name")
        .eq("id", classData?.instructor_id)
        .single();

      // Buscar dados de quem registrou
      const { data: checkedByData } = await supabase
        .from("users")
        .select("first_name, last_name, full_name")
        .eq("id", record.checked_in_by)
        .single();

      let duration = '1h30'; // Duração padrão
      if (classData?.start_time && classData?.end_time) {
        const start = new Date(classData.start_time);
        const end = new Date(classData.end_time);
        const durationMinutes = (end.getTime() - start.getTime()) / (1000 * 60);
        duration = `${Math.floor(durationMinutes / 60)}h${durationMinutes % 60 > 0 ? (durationMinutes % 60) + 'min' : ''}`;
      }

      const instructorName = instructorData?.full_name || 
                           [instructorData?.first_name, instructorData?.last_name].filter(Boolean).join(' ') || 
                           'Instrutor não identificado';

      const checkedInByName = checkedByData?.full_name || 
                             [checkedByData?.first_name, checkedByData?.last_name].filter(Boolean).join(' ') || 
                             'Sistema';

      return {
        id: record.id,
        checked_in_at: record.checked_in_at,
        class_type: classData?.name || 'Aula regular',
        class_description: classData?.description || '',
        instructor_name: `Professor ${instructorName}`,
        checked_in_by: checkedInByName,
        duration: duration,
        notes: record.notes
      };
         }));

    return NextResponse.json(processedData);
  } catch (error) {
    console.error("Erro ao processar solicitação:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
} 