import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/services/supabase/server";
import { cookies } from "next/headers";

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Obter cliente Supabase
    const supabase = await createClient();
    
    // Await params before accessing properties
    const resolvedParams = await Promise.resolve(params);
    const { userId } = resolvedParams;

    // Verificar se o userId é válido
    if (!userId) {
      return NextResponse.json(
        { error: "ID de usuário inválido" },
        { status: 400 }
      );
    }

    const { data: sessionData } = await supabase.auth.getSession();
    if (!sessionData?.session) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      );
    }

    const { data: student, error: studentError } = await supabase
      .from("students")
      .select("id, attendance_rate, last_attendance_date")
      .eq("user_id", userId)
      .single();

    if (studentError) {
      console.error("Erro ao buscar dados do estudante:", studentError);
      return NextResponse.json(
        { error: "Erro ao buscar dados de presença" },
        { status: 500 }
      );
    }

    // Usar o student.id (não o userId) para consultas de attendance
    const studentId = student.id;

    const { count: totalClassesCount, error: countError } = await supabase
      .from("attendance")
      .select("*", { count: "exact" })
      .eq("student_id", studentId);

    if (countError) {
      console.error("Erro ao contar aulas:", countError);
    }

    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { count: monthClassesCount, error: monthCountError } = await supabase
      .from("attendance")
      .select("*", { count: "exact" })
      .eq("student_id", studentId)
      .gte("checked_in_at", thirtyDaysAgo.toISOString());

    if (monthCountError) {
      console.error("Erro ao contar aulas do mês:", monthCountError);
    }

    const { data: streakData, error: streakError } = await supabase.rpc(
      "get_student_streak",
      { student_id_param: studentId }
    );

    let currentStreak = 0;
    let longestStreak = 0;

    if (!streakError && streakData && streakData.length > 0) {
      const streak = streakData[0];
      currentStreak = streak.current_streak || 0;
      longestStreak = streak.longest_streak || 0;
    } else {
      console.warn("Função de sequência não disponível:", streakError);
    }

    const statsResponse = {
      monthlyRate: student?.attendance_rate ? Math.round(student.attendance_rate) : 0,
      totalRate: student?.attendance_rate ? Math.round(student.attendance_rate) : 0,
      totalClasses: totalClassesCount || 0,
      monthlyClasses: monthClassesCount || 0,
      currentStreak: currentStreak,
      longestStreak: longestStreak,
      lastAttendance: student?.last_attendance_date || null,
    };

    return NextResponse.json(statsResponse);
  } catch (error) {
    console.error("Erro ao processar solicitação:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
} 