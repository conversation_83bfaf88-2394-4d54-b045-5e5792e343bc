import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/services/supabase/server';
import { getPermissionService } from '@/services/permissions/service';
import { cache } from 'react';

// Configuração de cache para o endpoint
export const dynamic = 'force-dynamic'; // Para dados dinâmicos baseados em auth/params
export const revalidate = 300; // Revalidar a cada 5 minutos (300 segundos)

// Função memoizada para buscar detalhes da faixa
const getBeltDetails = cache(async (userId: string, beltId: string, supabase: any) => {
  try {
    // Primeira abordagem: buscar diretamente a faixa pelo ID
    const { data: beltData, error: beltError } = await supabase
      .from('student_belts')
      .select(`
        id,
        degree,
        awarded_at,
        belt_level_id,
        users:awarded_by (
          first_name,
          last_name
        ),
        belt_levels:belt_levels!inner (
          belt_color,
          stripe_color,
          show_center_line,
          center_line_color,
          label,
          sort_order,
          modalities:modalities!inner(
              name
            )
        )
      `)
      .eq('id', beltId)
      .single();
      
    if (beltError) {
      console.error('Erro na primeira tentativa de buscar dados da faixa:', beltError);
    } else if (beltData) {
      const awardedAt = beltData.awarded_at 
        ? new Date(beltData.awarded_at).toLocaleDateString('pt-BR') 
        : null;
      
      let instructorName = null;
      if (beltData.users && typeof beltData.users === 'object') {
        const instructor = beltData.users as any;
        instructorName = `${instructor.first_name || ''}${instructor.last_name ? ' ' + instructor.last_name : ''}`;
      }
      
      const level = beltData.belt_levels as any;
      return {
        id: beltData.id,
        color: level?.belt_color || 'white',
        degree: beltData.degree,
        awardedAt,
        instructor: instructorName,
        stripeColor: level?.stripe_color || null,
        showCenterLine: level?.show_center_line || null,
        centerLineColor: level?.center_line_color || null,
        label: level?.label || null,
        sortOrder: level?.sort_order || null,
        modality_name: level?.modalities?.name || null,
        belt_label: level?.label || null,
      };
    }
    
    // Segunda abordagem: Verificar se a faixa pertence ao usuário e buscar direto
    const { data: studentData, error: studentError } = await supabase
      .from('students')
      .select('current_belt_id')
      .eq('user_id', userId)
      .single();
    
    if (studentError) {
      console.error('Erro ao verificar estudante:', studentError);
      throw new Error('Erro ao verificar dados do estudante');
    }
    
    if (studentData.current_belt_id !== beltId) {
      throw new Error('Faixa não pertence ao aluno');
    }
    
    const { data: simpleBeltData, error: simpleBeltError } = await supabase
      .from('student_belts')
      .select('*')
      .eq('id', beltId)
      .single();
      
    if (simpleBeltError) {
      console.error('Erro na consulta simplificada:', simpleBeltError);
    } else if (simpleBeltData) {
      // Buscar o instrutor com uma consulta adicional
      let instructorName = null;
      
      if (simpleBeltData.awarded_by) {
        const { data: instructorData, error: instructorError } = await supabase
          .from('users')
          .select('first_name, last_name')
          .eq('id', simpleBeltData.awarded_by)
          .single();
          
        if (!instructorError && instructorData) {
          instructorName = `${instructorData.first_name || ''}${instructorData.last_name ? ' ' + instructorData.last_name : ''}`;
        }
      }
      
      return {
        id: simpleBeltData.id,
        // Fallback usa valores mínimos
        color: 'white',
        degree: simpleBeltData.degree || 0,
        awardedAt: simpleBeltData.awarded_at 
          ? new Date(simpleBeltData.awarded_at).toLocaleDateString('pt-BR') 
          : null,
        instructor: instructorName,
        modality_name: null,
        belt_label: null,
      };
    }
    
    return {
      id: beltId,
      color: 'white',
      degree: 0,
      awardedAt: null,
      instructor: null,
      modality_name: null,
      belt_label: null,
    };
  } catch (error) {
    console.error('Erro ao buscar detalhes da faixa:', error);
    throw error;
  }
});

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params;
    const { searchParams } = new URL(request.url);
    const beltId = searchParams.get('beltId');
    
    if (!beltId) {
      return NextResponse.json(
        { error: 'ID da faixa não fornecido' },
        { status: 400 }
      );
    }
    
    const supabase = await createClient();
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }
    
    const permissionService = getPermissionService();
    const result = await permissionService.hasPermission(
      user.id,
      'profile',
      'view',
      userId
    );
    
    if (!result.granted) {
      return NextResponse.json(
        { error: 'Acesso não autorizado' },
        { status: 403 }
      );
    }
    
    // Usar função memoizada para buscar dados da faixa
    try {
      const beltDetails = await getBeltDetails(userId, beltId, supabase);
      
      const response = NextResponse.json(beltDetails);
      
      // Adicionar headers de cache para o cliente
      response.headers.set('Cache-Control', 'public, s-maxage=300, stale-while-revalidate=86400');
      response.headers.set('CDN-Cache-Control', 'public, s-maxage=300');
      response.headers.set('Vercel-CDN-Cache-Control', 'public, s-maxage=300');
      
      return response;
    } catch (error: any) {
      if (error.message === 'Faixa não pertence ao aluno') {
        return NextResponse.json(
          { error: error.message },
          { status: 403 }
        );
      }
      
      const fallbackResponse = NextResponse.json({
        id: beltId,
        color: 'white',
        degree: 0,
        awardedAt: null,
        instructor: null,
        modality_name: null,
        belt_label: null,
      });
      
      // Cache mais curto para fallback
      fallbackResponse.headers.set('Cache-Control', 'public, s-maxage=60, stale-while-revalidate=300');
      
      return fallbackResponse;
    }
    
  } catch (error) {
    console.error('Erro na API de detalhes da faixa:', error);
    let fallbackId = 'unknown';
    try {
      const url = new URL(request.url);
      fallbackId = url.searchParams.get('beltId') || 'unknown';
    } catch (e) {
      console.error('Erro ao extrair beltId da URL:', e);
    }
    
    const errorResponse = NextResponse.json({
      id: fallbackId,
      color: 'white',
      degree: 0,
      awardedAt: null,
      instructor: null,
      modality_name: null,
      belt_label: null,
    });
    
    // Cache ainda mais curto para respostas de erro
    errorResponse.headers.set('Cache-Control', 'public, s-maxage=30, stale-while-revalidate=60');
    
    return errorResponse;
  }
}