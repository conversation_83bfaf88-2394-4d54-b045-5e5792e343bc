import { NextRequest, NextResponse } from 'next/server';
import { checkUserMetadataConsistency } from '@/services/auth/utils/server-utils';
import { getPermissionService } from '@/services/permissions';
import { createTenantServerClient } from '@/services/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;
    
    if (!userId) {
      return NextResponse.json(
        { error: 'ID do usuário é obrigatório' }, 
        { status: 400 }
      );
    }

    // Verificar autenticação e permissões
    const supabase = await createTenantServerClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Não autenticado' }, 
        { status: 401 }
      );
    }

    const permissionService = getPermissionService();
    const permissionResult = await permissionService.hasPermission(
      user.id,
      'user',
      'view',
      userId
    );

    if (!permissionResult.granted) {
      return NextResponse.json(
        { error: 'Acesso negado' }, 
        { status: 403 }
      );
    }

    // Verificar consistência dos metadados
    const consistencyResult = await checkUserMetadataConsistency(userId);
    
    return NextResponse.json({
      consistent: consistencyResult.consistent,
      differences: consistencyResult.differences,
      userId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao verificar consistência de metadados:', error);
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor ao verificar consistência',
        details: error instanceof Error ? error.message : String(error)
      }, 
      { status: 500 }
    );
  }
} 