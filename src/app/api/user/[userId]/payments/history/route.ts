import { NextRequest, NextResponse } from "next/server";
import { createAdminClient } from "@/services/supabase/server";
import { formatPaymentMethodName } from "@/utils/payment-method-formatter";

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const supabase = await createAdminClient();
    
    const resolvedParams = await Promise.resolve(params);
    const { userId } = resolvedParams;

    if (!userId) {
      return NextResponse.json(
        { error: "ID de usuário inválido" },
        { status: 400 }
      );
    }

    const { data: sessionData } = await supabase.auth.getSession();
    if (!sessionData?.session) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      );
    }

    // Primeiro buscar o student_id baseado no user_id
    const { data: student, error: studentError } = await supabase
      .from("students")
      .select("id")
      .eq("user_id", userId)
      .single();

    if (studentError && studentError.code !== 'PGRST116') {
      console.error("Erro ao buscar dados do estudante:", studentError);
      return NextResponse.json(
        { error: "Erro ao buscar dados do estudante" },
        { status: 500 }
      );
    }

    if (!student) {
      return NextResponse.json([]);
    }

    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '10', 10);

    const { data, error } = await supabase
      .from("payments")
      .select(`
        id,
        amount,
        currency,
        status,
        payment_method,
        payment_type,
        description,
        due_date,
        paid_at,
        created_at,
        membership_id,
        metadata
      `)
      .eq("student_id", student.id)
      .in("status", ["paid", "failed", "awaiting_confirmation"]) // Incluir pagamentos aguardando confirmação
      .order('due_date', { ascending: false }) // Ordenar por data de vencimento (mais recente primeiro)
      .limit(limit);

    if (error && error.code !== 'PGRST116') {
      console.error("Erro ao buscar histórico de pagamentos:", error);
      return NextResponse.json(
        { error: "Erro ao buscar histórico de pagamentos" },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json([]);
    }

    const processedData = data.map((payment: any) => {
      // Remover referência ao stripe_payment_id que não existe na tabela
      let receiptUrl = undefined;

      // Traduzir tipo de pagamento para português
      const paymentTypeTranslations: Record<string, string> = {
        'recurring': 'Mensalidade',
        'signup_fee': 'Taxa de Inscrição',
        'graduation_fee': 'Taxa de Graduação',
        'late_fee': 'Multa por Atraso',
        'cancellation_fee': 'Taxa de Cancelamento',
        'manual': 'Cobrança Manual',
        'product': 'Produto/Serviço'
      };

      return {
        id: payment.id,
        amount: Number(payment.amount || 0),
        currency: payment.currency || 'BRL',
        status: payment.status,
        payment_method: formatPaymentMethodName(payment.payment_method),
        payment_type: payment.payment_type || 'manual',
        payment_type_label: paymentTypeTranslations[payment.payment_type] || 'Outros',
        description: payment.description || paymentTypeTranslations[payment.payment_type] || 'Pagamento',
        due_date: payment.due_date,
        paid_at: payment.paid_at, // Data real do pagamento
        created_at: payment.created_at,
        membership_id: payment.membership_id,
        receipt_url: receiptUrl,
        metadata: payment.metadata || {}
      };
    });

    return NextResponse.json(processedData);
  } catch (error) {
    console.error("Erro ao processar solicitação:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
} 