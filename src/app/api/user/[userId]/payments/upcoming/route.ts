import { NextRequest, NextResponse } from "next/server";
import { createAdminClient } from "@/services/supabase/server";

interface UpcomingPayment {
  id: string;
  due_date: string;
  amount: number;
  currency: string;
  status: string;
  payment_type: string;
  payment_type_label: string;
  description: string;
}

// Interfaces removidas - não são mais necessárias

interface PendingPaymentData {
  id: string;
  amount: number;
  currency: string;
  payment_type: string;
  description: string;
  due_date: string;
  status: string;
}

export async function GET(
  _request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const supabase = await createAdminClient();
    
    const resolvedParams = await Promise.resolve(params);
    const { userId } = resolvedParams;

    if (!userId) {
      return NextResponse.json(
        { error: "ID de usuário inválido" },
        { status: 400 }
      );
    }

    const { data: sessionData } = await supabase.auth.getSession();
    if (!sessionData?.session) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      );
    }

    // Buscar estudante
    const { data: student, error: studentError } = await supabase
      .from("students")
      .select(`
        id,
        user_id
      `)
      .eq("user_id", userId)
      .single();

    if (studentError || !student) {
      return NextResponse.json([]);
    }

    // Não precisamos mais buscar memberships para simular pagamentos

    // Buscar pagamentos pendentes e aguardando confirmação (incluindo recorrentes)
    const { data: pendingPayments, error: paymentsError } = await supabase
      .from("payments")
      .select(`
        id,
        amount,
        currency,
        payment_type,
        description,
        due_date,
        status
      `)
      .eq("student_id", student.id)
      .in("status", ["pending", "awaiting_confirmation"])
      .gte("due_date", new Date().toISOString().split('T')[0]) // Apenas pagamentos futuros
      .order('due_date', { ascending: true })
      .limit(10) as { data: PendingPaymentData[] | null, error: any };

    // Verificar erros significativos
    if (paymentsError && paymentsError.code !== 'PGRST116') {
      console.error("Erro ao buscar pagamentos pendentes:", paymentsError);
      return NextResponse.json(
        { error: "Erro ao buscar pagamentos pendentes" },
        { status: 500 }
      );
    }

    const upcomingPayments: UpcomingPayment[] = [];

    // Traduzir tipo de pagamento para português
    const paymentTypeTranslations: Record<string, string> = {
      'recurring': 'Mensalidade',
      'signup_fee': 'Taxa de Inscrição',
      'graduation_fee': 'Taxa de Graduação',
      'late_fee': 'Multa por Atraso',
      'cancellation_fee': 'Taxa de Cancelamento',
      'manual': 'Cobrança Manual',
      'product': 'Produto/Serviço'
    };

    // Converter pagamentos pendentes para o formato da resposta
    if (pendingPayments && pendingPayments.length > 0) {
      pendingPayments.forEach((payment: PendingPaymentData) => {
        upcomingPayments.push({
          id: payment.id,
          due_date: payment.due_date,
          amount: Number(payment.amount || 0),
          currency: payment.currency || 'BRL',
          status: payment.status,
          payment_type: payment.payment_type || 'manual',
          payment_type_label: paymentTypeTranslations[payment.payment_type] || 'Outros',
          description: payment.description || paymentTypeTranslations[payment.payment_type] || 'Pagamento'
        });
      });
    }

    // Ordenar por data de vencimento
    upcomingPayments.sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime());

    return NextResponse.json(upcomingPayments);
  } catch (error) {
    console.error("Erro ao processar solicitação:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
} 