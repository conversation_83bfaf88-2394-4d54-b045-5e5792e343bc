'use server';

import { createTenantServerClient } from '@/services/supabase/server';
import { getTenantSlug } from '@/services/tenant';

export interface ClassItem {
  id: string;
  time: string;
  endTime: string;
  name: string;
  duration: string;
  studentsCount: number;
  status: 'active' | 'upcoming' | 'completed';
}

export interface RecentCheckIn {
  id: string;
  studentName: string;
  className: string;
  time: string;
  checkedInAt: string;
  status: 'checked-in';
  avatar?: string;
  beltColor?: string;
  beltDegree?: number;
}

/**
 * Busca as aulas de hoje para exibir no sidebar
 * Inclui aulas em andamento, próximas e terminadas do dia atual
 */
export async function getTodayClasses(): Promise<{
  success: boolean;
  data?: {
    activeClasses: ClassItem[];
    upcomingClasses: ClassItem[];
    completedClasses: ClassItem[];
  };
  error?: string;
}> {
  try {
    const supabase = await createTenantServerClient();
    const tenantSlug = await getTenantSlug();

    if (!tenantSlug) {
      return { success: false, error: 'Tenant não encontrado' };
    }

    // Buscar tenant_id a partir do slug
    const { data: tenant } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', tenantSlug)
      .single();

    if (!tenant) {
      return { success: false, error: 'Tenant não encontrado' };
    }

    // Data de hoje - início e fim do dia
    const today = new Date();
    const startOfDay = new Date(today);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999);

    // Buscar aulas de hoje
    const { data: classes, error } = await supabase
      .from('classes')
      .select(`
        id,
        name,
        start_time,
        end_time,
        status
      `)
      .eq('tenant_id', tenant.id)
      .gte('start_time', startOfDay.toISOString())
      .lte('start_time', endOfDay.toISOString())
      .is('deleted_at', null)
      .order('start_time');

    if (error) {
      console.error('Erro ao buscar aulas:', error);
      return { success: false, error: 'Erro ao buscar aulas' };
    }

    // Buscar contagem de attendance para cada aula
    const classesWithAttendance = await Promise.all(
      (classes || []).map(async (classItem) => {
        const { count } = await supabase
          .from('attendance')
          .select('*', { count: 'exact', head: true })
          .eq('class_id', classItem.id);
        
        return {
          ...classItem,
          attendanceCount: count || 0
        };
      })
    );

    const now = new Date();
    const activeClasses: ClassItem[] = [];
    const upcomingClasses: ClassItem[] = [];
    const completedClasses: ClassItem[] = [];

    for (const classItem of classesWithAttendance) {
      const startTime = new Date(classItem.start_time);
      const endTime = new Date(classItem.end_time);
      
      // Calcular duração em minutos
      const durationMs = endTime.getTime() - startTime.getTime();
      const durationMinutes = Math.round(durationMs / (1000 * 60));
      
      const formattedClass: ClassItem = {
        id: classItem.id,
        time: startTime.toLocaleTimeString('pt-BR', {
          hour: '2-digit',
          minute: '2-digit'
        }),
        endTime: endTime.toLocaleTimeString('pt-BR', {
          hour: '2-digit',
          minute: '2-digit'
        }),
        name: classItem.name,
        duration: `${durationMinutes} minutos`,
        studentsCount: classItem.attendanceCount,
        status: classItem.status === 'ongoing' ? 'active' : 
                classItem.status === 'completed' ? 'completed' : 'upcoming'
      };

      // Categorizar a aula baseado no horário atual e status
      if (classItem.status === 'ongoing' || (now >= startTime && now <= endTime)) {
        activeClasses.push({ ...formattedClass, status: 'active' });
      } else if (classItem.status === 'completed' || now > endTime) {
        completedClasses.push({ ...formattedClass, status: 'completed' });
      } else {
        upcomingClasses.push(formattedClass);
      }
    }

    return {
      success: true,
      data: {
        activeClasses,
        upcomingClasses,
        completedClasses
      }
    };

  } catch (error) {
    console.error('Erro inesperado ao buscar aulas:', error);
    return { success: false, error: 'Erro inesperado' };
  }
}

/**
 * Busca os check-ins mais recentes do dia atual
 */
export async function getRecentCheckIns(): Promise<{
  success: boolean;
  data?: RecentCheckIn[];
  error?: string;
}> {
  try {
    const supabase = await createTenantServerClient();
    const tenantSlug = await getTenantSlug();

    if (!tenantSlug) {
      return { success: false, error: 'Tenant não encontrado' };
    }

    // Buscar tenant_id a partir do slug
    const { data: tenant } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', tenantSlug)
      .single();

    if (!tenant) {
      return { success: false, error: 'Tenant não encontrado' };
    }

    // Data de hoje - início do dia
    const today = new Date();
    const startOfDay = new Date(today);
    startOfDay.setHours(0, 0, 0, 0);

    // Buscar check-ins de hoje com dados relacionados
    const { data: checkIns, error } = await supabase
      .from('attendance')
      .select(`
        id,
        checked_in_at,
        student:students(
          id,
          user:users!students_user_id_fkey(
            first_name,
            last_name,
            avatar_url
          ),
          current_belt_id
        ),
        class:classes(
          id,
          name
        )
      `)
      .eq('tenant_id', tenant.id)
      .gte('checked_in_at', startOfDay.toISOString())
      .order('checked_in_at', { ascending: false })
      .limit(20); // Limitar aos 20 mais recentes

    if (error) {
      console.error('Erro ao buscar check-ins:', error);
      return { success: false, error: 'Erro ao buscar check-ins' };
    }

    const recentCheckIns: RecentCheckIn[] = await Promise.all(
      (checkIns || []).map(async (checkIn: any) => {
        const checkedInAt = new Date(checkIn.checked_in_at);
        const student = checkIn.student;
        const user = student?.user;
        const classData = checkIn.class;

        // Carregar detalhes da faixa via RPC
        let beltColor: string | undefined;
        let beltDegree: number | undefined;
        if (student) {
          const { data: beltDetails } = await (supabase as any).rpc('get_student_current_belt_details', { student_id_param: student.id });
          if ((beltDetails as any[])?.length > 0) {
            const belt = (beltDetails as any[])[0] as any;
            beltColor = belt.belt_color;
            beltDegree = belt.degree;
          }
        }

        return {
          id: checkIn.id,
          studentName: user ? `${user.first_name} ${user.last_name || ''}`.trim() : 'Aluno',
          className: classData?.name || 'Aula',
          time: checkedInAt.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
          }),
          checkedInAt: checkIn.checked_in_at,
          status: 'checked-in' as const,
          avatar: user?.avatar_url || undefined,
          beltColor,
          beltDegree
        };
      })
    );

    return {
      success: true,
      data: recentCheckIns
    };

  } catch (error) {
    console.error('Erro inesperado ao buscar check-ins:', error);
    return { success: false, error: 'Erro inesperado' };
  }
}

/**
 * Busca contagem de estudantes em uma aula específica
 */
export async function getClassStudentsCount(classId: string): Promise<{
  success: boolean;
  data?: { count: number; students: Array<{ id: string; name: string; avatar?: string; beltColor?: string; beltDegree?: number }> };
  error?: string;
}> {
  try {
    const supabase = await createTenantServerClient();
    const tenantSlug = await getTenantSlug();

    if (!tenantSlug) {
      return { success: false, error: 'Tenant não encontrado' };
    }

    // Buscar tenant_id a partir do slug
    const { data: tenant } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', tenantSlug)
      .single();

    if (!tenant) {
      return { success: false, error: 'Tenant não encontrado' };
    }

    // Buscar estudantes que fizeram check-in nesta aula
    const { data: attendance, error } = await supabase
      .from('attendance')
      .select(`
        id,
        student:students(
          id,
          user:users!students_user_id_fkey(
            first_name,
            last_name,
            avatar_url
          ),
          current_belt_id
        )
      `)
      .eq('tenant_id', tenant.id)
      .eq('class_id', classId);

    if (error) {
      console.error('Erro ao buscar estudantes da aula:', error);
      return { success: false, error: 'Erro ao buscar estudantes' };
    }

    const students = await Promise.all((attendance || []).map(async (item: any) => {
      const student = item.student;
      const user = student?.user;
      // Detalhes da faixa via RPC
      let beltColor: string | undefined;
      let beltDegree: number | undefined;
      if (student) {
        const { data: beltDetails } = await (supabase as any).rpc('get_student_current_belt_details', { student_id_param: student.id });
        if ((beltDetails as any[])?.length > 0) {
          const belt = (beltDetails as any[])[0] as any;
          beltColor = belt.belt_color;
          beltDegree = belt.degree;
        }
      }
      
      return {
        id: student?.id || '',
        name: user ? `${user.first_name} ${user.last_name || ''}`.trim() : 'Aluno',
        avatar: user?.avatar_url || undefined,
        beltColor,
        beltDegree,
      };
    }));

    return {
      success: true,
      data: {
        count: students.length,
        students
      }
    };

  } catch (error) {
    console.error('Erro inesperado ao buscar estudantes da aula:', error);
    return { success: false, error: 'Erro inesperado' };
  }
}

/**
 * Função de teste para verificar se as server actions estão funcionando
 * Pode ser removida após confirmação
 */
export async function testSidebarActions(): Promise<{
  success: boolean;
  results?: {
    todayClasses: any;
    recentCheckIns: any;
  };
  error?: string;
}> {
  try {
    console.log('Testando server actions...');
    
    const todayClassesResult = await getTodayClasses();
    console.log('getTodayClasses result:', todayClassesResult);
    
    const recentCheckInsResult = await getRecentCheckIns();
    console.log('getRecentCheckIns result:', recentCheckInsResult);
    
    return {
      success: true,
      results: {
        todayClasses: todayClassesResult,
        recentCheckIns: recentCheckInsResult
      }
    };
  } catch (error) {
    console.error('Erro no teste:', error);
    return {
      success: false,
      error: 'Erro no teste das server actions'
    };
  }
} 