'use client';

import { useState } from 'react';
import { CheckInTabs } from './checkin-tabs';
import { CodeTab } from './tabs/code-tab';
import { NameTab } from './tabs/name-tab';
import { QRTab } from './tabs/qr-tab';
import { ConfirmationModal } from './confirmation-modal';

type ActiveTab = 'code' | 'name' | 'qr';

export function CheckInMainCard() {
  const [activeTab, setActiveTab] = useState<ActiveTab>('code');
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [confirmationData, setConfirmationData] = useState<any>(null);

  const handleCheckInSuccess = (data: any) => {
    setConfirmationData(data);
    setShowConfirmation(true);
  };

  const handleGoBack = () => {
    setShowConfirmation(false);
    setConfirmationData(null);
  };

  if (showConfirmation) {
    return (
      <ConfirmationModal 
        data={confirmationData}
        onGoBack={handleGoBack}
      />
    );
  }

  return (
    <div className="bg-gradient-to-br from-teal-700 to-teal-800 dark:from-gray-800 dark:to-gray-900 rounded-3xl shadow-xl p-8">
      {/* Navigation Tabs */}
      <CheckInTabs 
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-white mb-2">Check-In</h2>
        <p className="text-white/70 dark:text-gray-300 text-sm">Faça o check-in de forma rápida e segura</p>
      </div>

      {/* Tab Content */}
      <div className="relative">
        {activeTab === 'code' && (
          <CodeTab onCheckInSuccess={handleCheckInSuccess} />
        )}
        
        {activeTab === 'name' && (
          <NameTab onCheckInSuccess={handleCheckInSuccess} />
        )}
        
        {/* {activeTab === 'qr' && (
          <QRTab onCheckInSuccess={handleCheckInSuccess} />
        )} */}
      </div>
    </div>
  );
} 