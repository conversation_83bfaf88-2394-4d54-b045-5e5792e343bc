'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';
import { useReceptionMode } from '@/contexts/ReceptionModeContext';
import { useUser } from '@/hooks/user/use-user-context';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';
import { validatePassword } from '../actions/validate-password';

interface ExitModalProps {
  isOpen: boolean;
  onClose: () => void;
  userName?: string;
}

export function ExitModal({
  isOpen,
  onClose,
  userName,
}: ExitModalProps) {
  const [isExiting, setIsExiting] = useState(false);
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const router = useRouter();
  const { isReceptionModeActive, state, deactivateMode } = useReceptionMode();
  const { profile } = useUser();

  const currentUserName = userName || state.activatedBy?.name || profile?.email || 'Usuário';

  const handleExit = async () => {
    if (!password) {
      setError('Digite sua senha para continuar');
      return;
    }

    setIsExiting(true);
    setError('');

    let exitSuccessful = false;

    try {
      if (isReceptionModeActive) {
        const userEmail = state.activatedBy?.email || profile?.email || '';
        
        if (!userEmail) {
          setError('Não foi possível identificar o usuário para validação.');
          return;
        }

        const result = await validatePassword(userEmail, password);

        if (!result.success) {
          setError(result.error || 'Senha incorreta. Tente novamente.');
          return;
        }

        deactivateMode();
        
        router.push('/dashboard');
      } else {
        await new Promise(resolve => setTimeout(resolve, 1500));
        router.push('/dashboard');
      }

      exitSuccessful = true;
    } catch (error) {
      console.error('Erro ao sair:', error);
      setError('Erro inesperado. Tente novamente.');
    } finally {
      if (exitSuccessful) {
        setTimeout(() => {
          setIsExiting(false);
          setPassword('');
          onClose();
        }, 800);
      } else {
        setIsExiting(false);
      }
    }
  };

  const handleClose = () => {
    if (!isExiting) {
      setPassword('');
      setError('');
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md p-8 text-center bg-white dark:bg-gray-900 rounded-2xl shadow-2xl [&>button:last-child]:hidden">
        <DialogHeader className="text-center sm:text-center space-y-0">
          <DialogTitle className="text-2xl font-semibold text-slate-800 dark:text-gray-100 mb-2">
            {isReceptionModeActive ? 'Sair do Modo de Recepção' : 'Sair do Modo de Atendimento'}
          </DialogTitle>
          <DialogDescription className="text-slate-600 dark:text-gray-300 mb-6">
            {isReceptionModeActive ? (
              <>
                Para desativar o modo de recepção, digite a senha do usuário que o ativou. <br />
                Ativado por{' '}
                <strong className="font-medium">{state.activatedBy?.name}</strong>.
              </>
            ) : (
              <>
                Para desativar o modo de atendimento, insira sua senha. <br />
                Conectado como{' '}
                <strong className="font-medium">{currentUserName}</strong>.
              </>
            )}
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert className="border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/30 mx-auto flex items-center justify-center">
            <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400" />
            <AlertDescription className="text-red-800 dark:text-red-300">
              {error}
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-4 max-w-sm mx-auto py-4">
          <Input
            type="password"
            placeholder="Sua senha"
            value={password}
            onChange={e => setPassword(e.target.value)}
            onKeyDown={e => {
              // Garante que eventos de teclado funcionem corretamente
              e.stopPropagation();
            }}
            onInput={e => {
              // Fallback para garantir que a entrada funcione
              const target = e.target as HTMLInputElement;
              setPassword(target.value);
            }}
            className="w-full py-3 px-4 rounded-lg border border-slate-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-slate-800 dark:text-gray-100 placeholder-slate-400 dark:placeholder-gray-400 text-center focus:outline-none focus:ring-2 focus:ring-slate-400 dark:focus:ring-gray-500 focus:border-slate-400 dark:focus:border-gray-500 h-12 text-base"
            autoComplete="off"
            autoCorrect="off"
            spellCheck={false}
          />
          <Button
            onClick={handleExit}
            disabled={isExiting || !password}
            className="w-full bg-slate-800 dark:bg-gray-700 hover:bg-slate-700 dark:hover:bg-gray-600 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg h-12 text-sm"
          >
            {isExiting ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Saindo...
              </div>
            ) : (
              isReceptionModeActive ? 'SAIR DO MODO DE RECEPÇÃO' : 'SAIR DO MODO DE ATENDIMENTO'
            )}
          </Button>
        </div>

        <DialogFooter className="sm:justify-center">
          <Button
            variant="link"
            onClick={handleClose}
            className="text-slate-500 dark:text-gray-400 hover:text-slate-700 dark:hover:text-gray-200 text-sm pt-2 transition-colors"
          >
            Cancelar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 