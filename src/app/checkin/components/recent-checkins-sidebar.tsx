'use client';

import { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Clock, User, Calendar, UserCheck } from 'lucide-react';
import { LottieAnimation } from './lottie-animation';
import { getRecentCheckIns, type RecentCheckIn } from '../actions/sidebar-actions';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';

export function RecentCheckInsSidebar() {
  const [recentCheckIns, setRecentCheckIns] = useState<RecentCheckIn[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadRecentCheckIns = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await getRecentCheckIns();
      
      if (result.success && result.data) {
        setRecentCheckIns(result.data);
      } else {
        setError(result.error || 'Erro ao carregar check-ins');
      }
    } catch (err) {
      setError('Erro inesperado ao carregar check-ins');
      console.error('Erro ao carregar check-ins:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadRecentCheckIns();
    
    // Atualizar dados a cada 10 segundos para mostrar check-ins em tempo real
    const interval = setInterval(loadRecentCheckIns, 10000);
    
    return () => clearInterval(interval);
  }, []);

  const formatTimeAgo = (checkedInAt: string) => {
    const now = new Date();
    const checkInTime = new Date(checkedInAt);
    
    const diffMs = now.getTime() - checkInTime.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Agora mesmo';
    if (diffMins < 60) return `${diffMins} min atrás`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h atrás`;
    
    return checkInTime.toLocaleDateString('pt-BR');
  };

  const getBeltColorClass = (beltColor?: string) => {
    if (!beltColor) return 'bg-gray-500';
    
    const colorMap: Record<string, string> = {
      'white': 'bg-gray-100 border border-gray-300',
      'blue': 'bg-blue-500',
      'purple': 'bg-purple-500',
      'brown': 'bg-amber-700',
      'black': 'bg-gray-900',
      'red': 'bg-red-500',
      'orange': 'bg-orange-500',
      'yellow': 'bg-yellow-500',
      'green': 'bg-green-500'
    };
    
    return colorMap[beltColor.toLowerCase()] || 'bg-gray-500';
  };

  const renderLoadingSkeleton = () => (
    <div className="space-y-4 p-6">
      {[...Array(5)].map((_, index) => (
        <div
          key={index}
          className="animate-pulse flex items-center space-x-3"
        >
          <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
          <div className="space-y-1">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-28"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="bg-gradient-to-br from-slate-50 to-slate-100 dark:from-gray-800 dark:to-gray-900 border border-slate-200 dark:border-gray-700 h-full rounded-3xl">
      <div className="p-6 border-b border-gray-100 dark:border-gray-700">
        <div className="flex items-center gap-3">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Check-ins Recentes
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Últimas atividades
            </p>
          </div>
        </div>
      </div>

      <ScrollArea className="h-[600px]">
        <div className="h-full">
          {isLoading ? (
            renderLoadingSkeleton()
          ) : error ? (
            <div className="flex h-full flex-col items-center justify-center p-6 text-center">
              <div className="mx-auto mb-3 flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
                <div className="text-red-500 text-xl">⚠️</div>
              </div>
              <p className="text-sm text-red-500 dark:text-red-400 mb-2">
                {error}
              </p>
              <button
                onClick={loadRecentCheckIns}
                className="text-xs text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Tentar novamente
              </button>
            </div>
          ) : recentCheckIns.length > 0 ? (
            <div className="space-y-4 p-6">
              {recentCheckIns.map(checkIn => (
                <div key={checkIn.id} className="flex items-center space-x-3">
                  <div className="relative">
                    <Avatar className="h-10 w-10">
                      <AvatarImage
                        src={checkIn.avatar}
                        alt={checkIn.studentName}
                      />
                      <AvatarFallback className="rounded-full bg-gradient-to-br from-blue-400 to-blue-600 text-white font-semibold text-xs">
                        {checkIn.studentName
                          .split(' ')
                          .map(n => n[0])
                          .join('')}
                      </AvatarFallback>
                    </Avatar>
                    {checkIn.beltColor && (
                      <div 
                        className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-900 ${getBeltColorClass(checkIn.beltColor)}`}
                        title={`Faixa ${checkIn.beltColor}${checkIn.beltDegree ? ` ${checkIn.beltDegree}° grau` : ''}`}
                      >
                        {checkIn.beltDegree && checkIn.beltDegree > 1 && (
                          <div className="text-xs text-white font-bold text-center leading-3">
                            {checkIn.beltDegree}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate">
                      {checkIn.studentName}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                      {checkIn.className}
                    </p>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3 text-gray-400" />
                      <p className="text-xs text-gray-500 dark:text-gray-500">
                        {formatTimeAgo(checkIn.checkedInAt)}
                      </p>
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <Badge 
                      variant="secondary" 
                      className="bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400"
                    >
                      <UserCheck className="w-3 h-3 mr-1" />
                      Check-in
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex h-full flex-col items-center justify-center p-6 text-center text-slate-500 dark:text-gray-400">
              <div className="mx-auto mb-3 flex h-16 w-16 items-center justify-center rounded-full bg-slate-100 dark:bg-white/10">
                <DotLottieReact
                  src="/check.lottie"
                  loop
                  autoplay
                  style={{ width: '100%', height: '100%' }}
                />
              </div>
              <p className="text-sm">Nenhum check-in recente</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
} 