'use client';

import { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { 
  Users, 
  Search, 
  Clock, 
  UserCheck, 
  UserX, 
  Calendar,
  MapPin,
  X,
} from 'lucide-react';
import { getClassStudentsCount } from '../actions/sidebar-actions';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Belt, BeltColor } from '@/components/belt';

interface Student {
  id: string;
  name: string;
  avatar?: string;
  belt?: BeltColor;
}

interface StudentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  classId?: string;
  className?: string;
}

export function StudentsModal({ isOpen, onClose, classId, className }: StudentsModalProps) {
  const [students, setStudents] = useState<Student[]>([]);
  const [studentCount, setStudentCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen && classId) {
      loadClassStudents();
    }
  }, [isOpen, classId]);

  useEffect(() => {
    // Limpa o termo de busca quando o modal fecha
    if (!isOpen) {
      setSearchTerm('');
    }
  }, [isOpen]);

  const loadClassStudents = async () => {
    if (!classId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await getClassStudentsCount(classId);
      
      if (result.success && result.data) {
        setStudents(
          result.data.students.map((s: any) => ({
            ...s,
            belt: (s.beltColor ?? undefined) as BeltColor | undefined,
          }))
        );
        setStudentCount(result.data.count);
      } else {
        setError(result.error || 'Erro ao carregar alunos');
      }
    } catch (err) {
      setError('Erro inesperado ao carregar alunos');
      console.error('Erro ao carregar alunos da aula:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getInitials = (fullName: string) =>
    fullName
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);

  const renderStudentItem = (student: Student) => (
    <div
      key={student.id}
      className="flex items-center gap-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
    >
      <Avatar className="h-10 w-10 ring-2 ring-border/10 shrink-0">
        {student.avatar && (
          <AvatarImage
            src={student.avatar}
            alt={`Avatar de ${student.name}`}
            className="object-cover"
          />
        )}
        <AvatarFallback className="bg-gradient-to-br from-blue-400 to-blue-600 text-white font-semibold text-sm">
          {getInitials(student.name)}
        </AvatarFallback>
      </Avatar>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
          {student.name}
        </p>
        <div className="flex items-center gap-2 mt-0.5">
          {student.belt && (
            <Belt color={student.belt} size="xs" />
          )}
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Presente na aula
          </p>
        </div>
      </div>
      <div className="flex-shrink-0">
        <UserCheck className="w-4 h-4 text-green-600 dark:text-green-400" />
      </div>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] dark:bg-gray-900 dark:border-gray-700">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <DialogTitle className="text-lg font-semibold dark:text-gray-100">
                Alunos da Aula
              </DialogTitle>
              <DialogDescription className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {className ? `${className} - ` : ''}Alunos presentes: {studentCount}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        {isLoading ? (
          <div className="py-8 text-center">
            <div className="w-8 h-8 border-2 border-blue-600 dark:border-blue-400 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Carregando alunos da aula...</p>
          </div>
        ) : error ? (
          <div className="py-8 text-center">
            <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <X className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <p className="text-red-600 dark:text-red-400 text-sm mb-4">{error}</p>
            <Button 
              onClick={loadClassStudents}
              variant="outline"
              size="sm"
            >
              Tentar novamente
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Estatísticas */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                <div className="flex items-center gap-2">
                  <UserCheck className="w-5 h-5 text-green-600 dark:text-green-400" />
                  <span className="text-sm font-medium text-green-800 dark:text-green-400">
                    Presentes
                  </span>
                </div>
                <p className="text-2xl font-bold text-green-900 dark:text-green-300 mt-1">
                  {studentCount}
                </p>
              </div>
              
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <div className="flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-blue-800 dark:text-blue-400">
                    Aula
                  </span>
                </div>
                <p className="text-lg font-semibold text-blue-900 dark:text-blue-300 mt-1 truncate">
                  {className || 'Aula Atual'}
                </p>
              </div>
            </div>

            {/* Busca */}
            {students.length > 0 && (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  ref={searchInputRef}
                  placeholder="Buscar aluno..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            )}

            {/* Lista de Estudantes */}
            {students.length > 0 ? (
              <ScrollArea className="max-h-[300px] space-y-2">
                <div className="space-y-2 pr-2">
                  {filteredStudents.length > 0 ? (
                    filteredStudents.map(renderStudentItem)
                  ) : (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      <Search className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                      <p className="text-sm">Nenhum aluno encontrado</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            ) : (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <Users className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <p className="text-lg font-medium mb-2">Nenhum aluno presente</p>
                <p className="text-sm">Ainda não há check-ins para esta aula</p>
              </div>
            )}

            {/* Botões de ação */}
            <div className="flex justify-end gap-2 pt-4 border-t border-gray-200 dark:border-gray-700">
              <Button 
                onClick={loadClassStudents}
                variant="outline"
                size="sm"
              >
                Atualizar
              </Button>
              <Button onClick={onClose}>
                Fechar
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
} 