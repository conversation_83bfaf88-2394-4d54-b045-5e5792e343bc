'use client';

import { useState, useEffect, useCallback } from 'react';
import { Check, X } from 'lucide-react';
import { VirtualKeypad } from '../virtual-keypad';
import { findStudentByCheckInCode } from '@/app/checkin/actions/search-student-by-code';

interface CodeTabProps {
  onCheckInSuccess: (data: any) => void;
}

export function CodeTab({ onCheckInSuccess }: CodeTabProps) {
  const [code, setCode] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleAddDigit = useCallback((digit: string) => {
    if (code.length < 6) {
      setCode(prev => prev + digit);
    }
  }, [code.length]);

  const handleClearLast = useCallback(() => {
    setCode(prev => prev.slice(0, -1));
  }, []);

  const handleClear = () => {
    setCode('');
  };

  const handleSubmit = useCallback(async () => {
    if (!code.trim() || isProcessing) return;

    setIsProcessing(true);
    setErrorMessage(null);
    try {
      const result = await findStudentByCheckInCode({ code });
      if (result.success) {
        onCheckInSuccess(result.data);
        setCode('');
      } else {
        setErrorMessage(result.error);
      }
    } catch (error) {
      console.error('Erro no check-in:', error);
      setErrorMessage('Erro inesperado');
    } finally {
      setIsProcessing(false);
    }
  }, [code, isProcessing, onCheckInSuccess]);

  // Suporte a teclado
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key >= '0' && e.key <= '9') {
        e.preventDefault();
        handleAddDigit(e.key);
      } else if (e.key === 'Backspace') {
        e.preventDefault();
        handleClearLast();
      } else if (e.key === 'Enter') {
        e.preventDefault();
        handleSubmit();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleAddDigit, handleClearLast, handleSubmit]);

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="relative inline-block">
          <input 
            type="text" 
            placeholder="Digite o código do aluno"
            value={code}
            onChange={() => {}} // Controlado via keypad
            className="
              w-58 py-4 px-10 rounded-2xl text-white text-center text-lg font-medium 
              bg-white/20 dark:bg-gray-700/50 backdrop-blur-lg border-2 border-white/10 dark:border-gray-600/50
              focus:outline-none focus:border-white/40 dark:focus:border-gray-500/70 focus:bg-white/25 dark:focus:bg-gray-600/60
              placeholder-white/60 dark:placeholder-gray-300/70
            "
            maxLength={6}
            readOnly
          />
          {code && (
            <button 
              onClick={handleClear}
              className="absolute inset-y-0 right-0 flex items-center pr-4 text-white/50 dark:text-gray-300/70 hover:text-white dark:hover:text-gray-200 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>
      </div>
      
      <VirtualKeypad
        onDigit={handleAddDigit}
        onClearLast={handleClearLast}
        onSubmit={handleSubmit}
        disabled={isProcessing}
      />
      
      <div className="text-center">
        <button 
          onClick={handleSubmit}
          disabled={!code.trim() || isProcessing}
          className="
            bg-emerald-400 hover:bg-emerald-500 dark:bg-emerald-500 dark:hover:bg-emerald-600 
            disabled:bg-emerald-600 dark:disabled:bg-emerald-700
            text-white font-semibold py-3 px-8 rounded-2xl 
            transition-all duration-200 shadow-lg hover:shadow-xl
            disabled:cursor-not-allowed
          "
        >
          {isProcessing ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
              Processando...
            </div>
          ) : (
            <>
              <Check className="w-4 h-4 inline mr-2" />
              Confirmar Check-in
            </>
          )}
        </button>
        {errorMessage && (
          <p className="text-red-400 text-sm mt-2">{errorMessage}</p>
        )}
      </div>
    </div>
  );
} 