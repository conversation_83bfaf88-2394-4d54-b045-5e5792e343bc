'use client';

import { useState, useEffect } from 'react';
// import { Search } from 'lucide-react'; // Removido pois não está em uso
import { useMutation } from '@tanstack/react-query';
import { searchStudentsByName, type SearchStudentResult } from '@/app/checkin/actions/search-students';
import { getStudentCheckInData } from '@/app/checkin/actions/get-student-checkin-data';
import { useDebounce } from '@/hooks/ui/use-debounce';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

interface NameTabProps {
  onCheckInSuccess: (data: any) => void;
}

export function NameTab({ onCheckInSuccess }: NameTabProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<SearchStudentResult[]>([]);

  const { mutate: performSearch, isPending: isSearching } = useMutation({
    mutationFn: (term: string) => searchStudentsByName({ searchTerm: term }),
    onSuccess: (resp) => {
      if (resp.success) {
        setSearchResults(resp.data);
      } else {
        setSearchResults([]);
        console.error(resp.error);
      }
    },
    onError: (err) => {
      console.error('Erro na busca:', err);
      setSearchResults([]);
    },
  });

  const handleSearch = () => {
    if (!searchTerm.trim()) return;
    performSearch(searchTerm.trim());
  };

  // Debounce para reduzir chamadas durante digitação
  const debouncedTerm = useDebounce(searchTerm, 300);

  useEffect(() => {
    if (debouncedTerm.trim()) {
      performSearch(debouncedTerm.trim());
    } else {
      setSearchResults([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedTerm]);

  const handleSelectUser = (user: SearchStudentResult) => {
    getStudentCheckInData({ studentId: user.id })
      .then((resp) => {
        if (resp.success) {
          onCheckInSuccess(resp.data);
        } else {
          console.error(resp.error);
        }
      })
      .catch((err) => {
        console.error('Erro ao buscar dados do aluno:', err);
      });
  };

  return (
    <div className="space-y-6">
      <div className="flex gap-2">
        <input 
          type="text" 
          placeholder="Digite o nome do aluno"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
          className="
            flex-1 py-4 px-6 rounded-2xl text-white text-center text-lg font-medium 
            bg-white/20 dark:bg-gray-700/50 backdrop-blur-lg border-2 border-white/10 dark:border-gray-600/50
            focus:outline-none focus:border-white/40 dark:focus:border-gray-500/70 focus:bg-white/25 dark:focus:bg-gray-600/60
            placeholder-white/60 dark:placeholder-gray-300/70
          "
        />
      </div>
      
      <div className="bg-white/20 dark:bg-gray-700/40 backdrop-blur-lg rounded-2xl p-6 min-h-40 border-2 border-white/10 dark:border-gray-600/50">
        {isSearching ? (
          <div className="flex items-center justify-center h-32">
            <div className="flex items-center gap-3 text-white/80 dark:text-gray-200">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white dark:border-gray-300" />
              <span>Buscando...</span>
            </div>
          </div>
        ) : searchResults.length > 0 ? (
          <div className="space-y-3">
            <h3 className="text-white/90 dark:text-gray-100 font-medium mb-4">
              {searchResults.length} resultado{searchResults.length !== 1 ? 's' : ''} encontrado{searchResults.length !== 1 ? 's' : ''}:
            </h3>
            {searchResults.map(user => (
              <button
                key={user.id}
                onClick={() => handleSelectUser(user)}
                className="
                  w-full flex items-center gap-3 p-3 rounded-xl 
                  bg-white/10 dark:bg-gray-600/30 hover:bg-white/20 dark:hover:bg-gray-600/50 
                  border border-white/10 dark:border-gray-500/50
                  transition-all duration-200 text-left
                "
              >
                <Avatar className="h-12 w-12">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <p className="text-white dark:text-gray-100 font-medium">{user.name}</p>
                  <p className="text-white/70 dark:text-gray-300 text-sm">{user.membership}</p>
                </div>
              </button>
            ))}
          </div>
        ) : searchTerm && !isSearching ? (
          <div className="flex items-center justify-center h-32">
            <p className="text-white/60 dark:text-gray-300 text-sm">Nenhum resultado encontrado para "{searchTerm}"</p>
          </div>
        ) : (
          <div className="flex items-center justify-center h-32">
            <p className="text-white/60 dark:text-gray-300 text-sm">Digite um nome para buscar</p>
          </div>
        )}
      </div>
    </div>
  );
} 