'use client';

import { useState, useRef, useEffect } from 'react';
import { Camera, CameraOff } from 'lucide-react';
import { findStudentByCheckInCode } from '@/app/checkin/actions/search-student-by-code';
import { useMutation } from '@tanstack/react-query';

interface QRTabProps {
  onCheckInSuccess: (data: any) => void;
}

export function QRTab({ onCheckInSuccess }: QRTabProps) {
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const startCamera = async () => {
    try {
      setError(null);
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' } // Preferir câmera traseira
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
        setIsCameraActive(true);
        setIsScanning(true);
      }
    } catch (err) {
      console.error('Erro ao acessar câmera:', err);
      setError('Não foi possível acessar a câmera. Verifique as permissões.');
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    
    setIsCameraActive(false);
    setIsScanning(false);
  };

  const { mutate: fetchByCode } = useMutation({
    mutationFn: (code: string) => findStudentByCheckInCode({ code }),
    onSuccess: (resp) => {
      if (resp.success) {
        stopCamera();
        onCheckInSuccess(resp.data);
      } else {
        setError(resp.error || 'QR inválido');
        stopCamera();
      }
    },
    onError: (err) => {
      console.error('Erro ao buscar aluno pelo QR:', err);
      setError('Erro ao processar QR Code');
      stopCamera();
    },
  });

  const handleQRCodeDetected = (qrData: string) => {
    console.log('QR Code detectado:', qrData);
    fetchByCode(qrData);
  };

  // Cleanup quando o componente é desmontado
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, []);

  return (
    <div className="space-y-6 text-center">
      <p className="text-white/80 dark:text-gray-200 text-lg">Aponte a câmera para o QR Code</p>
      
      <div className="relative mx-auto max-w-sm">
        <div className="bg-slate-900 dark:bg-gray-800 rounded-2xl h-48 flex items-center justify-center border-2 border-white/10 dark:border-gray-600/50 overflow-hidden">
          {isCameraActive ? (
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="text-center">
              <Camera className="w-12 h-12 text-white/40 dark:text-gray-400 mx-auto mb-2" />
              <p className="text-white/60 dark:text-gray-300 text-sm">
                {error ? 'Erro na câmera' : 'Clique para iniciar a câmera'}
              </p>
            </div>
          )}
        </div>
        
        {/* Overlay de scan */}
        {isScanning && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="w-32 h-32 border-2 border-emerald-400 dark:border-emerald-300 rounded-lg animate-pulse">
              <div className="w-full h-full border border-emerald-400/50 dark:border-emerald-300/50 rounded-lg m-1" />
            </div>
          </div>
        )}
      </div>
      
      {error && (
        <div className="bg-red-500/20 dark:bg-red-900/30 border border-red-400 dark:border-red-600 rounded-lg p-3">
          <p className="text-red-200 dark:text-red-300 text-sm">{error}</p>
        </div>
      )}
      
      <div className="space-y-3">
        {!isCameraActive ? (
          <button
            onClick={startCamera}
            className="
              bg-white/20 dark:bg-gray-700/50 hover:bg-white/30 dark:hover:bg-gray-600/70 text-white font-medium 
              py-3 px-6 rounded-xl transition-all duration-200
              flex items-center gap-2 mx-auto
            "
          >
            <Camera className="w-5 h-5" />
            Iniciar Câmera
          </button>
        ) : (
          <button
            onClick={stopCamera}
            className="
              bg-red-500/20 dark:bg-red-900/30 hover:bg-red-500/30 dark:hover:bg-red-800/50 text-white font-medium 
              py-3 px-6 rounded-xl transition-all duration-200 border border-red-400/50 dark:border-red-600/50
              flex items-center gap-2 mx-auto
            "
          >
            <CameraOff className="w-5 h-5" />
            Parar Escaneamento
          </button>
        )}

        {/* Simulação de detecção de QR Code para teste */}
        {isScanning && (
          <button
            onClick={() => handleQRCodeDetected('test-qr-code')}
            className="
              bg-emerald-500 dark:bg-emerald-600 hover:bg-emerald-600 dark:hover:bg-emerald-700 text-white font-medium 
              py-2 px-4 rounded-lg text-sm transition-all duration-200 mx-auto block
            "
          >
            Simular QR Code (Para Teste)
          </button>
        )}
      </div>
      
      <div className="bg-blue-500/20 dark:bg-blue-900/30 border border-blue-400/50 dark:border-blue-600/50 rounded-lg p-2 text-center max-w-xs mx-auto">
        <h4 className="text-blue-200 dark:text-blue-300 font-medium text-sm mb-1">
          Como escanear QR Code:
        </h4>
        <ul className="text-blue-300 dark:text-blue-200 text-xs space-y-0.5">
          <li>• Posicione o QR Code dentro do quadrado</li>
          <li>• Mantenha a câmera estável</li>
          <li>• Certifique-se de ter boa iluminação</li>
        </ul>
      </div>
    </div>
  );
} 