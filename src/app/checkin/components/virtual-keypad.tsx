'use client';

import { Check, Delete } from 'lucide-react';

interface VirtualKeypadProps {
  onDigit: (digit: string) => void;
  onClearLast: () => void;
  onSubmit: () => void;
  disabled?: boolean;
}

export function VirtualKeypad({ onDigit, onClearLast, onSubmit, disabled = false }: VirtualKeypadProps) {
  const keypadButtons = [
    '1', '2', '3',
    '4', '5', '6', 
    '7', '8', '9',
    'clear', '0', 'submit'
  ];

  const renderButton = (key: string) => {
    const isSpecial = key === 'clear' || key === 'submit';
    const baseClasses = `
      w-12 h-12 rounded-xl text-white font-semibold flex items-center justify-center
      bg-white/15 dark:bg-gray-700/50 backdrop-blur-sm border border-white/10 dark:border-gray-600/50
      transition-all duration-200
      hover:bg-white/25 dark:hover:bg-gray-600/70 hover:-translate-y-0.5
      active:translate-y-0
      disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:translate-y-0
    `;

    if (key === 'clear') {
      return (
        <button
          key={key}
          onClick={onClearLast}
          disabled={disabled}
          className={baseClasses}
          title="Apagar último dígito"
        >
          <Delete className="w-5 h-5" />
        </button>
      );
    }

    if (key === 'submit') {
      return (
        <button
          key={key}
          onClick={onSubmit}
          disabled={disabled}
          className={baseClasses}
          title="Confirmar"
        >
          <Check className="w-5 h-5" />
        </button>
      );
    }

    return (
      <button
        key={key}
        onClick={() => onDigit(key)}
        disabled={disabled}
        className={baseClasses}
      >
        {key}
      </button>
    );
  };

  return (
    <div className="grid grid-cols-3 gap-3 max-w-sm mx-auto justify-items-center">
      {keypadButtons.map(renderButton)}
    </div>
  );
} 