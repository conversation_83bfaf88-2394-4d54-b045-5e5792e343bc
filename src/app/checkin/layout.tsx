import { Metadata } from 'next';
import { PageTitleProvider } from '@/contexts/PageTitleContext';
import { validateLayoutAuth } from '@/services/auth/layout-auth';
import { isReceptionModeActive } from '@/hooks/reception-mode/use-reception-mode-server';
import { redirect } from 'next/navigation';

export const metadata: Metadata = {
  title: 'Check-in',
  description: 'Sistema de Check-in da academia',
};

export default async function CheckInLayout({
  children,
}: {  
  children: React.ReactNode;
}) {
  // Aplicar as mesmas regras do dashboard através do serviço compartilhado
  const { user, tenantData } = await validateLayoutAuth('/login');
  
  // Verificar se o modo de recepção está ativo
  const receptionModeActive = await isReceptionModeActive();
  
  if (!receptionModeActive) {
    // Se o modo de recepção não estiver ativo, redirecionar baseado na role do usuário
    const redirectPath = user?.app_metadata?.role === 'admin' ? '/dashboard' : '/home';
    redirect(redirectPath);
  }
  
  return (
    <PageTitleProvider>
      <div 
        style={{
          // Aplicar variáveis CSS do tenant para uso nos componentes
          '--tenant-primary': tenantData?.primary_color || '#3b82f6',
          '--tenant-secondary': tenantData?.secondary_color || '#64748b',
        } as React.CSSProperties}
        className="checkin-layout w-full"
        data-tenant={tenantData?.slug || 'default'}
      >
        {children}
      </div>
    </PageTitleProvider>
  );
} 