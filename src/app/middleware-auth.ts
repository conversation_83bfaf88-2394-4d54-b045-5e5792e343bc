import { NextRequest, NextResponse } from 'next/server';
import { checkSessionMiddleware } from '@/services/auth/middleware/auth-middleware-actions';
import { navigationConfig } from '@/src/config';

/**
 * Middleware de autenticação simplificado que utiliza server actions
 * para evitar exposição da API key do Supabase no cliente
 */
export async function authMiddleware(request: NextRequest): Promise<NextResponse> {
  const path = request.nextUrl.pathname;
  
  if (
    path.includes('.') || 
    path.startsWith('/_next/') ||
    path.startsWith('/static/') ||
    navigationConfig.routes.isPublic(path) ||
    navigationConfig.routes.shouldBypass(path)
  ) {
    return NextResponse.next();
  }
  
  const response = await checkSessionMiddleware(request);
  
  const userId = response.headers.get('x-auth-user-id');
  
  if (
    !userId && 
    !navigationConfig.routes.isAuth(path) && 
    !navigationConfig.routes.isPublic(path)
  ) {
    const loginUrl = new URL('/login', request.url);
    return NextResponse.redirect(loginUrl);
  }
  
  if (
    userId && 
    navigationConfig.routes.isAuth(path)
  ) {
    // Redirecionar para dashboard
    const dashboardUrl = new URL('/dashboard', request.url);
    return NextResponse.redirect(dashboardUrl);
  }
  
  return response;
} 