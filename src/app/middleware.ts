import { redirect } from 'next/navigation';
import { getPermissionService } from '@/services/permissions/service';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import { getBasicPermissionContext, isUserAdminServer } from '@/services/permissions/contexts/permission-context';

/**
 * Middleware para proteger rotas do dashboard administrativo
 * Utiliza o novo sistema de permissões e server actions para autenticação
 */
export async function protectDashboardRoute() {
  const user = await getCurrentUser();
  
  if (!user) {
    return redirect('/login');
  }
  
  const permissionService = getPermissionService();
  
  return permissionService.protectRoute(
    user.id,
    'user',
    'list',
    undefined,
    '/home?erro=acesso-negado'
  );
}

/**
 * Middleware para proteger acesso à página de alunos
 * Requer permissão de listar usuários tipo 'student'
 * Verifica explicitamente se o usuário é admin diretamente no banco de dados
 */
export async function protectStudentListRoute() {
  const user = await getCurrentUser();
  
  if (!user) {
    return redirect('/login');
  }
  
  const isAdmin = await isUserAdminServer(user.id);
  
  if (!isAdmin) {
    console.warn('Acesso negado à página de alunos: usuário não é administrador', { 
      userId: user.id
    });
    return redirect('/home?erro=acesso-negado&motivo=apenas-administradores');
  }
  
  const context = await getBasicPermissionContext(user.id);
  
  if (!context || context.currentUserRole !== 'admin') {
    console.warn('Verificação secundária de permissão falhou: usuário não é administrador', { 
      userId: user.id, 
      roleDetectada: context?.currentUserRole 
    });
    return redirect('/home?erro=acesso-negado&motivo=apenas-administradores');
  }
  
  const permissionService = getPermissionService();
  
  return permissionService.protectRoute(
    user.id,
    'user',
    'list',
    undefined,
    '/home?erro=acesso-negado'
  );
} 