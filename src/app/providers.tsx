'use client'

import { ThemeProvider } from 'next-themes'
import { PropsWithChildren } from 'react'
import { TenantThemeProvider } from '@/hooks/tenant/use-tenant-theme'
import { UserProvider } from '@/hooks/user/use-user-context'
import { PermissionCacheProvider } from '@/components/providers/permission-cache-provider'
import { QueryProvider } from '@/components/providers/query-provider'
import { CacheProvider } from '@/components/providers/cache-provider'
import { ToastProvider } from '@/components/toast-notification'
import { FavoritesProvider } from '@/contexts/FavoritesContext'
import { ReceptionModeProvider } from '@/contexts/ReceptionModeContext'

interface ProvidersProps extends PropsWithChildren {
  primaryColor?: string | null
  secondaryColor?: string | null
  logoUrl?: string | null
  tenantName?: string | null
  description?: string | null
  initialUserProfile?: any | null
  initialAvatarUrl?: string | null
}

export function Providers({ 
  children, 
  primaryColor = null, 
  secondaryColor = null,
  logoUrl = null,
  tenantName = null,
  description = null,
  initialUserProfile = null,
  initialAvatarUrl = null
}: ProvidersProps) {

  return (
    <ThemeProvider 
      attribute="class" 
      defaultTheme="system" 
      enableSystem
      disableTransitionOnChange={false}
      storageKey="apex-theme"
      themes={['light', 'dark', 'system']}
    >
      <TenantThemeProvider 
        initialPrimaryColor={primaryColor} 
        initialSecondaryColor={secondaryColor}
        initialLogoUrl={logoUrl}
        initialTenantName={tenantName}
        initialDescription={description}
      >
        <QueryProvider>
          <CacheProvider>
            <PermissionCacheProvider>
              <UserProvider 
                initialProfile={initialUserProfile}
                initialAvatarUrl={initialAvatarUrl || initialUserProfile?.avatarUrl}
              >
                <FavoritesProvider>
                  <ReceptionModeProvider>
                    <ToastProvider>
                      <div className="min-h-screen bg-background text-foreground transition-colors duration-200">
                        {children}
                      </div>
                    </ToastProvider>
                  </ReceptionModeProvider>
                </FavoritesProvider>
              </UserProvider>
            </PermissionCacheProvider>
          </CacheProvider>
        </QueryProvider>
      </TenantThemeProvider>
    </ThemeProvider>
  )
} 