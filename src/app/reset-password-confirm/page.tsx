import { Suspense } from 'react'
import { redirect } from 'next/navigation'
import { createClient } from '@/services/supabase/server'
import { SplashScreen } from '@/components/splash-screen'
import { AuthLoading, AuthResult } from '@/components/auth'
import { ResetPasswordConfirm } from '@/components/auth/reset-password-confirm'

interface PageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

async function validateResetToken(tokenHash: string) {
  try {
    // Apenas validar se o token_hash tem o formato correto
    // A validação real será feita na API quando o usuário submeter o formulário
    if (!tokenHash || tokenHash.length < 10) {
      return { 
        valid: false, 
        error: 'Token de redefinição inválido.'
      }
    }

    // Token tem formato válido - permitir acesso ao formulário
    return { 
      valid: true, 
      userEmail: undefined // Não temos o email ainda, será obtido na API
    }
  } catch (error) {
    console.error('Erro ao validar token:', error)
    return { 
      valid: false, 
      error: 'Erro interno. Tente novamente mais tarde.'
    }
  }
}

export default async function ResetPasswordConfirmPage({ searchParams }: PageProps) {
  const params = await searchParams
  const tokenHash = params.token_hash as string

  // Verificar se o token_hash foi fornecido
  if (!tokenHash) {
    redirect('/reset-password?error=' + encodeURIComponent('Token de redefinição não fornecido.'))
  }

  // Validar o token
  const validation = await validateResetToken(tokenHash)

  if (!validation.valid) {
    return (
      <SplashScreen>
        <div className="relative flex min-h-screen items-center justify-center overflow-hidden">
          {/* Padrão de fundo */}
          <div className="absolute inset-0 -z-10">
            <div className="relative h-full w-full bg-white dark:bg-gray-950">
              <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_110%)]" />
            </div>
          </div>

          {/* Resultado de erro */}
          <div className="relative flex w-full items-center justify-center bg-white/[0.96] px-4 dark:bg-gray-950/[0.96] sm:px-6">
            <div className="absolute inset-0 -z-10 bg-gradient-to-b from-white via-white/80 to-white/40 dark:from-gray-950 dark:via-gray-950/80 dark:to-gray-950/40" />
            <AuthResult
              type="error"
              title="Link Inválido"
              message={validation.error || 'O link de redefinição de senha é inválido ou expirou.'}
              onAction={() => window.location.href = '/reset-password'}
              actionLabel="Solicitar Novo Link"
            />
          </div>
        </div>
      </SplashScreen>
    )
  }

  return (
    <SplashScreen>
      <div className="relative flex min-h-screen items-center justify-center overflow-hidden">
        {/* Padrão de fundo */}
        <div className="absolute inset-0 -z-10">
          <div className="relative h-full w-full bg-white dark:bg-gray-950">
            <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_110%)]" />
          </div>
        </div>

        {/* Formulário */}
        <Suspense fallback={<AuthLoading />}>
          <ResetPasswordConfirm
            tokenHash={tokenHash}
            userEmail={validation.userEmail}
          />
        </Suspense>
      </div>
    </SplashScreen>
  )
}
