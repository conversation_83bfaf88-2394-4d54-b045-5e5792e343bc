import { redirect } from 'next/navigation';
import { getTenantData } from '@/services/tenant/tenant-service';

export const dynamic = 'force-dynamic';

interface Props {
  params: Promise<{
    slug: string;
  }>;
}

export default async function TenantPage({ params }: Props) {
  const { slug } = await params;
  
  // Verificar se o tenant existe
  const tenant = await getTenantData(slug);
  
  if (!tenant || !tenant.id) {
    // Se não encontrou um tenant válido, redirecionar para a página inicial
    redirect('/dashboard');
  }
  
  // Se encontrou um tenant válido, continue com a renderização da aplicação
  return (
    <div>
      <p>Carregando tenant: {slug}</p>
    </div>
  );
} 