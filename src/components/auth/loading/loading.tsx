'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { useTenantTheme } from '@/hooks/tenant/use-tenant-theme'
import { useEffect, useState } from 'react'

export interface AuthLoadingProps {
  logoUrl?: string
}

export function AuthLoading({ logoUrl: propLogoUrl }: AuthLoadingProps) {
  const { logoUrl: themeLogoUrl, tenantName } = useTenantTheme()
  const [mounted, setMounted] = useState(false)
  
  useEffect(() => {
    setMounted(true)
  }, [])
  
  const displayLogoUrl = propLogoUrl || (mounted ? themeLogoUrl : null) || '/placeholder-logo.png'
  const altText = mounted ? tenantName || 'Academia' : 'Logo Carregando'
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-950">
      <div className="relative flex flex-col items-center">
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{
            duration: 0.5,
            ease: "easeOut"
          }}
          className="relative mb-8"
        >
          <Image
            src={displayLogoUrl}
            alt={altText}
            width={96}
            height={96}
            priority
            className="object-contain"
          />
          
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="absolute -inset-4"
          >
            <svg className="h-32 w-32 animate-spin-slow" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="currentColor"
                strokeWidth="3"
                strokeLinecap="round"
                className="text-gray-200 dark:text-gray-800"
              />
              <motion.circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="currentColor"
                strokeWidth="3"
                strokeLinecap="round"
                className="text-red-500"
                strokeDasharray="283"
                strokeDashoffset="283"
                animate={{
                  strokeDashoffset: 0
                }}
                transition={{
                  duration: 1.5,
                  ease: "easeInOut",
                  repeat: Infinity
                }}
              />
            </svg>
          </motion.div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="text-center"
        >
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Autenticando
          </h2>
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: "100%" }}
            transition={{
              duration: 1,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }}
            className="mt-2 h-0.5 mx-auto w-24 bg-gradient-to-r from-red-500 to-red-600"
          />
        </motion.div>

        <div className="mt-4 flex space-x-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0.5, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{
                duration: 0.5,
                repeat: Infinity,
                repeatType: "reverse",
                delay: i * 0.2
              }}
              className="h-2 w-2 rounded-full bg-red-500"
            />
          ))}
        </div>
      </div>
    </div>
  )
} 