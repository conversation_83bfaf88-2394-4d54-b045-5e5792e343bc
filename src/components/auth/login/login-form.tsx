'use client'

import { AnimatePresence, motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { Eye, EyeOff } from 'lucide-react'
import { AuthResult, AuthWelcome } from '@/components/auth'
import { useLoginForm } from './login-hooks'
import Image from 'next/image'
import { useTenantTheme } from '@/src/hooks/tenant/use-tenant-theme'

export function LoginForm() {
  const router = useRouter()
  const { logoUrl } = useTenantTheme()
  
  const {
    form,
    handleLogin,
    isLoading,
    authError,
    showPassword,
    togglePasswordVisibility,
    showAuthResult,
    authResultData,
    handleBackToLogin,
    showWelcomeScreen,
    welcomeData,
    handleResetPassword,
    redirectTarget
  } = useLoginForm()
  
  if (showWelcomeScreen && welcomeData) {
    const finalRedirectTo = redirectTarget || (welcomeData.role === 'admin' ? '/dashboard' : '/home')
    
    return (
      <AuthWelcome 
        userData={welcomeData} 
        redirectTo={finalRedirectTo} 
        redirectDelay={2000} 
      />
    )
  }
  
  if (showAuthResult && authResultData) {
    return (
      <AuthResult
        type={authResultData.type}
        title={authResultData.title}
        message={authResultData.message}
        onAction={handleBackToLogin}
        actionLabel="Voltar ao Login"
      />
    );
  }
  
  return (
    <AnimatePresence mode="wait">
      <motion.div
        className="w-full max-w-md space-y-8 rounded-2xl bg-white p-8 shadow-xl shadow-gray-200/20 ring-1 ring-gray-200 dark:bg-gray-900 dark:shadow-none dark:ring-gray-800"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.5 }}
      >
        <div className="text-center">
          <motion.div
            initial={{ scale: 0.5, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Image
              src={logoUrl || "/logo.svg"}
              alt="Logo Academia"
              width={112}
              height={112}
              priority
              className="mx-auto h-28 w-28 object-contain"
            />
          </motion.div>

          <motion.h2
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mt-6 bg-gradient-to-r tenant-primary bg-clip-text text-3xl font-bold tracking-tight text-transparent dark:from-white dark:to-gray-200"
          >
            Bem-vindo de volta
          </motion.h2>
          <motion.p
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-2 text-sm text-gray-600 dark:text-gray-400"
          >
            Entre com suas credenciais para acessar o sistema
          </motion.p>
        </div>

        {authError && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="rounded-lg bg-red-50 p-4 dark:bg-red-900/50"
          >
            <p className="text-sm font-medium text-red-800 dark:text-red-200">
              {authError}
            </p>
          </motion.div>
        )}

        <form onSubmit={form.handleSubmit(handleLogin)} className="mt-8 space-y-6">
          <div className="space-y-5 rounded-md">
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-900 dark:text-gray-200"
              >
                Email
              </label>
              <div className="mt-1.5">
                <input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...form.register('email')}
                  className={`block w-full rounded-lg border ${form.formState.errors.email ? 'border-red-500' : 'border-gray-200'} bg-white px-4 py-3 text-gray-900 transition-colors placeholder:text-gray-500 focus:tenant-primary-border focus:outline-none focus:ring-4 focus:tenant-primary-border/10 dark:border-gray-800 dark:bg-gray-900 dark:text-white dark:placeholder:text-gray-400 dark:focus:tenant-primary-border dark:focus:tenant-primary-border/10`}
                  aria-invalid={!!form.formState.errors.email}
                />
                {form.formState.errors.email && (
                  <p className="mt-1.5 text-sm tenant-primary dark:tenant-primary">
                    {form.formState.errors.email.message}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-900 dark:text-gray-200"
              >
                Senha
              </label>
              <div className="space-y-1.5">
                <div className="relative mt-1.5">
                  <input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Sua senha"
                    {...form.register('password')}
                    className={`block w-full rounded-lg border ${form.formState.errors.password ? 'border-red-500' : 'border-gray-200'} bg-white px-4 py-3 text-gray-900 transition-colors placeholder:text-gray-500 focus:tenant-primary-border focus:outline-none focus:ring-4 focus:tenant-primary-border/10 dark:border-gray-800 dark:bg-gray-900 dark:text-white dark:placeholder:text-gray-400 dark:focus:tenant-primary-border dark:focus:tenant-primary-border/10`}
                    aria-invalid={!!form.formState.errors.password}
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    onClick={togglePasswordVisibility}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5" />
                    ) : (
                      <Eye className="h-5 w-5" />
                    )}
                  </button>
                </div>
                {form.formState.errors.password && (
                  <p className="text-sm tenant-primary dark:tenant-primary">
                    {form.formState.errors.password.message}
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="rememberMe"
                type="checkbox"
                {...form.register('rememberMe')}
                className="h-4 w-4 rounded border-gray-300 tenant-primary transition-colors focus:ring-tenant-primary dark:border-gray-800 dark:bg-gray-900 dark:focus:ring-offset-gray-900"
              />
              <label
                htmlFor="rememberMe"
                className="ml-2 block text-sm text-gray-900 dark:text-gray-200"
              >
                Lembrar-me
              </label>
            </div>

            <div className="text-sm">
              <button
                type="button"
                onClick={handleResetPassword}
                className="font-medium tenant-primary transition-colors hover:opacity-80 dark:tenant-primary dark:hover:opacity-80"
              >
                Esqueceu sua senha?
              </button>
            </div>
          </div>

          <div>
            <button
              type="submit"
              className="relative w-full rounded-lg tenant-primary-bg px-4 py-3 text-sm font-semibold text-white transition-all before:absolute before:inset-0 before:rounded-lg before:bg-transparent/0 before:transition-colors hover:before:bg-white/10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:tenant-primary-border disabled:cursor-not-allowed disabled:opacity-50"
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center justify-center gap-2">
                  <svg className="h-4 w-4 animate-spin" viewBox="0 0 24 24">
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  Entrando...
                </span>
              ) : (
                'Entrar'
              )}
            </button>
          </div>
        </form>
      </motion.div>
    </AnimatePresence>
  )
} 