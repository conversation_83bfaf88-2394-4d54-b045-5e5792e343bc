'use client'

import { useState, useEffect, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter, useSearchParams } from 'next/navigation'

import { clientSignInAction, getClientUserWelcomeData } from '@/services/auth'
import { loginSchema, type LoginFormData } from './login-schema'
import { AuthError, AUTH_ERRORS, AuthResultData } from './login-constants'
import { getCookie, deleteCookie, prepareFormData } from './login-utils'
import { useProfilePrefetch } from '@/hooks/user/use-user-context'

export function useLoginForm() {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [authError, setAuthError] = useState<string | null>(null)
  const [showAuthResult, setShowAuthResult] = useState(false)
  const [authResultData, setAuthResultData] = useState<AuthResultData | null>(null)
  const [showWelcomeScreen, setShowWelcomeScreen] = useState(false)
  const [welcomeData, setWelcomeData] = useState<{
    displayName: string;
    tenantName?: string;
    belt?: string;
    stripes?: number;
    beltLabel?: string;
    beltData?: any;
    role: string;
  } | null>(null)
  const [redirectTarget, setRedirectTarget] = useState<string | null>(null)
  
  const router = useRouter()
  const searchParams = useSearchParams()
  const { prefetchUserProfile } = useProfilePrefetch()
  
  const effectExecuted = useRef(false)
  const requestInProgress = useRef(false)
  const redirectionInProgress = useRef(false)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
    mode: 'onChange',
  })

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (effectExecuted.current) return;
    effectExecuted.current = true;
    
    // Capturar o parâmetro redirectedFrom da URL
    const redirectedFrom = searchParams.get('redirectedFrom')
    
    if (redirectedFrom) {
      setRedirectTarget(redirectedFrom)
    }
    
    const errorMessage = getCookie('auth_error');
    
    if (errorMessage) {
      setAuthError(errorMessage);
      deleteCookie('auth_error');
      
      if (errorMessage.includes('Você não tem acesso') || 
          errorMessage.includes('Academia não encontrada')) {
        setAuthResultData({
          type: 'error',
          title: 'Acesso Negado',
          message: errorMessage
        });
        setShowAuthResult(true);
      }
    }
  }, [searchParams]);

  const handleBackToLogin = () => {
    setShowAuthResult(false);
    setAuthResultData(null);
  }

  const handleResetPassword = () => {
    router.push('/login?mode=reset-password');
  }

  const handleAuthenticationError = (errorMessage: string) => {
    let errorMapping = Object.entries(AUTH_ERRORS).find(
      ([key]) => errorMessage === key
    );
    
    if (!errorMapping) {
      errorMapping = Object.entries(AUTH_ERRORS).find(
        ([key]) => errorMessage.includes(key)
      );
    }
    
    if (errorMapping) {
      const [_, { field, message }] = errorMapping;
      if (field === 'email' || field === 'password') {
        form.setError(field, { 
          type: 'manual', 
          message 
        });
      } else {
        setAuthError(message);
      }
    } else {
      setAuthError(errorMessage);
    }
  }

  async function handleLogin(data: LoginFormData) {
    if (redirectionInProgress.current || requestInProgress.current) return;
    
    try {
      requestInProgress.current = true;
      setIsLoading(true)
      setAuthError(null)
      form.clearErrors()
      
      const formData = prepareFormData(data)
      const result = await clientSignInAction(formData) as AuthError | { success: true, user?: { id: string } }
      
      if (result && typeof result === 'object' && 'error' in result) {
        if (result.error_code === 'user_not_in_tenant' && result.tenant_info) {
          const tenantInfo = result.tenant_info;
          const errorMessage = `Você não tem acesso a esta academia. Você está vinculado à academia "${tenantInfo.name}".`;
          
          setAuthResultData({
            type: 'error',
            title: 'Acesso Negado',
            message: errorMessage,
            tenantInfo: tenantInfo
          });
          setShowAuthResult(true);
        } else if (result.error_code === 'tenant_not_found') {
          const errorMessage = 'Academia não encontrada. Verifique o endereço ou contate o suporte.';
          
          setAuthResultData({
            type: 'error',
            title: 'Academia Não Encontrada',
            message: errorMessage
          });
          setShowAuthResult(true);
        } else if (result.error_code === 'invalid_credentials') {
          form.setError('password', { 
            type: 'manual', 
            message: 'Credenciais inválidas. Verifique seu email e senha.'
          });
        } else if (result.error_code === 'account_inactive') {
          setAuthResultData({
            type: 'error',
            title: 'Conta Inativa',
            message: result.error as string
          });
          setShowAuthResult(true);
        } else if (result.error_code === 'account_suspended') {
          setAuthResultData({
            type: 'error',
            title: 'Conta Suspensa',
            message: result.error as string
          });
          setShowAuthResult(true);
        } else {
          handleAuthenticationError(result.error as string);
        }
      } else {
        try {
          sessionStorage.setItem('last_login_time', Date.now().toString());
          
          if (result && typeof result === 'object' && result.user?.id) {
            console.log('[Login] Iniciando prefetch de dados de perfil e permissões');
            await prefetchUserProfile(result.user.id);
          }
          
          const userData = await getClientUserWelcomeData();
          console.log('[Login DEBUG] userData from getClientUserWelcomeData:', userData);
          setWelcomeData({
            displayName: userData.displayName,
            tenantName: userData.tenantName,
            belt: userData.belt,
            stripes: userData.stripes,
            beltLabel: userData.beltLabel,
            beltData: userData.beltData,
            role: userData.role
          });
          
          setShowWelcomeScreen(true);
        } catch (error) {
          console.error('Erro ao obter dados do usuário:', error);
          // Usar redirectTarget se disponível, senão /home
          router.push(redirectTarget || '/home');
        }
      }
    } catch (error: any) {
      console.error('[LoginForm] Erro durante o login:', error)
      setAuthError('Ocorreu um erro durante o login. Tente novamente.')
    } finally {
      setIsLoading(false)
      requestInProgress.current = false;
    }
  }

  const togglePasswordVisibility = () => setShowPassword(!showPassword)

  return {
    form,
    handleLogin,
    isLoading,
    authError,
    showPassword,
    togglePasswordVisibility,
    showAuthResult,
    authResultData,
    handleBackToLogin,
    showWelcomeScreen,
    welcomeData,
    handleResetPassword,
    redirectTarget
  }
} 