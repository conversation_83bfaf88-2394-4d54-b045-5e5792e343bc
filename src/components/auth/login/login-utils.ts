export function getCookie(name: string): string | null {
  if (typeof document === 'undefined') return null;
  
  const cookies = document.cookie.split(';');
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();
    if (cookie.startsWith(name + '=')) {
      return decodeURIComponent(cookie.substring(name.length + 1));
    }
  }
  return null;
}

export function deleteCookie(name: string): void {
  if (typeof document === 'undefined') return;
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
}

export function prepareFormData(data: { email: string; password: string; rememberMe?: boolean }): FormData {
  const formData = new FormData()
  formData.append('email', data.email)
  formData.append('password', data.password)
  if (data.rememberMe) {
    formData.append('remember_me', 'true')
  }
  return formData
} 