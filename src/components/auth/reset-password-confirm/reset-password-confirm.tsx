'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { AnimatePresence, motion } from 'framer-motion'
import { AuthResult } from '@/components/auth'
import { ResetPasswordConfirmForm } from './form'
import { ResetPasswordConfirmFormData } from './schema'

interface ResetPasswordConfirmProps {
  tokenHash: string
  userEmail?: string
  errorMessage?: string
}

export function ResetPasswordConfirm({ 
  tokenHash, 
  userEmail, 
  errorMessage: initialError 
}: ResetPasswordConfirmProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [errorMessage, setErrorMessage] = useState(initialError)
  const router = useRouter()

  const handleResetPassword = async (data: ResetPasswordConfirmFormData) => {
    try {
      setIsLoading(true)
      setErrorMessage(undefined)
      
      const response = await fetch('/api/auth/reset-password-confirm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token_hash: tokenHash,
          password: data.password,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        setErrorMessage(result.error || 'Erro ao atualizar senha')
        return
      }

      if (result.success) {
        setSuccess(true)
        
        // Redirecionar para login após 3 segundos
        setTimeout(() => {
          router.push('/login?success=' + encodeURIComponent('Senha atualizada com sucesso! Faça login com sua nova senha.'))
        }, 3000)
      } else {
        setErrorMessage(result.error || 'Erro ao atualizar senha')
      }
      
    } catch (error: any) {
      console.error('[ResetPasswordConfirm] Erro:', error)
      setErrorMessage('Erro interno. Tente novamente.')
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <AuthResult
        type="success"
        title="Senha atualizada com sucesso!"
        message="Sua senha foi redefinida. Redirecionando para o login..."
        onAction={() => router.push('/login')}
        actionLabel="Ir para Login"
      />
    )
  }

  return (
    <div className="relative flex w-full items-center justify-center bg-white/[0.96] px-4 dark:bg-gray-950/[0.96] sm:px-6">
      <div className="absolute inset-0 -z-10 bg-gradient-to-b from-white via-white/80 to-white/40 dark:from-gray-950 dark:via-gray-950/80 dark:to-gray-950/40" />
      <AnimatePresence mode="wait">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5 }}
        >
          <ResetPasswordConfirmForm
            onSubmit={handleResetPassword}
            isLoading={isLoading}
            errorMessage={errorMessage}
            userEmail={userEmail}
          />
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
