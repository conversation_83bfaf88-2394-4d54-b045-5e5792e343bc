'use client'

import { useRef } from 'react'
import HCaptch<PERSON> from '@hcaptcha/react-hcaptcha'

interface CaptchaProps {
  onVerify: (token: string) => void
  onExpire?: () => void
  onError?: (error: string) => void
  error?: string | null
  sitekey?: string
}

export const Captcha = ({
  onVerify,
  onExpire,
  onError,
  error,
  sitekey = process.env.NEXT_PUBLIC_HCAPTCHA_SITE_KEY || '10000000-ffff-ffff-ffff-000000000001' // Test key
}: CaptchaProps) => {
  const captchaRef = useRef<HCaptcha>(null)

  const handleVerify = (token: string) => {
    onVerify(token)
  }

  const handleExpire = () => {
    onExpire?.()
  }

  const handleError = (err: string) => {
    onError?.(err)
  }

  return (
    <div>
      <label className="block text-sm font-medium text-gray-900 dark:text-gray-200 mb-3">
        Verificação de segurança
      </label>

      <div className="space-y-3">
        <HCaptcha
          ref={captchaRef}
          sitekey={sitekey}
          onVerify={handleVerify}
          onExpire={handleExpire}
          onError={handleError}
          theme="light"
          size="normal"
        />

        {error && (
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        )}
      </div>
    </div>
  )
}