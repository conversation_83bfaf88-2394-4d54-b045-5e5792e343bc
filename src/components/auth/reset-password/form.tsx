'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { useState } from 'react'
import { ArrowLeftIcon } from 'lucide-react'
import { Alert, AlertDescription } from '@/src/components/ui/alert'
import { ResetPasswordFormData, resetPasswordSchema } from './schema'
import { Captcha } from './captcha'

interface ResetPasswordFormProps {
  onSubmit: (data: ResetPasswordFormData) => Promise<void>
  isLoading: boolean
  onBackToLogin: () => void
  errorMessage?: string | null
  successMessage?: string | null
}

export const ResetPasswordForm = ({
  onSubmit,
  isLoading,
  onBackToLogin,
  errorMessage,
  successMessage
}: ResetPasswordFormProps) => {
  const [hcaptchaToken, setHcaptchaToken] = useState<string | null>(null)
  const [captchaError, setCaptchaError] = useState<string | null>(null)

  const form = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      email: '',
      hcaptchaToken: '',
    },
  })

  const handleFormSubmit = async (data: ResetPasswordFormData) => {
    if (!hcaptchaToken) {
      setCaptchaError('Por favor, complete a verificação hCaptcha')
      return
    }

    setCaptchaError(null)
    // Incluir o token do hCaptcha nos dados
    const formDataWithCaptcha = {
      ...data,
      hcaptchaToken
    }
    await onSubmit(formDataWithCaptcha)
  }

  return (
    <form onSubmit={form.handleSubmit(handleFormSubmit)} className="mt-8 space-y-6">
      {errorMessage && (
        <Alert variant="destructive">
          <AlertDescription>{decodeURIComponent(errorMessage)}</AlertDescription>
        </Alert>
      )}
      
      {successMessage && (
        <Alert className="border-green-500 bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300">
          <AlertDescription>
            {decodeURIComponent(successMessage)}
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-5 rounded-md">
        <div>
          <label
            htmlFor="email"
            className="block text-sm font-medium text-gray-900 dark:text-gray-200"
          >
            Insira seu email
          </label>
          <div className="mt-1.5">
            <input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              {...form.register('email')}
              className={`block w-full rounded-lg border ${form.formState.errors.email ? 'border-red-500' : 'border-gray-200'} bg-white px-4 py-3 text-gray-900 transition-colors placeholder:text-gray-500 focus:tenant-primary-border focus:outline-none focus:ring-4 focus:tenant-primary-border/10 dark:border-gray-800 dark:bg-gray-900 dark:text-white dark:placeholder:text-gray-400 dark:focus:tenant-primary-border dark:focus:tenant-primary-border/10`}
            />
            {form.formState.errors.email && (
              <p className="mt-1.5 text-sm tenant-primary dark:tenant-primary">
                {form.formState.errors.email.message}
              </p>
            )}
          </div>
        </div>

        <Captcha
          onVerify={(token) => {
            setHcaptchaToken(token)
            setCaptchaError(null)
            form.setValue('hcaptchaToken', token)
          }}
          onExpire={() => {
            setHcaptchaToken(null)
            setCaptchaError('hCaptcha expirou, por favor tente novamente')
            form.setValue('hcaptchaToken', '')
          }}
          onError={() => {
            setHcaptchaToken(null)
            setCaptchaError('Erro na verificação hCaptcha')
            form.setValue('hcaptchaToken', '')
          }}
          error={form.formState.errors.hcaptchaToken?.message || captchaError}
        />
      </div>

      <div className="flex flex-col gap-3">
        <button
          type="submit"
          disabled={isLoading}
          className="relative w-full rounded-lg tenant-primary-bg px-4 py-3 text-sm font-semibold text-white transition-all before:absolute before:inset-0 before:rounded-lg before:bg-transparent/0 before:transition-colors hover:before:bg-white/10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:tenant-primary-border disabled:cursor-not-allowed disabled:opacity-50"
        >
          {isLoading ? (
            <span className="flex items-center justify-center gap-2">
              <svg className="h-4 w-4 animate-spin" viewBox="0 0 24 24">
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="none"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              Enviando...
            </span>
          ) : (
            'Enviar e-mail de redefinição'
          )}
        </button>

        <button
          type="button"
          onClick={onBackToLogin}
          className="flex items-center justify-center gap-2 text-sm font-medium tenant-primary transition-colors hover:opacity-80 dark:tenant-primary dark:hover:opacity-80"
        >
          <ArrowLeftIcon className="h-4 w-4" />
          Voltar para o login
        </button>
      </div>
    </form>
  )
} 