'use client'

import { useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { clientForgotPasswordAction } from '@/services/auth/client'
import { AuthResult } from '@/components/auth'
import { ResetPasswordContainer } from './container'
import { ResetPasswordForm } from './form'
import { ResetPasswordFormData } from './schema'

export function ResetPassword() {
  const [isLoading, setIsLoading] = useState(false)
  const [successSent, setSuccessSent] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const errorMessage = searchParams.get('error')
  const successMessage = searchParams.get('success')

  const handleResetPassword = async (data: ResetPasswordFormData) => {
    try {
      setIsLoading(true)

      const result = await clientForgotPasswordAction(
        data.email,
        window.location.origin + '/auth/callback?redirect_to=/home/<USER>',
        data.hcaptchaToken
      )

      if (result.success) {
        setSuccessSent(true)

        setTimeout(() => {
          if (result.redirect) {
            router.push(result.redirect)
          } else {
            router.push('/login')
          }
        }, 3000)
      } else {
        console.error('[ResetPasswordForm] Erro:', result.error)
      }

    } catch (error: any) {
      console.error('[ResetPasswordForm] Erro ao solicitar redefinição de senha:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackToLogin = () => {
    router.push('/login')
  }

  if (successSent) {
    return (
      <AuthResult
        type="success"
        title="E-mail enviado com sucesso!"
        message="Verifique sua caixa de entrada para redefinir sua senha. Redirecionando..."
        onAction={() => router.push('/login')}
        actionLabel="Voltar ao Login"
      />
    )
  }

  return (
    <ResetPasswordContainer>
      <ResetPasswordForm
        onSubmit={handleResetPassword}
        isLoading={isLoading}
        onBackToLogin={handleBackToLogin}
        errorMessage={errorMessage}
        successMessage={successMessage}
      />
    </ResetPasswordContainer>
  )
} 