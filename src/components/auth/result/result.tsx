'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { useEffect } from 'react'
import { AuthResultProps } from './types'

export function AuthResult({ type, title, message, onAction, redirectUrl }: AuthResultProps) {
  const isSuccess = type === 'success'

  useEffect(() => {
    if (redirectUrl && type === 'success') {
      const timer = setTimeout(() => {
        window.location.href = redirectUrl;
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [type, redirectUrl]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/90 backdrop-blur-sm dark:bg-gray-950/90">
      <div className="relative flex flex-col items-center">
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{
            duration: 0.5,
            ease: 'easeOut',
          }}
          className="relative mb-8"
        >
          <Image
            src="https://i.imgur.com/GAzKBJ1.png"
            alt="Logo Equipe Vilhena"
            width={96}
            height={96}
            priority
            className="object-contain"
          />

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="absolute -inset-4"
          >
            <svg className="h-32 w-32 animate-spin-slow" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="currentColor"
                strokeWidth="3"
                strokeLinecap="round"
                className="text-gray-200 dark:text-gray-800"
              />
              <motion.circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="currentColor"
                strokeWidth="3"
                strokeLinecap="round"
                className={isSuccess ? 'text-green-500' : 'text-red-500'}
                strokeDasharray="283"
                strokeDashoffset="283"
                animate={{
                  strokeDashoffset: 0,
                }}
                transition={{
                  duration: 1.5,
                  ease: 'easeInOut',
                }}
              />
            </svg>
          </motion.div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="text-center"
        >
          <h2
            className={`text-xl font-semibold ${
              isSuccess ? 'text-green-900 dark:text-green-200' : 'text-red-900 dark:text-red-200'
            }`}
          >
            {title}
          </h2>
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: '100%' }}
            transition={{
              duration: 1,
              ease: 'easeInOut',
            }}
            className={`mt-2 h-0.5 mx-auto w-24 ${
              isSuccess
                ? 'bg-gradient-to-r from-green-500 to-green-600'
                : 'bg-gradient-to-r from-red-500 to-red-600'
            }`}
          />
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">{message}</p>
          
          {onAction && (
            <motion.button
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              onClick={onAction}
              className={`mt-4 rounded-md px-4 py-2 text-sm font-medium text-white ${
                isSuccess ? 'bg-green-500 hover:bg-green-600' : 'bg-red-500 hover:bg-red-600'
              }`}
            >
              {isSuccess ? 'Continuar' : 'Voltar'}
            </motion.button>
          )}
        </motion.div>
      </div>
    </div>
  )
} 