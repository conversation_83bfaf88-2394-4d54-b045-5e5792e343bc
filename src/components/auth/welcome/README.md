# Componente AuthWelcome - Sistema de Faixas

Este documento descreve o sistema de faixas utilizado no componente `AuthWelcome` e seus utilitários.

## Funcionalidades

### 1. Dados da Faixa do Banco de Dados

O sistema agora utiliza dados completos da tabela `belt_levels` incluindo:

- **belt_color**: Cor da faixa em formato HEX (ex: `#FFFFFF`)
- **stripe_color**: Cor das listras em formato HEX (ex: `#FF0000`)
- **label**: Nome completo da faixa (ex: "Faixa Branca - 1º Grau")
- **degree**: Grau da faixa (0-11)
- **show_center_line**: Se deve mostrar linha central
- **center_line_color**: Cor da linha central

### 2. Componentes Disponíveis

#### BeltIndicator
Componente reutilizável para exibir indicadores de faixa:

```tsx
import { BeltIndicator } from './components/belt-indicator'

// Uso básico
<BeltIndicator beltData={user.beltData} />

// Com label
<BeltIndicator beltData={user.beltData} showLabel={true} />

// Tamanhos diferentes
<BeltIndicator beltData={user.beltData} size="sm" />
<BeltIndicator beltData={user.beltData} size="md" />
<BeltIndicator beltData={user.beltData} size="lg" />
```

### 3. Utilitários de Cores

#### getBeltIndicatorClasses()
Gera classes CSS para o indicador de faixa:

```tsx
import { getBeltIndicatorClasses } from './utils/belt-colors'

const classes = getBeltIndicatorClasses(beltData)
// Retorna: "h-3 w-3 rounded-full ring-1 ring-gray-400"
```

#### getBeltIndicatorStyle()
Gera estilos inline para cores sólidas:

```tsx
import { getBeltIndicatorStyle } from './utils/belt-colors'

const style = getBeltIndicatorStyle(beltData)
// Retorna: { backgroundColor: "#FFFFFF" }
```

#### getBeltStripeStyle()
Gera estilos inline para faixas com listras:

```tsx
import { getBeltStripeStyle } from './utils/belt-colors'

const style = getBeltStripeStyle(beltData)
// Retorna: { background: "linear-gradient(...)" }
```

#### hasBeltStripes()
Verifica se a faixa tem listras:

```tsx
import { hasBeltStripes } from './utils/belt-colors'

const hasStripes = hasBeltStripes(beltData)
// Retorna: true ou false
```

#### getBeltDisplayName()
Obtém o nome de exibição da faixa:

```tsx
import { getBeltDisplayName } from './utils/belt-colors'

const name = getBeltDisplayName(beltData)
// Retorna: "Faixa Branca - 1º Grau"
```

### 4. Exemplos de Uso

#### Exemplo 1: Indicador Simples
```tsx
{user.beltData && (
  <div className="flex items-center gap-2">
    <BeltIndicator beltData={user.beltData} size="sm" />
    <span>{getBeltDisplayName(user.beltData)}</span>
  </div>
)}
```

#### Exemplo 2: Indicador com Listras
```tsx
{user.beltData && (
  <div 
    className={getBeltIndicatorClasses(user.beltData)}
    style={hasBeltStripes(user.beltData) 
      ? getBeltStripeStyle(user.beltData) 
      : getBeltIndicatorStyle(user.beltData)
    }
  />
)}
```

#### Exemplo 3: Lista de Faixas
```tsx
{beltLevels.map(belt => (
  <div key={belt.id} className="flex items-center gap-3 p-2">
    <BeltIndicator beltData={belt} size="md" />
    <span className="font-medium">{belt.label}</span>
    <span className="text-sm text-gray-500">
      {belt.degree}º grau
    </span>
  </div>
))}
```

### 5. Cores Suportadas

O sistema suporta qualquer cor em formato HEX e automaticamente:

- Determina se a cor é clara ou escura
- Aplica a cor de texto apropriada
- Adiciona bordas quando necessário para visibilidade
- Gera gradientes para listras

### 6. Fallbacks

O sistema inclui fallbacks robustos:

- Se `beltData` não estiver disponível, usa cores padrão
- Se `label` não estiver disponível, gera nome baseado na cor
- Se cores não forem válidas, usa cinza padrão

### 7. Estrutura de Dados

```typescript
interface BeltData {
  belt_color: string      // "#FFFFFF"
  stripe_color?: string   // "#FF0000"
  label: string          // "Faixa Branca - 1º Grau"
  degree?: number        // 1
  show_center_line?: boolean
  center_line_color?: string
}
```

### 8. Considerações de Performance

- As cores são calculadas dinamicamente para suportar qualquer cor HEX
- Utilitários são otimizados para evitar recálculos
- Componentes são leves e reutilizáveis
- Suporte a SSR (Server-Side Rendering)

### 9. Acessibilidade

- Suporte a `title` nos indicadores para screen readers
- Contraste automático entre texto e fundo
- Bordas visuais para cores claras
- Nomes descritivos das faixas