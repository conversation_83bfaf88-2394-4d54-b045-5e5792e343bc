'use client'

import { BeltData } from '../types'
import {
  getBeltIndicatorClasses,
  getBeltIndicatorStyle,
  getBeltStripeStyle,
  hasBeltStripes,
  getBeltDisplayName
} from '../utils/belt-colors'

interface BeltIndicatorProps {
  beltData: BeltData | null
  size?: 'sm' | 'md' | 'lg'
  showLabel?: boolean
  className?: string
}

const sizeClasses = {
  sm: 'h-3 w-3',
  md: 'h-4 w-4',
  lg: 'h-6 w-6'
}

export function BeltIndicator({
  beltData,
  size = 'md',
  showLabel = false,
  className = ''
}: BeltIndicatorProps) {
  if (!beltData) {
    return null
  }

  const baseClasses = `${sizeClasses[size]} rounded-full flex-shrink-0`
  const indicatorClasses = getBeltIndicatorClasses(beltData)
  const hasStripes = hasBeltStripes(beltData)
  const style = hasStripes ? getBeltStripeStyle(beltData) : getBeltIndicatorStyle(beltData)

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div
        className={`${baseClasses} ${indicatorClasses}`}
        style={style}
        title={beltData.label}
      />
      {showLabel && (
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {getBeltDisplayName(beltData)}
        </span>
      )}
    </div>
  )
}
