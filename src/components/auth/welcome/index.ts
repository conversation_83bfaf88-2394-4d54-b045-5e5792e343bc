// Componente principal
export { AuthWelcome } from './welcome'

// Componentes auxiliares
export { BeltIndicator } from './components/belt-indicator'

// Tipos
export type { UserWelcomeProps, UserData, BeltData } from './types'

// Utilitários de cores
export {
  getBeltIndicatorClasses,
  getBeltIndicatorStyle,
  getBeltStripeStyle,
  hasBeltStripes,
  getBeltDisplayName,
  getBeltTextColor,
  getBeltContainerClasses,
  isLightColor
} from './utils/belt-colors'
