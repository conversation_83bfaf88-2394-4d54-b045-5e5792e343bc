export interface BeltData {
  belt_color: string // Cor em HEX (ex: "#FFFFFF")
  stripe_color?: string // Cor da faixa em HEX (ex: "#FF0000")
  label: string // Nome da faixa (ex: "Faixa Branca - 1º Grau")
  degree?: number // Grau da faixa
  show_center_line?: boolean
  center_line_color?: string
}

export interface UserWelcomeProps {
  userData?: {
    displayName: string
    tenantName?: string
    belt?: string | null
    stripes?: number | null
    beltLabel?: string | null
    stripeColor?: string | null
    beltData?: BeltData | null
    role?: string
  }
  redirectTo?: string
  redirectDelay?: number
  logoUrl?: string
}

export interface UserData {
  displayName: string
  tenantName?: string
  belt?: string | null
  stripes?: number | null
  beltLabel?: string | null
  stripeColor?: string | null
  beltData?: BeltData | null | string
  role?: string
  error?: string
}
