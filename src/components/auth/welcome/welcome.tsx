'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { useEffect, useState, useRef } from 'react'
import { getClientUserWelcomeData } from '@/services/auth'
import { useRouter } from 'next/navigation'
import { UserData, UserWelcomeProps, BeltData } from './types'
import { useTenantTheme } from '@/hooks/tenant/use-tenant-theme'
import { useQueryClient } from '@tanstack/react-query'
import { BeltWithDetails } from '@/components/belt/BeltWithDetails'

export function AuthWelcome({ userData: initialUserData, redirectTo = '/home', redirectDelay = 3000, logoUrl: propLogoUrl }: UserWelcomeProps) {
  const [user, setUser] = useState<UserData | null>(initialUserData || null)
  const [isLoading, setIsLoading] = useState(!initialUserData)
  const redirectionInProgress = useRef(false)
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const router = useRouter()
  const { logoUrl: themeLogoUrl, tenantName } = useTenantTheme()
  const [mounted, setMounted] = useState(false)
  const queryClient = useQueryClient()

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    async function loadUserData() {
      try {
        // Verificar se precisamos buscar dados completos
        const needsFullData = !initialUserData ||
          (!initialUserData.beltData && !initialUserData.beltLabel && initialUserData.belt)

        if (needsFullData) {
          const data = await getClientUserWelcomeData()
          // Se houver erro na primeira tentativa, tentar novamente após um delay maior
          if (data.error) {
            console.warn('Primeira tentativa falhou, aguardando um momento para tentar novamente...', data.error)

            // Definir dados temporários para mostrar algo ao usuário
            setUser(data)

            // Aguardar 1.5 segundos e tentar novamente
            setTimeout(async () => {
              try {
                console.log('Realizando segunda tentativa de obter dados...')
                let retryData = await getClientUserWelcomeData()
                console.log('Dados da segunda tentativa:', retryData)

                if (retryData && typeof retryData.beltData === 'string') {
                  try {
                    const parsedBeltData = JSON.parse(retryData.beltData)
                    retryData = { ...retryData, beltData: parsedBeltData }
                  } catch (e) {
                    console.error('Falha ao fazer parse do beltData em retry:', e)
                  }
                }

                // Atualizar dados mesmo se houver erro
                setUser(retryData)
              } catch (retryError) {
                console.error('Erro na segunda tentativa:', retryError)
                // Manter os dados da primeira tentativa, já definidos
              }
            }, 1500)
          } else {
            let finalData = data
            if (finalData && typeof finalData.beltData === 'string') {
              try {
                const parsedBeltData = JSON.parse(finalData.beltData)
                finalData = { ...finalData, beltData: parsedBeltData }
              } catch (e) {
                console.error('Falha ao fazer parse do beltData:', e)
              }
            }
            // Sem erro, definir os dados normalmente
            setUser(finalData)
          }
        } else {
          let finalInitialData = initialUserData
          if (finalInitialData && typeof finalInitialData.beltData === 'string') {
            try {
              const parsedBeltData = JSON.parse(finalInitialData.beltData)
              finalInitialData = { ...finalInitialData, beltData: parsedBeltData }
            } catch (e) {
              console.error('Falha ao fazer parse do beltData inicial:', e)
            }
          }
          setUser(finalInitialData)
        }

        setIsLoading(false)

        // Invalidar queries relacionadas ao papel do usuário antes do redirecionamento
        // para garantir que as permissões sejam recarregadas corretamente
        queryClient.invalidateQueries({ queryKey: ['user-role'] as const })
        queryClient.invalidateQueries({ queryKey: ['user-admin-status'] as const })
        queryClient.invalidateQueries({ queryKey: ['user-profile'] as const })

        console.log('Invalidando cache de permissões do usuário antes do redirecionamento')

        timerRef.current = setTimeout(() => {
          if (redirectionInProgress.current) return
          redirectionInProgress.current = true

          // Se redirectTo não é o padrão (/home), usar ele prioritariamente
          // Senão, utilizar lógica baseada em role
          const targetPath = redirectTo !== '/home' ? redirectTo : initialUserData?.role === 'admin' || user?.role === 'admin' ? '/dashboard' : redirectTo

          router.push(targetPath)
        }, redirectDelay)
      } catch (error) {
        console.error('Erro ao carregar dados do usuário:', error)

        // Mesmo com erro, continuar mostrando a tela de boas-vindas
        setUser({
          displayName: 'Usuário'
        })

        setIsLoading(false)

        // Ainda redirecionar após o tempo de atraso
        timerRef.current = setTimeout(() => {
          router.push(redirectTo)
        }, redirectDelay)
      }
    }

    loadUserData()

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }
    }
  }, [initialUserData, redirectTo, redirectDelay, router, user?.role, queryClient])

  if (isLoading || !user) return null

  // Prioriza o logo passado por prop, depois o do tema, e por último o padrão
  const displayLogoUrl = propLogoUrl || (mounted ? themeLogoUrl : null) || '/placeholder-logo.png'
  const altText = mounted ? tenantName || 'Academia' : 'Logo'
  
  // Criar beltData a partir das propriedades individuais se não estiver disponível
  let finalBeltData: BeltData | null = null
  
  if (user.beltData) {
    if (typeof user.beltData === 'string') {
      try {
        finalBeltData = JSON.parse(user.beltData) as BeltData
      } catch (e) {
        console.error('Falha ao fazer parse do beltData:', e)
      }
    } else {
      finalBeltData = user.beltData as BeltData
    }
  } else if (user.belt) {
    // Se não temos beltData mas temos belt e stripes, criamos um objeto com o nome da faixa completo
    // usando o campo beltLabel que vem do servidor e contém o nome completo da faixa da tabela belt_levels
    finalBeltData = {
      belt_color: user.belt,
      degree: user.stripes || 0,
      label: user.beltLabel || '', // Usar o nome completo da faixa que vem do servidor
      stripe_color: user.stripeColor || '#FFFFFF'
    }
  }

  // FALLBACK: Se finalBeltData existe mas não tem label, tentar gerar um nome baseado na cor
  if (finalBeltData && (!finalBeltData.label || finalBeltData.label.trim() === '')) {
    const colorToName: Record<string, string> = {
      '#FFFFFF': 'Faixa Branca',
      '#FAFF00': 'Faixa Amarela',
      '#FFA500': 'Faixa Laranja',
      '#22C55E': 'Faixa Verde',
      '#2563EB': 'Faixa Azul',
      '#7C3AED': 'Faixa Roxa',
      '#92400E': 'Faixa Marrom',
      '#000000': 'Faixa Preta',
      '#EF4444': 'Faixa Vermelha',
      '#909090': 'Faixa Cinza'
    }

    const baseName = colorToName[finalBeltData.belt_color] || 'Faixa'
    const degree = finalBeltData.degree || 0
    finalBeltData.label = degree > 0 ? `${baseName} - ${degree}º Grau` : baseName
    console.log('[Welcome DEBUG] Generated fallback label:', finalBeltData.label)
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/90 backdrop-blur-sm dark:bg-gray-950/90">
      <div className="relative flex flex-col items-center">
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{
            duration: 0.5,
            ease: 'easeOut'
          }}
          className="relative mb-8">
          <Image src={displayLogoUrl} alt={altText} width={96} height={96} priority className="object-contain" />

          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.5 }} className="absolute -inset-4">
            <svg className="h-32 w-32" viewBox="0 0 100 100">
              <circle cx="50" cy="50" r="45" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" className="text-gray-200 dark:text-gray-800" />
              <motion.circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="currentColor"
                strokeWidth="3"
                strokeLinecap="round"
                className="text-green-500"
                strokeDasharray="283"
                strokeDashoffset="283"
                animate={{
                  strokeDashoffset: 0
                }}
                transition={{
                  duration: 1,
                  ease: 'easeInOut'
                }}
              />
            </svg>
          </motion.div>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.8 }} className="text-center">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">Bem-vindo de volta, {user.displayName.split(' ')[0]}!</h2>
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: '100%' }}
            transition={{
              duration: 1,
              ease: 'easeInOut',
              delay: 1
            }}
            className="mx-auto mt-2 h-0.5 w-32 bg-gradient-to-r from-green-500 to-green-600"
          />
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Você está sendo redirecionado para a área do {user.belt ? 'aluno' : 'usuário'}</p>
        </motion.div>

        <div className="mt-6 flex space-x-1.5">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0.5, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{
                duration: 0.5,
                repeat: Infinity,
                repeatType: 'reverse',
                delay: i * 0.2
              }}
              className="h-2 w-2 rounded-full bg-green-500"
            />
          ))}
        </div>

        {/* Exibição da faixa do usuário */}
        {(finalBeltData || user.beltLabel) && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.4 }}
            className="mt-6"
          >
            <div className="flex flex-col items-center gap-2">
              {finalBeltData && (
                <BeltWithDetails
                  color={finalBeltData.belt_color}
                  degree={finalBeltData.degree ?? 0}
                  label={finalBeltData.label}
                  stripeColor={finalBeltData.stripe_color}
                  showCenterLine={finalBeltData.show_center_line ?? false}
                  centerLineColor={finalBeltData.center_line_color}
                  size="lg"
                  className="mx-auto"
                />
              )}

              {/* Nome da faixa - garantir que seja sempre exibido */}
              {(() => {
                const displayText = finalBeltData?.label || user.beltLabel || '';
                console.log('[Welcome DEBUG] Rendering belt name span with text:', displayText);
                return (
                  <span
                    className="text-sm font-medium text-gray-900 dark:text-gray-100 text-center mt-2 px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full"
                    title={displayText}
                  >
                    {displayText}
                  </span>
                );
              })()}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}
