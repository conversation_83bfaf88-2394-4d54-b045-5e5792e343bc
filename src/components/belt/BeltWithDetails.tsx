import React from 'react';
import { JiuJitsuBelt } from './JiuJitsuBelt';
import { BeltColor } from './index';
import { TooltipProvider, TooltipRoot, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';

interface BeltWithDetailsProps {
  color: BeltColor | string;
  degree?: number;
  label?: string | null;
  stripeColor?: string | null;
  showCenterLine?: boolean | null;
  centerLineColor?: string | null;
  size?: "xs" | "sm" | "md" | "lg";
  className?: string;
}

const beltSizes = {
  xs: "h-2 w-8",
  sm: "h-3 w-12", 
  md: "h-4 w-16",
  lg: "h-6 w-20"
} as const;

export function BeltWithDetails({
  color,
  degree = 0,
  label,
  stripeColor,
  showCenterLine = false,
  centerLineColor,
  size = "sm",
  className,
}: BeltWithDetailsProps) {
  const tooltipText = label || `${getColorName(color)} ${degree ? `(${degree}° grau)` : ''}`;

  return (
    <TooltipProvider>
      <TooltipRoot>
        <TooltipTrigger asChild>
          <div className="cursor-help">
            <JiuJitsuBelt
              beltColor={color}
              degrees={degree}
              stripeColor={stripeColor || undefined}
              showCenterLine={showCenterLine || false}
              centerLineColor={centerLineColor || undefined}
              className={`${beltSizes[size]} ${className || ''}`}
            />
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltipText}</p>
        </TooltipContent>
      </TooltipRoot>
    </TooltipProvider>
  );
}

// Função auxiliar para obter o nome da cor em português
function getColorName(color: BeltColor | string): string {
  const colorMap: Record<string, string> = {
    white: 'Branca',
    blue: 'Azul',
    purple: 'Roxa',
    brown: 'Marrom',
    black: 'Preta',
  };
  
  return colorMap[color.toLowerCase()] || color;
}
