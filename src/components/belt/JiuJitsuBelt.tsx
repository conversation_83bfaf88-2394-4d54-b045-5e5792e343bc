'use client'

import { cn } from "@/lib/utils"

export type JiuJitsuBeltColor = "white" | "blue" | "purple" | "brown" | "black"
export type JiuJitsuStripeColor = "black" | "red" | "white"
export type StripePosition = 'middle' | 'edge' | 'full'

interface JiuJitsuBeltProps {
  beltColor: JiuJitsuBeltColor | string
  stripeColor?: JiuJitsuStripeColor | string
  /**
   * Número de graus (listras verticais) exibidos no container de faixa de grau.
   */
  degrees?: number
  /**
   * Posição do container de faixa de grau.
   */
  stripePosition?: StripePosition
  /**
   * Mostra ou esconde a linha horizontal central.
   * @default false
   */
  showCenterLine?: boolean
  /**
   * Cor da linha horizontal central (hex, rgb ou nome). Apenas utilizada quando showCenterLine = true.
   */
  centerLineColor?: string
  className?: string
}

const beltColorClasses: Record<JiuJitsuBeltColor, string> = {
  white: "bg-white",
  blue: "bg-blue-600",
  purple: "bg-purple-600",
  brown: "bg-amber-800",
  black: "bg-black",
}

const stripeColorClasses: Record<JiuJitsuStripeColor, string> = {
  black: "bg-black",
  red: "bg-red-600",
  white: "bg-zinc-200",
}

function isPredefinedBeltColor(color: string): color is JiuJitsuBeltColor {
  return color in beltColorClasses;
}

function isPredefinedStripeColor(color: string): color is JiuJitsuStripeColor {
  return color in stripeColorClasses;
}

export function JiuJitsuBelt({
  beltColor,
  stripeColor: degreeStripeColorProp,
  degrees = 0,
  stripePosition = 'middle',
  showCenterLine = false,
  centerLineColor,
  className,
}: JiuJitsuBeltProps) {
  const isBlackBelt =
    beltColor.toLowerCase() === 'black' || beltColor.toLowerCase() === '#000000'

  const stripeBarColor = isBlackBelt ? 'red' : 'black'
  const degreeStripeColor =
    degreeStripeColorProp || '#FFFFFF'

  const validDegrees = Math.max(0, Math.min(6, Math.floor(degrees)))

  const beltStyle = isPredefinedBeltColor(beltColor)
    ? {}
    : { backgroundColor: beltColor }
  const beltClassName = isPredefinedBeltColor(beltColor)
    ? beltColorClasses[beltColor]
    : ''

  const stripeStyle = isPredefinedStripeColor(stripeBarColor)
    ? {}
    : { backgroundColor: stripeBarColor }

  const getStripeClassName = () => {
    if (stripePosition === 'full') return ''
    if (isPredefinedStripeColor(stripeBarColor)) {
      return beltColor === 'black' && stripeBarColor === 'black'
        ? 'bg-zinc-900'
        : stripeColorClasses[stripeBarColor]
    }
    return ''
  }

  const stripeContainerPositions: Record<StripePosition, string> = {
    middle: "absolute inset-y-0 left-1/2 -translate-x-1/2 w-[60%]",
    edge: "absolute inset-y-0 right-0 w-[10%]",
    full: "absolute inset-0"
  }

  // Define a cor da linha horizontal central.
  const horizontalLineColor = centerLineColor || stripeBarColor

  return (
    <div
      className={cn("relative w-full h-full rounded-sm overflow-hidden", className)}
    >
      <div
        className={cn("absolute inset-0", beltClassName)}
        style={beltStyle}
      />

      {/* Linha horizontal central opcional (renderizada abaixo do container da faixa de grau) */}
      {showCenterLine && (
        <div
          className="absolute left-0 right-0 top-1/2 -translate-y-1/2 h-[20%] border border-white/20 dark:border-white/15"
          style={{ backgroundColor: horizontalLineColor, zIndex: 0 }}
        />
      )}

      <div
        className={cn(stripeContainerPositions[stripePosition], getStripeClassName())}
        style={stripeStyle}
      >
        {validDegrees > 0 && stripePosition !== 'full' && (
          <div className="absolute inset-0 flex items-center justify-center gap-1">
            {[...Array(validDegrees)].map((_, i) => (
              <div
                key={i}
                className="h-full w-[3px]"
                style={{ backgroundColor: degreeStripeColor }}
              />
            ))}
          </div>
        )}
      </div>

      <div className="absolute inset-y-0 left-0 w-[15%] bg-gradient-to-r from-black/10 to-transparent" />
      <div className="absolute inset-y-0 right-0 w-[15%] bg-gradient-to-l from-black/10 to-transparent" />
      <div className="absolute inset-0 border border-black/10" />
    </div>
  )
}