import { cn } from "@/lib/utils"
import { JiuJitsuBelt, JiuJitsuBeltColor } from "./JiuJitsuBelt";
import { BeltWithDetails } from "./BeltWithDetails";

export { JiuJitsuBelt } from "./JiuJitsuBelt";
export { BeltWithDetails } from "./BeltWithDetails";

export type BeltColor = "white" | "blue" | "purple" | "brown" | "black";
type BeltSize = "xs" | "sm" | "md" | "lg";

interface BeltProps {
  color: BeltColor;
  size?: BeltSize;
  className?: string;
  degrees?: number;
}

export const beltColorTranslation = {
  white: "Branca",
  blue: "Azul",
  purple: "Roxa",
  brown: "Marrom",
  black: "Preta"
} as const;

const beltSizes = {
  xs: "w-16 h-4",
  sm: "w-24 h-6",
  md: "w-32 h-8",
  lg: "w-48 h-12",
} as const;

export function Belt({ color, degrees, size = "md", className }: BeltProps) {
  return <JiuJitsuBelt beltColor={color} degrees={degrees} className={cn(beltSizes[size], className)} />;
}

// Interface para dados de faixa completos
export interface BeltData {
  belt_level_id?: string;
  color: string;
  degree?: number;
  stripeColor?: string | null;
  showCenterLine?: boolean | null;
  centerLineColor?: string | null;
  label?: string | null;
  sortOrder?: number | null;
}

// Helper para renderizar faixas com detalhes completos
interface StudentBeltRendererProps {
  currentBelt?: BeltData;
  fallbackBelt?: string;
  size?: "xs" | "sm" | "md" | "lg";
  className?: string;
}

export function StudentBeltRenderer({ 
  currentBelt, 
  fallbackBelt, 
  size = "sm", 
  className 
}: StudentBeltRendererProps) {
  if (currentBelt) {
    return (
      <BeltWithDetails
        color={currentBelt.color}
        degree={currentBelt.degree || 0}
        label={currentBelt.label}
        stripeColor={currentBelt.stripeColor}
        showCenterLine={currentBelt.showCenterLine}
        centerLineColor={currentBelt.centerLineColor}
        size={size}
        className={className}
      />
    );
  }
  
  if (fallbackBelt) {
    return (
      <BeltWithDetails
        color={fallbackBelt.toLowerCase()}
        degree={0}
        size={size}
        className={className}
      />
    );
  }
  
  return <span className="text-muted-foreground text-sm">N/A</span>;
}

type BeltDisplayProps = {
  belt: "white" | "blue" | "purple" | "brown" | "black"
  stripes?: number
  size?: "sm" | "md" | "lg"
  className?: string
  showTranslation?: boolean
}

const beltColors = {
  white: "bg-white border-gray-200",
  blue: "bg-blue-600",
  purple: "bg-purple-600",
  brown: "bg-amber-800",
  black: "bg-black"
} as const

const displayBeltSizes = {
  sm: {
    belt: "h-1.5 w-8",
    stripe: "h-1.5 w-1.5"
  },
  md: {
    belt: "h-2 w-12",
    stripe: "h-2 w-2"
  },
  lg: {
    belt: "h-3 w-16",
    stripe: "h-3 w-3"
  }
} as const

export function BeltDisplay({ belt, stripes = 0, size = "md", className, showTranslation = true }: BeltDisplayProps) {
  // Garantir que stripes seja um número válido não negativo
  const validStripes = Math.max(0, Math.floor(Number(stripes) || 0));
  
  return (
    <div className="flex items-center gap-1">
      <div className={cn("rounded border", beltColors[belt], displayBeltSizes[size].belt, className)} />
      <div className="flex gap-0.5">
        {validStripes > 0 && [...Array(validStripes)].map((_, i) => (
          <div key={i} className={cn("rounded-full bg-yellow-400", displayBeltSizes[size].stripe)} />
        ))}
      </div>
      {showTranslation && (
        <span className="text-xs text-gray-600 ml-1">{beltColorTranslation[belt]}</span>
      )}
    </div>
  )
} 