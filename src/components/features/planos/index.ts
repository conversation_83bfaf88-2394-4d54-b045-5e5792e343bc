// Imports
import { PlanFormProvider } from './PlanFormContext'
import { PlanForm<PERSON>ontainer, PlanFormHeader, PlanFormContent, PlanFormActions } from './PlanFormContainer'
import { PlanFormSection, PlanFormSectionHeader, PlanFormSectionContent, PlanFormSectionStatus } from './PlanFormSection'
import { PlanFormButton, PlanFormSubmitButton, PlanFormCancelButton, PlanFormDeleteButton } from './PlanFormButton'
import { PlanFormDetailsSection } from './PlanFormDetailsSection'
import { PlanFormPricingSection } from './PlanFormPricingSection'
import { PlanFormPerSessionSection } from './PlanFormPerSessionSection'
import { PlanFormTrialSection } from './PlanFormTrialSection'
import { PlanFormDurationSection } from './PlanFormDurationSection'
import { PlanFormBenefitsSection } from './PlanFormBenefitsSection'
import { PlanFormAcademyAccessSection } from './PlanFormAcademyAccessSection'
import { PlanFormSuccessFeedback } from './PlanFormSuccessFeedback'

export { PlanFormProvider, usePlanForm } from './PlanFormContext'
export { usePlanFormStatus } from './hooks/usePlanFormStatus'
export { PlanFormDetailsSection as Details } from './PlanFormDetailsSection'
export { PlanFormPricingSection as Pricing } from './PlanFormPricingSection'
export { PlanFormPerSessionSection as PerSession } from './PlanFormPerSessionSection'
export { PlanFormTrialSection as Trial } from './PlanFormTrialSection'
export { PlanFormDurationSection as Duration } from './PlanFormDurationSection'
export { PlanFormAcademyAccessSection as AcademyAccess } from './PlanFormAcademyAccessSection'
export { PlanFormBenefitsSection as Benefits } from './PlanFormBenefitsSection'
export { PlanFormSuccessFeedback as SuccessFeedback } from './PlanFormSuccessFeedback'

// Container components
export {
  PlanFormContainer as Container,
  PlanFormHeader as Header,
  PlanFormContent as Content,
  PlanFormActions as Actions,
} from './PlanFormContainer'

// Section components
export {
  PlanFormSection as Section,
  PlanFormSectionHeader as SectionHeader,
  PlanFormSectionContent as SectionContent,
  PlanFormSectionStatus as SectionStatus,
} from './PlanFormSection'

// Button components
export {
  PlanFormButton as Button,
  PlanFormSubmitButton as SubmitButton,
  PlanFormCancelButton as CancelButton,
  PlanFormDeleteButton as DeleteButton,
} from './PlanFormButton'

// Namespace para melhor organização
export const PlanForm = {
  Provider: PlanFormProvider,
  Container: {
    Root: PlanFormContainer,
    Header: PlanFormHeader,
    Content: PlanFormContent,
    Actions: PlanFormActions,
  },
  Section: {
    Root: PlanFormSection,
    Header: PlanFormSectionHeader,
    Content: PlanFormSectionContent,
    Status: PlanFormSectionStatus,
  },
  Button: {
    Root: PlanFormButton,
    Submit: PlanFormSubmitButton,
    Cancel: PlanFormCancelButton,
    Delete: PlanFormDeleteButton,
  },
  Details: PlanFormDetailsSection,
  Pricing: PlanFormPricingSection,
  PerSession: PlanFormPerSessionSection,
  Trial: PlanFormTrialSection,
  Duration: PlanFormDurationSection,
  AcademyAccess: PlanFormAcademyAccessSection,
  Benefits: PlanFormBenefitsSection,
  SuccessFeedback: PlanFormSuccessFeedback,
}
