'use client';

import { useTenantTheme } from '@/hooks/tenant/use-tenant-theme';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  showName?: boolean;
  className?: string;
  logoClassName?: string;
  nameClassName?: string;
  customName?: string;
}

export function Logo({ 
  size = 'md', 
  showName = true,
  className,
  logoClassName,
  nameClassName,
  customName
}: LogoProps) {
  const { primaryColor, logoUrl, tenantName } = useTenantTheme();
  const [mounted, setMounted] = useState(false);
  
  // Se um nome personalizado for fornecido, ele tem prioridade
  // Caso contrário, use o nome do tenant do contexto ou um fallback
  const displayName = customName || tenantName || 'Academia';
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  if (!mounted) return null;
  
  const hasLogo = logoUrl && logoUrl !== '/placeholder-logo.png';
  
  const dimensions = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };
  
  const fontSizes = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-xl'
  };
  
  const textSizes = {
    sm: 'text-base',
    md: 'text-xl',
    lg: 'text-2xl'
  };
  
  return (
    <div className={cn("flex items-center", className)}>
      {hasLogo ? (
        <div className={cn(`relative overflow-hidden rounded-full`, dimensions[size], logoClassName)}>
          <Image
            src={logoUrl}
            alt={displayName}
            fill
            className="object-cover"
            priority
          />
        </div>
      ) : (
        <div 
          className={cn(
            "flex items-center justify-center rounded-full text-white font-bold", 
            dimensions[size], 
            fontSizes[size],
            logoClassName
          )}
          style={{ backgroundColor: primaryColor || '#f1341b' }}
        >
          {displayName.substring(0, 1)}
        </div>
      )}
      
      {showName && (
        <span className={cn(
          "ml-3 font-semibold text-white truncate", 
          textSizes[size],
          nameClassName
        )}>
          {displayName}
        </span>
      )}
    </div>
  );
} 