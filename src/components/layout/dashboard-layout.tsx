"use client"

import { useState, useEffect } from 'react';
import { cn } from "@/lib/utils";
import { Header } from "@/components/layout/header/Header";
import { Sidebar } from "@/components/layout/sidebar";
import { TenantThemeProvider } from "@/hooks/tenant/use-tenant-theme";
import { AIChatProvider, useAIChat } from "@/hooks/ui/use-ai-chat";
import { AIChat } from "@/components/ui/ai-chat";

interface DashboardLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  primaryColor?: string;
  secondaryColor?: string;
  tenantLogo?: string;
}

export function DashboardLayout({
  children,
  title = 'Dashboard',
  subtitle = 'Gerenciamento de Academia',
  primaryColor,
  secondaryColor,
  tenantLogo,
}: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <TenantThemeProvider
      initialPrimaryColor={primaryColor}
      initialSecondaryColor={secondaryColor}
    >
      <AIChatProvider>
        <DashboardLayoutContent 
          sidebarOpen={sidebarOpen}
          setSidebarOpen={setSidebarOpen}
          isExpanded={isExpanded}
          setIsExpanded={setIsExpanded}
          title={title}
          subtitle={subtitle}
          tenantLogo={tenantLogo}
        >
          {children}
        </DashboardLayoutContent>
      </AIChatProvider>
    </TenantThemeProvider>
  );
}

function DashboardLayoutContent({
  children,
  sidebarOpen,
  setSidebarOpen,
  isExpanded,
  setIsExpanded,
  title,
  subtitle,
  tenantLogo,
}: {
  children: React.ReactNode;
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
  title: string;
  subtitle: string;
  tenantLogo?: string;
}) {
  const { isOpen, closeChat } = useAIChat();

  return (
    <>
      <div className="min-h-screen">
        <Sidebar 
          sidebarOpen={sidebarOpen} 
          setSidebarOpen={setSidebarOpen}
          isExpanded={isExpanded}
          setIsExpanded={setIsExpanded}
          tenantName={title}
          tenantLogo={tenantLogo}
        />

        <div 
          className={cn(
            "flex flex-col will-change-[padding]",
            isExpanded 
              ? "lg:pl-72 transition-[padding] duration-150 ease-out" 
              : "lg:pl-20 transition-[padding] duration-150 ease-out"
          )}
          style={{ 
            transform: 'translateZ(0)',
            backfaceVisibility: 'hidden'
          }}
        >
          <Header setSidebarOpen={setSidebarOpen} title={title} subtitle={subtitle} />

          <main className="flex-1 from-background">
            <div className="py-4 px-4 sm:px-6 lg:px-6">{children}</div>
          </main>
        </div>
      </div>

      {/* Chat de IA */}
      <AIChat 
        isOpen={isOpen}
        onClose={closeChat}
      />
    </>
  );
} 