"use client";

import { useState, useEffect } from "react";
import { useScroll, motion } from "framer-motion";
import { toast } from "sonner";
import { 
  MobileMenuToggle, 
  DynamicPageTitle,
  UserMenu, 
  LogoutDialog,
  NotificationsPopover,
  ThemeSwitcher
} from "./components";

interface HeaderProps {
  setSidebarOpen: (open: boolean) => void;
  title?: string | React.ReactNode;
  subtitle?: string;
}

export function Header({ setSidebarOpen, title, subtitle }: HeaderProps) {
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);
  const { scrollY } = useScroll();
  
  useEffect(() => {
    const unsubscribe = scrollY.on("change", (latest) => {
      // Lógica de scroll futura pode ser implementada aqui
    });
    
    return () => unsubscribe();
  }, [scrollY]);

  const handleLogoutSuccess = () => {
    setShowLogoutDialog(false);
    toast.success("Logout realizado com sucesso", {
      description: "Você será redirecionado para a página de login.",
      duration: 3000,
    });
  };

  return (
    <>
      {/* Header Minimalista - Integrado ao Conteúdo */}
      <div className="header-fixed flex items-center justify-between pt-4 pb-2 px-4 sm:px-6 lg:px-6 bg-background/50 backdrop-blur-sm">
        {/* Lado Esquerdo - Menu Mobile + Título */}
        <div className="flex items-start gap-x-4 min-w-0 flex-1">
          <div className="mt-1">
            <MobileMenuToggle setSidebarOpen={setSidebarOpen} />
          </div>
          
          <div className="min-w-0 flex-1">
            <DynamicPageTitle defaultTitle={title} />
          </div>
        </div>

        {/* Lado Direito - Controles */}
        <motion.div 
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="flex items-center gap-x-3 sm:gap-x-4 mt-1"
        >
          <NotificationsPopover />
          <ThemeSwitcher />

          {/* Separador sutil */}
          <div
            className="hidden sm:block h-5 w-px bg-border dark:bg-gray-700"
            aria-hidden="true"
          />

          <UserMenu onLogoutClick={() => setShowLogoutDialog(true)} />
        </motion.div>
      </div>

      {/* Modal de Logout */}
      <LogoutDialog 
        isOpen={showLogoutDialog}
        onClose={() => setShowLogoutDialog(false)}
        onConfirm={handleLogoutSuccess}
      />
    </>
  );
} 