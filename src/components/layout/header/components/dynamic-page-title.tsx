'use client';

import { usePathname } from 'next/navigation';
import { PageTitle } from './page-title';
import { usePageTitle } from '@/contexts/PageTitleContext';
import {
  CalendarDays,
  Users,
  GraduationCap,
  Home,
  User,
  BookOpen,
  UserCheck,
  Calendar,
  DollarSign,
  CreditCard,
  Clock,
  Wallet,
  Calculator,
  TrendingUp,
  Settings,
  Banknote,
  Percent,
} from 'lucide-react';
import { Cog6ToothIcon } from '@heroicons/react/24/outline';
import { ReactNode } from 'react';

const routeConfig: Record<string, { title: string; subtitle: string; icon: ReactNode }> = {
  '/agenda': {
    title: 'Agenda',
    subtitle: 'Visualize todos os seus eventos e aulas agendadas',
    icon: <CalendarDays className="h-6 w-6 text-primary" />
  },
  '/alunos': {
    title: 'Alunos',
    subtitle: 'Gerencie informações e dados dos alunos',
    icon: <Users className="h-6 w-6 text-primary" />
  },
  '/instrutores': {
    title: 'Instrutores',
    subtitle: 'Administre o corpo docente da escola',
    icon: <GraduationCap className="h-6 w-6 text-primary" />
  },
  '/turmas': {
    title: 'Turmas',
    subtitle: 'Gerencie turmas, matrículas e horários da sua academia',
    icon: <Users className="h-6 w-6 text-primary" />
  },
  '/presenca': {
    title: 'Presença',
    subtitle: 'Gerencie a presença dos alunos e acompanhe o desempenho das aulas em tempo real',
    icon: <UserCheck className="h-6 w-6 text-primary" />
  },
  '/dashboard': {
    title: 'Dashboard',
    subtitle: 'Visão geral das atividades e métricas',
    icon: <Home className="h-6 w-6 text-primary" />
  },
  '/home': {
    title: 'Início',
    subtitle: 'Página inicial da plataforma',
    icon: <Home className="h-6 w-6 text-primary" />
  },
  '/perfil': {
    title: 'Perfil',
    subtitle: 'Informações do seu perfil',
    icon: <User className="h-6 w-6 text-primary" />
  },
  '/aulas': {
    title: 'Aulas',
    subtitle: 'Gerencie aulas e atividades acadêmicas',
    icon: <BookOpen className="h-6 w-6 text-primary" />
  },
  '/aulas/presenca': {
    title: 'Controle de Presença',
    subtitle: 'Gerencie a presença dos alunos e acompanhe o desempenho das aulas em tempo real',
    icon: <UserCheck className="h-6 w-6 text-primary" />
  },
  '/aulas/calendario': {
    title: 'Calendário',
    subtitle: 'Visualize e organize as aulas no calendário',
    icon: <Calendar className="h-6 w-6 text-primary" />
  },
  '/aulas/turmas': {
    title: 'Turmas',
    subtitle: 'Administre turmas e grupos de alunos',
    icon: <Users className="h-6 w-6 text-primary" />
  },

  // Rotas financeiras
  '/financeiro': {
    title: 'Financeiro',
    subtitle: 'Gerencie todas as operações financeiras da academia',
    icon: <Banknote className="h-6 w-6 text-primary" />
  },
  '/financeiro/mensalidades': {
    title: 'Mensalidades',
    subtitle: 'Gerencie pagamentos e mensalidades dos alunos',
    icon: <DollarSign className="h-6 w-6 text-primary" />
  },
  '/financeiro/pagamentos': {
    title: 'Pagamentos',
    subtitle: 'Lista completa de pagamentos e transações da academia',
    icon: <CreditCard className="h-6 w-6 text-primary" />
  },
  '/financeiro/recorrentes': {
    title: 'Pagamentos Recorrentes',
    subtitle: 'Gestão de assinaturas e pagamentos automáticos',
    icon: <Clock className="h-6 w-6 text-primary" />
  },
  '/financeiro/descontos': {
    title: 'Descontos',
    subtitle: 'Configuração de descontos e códigos promocionais',
    icon: <Percent className="h-6 w-6 text-primary" />
  },
  '/financeiro/formas-pagamento': {
    title: 'Formas de Pagamento',
    subtitle: 'Configuração de métodos de pagamento aceitos',
    icon: <Wallet className="h-6 w-6 text-primary" />
  },
  '/financeiro/contabilidade': {
    title: 'Contabilidade',
    subtitle: 'Relatórios contábeis e exportação de dados',
    icon: <Calculator className="h-6 w-6 text-primary" />
  },
  '/financeiro/crescimento': {
    title: 'Análise de Crescimento',
    subtitle: 'Métricas de crescimento da receita e base de alunos',
    icon: <TrendingUp className="h-6 w-6 text-primary" />
  },
  '/financeiro/configuracoes': {
    title: 'Configurações Financeiras',
    subtitle: 'Configurações gerais do módulo financeiro',
    icon: <Settings className="h-6 w-6 text-primary" />
  },

  '/academia/configuracoes': {
    title: 'Configurações da Academia',
    subtitle: 'Gerencie as configurações da sua academia',
    icon: <Cog6ToothIcon className="h-6 w-6 text-primary" />,
  },
};

export function DynamicPageTitle({ defaultTitle }: { 
  defaultTitle?: string | React.ReactNode;
}) {
  const pathname = usePathname();
  const { pageTitle: contextPageTitle, pageSubtitle: contextPageSubtitle, pageIcon: contextPageIcon } = usePageTitle();
  
  if (contextPageTitle) {
    return (
      <PageTitle 
        title={contextPageTitle} 
        subtitle={contextPageSubtitle || undefined} 
        icon={contextPageIcon || undefined}
      />
    );
  }
  
  // Ordenar as rotas por especificidade (mais específicas primeiro)
  const sortedRoutes = Object.keys(routeConfig).sort((a, b) => b.length - a.length);
  
  const matchingRoute = sortedRoutes.find(route => 
    pathname === route || pathname?.startsWith(`${route}/`)
  );
  
  if (matchingRoute) {
    const config = routeConfig[matchingRoute];
    return (
      <PageTitle 
        title={config.title} 
        subtitle={config.subtitle} 
        icon={config.icon}
      />
    );
  }
  
  return <PageTitle title={defaultTitle} />;
} 