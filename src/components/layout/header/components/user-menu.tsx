"use client";

import { Fragment, useEffect, useState } from "react";
import { Menu, MenuButton, MenuItem, MenuItems, Transition } from "@headlessui/react";
import { ChevronDownIcon, SparklesIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import { cn } from '@/lib/utils';
import { useUser } from '@/hooks/user/use-user-context'
import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { UserAvatar } from "@/components/ui/user-avatar";
import { useAIChat } from "@/hooks/ui/use-ai-chat";
import { extractNameParts, formatDisplayName } from '@/utils/name-utils';

const userNavigation = [
  { id: "profile", name: "Meu Perfil", href: "/perfil", dynamicHref: true },
  { id: "settings", name: "Configurações", href: "/perfil/configuracoes" },
  { id: "ai", name: "Pergunte a IA", href: "#", isAI: true },
  { id: "logout", name: "<PERSON><PERSON>", href: "#", isLogout: true },
];

interface UserMenuProps {
  onLogoutClick: () => void;
}

export const UserMenu = ({ onLogoutClick }: UserMenuProps) => {
  const { profile, isLoading, avatarUrl, refetchUserData } = useUser();
  const { primaryColor } = useTenantTheme();
  const { openChat } = useAIChat();
  
  // Estado local para garantir persistência do nome entre navegações
  const [userName, setUserName] = useState<string>('Usuário');

  // Atualizar o nome do usuário quando o perfil for carregado ou atualizado
  useEffect(() => {
    if (profile?.fullName) {
      const { firstName, lastName } = extractNameParts(profile.fullName);
      const displayName = formatDisplayName(firstName, lastName);
      setUserName(displayName);
    } else if (profile?.firstName) {
      const lastPart = profile.lastName?.split(' ').filter(Boolean)[0] || '';
      const displayName = formatDisplayName(profile.firstName, lastPart);
      setUserName(displayName);
    }
  }, [profile?.fullName, profile?.firstName, profile?.lastName]);

  // Forçar carregamento dos dados do usuário ao montar o componente
  useEffect(() => {
    const loadUserData = async () => {
      await refetchUserData();
      console.log('[UserMenu] Dados do usuário carregados');
    };
    
    loadUserData();
  }, [refetchUserData]);

  // Adicionar listener para eventos de atualização de perfil
  useEffect(() => {
    const handleProfileUpdated = (event: Event) => {
      const customEvent = event as CustomEvent;
      const updatedUserId = customEvent.detail?.userId;
      const currentUserId = profile?.id;
      
      // Verificar se a atualização é para o usuário atual
      if (currentUserId && updatedUserId === currentUserId) {
        console.log('[UserMenu] Detectada atualização do perfil do usuário logado, recarregando dados');
        refetchUserData();
      }
    };
    
    // Ouvir ambos os tipos de eventos (específico e global)
    window.addEventListener('profile:updated', handleProfileUpdated);
    window.addEventListener('app:profile-change', () => {
      console.log('[UserMenu] Detectado evento global de mudança de perfil, recarregando dados');
      refetchUserData();
    });
    
    return () => {
      window.removeEventListener('profile:updated', handleProfileUpdated);
      window.removeEventListener('app:profile-change', () => refetchUserData());
    };
  }, [profile?.id, refetchUserData]);

  // Usar o avatar do contexto global se disponível, caso contrário, usar o do perfil
  const currentAvatarUrl = avatarUrl || profile?.avatarUrl;

  return (
    <Menu as="div" className="relative">
      <MenuButton className="flex items-center gap-x-3 rounded-lg p-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
        <span className="sr-only">Abrir menu do usuário</span>
        
        {isLoading ? (
          <div className="h-8 w-8 rounded-full animate-pulse bg-gray-200 dark:bg-gray-700" />
        ) : (
          <UserAvatar
            src={currentAvatarUrl}
            name={userName}
            alt=""
            size="sm"
            className="ring-2 ring-white dark:ring-gray-800"
          />
        )}
        
        <span className="hidden sm:flex sm:items-center">
          {isLoading ? (
            <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
          ) : (
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {userName}
            </span>
          )}
          <ChevronDownIcon className="ml-2 h-4 w-4 text-gray-400" aria-hidden="true" />
        </span>
      </MenuButton>
      
      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <MenuItems className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-lg bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700">
          {userNavigation.map((item) => (
            <MenuItem key={item.id}>
              {({ active }) => {
                const itemHref = (item.dynamicHref && profile?.id) 
                  ? `/perfil/${profile.id}` 
                  : item.href;
                
                // Estilização especial para o botão IA
                if (item.isAI) {
                  return (
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        openChat();
                      }}
                      className={cn(
                        "w-full flex items-center gap-2 px-4 py-2 text-sm",
                        "text-purple-700 dark:text-purple-300",
                        active 
                          ? "bg-purple-50 dark:bg-purple-900/30" 
                          : "",
                        "hover:bg-purple-50 dark:hover:bg-purple-900/30"
                      )}
                    >
                      <SparklesIcon className="h-4 w-4" />
                      <span>{item.name}</span>
                    </button>
                  );
                }
                
                return (
                  <Link
                    href={itemHref}
                    onClick={item.isLogout ? (e) => {
                      e.preventDefault();
                      onLogoutClick();
                    } : undefined}
                    className={cn(
                      "block px-4 py-2 text-sm transition-colors",
                      active ? "bg-gray-50 dark:bg-gray-700" : "",
                      item.isLogout 
                        ? "text-red-600 dark:text-red-400" 
                        : "text-gray-900 dark:text-gray-100",
                      "hover:bg-gray-50 dark:hover:bg-gray-700"
                    )}
                  >
                    {item.name}
                  </Link>
                );
              }}
            </MenuItem>
          ))}
        </MenuItems>
      </Transition>
    </Menu>
  );
}; 