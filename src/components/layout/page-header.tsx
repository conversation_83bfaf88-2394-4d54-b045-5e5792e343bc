'use client';

import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { ReactNode } from "react";
import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { cn } from "@/lib/utils";

interface PageHeaderProps {
  backHref?: string;
  backLabel?: string;
  children?: ReactNode;
  className?: string;
}

export function PageHeader({ 
  backHref, 
  backLabel = "Voltar", 
  children,
  className 
}: PageHeaderProps) {
  const { primaryColor } = useTenantTheme();
  
  return (
    <header className={cn("border-b border-gray-200 dark:border-gray-800", className)}>
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="py-6">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              {backHref && (
                <Link
                  href={backHref}
                  className="inline-flex items-center text-sm hover:opacity-80 transition-opacity"
                  style={{ color: primaryColor || '#6b7280' }}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  {backLabel}
                </Link>
              )}
            </div>
            <div className="flex items-center gap-4">
              {children}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
} 