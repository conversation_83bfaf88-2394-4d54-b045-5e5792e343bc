"use client";

import { BuildingIcon, ShieldCheckIcon } from "lucide-react";
import { useUserBranch } from "@/hooks/user/Auth";
import { useUserRole } from "@/hooks/user/Permissions";
import clsx from "clsx";
import { Skeleton } from "@/components/ui/skeleton";
import { BranchInfoSkeleton } from "./BranchInfoSkeleton";

interface BranchInfoProps {
  isHovered: boolean;
  themeColor: string;
}

export function BranchInfo({ isHovered, themeColor }: BranchInfoProps) {
  const { branch, isLoading: branchLoading } = useUserBranch();
  const { isAdmin, isLoading: roleLoading } = useUserRole();
  
  // Se ambos estão carregando, mostrar skeleton completo
  if (branchLoading && roleLoading) {
    return <BranchInfoSkeleton isHovered={isHovered} themeColor={themeColor} />;
  }
  
  return (
    <div className={clsx(
      "flex items-center gap-x-2 py-3",
      "border-t border-t-gray-100/50 dark:border-t-gray-800/40",
      isHovered ? "px-4" : "justify-center"
    )}
    style={{ borderColor: `${themeColor}20` }}>
      <div className={clsx(
        "flex items-center justify-center",
        "rounded-full p-1.5",
        "bg-gray-100/80 dark:bg-gray-800/50", 
        "text-gray-600 dark:text-gray-400"
      )}>
        {isAdmin ? (
          <ShieldCheckIcon 
            className={clsx(
              "shrink-0",
              isHovered ? "w-4 h-4" : "w-5 h-5"
            )}
            style={{ color: themeColor }}
          />
        ) : (
          <BuildingIcon 
            className={clsx(
              "shrink-0",
              isHovered ? "w-4 h-4" : "w-5 h-5"
            )}
            style={{ color: themeColor }}
          />
        )}
      </div>
      
      {isHovered && (
        <div className="flex flex-col min-w-0 overflow-hidden">
          <div className="text-xs font-medium" style={{ color: themeColor }}>
            {isAdmin ? "Função:" : "Filial:"}
          </div>
          {isAdmin ? (
            <div className="text-sm font-medium truncate" style={{ color: themeColor }}>
              Administrador
            </div>
          ) : branchLoading ? (
            <Skeleton className="h-4 w-24" />
          ) : (
            <div className="text-sm font-medium truncate" style={{ color: themeColor }}>
              {branch?.name || "Não vinculado"}
            </div>
          )}
        </div>
      )}
    </div>
  );
} 