"use client";

import Link from "next/link";
import Image from "next/image";
import camisetaPreview from "@/assets/images/products/camiseta-preview.png";
import bonePreview from "@/assets/images/products/bone-preview.png";
import canecaPreview from "@/assets/images/products/caneca-preview.png";

interface ShopPreviewProps {
  isHovered: boolean;
}

export function ShopPreview({ isHovered }: ShopPreviewProps) {
  const products = [
    {
      id: 1,
      name: "Camise<PERSON>",
      image: camisetaPreview,
      category: "camisetas",
    },
    {
      id: 2,
      name: "Bon<PERSON>",
      image: bonePreview,
      category: "bones",
    },
    {
      id: 3,
      name: "Can<PERSON><PERSON>",
      image: canecaPreview,
      category: "canecas",
    },
  ];

  if (!isHovered) return null;

  return (
    <div className="mt-auto px-3 py-4 border-t border-gray-200/80 dark:border-gray-800/80">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-xs font-semibold text-gray-400 dark:text-gray-500">
            LOJA
          </h3>
          <Link
            href="/loja"
            className="text-xs text-primary hover:text-primary/80 transition-colors duration-200"
          >
            Ver todos
          </Link>
        </div>
        
        <div className="grid grid-cols-3 gap-2">
          {products.map((product) => (
            <Link
              key={product.id}
              href={`/loja?category=${product.category}`}
              className="group relative aspect-square overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-800"
            >
              <Image
                src={product.image}
                alt={product.name}
                fill
                className="object-cover transition-all duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
              <div className="absolute inset-x-0 bottom-0 p-2">
                <p className="text-xs font-medium text-white opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                  {product.name}
                </p>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
} 