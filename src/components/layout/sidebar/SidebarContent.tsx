"use client";

import Image from "next/image";
import Link from "next/link";
import clsx from "clsx";
import { useState, useEffect, useMemo } from "react";
import { Navigation } from "./types";
import { SidebarSection } from "./SidebarSection";
import { SidebarLink } from "./SidebarLink";
import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { useAdminStatus } from "@/hooks/user/Permissions";
import { useUserRole } from "@/hooks/user/Permissions";
import { useRolePermissions } from "@/hooks/user/Permissions/use-role-permissions";
import { useSidebarFavorites } from "@/hooks/ui/use-sidebar-favorites";
import { TenantBrandedScrollArea } from "@/components/ui/tenant-branded-scroll-area";
import { BranchInfo } from "./BranchInfo";
import { usePathname } from "next/navigation";
import { navigationConfig } from "@/config/navigation";
import { PanelLeft, PanelLeftOpen } from "lucide-react";
import { HomeIcon, CalendarIcon } from "@heroicons/react/24/solid";
import { 
  TooltipProvider, 
  TooltipRoot, 
  TooltipTrigger, 
  TooltipContent, 
  TooltipPortal 
} from "@/components/ui/tooltip";
import { SidebarSkeleton } from "./SidebarSkeleton";

interface SidebarContentProps {
  navigation: Navigation;
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
  tenantName?: string;
  tenantLogo?: string;
  isMobile?: boolean;
}

export function SidebarContent({ 
  navigation, 
  isExpanded, 
  setIsExpanded,
  tenantName: propsTenantName, 
  tenantLogo: propsTenantLogo,
  isMobile = false
}: SidebarContentProps) {
  const { isAdmin, isLoading: isAdminLoading } = useAdminStatus();
  const { hasAdminPermissions, isLoading: isRoleLoading, role, isInstructor } = useUserRole();
  const { 
    canAccessTurmas, 
    canAccessAulasLivres, 
    canAccessPresenca, 
    userRole,
    isLoading: isRolePermissionsLoading 
  } = useRolePermissions();
  const { primaryColor, logoUrl, tenantName: contextTenantName } = useTenantTheme();
  const { favorites: dynamicFavorites, getRecentItems } = useSidebarFavorites();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, [primaryColor, isAdmin, isAdminLoading, hasAdminPermissions, isRoleLoading, role, isInstructor, userRole, canAccessTurmas, canAccessAulasLivres, canAccessPresenca]);

  // Força recarregamento das consultas de papel do usuário quando o componente monta
  useEffect(() => {
    const loadTimeout = setTimeout(() => {
      if (isAdminLoading || isRoleLoading || isRolePermissionsLoading) {
        // A dependência alterada forçará uma nova renderização
        setMounted(prev => !prev);
      }
    }, 500);

    return () => clearTimeout(loadTimeout);
  }, [isAdminLoading, isRoleLoading, isRolePermissionsLoading]);

  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    principal: true, // Seção principal sempre expandida
    academia: true,
    treinos: true, // Seção de aulas expandida por padrão
    presencas: true, // Nova seção de presenças
    financeiro: true, // Nova seção financeiro
    configuracoes: true,
    loja: true,
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Prioridade: 1. props, 2. contexto, 3. fallback
  const displayName = propsTenantName || contextTenantName || 'Academia';
  const finalLogoUrl = (mounted && logoUrl) || propsTenantLogo || '/logo.png';
  const hasLogo = finalLogoUrl && finalLogoUrl !== '/placeholder-logo.png';
  const isVilhena = displayName === "Vilhena";
  const vilhenaRedColor = "#ff0000";
  const themeColor = primaryColor || (isVilhena ? vilhenaRedColor : '#000000');

  // Define valores padrão seguros durante o carregamento
  const safeIsAdmin = isAdminLoading ? false : isAdmin;
  const safeIsInstructor = isRoleLoading ? false : isInstructor;

  const isLoadingCriticalData = !mounted || 
    (isAdminLoading && role === undefined) ||
    (isRoleLoading && role === undefined) ||
    isRolePermissionsLoading;

  // Função para filtrar itens com base em permissões baseadas em roles
  const filterItemsByRolePermissions = useMemo(() => {
    return (items: any[]) => {
      return items.filter(item => {
        // Verificar adminOnly (legacy)
        if (item.adminOnly && !safeIsAdmin) {
          return false;
        }
        
        // Verificar instructorOnly (legacy)
        if (item.instructorOnly && !safeIsInstructor && !safeIsAdmin) {
          return false;
        }

        // Nova lógica baseada em role-specific access
        if (item.instructorAccess !== undefined || item.studentAccess !== undefined) {
          // Se o item for "Home" e o usuário for admin, negar acesso explicitamente
          if (safeIsAdmin && item.name === "Home") {
            return false;
          }

          switch (userRole) {
            case 'admin':
              return true; // Admins geralmente têm acesso (exceto casos explicitamente negados)
            case 'instructor':
              return item.instructorAccess === true;
            case 'student':
              return item.studentAccess === true;
            default:
              return false;
          }
        }

        // Impedir que administradores vejam "Home" (fallback extra de segurança)
        if (safeIsAdmin && item.name === "Home") {
          return false;
        }

        // Verificações específicas para certas rotas
        if (item.href === '/turmas') {
          return canAccessTurmas;
        }
        
        if (item.href === '/aulas/livres') {
          return canAccessAulasLivres;
        }
        
        if (item.href === '/presenca') {
          return canAccessPresenca;
        }
        
        return true;
      });
    };
  }, [safeIsAdmin, safeIsInstructor, userRole, canAccessTurmas, canAccessAulasLivres, canAccessPresenca]);

  // Memoizado para evitar recálculos desnecessários
  const favoriteIcons = useMemo(() => {
    if (!mounted) return [];
    
    // Usar favoritos dinâmicos do contexto
    if (dynamicFavorites.length > 0) {
      return filterItemsByRolePermissions(dynamicFavorites);
    }
    
    // Fallback para favoritos hardcoded (modifica o texto de "Dashboard" para "Home" para usuários que não são admin)
    const updatedFavorites = navigation.favorites.map(item => {
      if (item.name === "Dashboard") {
        if (!safeIsAdmin) {
          return {...item, name: "Home", href: "/home"};
        }
      }
      return item;
    });
    
    return filterItemsByRolePermissions(updatedFavorites);
  }, [navigation.favorites, dynamicFavorites, safeIsAdmin, safeIsInstructor, mounted, filterItemsByRolePermissions]);

  // Detecta se estamos em uma rota de aulas para manter a seção expandida
  const pathname = usePathname?.() || '';
  const isInAulasSection = pathname.startsWith('/aulas');

  // Ajusta o estado de expansão baseado na rota atual
  useEffect(() => {
    if (isInAulasSection && !expandedSections.treinos) {
      setExpandedSections(prev => ({
        ...prev,
        treinos: true
      }));
    }
  }, [isInAulasSection]);

  // Itens da seção principal (Dashboard para admins, Home para outros)
  const principalItems = useMemo(() => {
    if (!mounted) return [];
    
    const items = [
      {
        name: "Dashboard",
        href: "/dashboard",
        icon: HomeIcon,
        adminOnly: true,
        metadata: {
          description: "Painel administrativo",
          category: "Principal",
          isMainFeature: true
        }
      },
      {
        name: "Home",
        href: "/home",
        icon: HomeIcon,
        adminOnly: false,
        instructorAccess: true,
        studentAccess: true,
        metadata: {
          description: "Página inicial",
          category: "Principal",
          isMainFeature: true
        }
      },
      {
        name: "Minha Agenda",
        href: "/agenda",
        icon: CalendarIcon,
        metadata: {
          description: "Visualize suas aulas e compromissos",
          category: "Principal",
          isMainFeature: false
        }
      }
    ];
    
    return filterItemsByRolePermissions(items);
  }, [mounted, filterItemsByRolePermissions]);

  // Memoizado para evitar recálculos desnecessários
  const visibleSections = useMemo(() => {
    if (!mounted) return [];
    
    // Na sidebar retraída, sempre mostrar itens recentemente visitados
    const recentItems = getRecentItems(6);
    
    // Se não há itens recentes suficientes, completar com itens das seções
    if (recentItems.length < 6) {
      const remainingSlots = 6 - recentItems.length;
      
      // Filtrar itens de cada seção e só incluir se houver itens visíveis
      const academiaItems = filterItemsByRolePermissions(navigation.academia);
      const treinosItems = filterItemsByRolePermissions(navigation.treinos);
      const presencasItems = filterItemsByRolePermissions(navigation.presencas);
      const financeiroItems = safeIsAdmin ? filterItemsByRolePermissions(navigation.financeiro) : [];
      const configuracoesItems = filterItemsByRolePermissions(navigation.configuracoes);
      const lojaItems = safeIsAdmin ? filterItemsByRolePermissions(navigation.loja) : [];
      
      const staticItems = [
        ...academiaItems.slice(0, 2),
        ...treinosItems.slice(0, 3),
        ...presencasItems.slice(0, 2),
        ...financeiroItems.slice(0, 2),
        ...configuracoesItems.slice(0, 2),
        ...lojaItems.slice(0, 1),
      ].filter(item => 
        // Evitar duplicatas com itens recentes e favoritos
        !recentItems.some(recent => recent.href === item.href) &&
        !favoriteIcons.some(fav => fav.href === item.href)
      ).slice(0, remainingSlots);
      
      return [...recentItems, ...staticItems];
    }
    
    return recentItems;
  }, [navigation, safeIsAdmin, safeIsInstructor, mounted, filterItemsByRolePermissions, favoriteIcons, dynamicFavorites, getRecentItems]);

  // Função para alternar o estado de expansão
  const toggleExpansion = () => {
    setIsExpanded(!isExpanded);
  };

  // Determinar para onde a logo deve redirecionar baseado no papel do usuário
  const logoRedirectHref = safeIsAdmin ? "/dashboard" : "/home";

  // Mostrar skeleton durante carregamento de dados críticos
  if (isLoadingCriticalData) {
    return <SidebarSkeleton isExpanded={isExpanded} themeColor={themeColor} />;
  }

  return (
    <div 
      className={clsx(
        "flex flex-col h-screen border-r",
        "will-change-[width] gpu-accelerated",
        "bg-gradient-to-b from-white via-gray-50/30 to-gray-100/20",
        "dark:from-gray-900 dark:via-gray-900/95 dark:to-gray-800/20",
        "shadow-xl shadow-gray-200/20 dark:shadow-gray-950/40",
        "rounded-r-3xl overflow-hidden backdrop-blur-sm",
        isExpanded 
          ? "w-72 transition-all duration-300 ease-in-out" 
          : "w-16 transition-all duration-300 ease-in-out"
      )}
      style={{ 
        transform: 'translateZ(0)',
        backfaceVisibility: 'hidden',
        borderColor: `${themeColor}15`
      }}
    >
      {!isExpanded && (
        <div className="absolute inset-y-0 right-0 w-[1px] flex items-center justify-end">
          <div 
            className="h-24 w-[1px] bg-gradient-to-b from-transparent via-current to-transparent opacity-50" 
            style={{ color: `${themeColor}40` }}
          />
        </div>
      )}

      <div className={clsx(
        "flex h-16 shrink-0 items-center",
        "border-b border-border/60 dark:border-gray-800/40",
        "bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm",
        "shadow-sm",
        isExpanded ? "px-6 gap-x-3" : "justify-center"
      )}
      style={{ borderColor: `${themeColor}10` }}
      >
        {/* Logo clicável com tooltip */}
        <TooltipProvider delayDuration={100}>
          <TooltipRoot>
            <TooltipTrigger asChild>
              <Link
                href={logoRedirectHref}
                className={clsx(
                  "relative overflow-hidden transition-all duration-300 ease-in-out",
                  "ring-2 shadow-sm hover:shadow-md",
                  "focus:outline-none focus:ring-4 focus:ring-offset-1",
                  "group",
                  isExpanded 
                    ? "rounded-xl h-8 w-8" 
                    : "rounded-full h-10 w-10 hover:scale-105"
                )}
                style={{ 
                  '--tw-ring-color': `${themeColor}30`,
                  '--tw-focus-ring-color': `${themeColor}40`
                } as React.CSSProperties}
              >
                {hasLogo ? (
                  <Image
                    src={finalLogoUrl}
                    alt={`${displayName} - Ir para ${safeIsAdmin ? 'Dashboard' : 'Home'}`}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                    priority
                  />
                ) : (
                  <div 
                    className={clsx(
                      "flex items-center justify-center font-medium transition-all duration-300 ease-in-out",
                      "w-full h-full group-hover:scale-110"
                    )}
                    style={{ 
                      backgroundColor: themeColor,
                      color: '#fff' 
                    }}
                  >
                    {displayName.substring(0, 1)}
                  </div>
                )}
                
                {/* Overlay de hover */}
                <div className={clsx(
                  "absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-300",
                  isExpanded ? "rounded-xl" : "rounded-full",
                  "bg-white dark:bg-gray-900"
                )} />
              </Link>
            </TooltipTrigger>
            <TooltipPortal>
              <TooltipContent 
                side={isExpanded ? "bottom" : "right"} 
                className="font-medium"
              >
                Ir para {safeIsAdmin ? 'Dashboard' : 'Home'}
              </TooltipContent>
            </TooltipPortal>
          </TooltipRoot>
        </TooltipProvider>

        {isExpanded && (
          <>
            <span 
              className={clsx(
                "text-base font-medium flex-1",
                "transition-opacity duration-300 ease-in-out",
              )}
              style={{ color: themeColor }}
            >
              {displayName}
            </span>
            
            {/* Botão de Expandir/Colapsar - Escondido no mobile */}
            {!isMobile && (
              <TooltipProvider delayDuration={100}>
                <TooltipRoot>
                  <TooltipTrigger asChild>
                    <button
                      onClick={toggleExpansion}
                      className={clsx(
                        "p-1.5 rounded-lg transition-all duration-200 ease-in-out",
                        "hover:bg-muted/60 hover:shadow-sm dark:hover:bg-gray-800/50",
                        "focus:outline-none focus:ring-2 focus:ring-offset-1",
                        "text-muted-foreground hover:text-foreground dark:text-gray-500 dark:hover:text-gray-400"
                      )}
                      style={{ 
                        '--tw-ring-color': `${themeColor}40`
                      } as React.CSSProperties}
                    >
                      <PanelLeft className="h-4 w-4" />
                    </button>
                  </TooltipTrigger>
                  <TooltipPortal>
                    <TooltipContent side="bottom" className="font-medium">
                      Fechar sidebar
                    </TooltipContent>
                  </TooltipPortal>
                </TooltipRoot>
              </TooltipProvider>
            )}
          </>
        )}
      </div>

      {/* Botão para abrir quando colapsada - Seção separada - Escondido no mobile */}
      {!isExpanded && !isMobile && (
        <div className="flex justify-center py-3 border-b border-border/40 dark:border-gray-800/40" style={{ borderColor: `${themeColor}10` }}>
          <TooltipProvider delayDuration={100}>
            <TooltipRoot>
              <TooltipTrigger asChild>
                <button
                  onClick={toggleExpansion}
                  className={clsx(
                    "p-2 rounded-full transition-all duration-200 ease-in-out",
                    "hover:bg-muted/60 hover:shadow-md dark:hover:bg-gray-800/50",
                    "focus:outline-none focus:ring-2 focus:ring-offset-1",
                    "text-muted-foreground hover:text-foreground dark:text-gray-500 dark:hover:text-gray-400",
                    "hover:scale-105"
                  )}
                  style={{ 
                    '--tw-ring-color': `${themeColor}40`
                  } as React.CSSProperties}
                >
                  <PanelLeftOpen className="h-4 w-4" />
                </button>
              </TooltipTrigger>
              <TooltipPortal>
                <TooltipContent side="right" className="font-medium">
                  Abrir sidebar
                </TooltipContent>
              </TooltipPortal>
            </TooltipRoot>
          </TooltipProvider>
        </div>
      )}

      {isExpanded ? (
        <TenantBrandedScrollArea className="flex-1 py-5 px-3" forceColor={themeColor}>
          <nav className="flex flex-col h-full">
            <ul role="list" className="flex flex-col gap-y-2 min-h-full">
              {/* Mostrar seção de favoritos apenas se houver favoritos */}
              {favoriteIcons.length > 0 && (
                <SidebarSection
                  title="FAVORITOS"
                  items={favoriteIcons}
                  isExpanded={true}
                  onToggle={() => {}}
                  isCollapsible={false}
                  isHovered={isExpanded}
                  userRoleState={{ isAdmin, isInstructor, isLoading: isAdminLoading || isRoleLoading }}
                  showFavoriteButton={false}
                />
              )}

              <div className="space-y-7 flex-1 mt-4">
                <SidebarSection
                  title="PRINCIPAL"
                  items={principalItems}
                  isExpanded={expandedSections.principal || true}
                  onToggle={() => toggleSection('principal')}
                  isHovered={isExpanded}
                  isCollapsible={false}
                  userRoleState={{ isAdmin, isInstructor, isLoading: isAdminLoading || isRoleLoading }}
                />

                <SidebarSection
                  title="ACADEMIA"
                  items={navigation.academia}
                  isExpanded={expandedSections.academia}
                  onToggle={() => toggleSection('academia')}
                  isHovered={isExpanded}
                  userRoleState={{ isAdmin, isInstructor, isLoading: isAdminLoading || isRoleLoading }}
                />
 
                <SidebarSection
                  title="FINANCEIRO"
                  items={navigation.financeiro}
                  isExpanded={expandedSections.financeiro}
                  onToggle={() => toggleSection('financeiro')}
                  adminOnly
                  isHovered={isExpanded}
                  userRoleState={{ isAdmin, isInstructor, isLoading: isAdminLoading || isRoleLoading }}
                />

                <SidebarSection
                  title="TURMAS"
                  items={navigation.treinos}
                  isExpanded={expandedSections.treinos}
                  onToggle={() => toggleSection('treinos')}
                  isHovered={isExpanded}
                  userRoleState={{ isAdmin, isInstructor, isLoading: isAdminLoading || isRoleLoading }}
                />

                <SidebarSection
                  title="PRESENÇAS"
                  items={navigation.presencas}
                  isExpanded={expandedSections.presencas}
                  onToggle={() => toggleSection('presencas')}
                  isHovered={isExpanded}
                  userRoleState={{ isAdmin, isInstructor, isLoading: isAdminLoading || isRoleLoading }}
                />

                <SidebarSection
                  title="LOJA"
                  items={navigation.loja}
                  isExpanded={expandedSections.loja}
                  onToggle={() => toggleSection('loja')}
                  adminOnly
                  isHovered={isExpanded}
                  userRoleState={{ isAdmin, isInstructor, isLoading: isAdminLoading || isRoleLoading }}
                />

                <SidebarSection
                  title="CONFIGURAÇÕES"
                  items={navigation.configuracoes}
                  isExpanded={expandedSections.configuracoes}
                  onToggle={() => toggleSection('configuracoes')}
                  isHovered={isExpanded}
                  userRoleState={{ isAdmin, isInstructor, isLoading: isAdminLoading || isRoleLoading }}
                />
              </div>
            </ul>
          </nav>
        </TenantBrandedScrollArea>
      ) : (
        <TenantBrandedScrollArea className="flex-1 py-6 px-1.5" forceColor={themeColor}>
          <nav className="flex flex-col h-full">
            <ul role="list" className="flex flex-col items-center gap-y-8">
              {/* Favoritos */}
              {favoriteIcons.map((item) => (
                <SidebarLink 
                  key={item.name} 
                  item={item} 
                  isAdmin={isAdmin} 
                  isHovered={false}
                  showFavoriteButton={false}
                />
              ))}

              {visibleSections.length > 0 && (
                <div className="relative w-8 py-2">
                  <div className="absolute inset-0 flex items-center" aria-hidden="true">
                    <div 
                      className="w-full h-[1px] bg-gradient-to-r from-transparent via-current to-transparent opacity-60" 
                      style={{ color: `${themeColor}40` }}
                    />
                  </div>
                </div>
              )}

              {/* Outros itens (não favoritos) */}
              {visibleSections.map((item) => (
                <SidebarLink
                  key={item.name}
                  item={item}
                  isAdmin={isAdmin}
                  isHovered={false}
                  showFavoriteButton={false}
                />
              ))}
            </ul>
          </nav>
        </TenantBrandedScrollArea>
      )}
      
      {/* Informação da Filial */}
      <BranchInfo isHovered={isExpanded} themeColor={themeColor} />
    </div>
  );
} 