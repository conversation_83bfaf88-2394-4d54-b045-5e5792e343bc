"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import clsx from "clsx";
import { useState } from "react";
import { NavigationItem } from "./types";
import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { useSidebarFavorites } from "@/hooks/ui/use-sidebar-favorites";
import { useRolePermissions } from "@/hooks/user/Permissions/use-role-permissions";
import { 
  TooltipProvider, 
  TooltipRoot, 
  TooltipTrigger, 
  TooltipContent, 
  TooltipPortal 
} from "@/components/ui/tooltip";
import { Ripple } from "@/components/ui/ripple";
import { StarIcon } from "@heroicons/react/24/outline";
import { StarIcon as StarIconSolid } from "@heroicons/react/24/solid";
import { ReceptionModeActivationModal } from "@/components/reception-mode/ReceptionModeActivationModal";

interface SidebarLinkProps {
  item: NavigationItem;
  isAdmin: boolean;
  isHovered?: boolean;
  showFavoriteButton?: boolean;
}

export function SidebarLink({ item, isAdmin, isHovered = true, showFavoriteButton = true }: SidebarLinkProps) {
  const pathname = usePathname();
  const isActive = pathname === item.href && !item.href.startsWith('#');
  const { primaryColor } = useTenantTheme();
  const { isFavorite, toggleFavorite } = useSidebarFavorites();
  const { userRole } = useRolePermissions();
  const [showReceptionModal, setShowReceptionModal] = useState(false);
  
  const themeColor = primaryColor || 'var(--primary)';
  const itemIsFavorite = isFavorite(item.href);

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    toggleFavorite(item);
  };

  const handleClick = (e: React.MouseEvent) => {
    if (item.href === '#reception-mode') {
      e.preventDefault();
      
      // Verificar se o usuário tem permissão para ativar o modo de recepção
      const canActivateReceptionMode = userRole === 'admin' || userRole === 'instructor';
      
      if (canActivateReceptionMode) {
        setShowReceptionModal(true);
      }
    }
  };

  if (item.adminOnly && !isAdmin) {
    return null;
  }

  // Verificação adicional para o item "Modo de Recepção"
  if (item.href === '#reception-mode') {
    const canAccessReceptionMode = userRole === 'admin' || userRole === 'instructor';
    if (!canAccessReceptionMode) {
      return null;
    }
  }

  const linkContent = (
    <Link
      href={item.href}
      onClick={handleClick}
      className={clsx(
        "group flex text-sm font-medium",
        "transition-all duration-200 ease-in-out",
        "relative overflow-hidden",
        isHovered 
          ? "px-3 py-2 gap-x-3 rounded-xl" 
          : "p-2.5 justify-center rounded-full",
        isActive
          ? isHovered 
            ? "bg-gradient-to-r from-gray-50/80 to-gray-50/50 dark:from-gray-800/40 dark:to-gray-800/20" 
            : "bg-gradient-to-br from-gray-50/90 to-white dark:from-gray-800 dark:to-gray-800/80"
          : isHovered 
            ? "hover:bg-gray-50/30 dark:hover:bg-gray-800/20" 
            : "hover:bg-gray-50/70 dark:hover:bg-gray-800/30",
        "shadow-sm",
        !isHovered && "hover:shadow hover:-translate-y-0.5",
        "border border-transparent",
        isActive && !isHovered && "border-gray-200/50 dark:border-gray-700/30 shadow"
      )}
    >
      <div className={clsx(
        "absolute inset-0 opacity-0 transition-opacity duration-300",
        isHovered ? "rounded-xl" : "rounded-full",
        "bg-gradient-to-br from-primary-500/5 to-transparent",
        "group-hover:opacity-100"
      )} />

      <Ripple 
        color={isActive ? `${themeColor}30` : "rgba(0, 0, 0, 0.1)"} 
        duration={600}
      />

      <item.icon
        className={clsx(
          "shrink-0 relative z-10",
          "transition-all duration-200 ease-in-out",
          isHovered ? "h-5 w-5" : "h-[20px] w-[20px]",
          isActive 
            ? "text-primary-600 dark:text-primary-400" 
            : "text-gray-500 dark:text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300",
          !isHovered && "group-hover:scale-105"
        )}
        style={isActive ? { color: themeColor } : {}}
        aria-hidden="true"
      />
      {isHovered && (
        <span className={clsx(
          "truncate relative z-10",
          "transition-colors duration-200 ease-in-out",
          isActive 
            ? "text-gray-900 dark:text-gray-100 font-semibold" 
            : "text-gray-600 dark:text-gray-400 group-hover:text-gray-800 dark:group-hover:text-gray-200"
        )}>
          {item.name}
        </span>
      )}
      {isHovered && showFavoriteButton && (
        <button
          onClick={handleFavoriteClick}
          className={clsx(
            "ml-auto p-1 rounded relative z-10",
            "transition-all duration-200 ease-in-out",
            "hover:bg-gray-100 dark:hover:bg-gray-800",
            "opacity-0 group-hover:opacity-100",
            itemIsFavorite && "opacity-100"
          )}
          title={itemIsFavorite ? "Remover dos favoritos" : "Adicionar aos favoritos"}
        >
          {itemIsFavorite ? (
            <StarIconSolid
              className="h-4 w-4 text-yellow-500"
              style={{ color: themeColor }}
            />
          ) : (
            <StarIcon className="h-4 w-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
          )}
        </button>
      )}
      {isHovered && item.badge && (
        <span 
          className={clsx(
            "ml-auto px-2 min-w-[1.5rem] h-5 rounded-full text-xs flex items-center justify-center relative z-10",
            "transition-all duration-200 ease-in-out",
            "font-medium tracking-wide",
            isActive
              ? "bg-primary-100/80 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300"
              : "bg-gray-100/80 dark:bg-gray-800/80 text-gray-600 dark:text-gray-400",
            "group-hover:scale-105",
            item.isNew && "ring-2 ring-primary-500/20"
          )}
          style={isActive ? { 
            backgroundColor: `${themeColor}15`,
            color: themeColor
          } : {}}
        >
          {item.badge}
        </span>
      )}
    </Link>
  );

  return (
    <li className="relative group">
      {!isHovered ? (
        <TooltipProvider delayDuration={100}>
          <TooltipRoot>
            <TooltipTrigger asChild>
              {linkContent}
            </TooltipTrigger>
            <TooltipPortal>
              <TooltipContent 
                side="right" 
                className="font-medium rounded-xl"
                style={isActive ? { backgroundColor: themeColor } : {}}
              >
                {item.name}
              </TooltipContent>
            </TooltipPortal>
          </TooltipRoot>
        </TooltipProvider>
      ) : (
        linkContent
      )}
      
      {!isHovered && item.badge && (
        <div className="absolute top-0 right-0 translate-x-[2px] translate-y-[-2px]">
          <span 
            className={clsx(
              "min-w-[18px] h-[18px] px-1",
              "rounded-full text-[10px] font-medium",
              "flex items-center justify-center",
              "bg-primary-500 text-white",
              "ring-1 ring-white dark:ring-gray-900",
              "shadow-sm",
              "transition-transform duration-200 ease-in-out",
              "scale-90 group-hover:scale-100"
            )}
            style={{ backgroundColor: themeColor }}
          >
            {item.badge}
          </span>
          {item.isNew && (
            <span 
              className={clsx(
                "absolute top-0 right-0",
                "min-w-[18px] h-[18px]",
                "rounded-full",
                "animate-ping-slow",
                "bg-primary-500/30"
              )}
              style={{ backgroundColor: `${themeColor}30` }}
            />
          )}
        </div>
      )}

      {/* Modal de Ativação do Modo de Recepção */}
      <ReceptionModeActivationModal
        isOpen={showReceptionModal}
        onClose={() => setShowReceptionModal(false)}
      />
    </li>
  );
} 