import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import clsx from "clsx";

interface SidebarSkeletonProps {
  isExpanded: boolean;
  themeColor: string;
}

export function SidebarSkeleton({ isExpanded, themeColor }: SidebarSkeletonProps) {
  return (
    <div 
      className={clsx(
        "flex flex-col h-screen border-r",
        "will-change-[width] gpu-accelerated",
        "bg-gradient-to-b from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-900/95",
        "shadow-[1px_0_0_0_rgba(0,0,0,0.05)]",
        "rounded-r-3xl overflow-hidden",
        isExpanded 
          ? "w-72 transition-all duration-300 ease-in-out" 
          : "w-16 transition-all duration-300 ease-in-out"
      )}
      style={{ 
        transform: 'translateZ(0)',
        backfaceVisibility: 'hidden',
        borderColor: `${themeColor}30`
      }}
    >
      {/* Header Skeleton */}
      <div className={clsx(
        "flex h-16 shrink-0 items-center",
        "border-b border-gray-100/50 dark:border-gray-800/40",
        "bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm",
        isExpanded ? "px-6 gap-x-3" : "justify-center"
      )}
      style={{ borderColor: `${themeColor}20` }}
      >
        {/* Logo Skeleton - Clicável para /home por padrão durante loading */}
        <Link
          href="/home"
          className={clsx(
            "transition-all duration-300 ease-in-out",
            "focus:outline-none focus:ring-2 focus:ring-offset-1",
            isExpanded ? "rounded-xl" : "rounded-full hover:scale-105"
          )}
          style={{ 
            '--tw-ring-color': `${themeColor}40`
          } as React.CSSProperties}
        >
          <Skeleton 
            className={clsx(
              "shrink-0",
              isExpanded ? "h-8 w-8 rounded-xl" : "h-10 w-10 rounded-full"
            )}
          />
        </Link>
        
        {isExpanded && (
          <>
            {/* Tenant Name Skeleton */}
            <Skeleton className="h-5 flex-1 max-w-32" />
            
            {/* Toggle Button Skeleton */}
            <Skeleton className="h-7 w-7 rounded-lg" />
          </>
        )}
      </div>

      {/* Expand Button Skeleton (when collapsed) */}
      {!isExpanded && (
        <div className="flex justify-center py-3 border-b border-gray-100/50 dark:border-gray-800/40" style={{ borderColor: `${themeColor}20` }}>
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
      )}

      {/* Navigation Skeleton */}
      <div className="flex-1 py-5 px-3">
        {isExpanded ? (
          <div className="space-y-6">
            {/* Favorites Section */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-20 mx-3" />
              <div className="space-y-1 px-2">
                {Array.from({ length: 2 }).map((_, i) => (
                  <div key={i} className="flex items-center gap-x-3 px-3 py-2">
                    <Skeleton className="h-5 w-5 rounded" />
                    <Skeleton className="h-4 flex-1" />
                  </div>
                ))}
              </div>
            </div>

            {/* Other Sections */}
            {Array.from({ length: 3 }).map((_, sectionIndex) => (
              <div key={sectionIndex} className="space-y-2">
                <div className="flex items-center justify-between px-3 py-2">
                  <Skeleton className="h-3 w-16" />
                  <Skeleton className="h-3 w-3 rounded" />
                </div>
                <div className="space-y-1 px-2">
                  {Array.from({ length: 2 }).map((_, i) => (
                    <div key={i} className="flex items-center gap-x-3 px-3 py-2">
                      <Skeleton className="h-5 w-5 rounded" />
                      <Skeleton className="h-4 flex-1" />
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center gap-y-8">
            {/* Collapsed Navigation Items */}
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} className="h-10 w-10 rounded-full" />
            ))}
            
            {/* Separator */}
            <div className="w-8 h-[1px] bg-gray-200/50 dark:bg-gray-700/50" />
            
            {/* More Items */}
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i + 6} className="h-10 w-10 rounded-full" />
            ))}
          </div>
        )}
      </div>

      {/* Branch Info Skeleton */}
      <div className={clsx(
        "flex items-center gap-x-2 py-3",
        "border-t border-t-gray-100/50 dark:border-t-gray-800/40",
        isExpanded ? "px-4" : "justify-center"
      )}
      style={{ borderColor: `${themeColor}20` }}>
        <Skeleton 
          className={clsx(
            "shrink-0 rounded-full",
            isExpanded ? "h-7 w-7" : "h-8 w-8"
          )}
        />
        
        {isExpanded && (
          <div className="flex flex-col gap-y-1 min-w-0 overflow-hidden">
            <Skeleton className="h-3 w-12" />
            <Skeleton className="h-4 w-24" />
          </div>
        )}
      </div>
    </div>
  );
} 