"use client";

import { Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { SidebarContent } from "./SidebarContent";
import { SidebarProps } from "./types";
import { navigation } from "./navigation";
import { clsx } from "clsx";

// Exportações
export { BranchInfo } from "./BranchInfo";
export { SidebarSkeleton } from "./SidebarSkeleton";
export { BranchInfoSkeleton } from "./BranchInfoSkeleton";

export function Sidebar({ 
  sidebarOpen, 
  setSidebarOpen, 
  isExpanded, 
  setIsExpanded,
  tenantName = 'Academia',
  tenantLogo = '/placeholder-logo.png'
}: SidebarProps) {

  return (
    <>
      {/* Mobile Sidebar */}
      <Transition.Root show={sidebarOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setSidebarOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-in-out duration-200"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-in-out duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/60" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transform transition ease-in-out duration-200"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transform transition ease-in-out duration-200"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative flex w-full max-w-xs flex-1 rounded-r-3xl overflow-hidden">
                <SidebarContent 
                  navigation={navigation} 
                  isExpanded={true} 
                  setIsExpanded={setIsExpanded}
                  tenantName={tenantName}
                  tenantLogo={tenantLogo}
                  isMobile={true}
                />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Desktop Sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:h-screen will-change-transform rounded-r-3xl overflow-hidden">
        <div className={clsx(
          "h-full",
          "transition-all duration-300 ease-in-out",
          !isExpanded && "shadow-sm"
        )}>
          <SidebarContent 
            navigation={navigation} 
            isExpanded={isExpanded} 
            setIsExpanded={setIsExpanded}
            tenantName={tenantName}
            tenantLogo={tenantLogo}
          />
        </div>
      </div>
    </>
  );
} 