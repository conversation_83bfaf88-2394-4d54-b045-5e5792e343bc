import {
  HomeIcon,
  UsersIcon,
  BanknotesIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  ChatBubbleLeftIcon,
  FolderIcon,
  AcademicCapIcon,
  BuildingStorefrontIcon,
  ShoppingBagIcon,
  UserGroupIcon,
  ChartBarSquareIcon,
  BookOpenIcon,
  StarIcon,
  TrophyIcon,
  CalendarIcon,
  CalendarDaysIcon,
  CheckCircleIcon,
  QrCodeIcon,
  SparklesIcon,
} from "@heroicons/react/24/solid";
import { 
  ShoppingCart, 
  Package, 
  DollarSign, 
  Clock, 
  Percent, 
  Wallet, 
  Calculator, 
  TrendingUp 
} from "lucide-react";
import type { Navigation, NavigationItem } from "./types";

// Importando o navigationConfig do arquivo de configuração usando caminho relativo
import { navigationConfig } from "@/config/navigation";

// Interface para items do navigation config
interface ConfigItem {
  title: string;
  href?: string;
  icon?: string;
  items?: ConfigItem[];
  adminOnly?: boolean;
  instructorOnly?: boolean;
  instructorAccess?: boolean;
  studentAccess?: boolean;
  badge?: number;
  isNew?: boolean;
}

// Interface para grupos no navigation config
interface ConfigGroup {
  title: string;
  icon?: string;
  items?: ConfigItem[];
}

// Função para mapear os nomes de ícones para componentes reais
const getIconComponent = (iconName: string) => {
  const iconMap: Record<string, any> = {
    home: HomeIcon,
    users: UsersIcon,
    creditCard: BanknotesIcon,
    banknotes: BanknotesIcon,
    chartBar: ChartBarIcon,
    settings: Cog6ToothIcon,
    chat: ChatBubbleLeftIcon,
    folder: FolderIcon,
    academicCap: AcademicCapIcon,
    store: BuildingStorefrontIcon,
    shoppingBag: ShoppingBagIcon,
    userGroup: UserGroupIcon,
    chartBarSquare: ChartBarSquareIcon,
    bookOpen: BookOpenIcon,
    star: StarIcon,
    trophy: TrophyIcon,
    package: Package,
    shoppingCart: ShoppingCart,
    dashboard: HomeIcon,
    calendar: CalendarIcon,
    calendarDays: CalendarDaysIcon,
    checkCircle: CheckCircleIcon,
    qrCode: QrCodeIcon,
    sparkles: SparklesIcon,
    dollarSign: DollarSign,
    clock: Clock,
    percent: Percent,
    wallet: Wallet,
    calculator: Calculator,
    trendingUp: TrendingUp,
  };

  return iconMap[iconName] || HomeIcon;
};

// Função para extrair e formatar os itens de um grupo da sidebar config
const extractItemsFromGroup = (groupName: string): NavigationItem[] => {
  const group = navigationConfig.sidebarNav.find((g: ConfigGroup) => g.title === groupName);
  
  if (!group || !group.items) {
    return [];
  }
  
  return group.items.map((item: ConfigItem) => ({
    name: item.title,
    href: item.href || "#",
    icon: getIconComponent(item.icon || "home"),
    badge: item.badge,
    isNew: item.isNew,
    adminOnly: item.adminOnly,
    instructorOnly: item.instructorOnly,
    instructorAccess: item.instructorAccess,
    studentAccess: item.studentAccess,
    // Metadados para melhor UX
    metadata: {
      description: getItemDescription(item.title),
      category: groupName,
      isMainFeature: ['Agenda', 'Turmas', 'Instrutores', 'Alunos'].includes(item.title)
    }
  }));
};

// Função para obter descrições contextuais dos itens de navegação
const getItemDescription = (itemTitle: string): string => {
  const descriptions: Record<string, string> = {
    'Agenda': 'Visualize suas aulas e compromissos',
    'Turmas': 'Gerencie grupos de aulas e matrículas',
    'Calendário': 'Visão completa da programação',
    'Aulas Livres': 'Aulas avulsas sem turma fixa',
    'Presença': 'Controle de frequência dos alunos',
    'Check-in QR': 'Scanner para registro rápido',
    'Relatórios': 'Análises e métricas de performance',
    'Instrutores': 'Gestão de professores e especialistas',
    'Alunos': 'Cadastro e acompanhamento de estudantes',
    'Pagamentos': 'Lista completa de pagamentos e transações',
    'Mensalidades': 'Gestão de mensalidades dos alunos',
    'Recorrentes e Planos': 'Pagamentos automáticos e assinaturas',
    'Formas de Pagamento': 'Métodos de pagamento aceitos',
    'Crescimento': 'Análise de crescimento e métricas',
    'Configurações': 'Configurações do sistema',
  };
  
  // Descrições específicas por contexto para "Visão Geral"
  if (itemTitle === 'Visão Geral') {
    return 'Resumo e visão geral';
  }
  
  return descriptions[itemTitle] || '';
};

// Convertendo a configuração para o formato usado pelo sidebar
export const navigation: Navigation = {
  favorites: [], // Os favoritos agora são dinâmicos e gerenciados pelo FavoritesContext
  academia: extractItemsFromGroup("Academia"),
  treinos: extractItemsFromGroup("Aulas & Turmas"),
  presencas: extractItemsFromGroup("Presenças"),
  financeiro: extractItemsFromGroup("Financeiro"),
  configuracoes: extractItemsFromGroup("Configurações"),
  loja: extractItemsFromGroup("Loja"),
};

// Itens específicos da loja para clientes
export const shopNavigation: NavigationItem[] = extractItemsFromGroup("Loja Cliente"); 