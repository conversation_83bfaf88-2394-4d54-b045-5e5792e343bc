import { ForwardRefExoticComponent, RefAttributes, SVGProps } from "react";
import type { ComponentType } from 'react'

export type HeroIcon = ForwardRefExoticComponent<Omit<SVGProps<SVGSVGElement>, "ref"> & {
    title?: string;
    titleId?: string;
} & RefAttributes<SVGSVGElement>>;

export interface NavigationItem {
  name: string;
  href: string;
  icon: HeroIcon | React.ElementType;
  badge?: number;
  title?: string;
  adminOnly?: boolean;
  isNew?: boolean;
  instructorOnly?: boolean;
  instructorAccess?: boolean;
  studentAccess?: boolean;
  metadata?: {
    description: string;
    category: string;
    isMainFeature: boolean;
  };
}

export interface Navigation {
  favorites: NavigationItem[];
  academia: NavigationItem[];
  treinos: NavigationItem[];
  presencas: NavigationItem[];
  financeiro: NavigationItem[];
  configuracoes: NavigationItem[];
  loja: NavigationItem[];
}

export interface SidebarProps {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
  tenantName?: string;
  tenantLogo?: string;
}

export type NavigationSection = 'favorites' | 'academia' | 'treinos' | 'presencas' | 'financeiro' | 'configuracoes' | 'loja'

// Definir um tipo para permissões
export type UserRole = 'admin' | 'instructor' | 'student' | null;

export interface UserPermissions {
  isAdmin: boolean;
  isInstructor: boolean;
  isStudent: boolean;
  role: UserRole;
} 