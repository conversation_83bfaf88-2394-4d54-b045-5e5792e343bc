/**
 * Editor de templates de notificação
 * Componente para criação e edição de templates com preview em tempo real
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  useTemplatePreview,
  useTemplateValidationDebounced
} from '@/hooks/notifications/use-template-preview';
import { useTemplateVariables } from '@/hooks/notifications/use-template-variables';
import { EmailPreview } from './email-preview';
import { VariableInserter } from './variable-inserter';
import type { 
  NotificationTemplate, 
  NotificationType, 
  NotificationChannel,
  CreateTemplateData,
  UpdateTemplateData 
} from '@/services/notifications/types/notification-types';

interface TemplateEditorProps {
  template?: NotificationTemplate;
  onSave: (data: CreateTemplateData | UpdateTemplateData) => Promise<void>;
  onCancel: () => void;
  isEditing?: boolean;
}

export function TemplateEditor({ 
  template, 
  onSave, 
  onCancel, 
  isEditing = false 
}: TemplateEditorProps) {
  // Estado do formulário
  const [formData, setFormData] = useState({
    name: template?.name || '',
    type: template?.type || 'payment' as NotificationType,
    channel: template?.channel || 'in_app' as NotificationChannel,
    subject_template: template?.subject_template || '',
    body_template: template?.body_template || ''
  });

  const [saving, setSaving] = useState(false);

  // Hooks para funcionalidades
  const { variables, exampleVariables, loadVariables } = useTemplateVariables();
  const { renderTemplate } = useTemplatePreview();
  const validation = useTemplateValidationDebounced(
    formData.body_template, 
    formData.type, 
    500
  );

  // Carregar variáveis quando tipo mudar
  useEffect(() => {
    loadVariables(formData.type);
  }, [formData.type, loadVariables]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      if (isEditing) {
        const updateData: UpdateTemplateData = {
          name: formData.name,
          subject_template: formData.subject_template,
          body_template: formData.body_template
        };
        await onSave(updateData);
      } else {
        const createData: CreateTemplateData = {
          name: formData.name,
          type: formData.type,
          channel: formData.channel,
          subject_template: formData.subject_template,
          body_template: formData.body_template
        };
        await onSave(createData);
      }
    } finally {
      setSaving(false);
    }
  };

  const insertVariable = (variableKey: string) => {
    const textarea = document.getElementById('body-template') as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = textarea.value;
      const before = text.substring(0, start);
      const after = text.substring(end);
      const newText = before + `{{${variableKey}}}` + after;

      handleInputChange('body_template', newText);

      // Reposicionar cursor
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(
          start + variableKey.length + 4,
          start + variableKey.length + 4
        );
      }, 0);
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Editor */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>
              {isEditing ? 'Editar Template' : 'Novo Template'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Nome */}
            <div>
              <Label htmlFor="name">Nome do Template</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Ex: Lembrete de Pagamento"
              />
            </div>

            {/* Tipo e Canal (apenas para criação) */}
            {!isEditing && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="type">Tipo</Label>
                  <Select 
                    value={formData.type} 
                    onValueChange={(value) => handleInputChange('type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="payment">Pagamento</SelectItem>
                      <SelectItem value="class">Aula</SelectItem>
                      <SelectItem value="enrollment">Matrícula</SelectItem>
                      <SelectItem value="event">Evento</SelectItem>
                      <SelectItem value="system">Sistema</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="channel">Canal</Label>
                  <Select 
                    value={formData.channel} 
                    onValueChange={(value) => handleInputChange('channel', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {/* <SelectItem value="in_app">In-App</SelectItem> */}
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="whatsapp">WhatsApp</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {/* Assunto (apenas para email) */}
            {formData.channel === 'email' && (
              <div>
                <Label htmlFor="subject">Assunto (Email)</Label>
                <Input
                  id="subject"
                  value={formData.subject_template}
                  onChange={(e) => handleInputChange('subject_template', e.target.value)}
                  placeholder="Ex: {{academyName}} - Lembrete de Pagamento"
                />
              </div>
            )}

            {/* Corpo do template */}
            <div>
              <Label htmlFor="body-template">Conteúdo do Template</Label>
              <Textarea
                id="body-template"
                value={formData.body_template}
                onChange={(e) => handleInputChange('body_template', e.target.value)}
                placeholder="Digite o conteúdo do template aqui..."
                rows={12}
                className="font-mono text-sm"
              />
            </div>

            {/* Validação */}
            {validation && (
              <div className="space-y-2">
                {validation.errors.length > 0 && (
                  <Alert variant="destructive">
                    <AlertDescription>
                      <strong>Erros:</strong>
                      <ul className="list-disc list-inside mt-1">
                        {validation.errors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}

                {/* {validation.warnings.length > 0 && (
                  <Alert>
                    <AlertDescription>
                      <strong>Avisos:</strong>
                      <ul className="list-disc list-inside mt-1">
                        {validation.warnings.map((warning, index) => (
                          <li key={index}>{warning}</li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )} */}
              </div>
            )}

            {/* Ações */}
            <div className="flex gap-2 pt-4">
              <Button 
                onClick={handleSave} 
                disabled={saving || !validation?.isValid}
                className="flex-1"
              >
                {saving ? 'Salvando...' : 'Salvar Template'}
              </Button>
              <Button variant="outline" onClick={onCancel}>
                Cancelar
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Preview e Variáveis */}
      <div className="space-y-6">
        <Tabs defaultValue="preview" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="preview">Preview</TabsTrigger>
            <TabsTrigger value="variables">Variáveis</TabsTrigger>
          </TabsList>

          <TabsContent value="preview">
            {formData.channel === 'email' ? (
              <EmailPreview
                subject={formData.subject_template}
                body={formData.body_template}
                variables={exampleVariables}
              />
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-muted/50 dark:bg-muted/20 p-4 rounded-lg border border-border">
                    <div
                      className="whitespace-pre-wrap text-foreground"
                      dangerouslySetInnerHTML={{ __html: renderTemplate(formData.body_template, exampleVariables) }}
                    />
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="variables">
            <VariableInserter
              variables={variables}
              exampleVariables={exampleVariables}
              onInsertVariable={insertVariable}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
