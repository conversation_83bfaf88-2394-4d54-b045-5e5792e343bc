'use client';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Check, X, Loader2 } from 'lucide-react';
import { AddressFormProps } from './types';

export function AddressForm({
  className,
  addressFields,
  error,
  isSaving,
  onCancel,
  onConfirm,
  onFieldChange,
  handlePostalCodeChange
}: AddressFormProps) {
  return (
    <div className={`px-6 py-4 flex items-start ${className || ''}`}>
      {/* <div className="flex-shrink-0 mt-0.5 mr-3">
        <MapPin className="h-4 w-4" />
      </div> */}
      
      <div className="flex-grow">
        <p className="text-sm font-medium text-slate-500 dark:text-slate-400 mb-2">Endereço</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div className="md:col-span-2">
            <label htmlFor="street" className="block text-xs mb-1">Nome da Rua/Avenida/Localidade</label>
            <Input
              id="street"
              value={addressFields.street}
              onChange={(e) => onFieldChange('street', e.target.value)}
              className={error ? "border-rose-500" : ""}
              placeholder="Ex: Folha 32, Avenida Tancredo Neves"
            />
          </div>
          
          <div>
            <label htmlFor="street_number" className="block text-xs mb-1">
              Núm. da Casa/Apto.
              {/* <span className="text-slate-400 ml-1 text-[10px]">(apenas números)</span> */}
            </label>
            <Input
              id="street_number"
              value={addressFields.street_number}
              onChange={(e) => onFieldChange('street_number', e.target.value)}
              placeholder="Ex: 273, 100A"
            />
          </div>
          
          <div>
            <label htmlFor="complement" className="block text-xs mb-1">Complemento</label>
            <Input
              id="complement"
              value={addressFields.complement}
              onChange={(e) => onFieldChange('complement', e.target.value)}
              placeholder="Ex: Apto 101, Bloco B, Quadra 8"
            />
          </div>
          
          <div className="md:col-span-2">
            <label htmlFor="neighborhood" className="block text-xs mb-1">
              Bairro
            </label>
            <Input
              id="neighborhood"
              value={addressFields.neighborhood}
              onChange={(e) => onFieldChange('neighborhood', e.target.value)}
              placeholder="Ex: Nova Marabá, Centro, Cidade Nova"
              className={addressFields.neighborhood === addressFields.street_number ? "border-rose-500" : ""}
            />
          </div>
          
          <div>
            <label htmlFor="city" className="block text-xs mb-1">Cidade</label>
            <Input
              id="city"
              value={addressFields.city}
              onChange={(e) => onFieldChange('city', e.target.value)}
              placeholder="Ex: Marabá"
            />
          </div>
          
          <div>
            <label htmlFor="state" className="block text-xs mb-1">Estado (UF)</label>
            <Input
              id="state"
              value={addressFields.state}
              onChange={(e) => onFieldChange('state', e.target.value)}
              maxLength={2}
              placeholder="Ex: PA"
              className="uppercase"
            />
          </div>
          
          <div className="md:col-span-2">
            <label htmlFor="postal_code" className="block text-xs mb-1">CEP</label>
            <Input
              id="postal_code"
              value={addressFields.postal_code}
              onChange={(e) => handlePostalCodeChange(e.target.value)}
              maxLength={9}
              placeholder="XXXXX-XXX"
              className="max-w-[200px]"
            />
          </div>
        </div>
        
        {error && (
          <p className="text-sm text-rose-500 mt-2">{error}</p>
        )}
        
        <div className="flex gap-2 mt-4 justify-end">
          <Button 
            size="sm" 
            variant="outline" 
            className="text-red-600 dark:text-red-500 border-red-600 dark:border-red-500 hover:bg-red-50 dark:hover:bg-red-900/30"
            onClick={onCancel}
            disabled={isSaving}
          >
            <X className="h-4 w-4 mr-1" /> Cancelar
          </Button>
          <Button 
            size="sm" 
            className="bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white dark:text-slate-900"
            onClick={onConfirm}
            disabled={isSaving}
          >
            {isSaving ? (
              <Loader2 className="h-4 w-4 animate-spin mr-1" />
            ) : (
              <Check className="h-4 w-4 mr-1" />
            )}
            Salvar
          </Button>
        </div>
      </div>
    </div>
  );
} 