import { AddressComponent } from './AddressComponent';
import { AddressDisplay } from './AddressDisplay';
import { AddressForm } from './AddressForm';
import { AddressFields, EMPTY_ADDRESS } from './types';

export { AddressComponent, AddressDisplay, AddressForm };

export type { AddressFields };
export { EMPTY_ADDRESS };

export const EditableAddress = {
  Root: AddressComponent,
  Display: AddressDisplay,
  Form: AddressForm
};

export default AddressComponent; 