export interface AddressFields {
  street: string;
  street_number: string;
  complement: string;
  neighborhood: string;
  city: string;
  state: string;
  postal_code: string;
}

export const EMPTY_ADDRESS: AddressFields = {
  street: '',
  street_number: '',
  complement: '',
  neighborhood: '',
  city: '',
  state: '',
  postal_code: ''
};

export interface AddressComponentProps {
  address: string;
  userId?: string;
  className?: string;
  useDirectFields?: boolean;
  onAddressUpdated?: () => void;
}

export interface AddressDisplayProps extends AddressComponentProps {
  onEdit: () => void;
  wasRecentlyEdited: boolean;
  formatAddressForDisplay: () => string[];
}

export interface AddressFormProps extends AddressComponentProps {
  addressFields: AddressFields;
  error?: string;
  isSaving: boolean;
  onCancel: () => void;
  onConfirm: () => Promise<void>;
  onFieldChange: (field: keyof AddressFields, value: string) => void;
  handlePostalCodeChange: (value: string) => void;
}

export interface UseAddressProps extends AddressComponentProps {
  getCurrentValue: (field: string) => any;
  setFieldValue: (field: string, value: any) => Promise<boolean>;
} 