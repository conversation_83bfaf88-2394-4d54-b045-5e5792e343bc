import { AddressFields } from './types';

/**
 * Valida o CEP (formato XXXXX-XXX ou XXXXXXXX)
 */
export const validatePostalCode = (postalCode: string): boolean => {
  if (!postalCode) return true;
  
  const cepDigits = postalCode.replace(/\D/g, '');
  
  return cepDigits.length === 8;
};

/**
 * Formata o CEP para exibição (XXXXX-XXX)
 */
export const formatPostalCode = (postalCode: string): string => {
  if (!postalCode) return '';
  
  const cepDigits = postalCode.replace(/\D/g, '');
  
  if (cepDigits.length === 8) {
    return `${cepDigits.slice(0, 5)}-${cepDigits.slice(5)}`;
  }
  
  return postalCode;
};

/**
 * Valida todos os campos de endereço
 */
export const validateAddressFields = (addressFields: AddressFields): string | undefined => {
  if (addressFields.postal_code && !validatePostalCode(addressFields.postal_code)) {
    return 'CEP inválido. O formato deve ser XXXXX-XXX ou XXXXXXXX.';
  }
  
  if (addressFields.state && addressFields.state.length !== 2) {
    return 'Estado deve ser informado como UF (dois caracteres).';
  }
  
  if (addressFields.neighborhood && /^\d+$/.test(addressFields.neighborhood)) {
    return 'O bairro não pode ser apenas um número. Verifique se está preenchendo o campo corretamente.';
  }
  
  if (addressFields.neighborhood && 
      addressFields.street_number && 
      addressFields.neighborhood === addressFields.street_number) {
    return 'O bairro não pode ser igual ao número. Verifique se está preenchendo os campos corretamente.';
  }
  
  return undefined;
};

/**
 * Converte uma string de endereço em campos individuais
 */
export const parseAddressString = (addressString: string): AddressFields => {
  if (!addressString) {
    return {
      street: '',
      street_number: '',
      complement: '',
      neighborhood: '',
      city: '',
      state: '',
      postal_code: ''
    };
  }

  try {
    const parsedAddress: AddressFields = { 
      street: '',
      street_number: '',
      complement: '',
      neighborhood: '',
      city: '',
      state: '',
      postal_code: ''
    };
    
    const parts = addressString.split(/[\n,]+/).map(part => part.trim());
    
    if (parts.length > 0) {
      const streetMatch = parts[0].match(/^(.*?)\s*(\d+)?$/);
      if (streetMatch) {
        parsedAddress.street = streetMatch[1].trim();
        
        if (streetMatch[2]) {
          parsedAddress.street += ` ${streetMatch[2]}`;
        }
      } else {
        parsedAddress.street = parts[0].trim();
      }
      
      if (parts.length > 1 && /^\d+[a-zA-Z]?$/.test(parts[1].trim())) {
        parsedAddress.street_number = parts[1].trim();
      }
    }
    
    const complementIndex = parts.findIndex(part => 
      part.toLowerCase().includes('apto') || 
      part.toLowerCase().includes('bloco') || 
      part.toLowerCase().includes('quadra') ||
      part.toLowerCase().includes('casa')
    );
    
    if (complementIndex > 0) {
      parsedAddress.complement = parts[complementIndex].trim();
    }
    
    const neighborhoodIndex = parts.findIndex(part => 
      !part.includes('CEP') && 
      !parts[0].includes(part) && 
      (complementIndex === -1 || part !== parts[complementIndex]) &&
      !part.includes('-')
    );
    
    if (neighborhoodIndex > 0) {
      parsedAddress.neighborhood = parts[neighborhoodIndex].trim();
    }
    
    // Processar cidade e estado (geralmente conteúdo que contém hífen)
    const cityStatePart = parts.find(part => part.includes('-'));
    if (cityStatePart) {
      const cityState = cityStatePart.split('-').map(part => part.trim());
      parsedAddress.city = cityState[0];
      
      if (cityState.length > 1) {
        parsedAddress.state = cityState[1];
      }
    }
    
    // Processar CEP (normalmente começa com "CEP:")
    const cepPart = parts.find(part => part.toLowerCase().includes('cep'));
    if (cepPart) {
      const cep = cepPart.replace(/cep:?/i, '').trim();
      parsedAddress.postal_code = cep;
    }
    
    return parsedAddress;
  } catch (error) {
    console.error('Erro ao parsear endereço:', error);
    return {
      street: '',
      street_number: '',
      complement: '',
      neighborhood: '',
      city: '',
      state: '',
      postal_code: ''
    };
  }
};

/**
 * Formata os campos de endereço em uma string formatada
 */
export const formatAddressFromFields = (addressFields: AddressFields): string => {
  const parts = [];
  
  if (addressFields.street) {
    const streetPart = addressFields.street + (addressFields.street_number ? `, ${addressFields.street_number}` : '');
    parts.push(streetPart);
  }
  
  if (addressFields.complement) {
    parts.push(addressFields.complement);
  }
  
  if (addressFields.neighborhood) {
    parts.push(addressFields.neighborhood);
  }
  
  if (addressFields.city) {
    const cityStatePart = addressFields.city + (addressFields.state ? ` - ${addressFields.state}` : '');
    parts.push(cityStatePart);
  }
  
  if (addressFields.postal_code) {
    parts.push(`CEP: ${addressFields.postal_code}`);
  }
  
  return parts.join('\n');
}; 