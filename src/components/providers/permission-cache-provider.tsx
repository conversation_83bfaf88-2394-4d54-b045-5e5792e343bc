'use client';

import { ReactNode, useEffect } from 'react';
import { getPermissionClientService } from '@/services/permissions/client-service';
import { useQueryClient } from '@tanstack/react-query';
import { CACHE_KEYS } from '@/constants/cache-keys';

interface PermissionCacheProviderProps {
  children: ReactNode;
}

export function PermissionCacheProvider({ children }: PermissionCacheProviderProps) {
  const queryClient = useQueryClient();
  
  useEffect(() => {
    // Configurar o serviço de permissões com o QueryClient
    const permissionService = getPermissionClientService();
    permissionService.setQueryClient(queryClient);
    
    console.log('[PermissionCacheProvider] Serviço de permissões configurado com QueryClient');
    
    // Configurar evento de logout para limpar o cache de permissões
    const handleLogout = () => {
      permissionService.invalidatePermissionCache();
      queryClient.invalidateQueries({ queryKey: [...CACHE_KEYS.USER_METADATA] });
    };
    
    // Adicionar evento para ouvir logout
    window.addEventListener('app:logout', handleLogout);
    
    // Verificar mudanças de sessão em abas diferentes usando storage events
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'supabase.auth.token' && !e.newValue) {
        // Token removido, provavelmente um logout em outra aba
        handleLogout();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('app:logout', handleLogout);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [queryClient]);
  
  return <>{children}</>;
} 