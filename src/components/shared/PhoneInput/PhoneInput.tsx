'use client';

import { useState, useEffect, useMemo } from 'react';
import { Input } from "@/components/ui/input";
import { CountrySelect } from "@/components/shared/PhoneInput/country-select";
import usePhoneFormat from "@/hooks/form/usePhoneFormat";
import { ControllerRenderProps, FieldError } from 'react-hook-form';
import { exceedsMaxDigits } from './phone-config';
import { parsePhoneNumber } from '@/utils/phone-utils';

export interface PhoneInputProps {
  field: ControllerRenderProps<any, any>;
  error?: FieldError;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  defaultCountryCode?: string;
  /**
   * Define se o campo é obrigatório. Quando true, o atributo HTML `required` é
   * propagado para o elemento de <Input>, permitindo que validações nativas do
   * navegador sejam utilizadas e também servindo como indicativo semântico de
   * obrigatoriedade.
   */
  required?: boolean;
}

export function PhoneInput({
  field,
  error,
  className = "",
  placeholder = "(00) 00000-0000",
  disabled = false,
  defaultCountryCode = "+55",
  required = false
}: PhoneInputProps) {
  const { formatNationalNumber } = usePhoneFormat();

  const { countryCode, nationalNumber } = useMemo(
    () => parsePhoneNumber(field.value, defaultCountryCode),
    [field.value, defaultCountryCode]
  );
  
  const [phoneWithoutCountryCode, setPhoneWithoutCountryCode] = useState(nationalNumber);

  useEffect(() => {
    // Quando o valor do formulário muda externamente (ex: carregamento inicial),
    // atualiza o estado interno do número local.
    const { nationalNumber: newNationalNumber } = parsePhoneNumber(field.value, defaultCountryCode);
    const formatted = formatNationalNumber(newNationalNumber, countryCode);
    setPhoneWithoutCountryCode(formatted);
  }, [field.value, defaultCountryCode, countryCode, formatNationalNumber]);
  
  const isExceedingLimit = useMemo(
    () => exceedsMaxDigits(countryCode, phoneWithoutCountryCode),
    [countryCode, phoneWithoutCountryCode]
  );

  const handleCountryChange = (newCountryCode: string) => {
    const newValue = `${newCountryCode}${phoneWithoutCountryCode.replace(/\D/g, '')}`;
    field.onChange(newValue);
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value;
    
    if (exceedsMaxDigits(countryCode, input) && input.length > phoneWithoutCountryCode.length) {
      return; // Previne digitação além do limite
    }
    
    const formatted = formatNationalNumber(input, countryCode);
    setPhoneWithoutCountryCode(formatted);
    
    const digitsOnly = formatted.replace(/\D/g, '');
    const newRawValue = digitsOnly ? `${countryCode}${digitsOnly}` : '';
    field.onChange(newRawValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  };
  
  return (
    <div className="relative w-full">
      <div className="absolute inset-y-0 left-0 flex items-center pl-3">
        <CountrySelect 
          value={countryCode}
          onValueChange={handleCountryChange}
          disabled={disabled}
          showFlag={true}
          triggerClassName="bg-transparent border-0 shadow-none hover:bg-transparent p-0"
        />
      </div>
      <Input 
        type="tel" 
        placeholder={placeholder}
        value={phoneWithoutCountryCode}
        onChange={handlePhoneChange}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        required={required}
        className={`pl-24 w-full ${isExceedingLimit || error ? 'border-red-500' : ''} ${className}`}
      />
      {isExceedingLimit && !error && (
        <div className="text-xs text-red-500 mt-1">
          Número de dígitos excede o limite para este país
        </div>
      )}
    </div>
  );
} 

/**
 * Simple PhoneInput wrapper for use without react-hook-form
 * Accepts value and onChange props directly
 */
export interface SimplePhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  defaultCountryCode?: string;
  required?: boolean;
}

export function SimplePhoneInput({
  value,
  onChange,
  className = "",
  placeholder = "(00) 00000-0000",
  disabled = false,
  defaultCountryCode = "+55",
  required = false
}: SimplePhoneInputProps) {
  // Create a mock field object that mimics react-hook-form's ControllerRenderProps
  const mockField = {
    value,
    onChange,
    onBlur: () => {},
    name: 'phone',
    ref: () => {}
  };

  return (
    <PhoneInput
      field={mockField}
      className={className}
      placeholder={placeholder}
      disabled={disabled}
      defaultCountryCode={defaultCountryCode}
      required={required}
    />
  );
} 