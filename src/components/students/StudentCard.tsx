'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { StudentBeltRenderer } from '@/components/belt';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  MoreHorizontal, 
  Mail, 
  Phone,
  UserMinus,
  PauseCircle,
  PlayCircle,
  Eye
} from 'lucide-react';
import Link from 'next/link';
import { formatDate } from '@/utils/format';
import usePhoneFormat from '@/hooks/form/usePhoneFormat';

interface StudentCardProps {
  enrollment: any; // TODO: Define proper type
  getInitials: (name: string) => string;
  classGroupId?: string;
  onUnenrollStudent?: (enrollmentId: string, studentName: string) => void;
  onPauseEnrollment?: (enrollmentId: string, studentName: string) => void;
  onReactivateEnrollment?: (enrollmentId: string, studentName: string) => void;
}

export function StudentCard({ 
  enrollment, 
  getInitials,
  classGroupId,
  onUnenrollStudent,
  onPauseEnrollment,
  onReactivateEnrollment
}: StudentCardProps) {
  const { formatPhoneNumber } = usePhoneFormat();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'active': { 
        label: 'Ativo', 
        className: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800' 
      },
      'inactive': { 
        label: 'Inativo', 
        className: 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-800' 
      },
      'suspended': { 
        label: 'Suspenso', 
        className: 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800' 
      },
      'completed': { 
        label: 'Concluído', 
        className: 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800' 
      },
      'paused': { 
        label: 'Pausado', 
        className: 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-800' 
      }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      className: 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-800' 
    };
    
    return (
      <Badge 
        variant="outline" 
        className={`text-xs font-medium ${config.className}`}
      >
        {config.label}
      </Badge>
    );
  };

  const studentName = (enrollment as any).user?.full_name || (enrollment as any).students?.users?.full_name || 'Nome não disponível';
  const studentEmail = (enrollment as any).user?.email || (enrollment as any).students?.users?.email;
  const studentPhone = (enrollment as any).user?.phone || (enrollment as any).students?.users?.phone;
  const studentAvatar = (enrollment as any).user?.avatar_url || (enrollment as any).students?.users?.avatar_url;
  const userId = (enrollment as any).user?.id || (enrollment as any).students?.users?.id;
  
  // Dados da faixa - priorizar current_belt com detalhes completos
  const currentBelt = (enrollment as any).current_belt;
  const fallbackBelt = (enrollment as any).belt || (enrollment as any).students?.current_belt?.belt_color;
  
  // Verificar se a matrícula está pausada
  const isPaused = (enrollment as any).is_paused || false;
  const pauseInfo = (enrollment as any).pause_info;
  
  // Determinar o status correto da matrícula considerando pausas
  const getEnrollmentStatus = () => {
    if (isPaused) {
      return 'paused';
    }
    return enrollment.status;
  };
  
  const enrollmentStatus = getEnrollmentStatus();

  return (
    <article 
      className="group relative overflow-hidden rounded-xl border border-border/20 bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 hover:border-border/40 focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
      role="article"
      aria-label={`Informações do aluno ${studentName}`}
    >
      <div className="p-4 sm:p-6">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          {/* Student Info */}
          <div className="flex items-center space-x-3 sm:space-x-4 flex-1 min-w-0">
            <Avatar className="h-10 w-10 sm:h-12 sm:w-12 ring-2 ring-border/10 shrink-0">
              <AvatarImage 
                src={studentAvatar || ''} 
                alt={`Avatar de ${studentName}`} 
              />
              <AvatarFallback className="bg-muted text-muted-foreground font-semibold text-sm sm:text-base">
                {getInitials(studentName)}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0 space-y-1 sm:space-y-2">
              <div className="flex items-start gap-2 flex-wrap">
                <h3 className="font-semibold text-foreground text-sm sm:text-base leading-tight">
                  <Link 
                    href={`/perfil/${userId}`}
                    className="hover:underline focus:underline focus:outline-none"
                    aria-label={`Ver perfil completo de ${studentName}`}
                  >
                    {studentName}
                  </Link>
                </h3>
                <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
                  {getStatusBadge(enrollmentStatus)}
                  <StudentBeltRenderer
                    currentBelt={currentBelt}
                    fallbackBelt={fallbackBelt}
                    size="sm"
                  />
                </div>
              </div>
              
              <div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:gap-4 text-xs sm:text-sm text-muted-foreground">
                {studentEmail && (
                  <div className="flex items-center gap-1 min-w-0">
                    <Mail className="h-3 w-3 sm:h-4 sm:w-4 shrink-0" aria-hidden="true" />
                    <span className="truncate" title={studentEmail}>
                      {studentEmail}
                    </span>
                  </div>
                )}
                {studentPhone && (
                  <div className="flex items-center gap-1">
                    <Phone className="h-3 w-3 sm:h-4 sm:w-4 shrink-0" aria-hidden="true" />
                    <span title={studentPhone}>{formatPhoneNumber(studentPhone)}</span>
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <time dateTime={enrollment.enrollment_date}>
                  Matriculado em {formatDate(enrollment.enrollment_date)}
                </time>
              </div>
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex items-center justify-end gap-2 sm:ml-4">
            <Button 
              variant="outline" 
              size="sm" 
              asChild
              className="hidden md:flex text-xs sm:text-sm"
            >
              <Link 
                href={`/perfil/${userId}`}
                aria-label={`Ver perfil completo de ${studentName}`}
              >
                <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" aria-hidden="true" />
                Ver Perfil
              </Link>
            </Button>
            
            <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-8 w-8 p-0"
                  aria-label={`Abrir menu de ações para ${studentName}`}
                >
                  <MoreHorizontal className="h-4 w-4" aria-hidden="true" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Ações do Aluno</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link 
                    href={`/perfil/${userId}`} 
                    className="flex md:hidden"
                    aria-label={`Ver perfil completo de ${studentName}`}
                  >
                    <Eye className="h-4 w-4 mr-2" aria-hidden="true" />
                    Ver perfil
                  </Link>
                </DropdownMenuItem>
                {enrollmentStatus === 'active' && (
                  <>
                    {onPauseEnrollment && (
                      <DropdownMenuItem
                        onClick={() => {
                          setIsDropdownOpen(false);
                          // Usar setTimeout para garantir que o dropdown feche antes de abrir o modal
                          setTimeout(() => onPauseEnrollment(enrollment.id, studentName), 100);
                        }}
                      >
                        <PauseCircle className="h-4 w-4 mr-2" aria-hidden="true" />
                        Pausar matrícula
                      </DropdownMenuItem>
                    )}
                    {onUnenrollStudent && (
                      <DropdownMenuItem 
                        className="text-destructive focus:text-destructive focus:bg-destructive/10"
                        onClick={() => {
                          setIsDropdownOpen(false);
                          // Usar setTimeout para garantir que o dropdown feche antes de abrir o modal
                          setTimeout(() => onUnenrollStudent(enrollment.id, studentName), 100);
                        }}
                      >
                        <UserMinus className="h-4 w-4 mr-2" aria-hidden="true" />
                        Cancelar matrícula
                      </DropdownMenuItem>
                    )}
                  </>
                )}
                {enrollmentStatus === 'paused' && onReactivateEnrollment && (
                  <DropdownMenuItem
                    onClick={() => {
                      setIsDropdownOpen(false);
                      // Usar setTimeout para garantir que o dropdown feche antes de abrir o modal
                      setTimeout(() => onReactivateEnrollment(enrollment.id, studentName), 100);
                    }}
                  >
                    <PlayCircle className="h-4 w-4 mr-2" aria-hidden="true" />
                    Reativar matrícula
                  </DropdownMenuItem>
                )}
                {(enrollmentStatus === 'inactive' || enrollmentStatus === 'suspended') && onReactivateEnrollment && (
                  <DropdownMenuItem
                    onClick={() => {
                      setIsDropdownOpen(false);
                      // Usar setTimeout para garantir que o dropdown feche antes de abrir o modal
                      setTimeout(() => onReactivateEnrollment(enrollment.id, studentName), 100);
                    }}
                  >
                    <PlayCircle className="h-4 w-4 mr-2" aria-hidden="true" />
                    Reativar matrícula
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        
        {isPaused && pauseInfo && (
          <div className="mt-3 sm:mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg dark:bg-amber-950/20 dark:border-amber-800">
            <div className="flex items-center gap-2 mb-2">
              <PauseCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
              <span className="text-xs font-medium text-amber-800 dark:text-amber-200">
                Matrícula Pausada
              </span>
            </div>
            <p className="text-xs text-amber-700 dark:text-amber-300">
              Pausada em {formatDate(pauseInfo.paused_at)}
              {pauseInfo.reason && (
                <span className="block mt-1">
                  <span className="font-medium">Motivo:</span> {pauseInfo.reason}
                </span>
              )}
            </p>
          </div>
        )}
        
        {enrollment.notes && (
          <div className="mt-3 sm:mt-4 p-3 bg-muted/50 rounded-lg">
            <p className="text-xs sm:text-sm text-muted-foreground">
              <span className="font-medium">Observações:</span> {enrollment.notes}
            </p>
          </div>
        )}
      </div>
    </article>
  );
} 