'use client';

import { useEffect, useState, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, X, RefreshCw } from 'lucide-react';
import type { EnrollmentFilters } from '@/hooks/alunos/use-students-for-enrollment';

interface StudentEnrollmentFiltersProps {
  filters: EnrollmentFilters;
  onFiltersChange: (filters: Partial<EnrollmentFilters>) => void;
  onClearFilters: () => void;
  stats?: {
    total: number;
    available: number;
    alreadyEnrolled: number;
  };
  isLoading?: boolean;
  className?: string;
}

export function StudentEnrollmentFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  stats,
  isLoading = false,
  className = ''
}: StudentEnrollmentFiltersProps) {
  // Estado local para o input de busca
  const [searchInput, setSearchInput] = useState(filters.search || '');
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Sincronizar o input local com o filtro externo
  useEffect(() => {
    setSearchInput(filters.search || '');
  }, [filters.search]);

  // Cleanup do timeout quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Mapeamento das cores das faixas para exibição
  const beltColorLabels: Record<string, string> = {
    white: 'Branca',
    blue: 'Azul',
    purple: 'Roxa',
    brown: 'Marrom',
    black: 'Preta'
  };

  const hasActiveFilters = Boolean(
    filters.search || 
    filters.belt_color || 
    (filters.status && filters.status !== 'active') ||
    filters.branch_id
  );

  const handleSearchChange = (value: string) => {
    setSearchInput(value);
    
    // Limpar timeout anterior se existir
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Implementar debounce de 500ms
    searchTimeoutRef.current = setTimeout(() => {
      onFiltersChange({ search: value });
    }, 500);
  };

  const handleBeltColorChange = (value: string) => {
    onFiltersChange({ belt_color: value === 'all' ? undefined : value });
  };

  const handleStatusChange = (value: string) => {
    onFiltersChange({ status: value === 'all' ? undefined : value as 'active' | 'inactive' });
  };

  const handleBranchChange = (value: string) => {
    onFiltersChange({ branch_id: value === 'all' ? undefined : value });
  };

  const handleClearSearch = () => {
    setSearchInput('');
    
    // Limpar timeout se existir
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }
    
    onFiltersChange({ search: '' });
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Estatísticas e ações principais */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          {stats && (
            <>
              <span>
                {stats.total} estudante{stats.total !== 1 ? 's' : ''} encontrado{stats.total !== 1 ? 's' : ''}
              </span>
              <span>•</span>
              <span className="text-green-600 dark:text-green-300">
                {stats.available} disponível{stats.available !== 1 ? 'is' : ''}
              </span>
              {stats.alreadyEnrolled > 0 && (
                <>
                  <span>•</span>
                  <span className="text-amber-600 dark:text-amber-300">
                    {stats.alreadyEnrolled} já matriculado{stats.alreadyEnrolled !== 1 ? 's' : ''}
                  </span>
                </>
              )}
            </>
          )}
        </div>
        
        {hasActiveFilters && (
          <Button
            onClick={onClearFilters}
            variant="ghost"
            size="sm"
            className="h-8 px-2 text-muted-foreground hover:text-foreground"
            disabled={isLoading}
          >
            <X className="h-4 w-4 mr-1" />
            Limpar filtros
          </Button>
        )}
      </div>

      {/* Filtros */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Busca */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar por nome ou email..."
            value={searchInput}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-9"
            disabled={isLoading}
          />
          {searchInput && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-muted/80 transition-colors"
              onClick={handleClearSearch}
              disabled={isLoading}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Status do estudante */}
        <Select
          value={filters.status || 'active'}
          onValueChange={handleStatusChange}
          disabled={isLoading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="active">Apenas ativos</SelectItem>
            <SelectItem value="inactive">Apenas inativos</SelectItem>
            <SelectItem value="all">Todos os status</SelectItem>
          </SelectContent>
        </Select>

        {/* Cor da faixa */}
        <Select
          value={filters.belt_color || 'all'}
          onValueChange={handleBeltColorChange}
          disabled={isLoading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Faixa" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todas as faixas</SelectItem>
            <SelectItem value="white">Branca</SelectItem>
            <SelectItem value="blue">Azul</SelectItem>
            <SelectItem value="purple">Roxa</SelectItem>
            <SelectItem value="brown">Marrom</SelectItem>
            <SelectItem value="black">Preta</SelectItem>
          </SelectContent>
        </Select>

        {/* Unidade/Filial - Pode ser implementado depois se necessário */}
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">
            {isLoading ? 'Carregando...' : 'Filtros ativos'}
          </span>
        </div>
      </div>

      {/* Tags de filtros ativos */}
      {hasActiveFilters && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-muted-foreground">Filtros ativos:</span>
          
          {filters.search && (
            <Badge variant="secondary" className="gap-1">
              Busca: "{filters.search}"
              <button
                onClick={handleClearSearch}
                className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                disabled={isLoading}
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filters.belt_color && (
            <Badge variant="secondary" className="gap-1">
              Faixa: {beltColorLabels[filters.belt_color] || filters.belt_color}
              <button
                onClick={() => handleBeltColorChange('all')}
                className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                disabled={isLoading}
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filters.status && filters.status !== 'active' && (
            <Badge variant="secondary" className="gap-1">
              Status: {filters.status === 'inactive' ? 'Inativos' : 'Todos'}
              <button
                onClick={() => handleStatusChange('active')}
                className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                disabled={isLoading}
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
} 