'use client';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { StudentCard } from './StudentCard';
import { StudentEmptyState } from './StudentEmptyState';
import { Student, StudentCardActions } from './types';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import * as Tooltip from '@radix-ui/react-tooltip';
import { StudentBeltRenderer } from '@/components/belt';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import usePhoneFormat from '@/hooks/form/usePhoneFormat';

interface StudentListProps extends StudentCardActions {
  students: Student[];
  isLoading?: boolean;
  classGroupId?: string;
  onClearFilters?: () => void;
  onUnenrollStudent?: (enrollmentId: string, studentName: string) => void;
  onPauseEnrollment?: (enrollmentId: string, studentName: string) => void;
  onReactivateEnrollment?: (enrollmentId: string, studentName: string) => void;
  selectedEnrollments: string[];
  onSelectionChange: (ids: string[]) => void;
  onRowSelection?: (id: string, event: React.MouseEvent<HTMLTableRowElement>) => void;
}

const statusConfig: Record<Student['status'], { label: string; className: string }> = {
  active: { label: 'Ativo', className: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/50 dark:text-green-300 dark:border-green-800' },
  inactive: { label: 'Inativo', className: 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800/50 dark:text-gray-300 dark:border-gray-700' },
  suspended: { label: 'Suspenso', className: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/50 dark:text-yellow-300 dark:border-yellow-800' },
  completed: { label: 'Concluído', className: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/50 dark:text-blue-300 dark:border-blue-800' },
};

export function StudentList({ 
  students, 
  isLoading = false,
  classGroupId,
  onClearFilters,
  onStatusToggle,
  onEdit,
  onRemove,
  onContact,
  onUnenrollStudent,
  onPauseEnrollment,
  onReactivateEnrollment,
  selectedEnrollments,
  onSelectionChange,
  onRowSelection,
}: StudentListProps) {
  const { formatPhoneNumber } = usePhoneFormat();

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const allEnrollmentIds = students.map(s => s.id);
  const isAllSelected = selectedEnrollments.length > 0 && selectedEnrollments.length === allEnrollmentIds.length;
  const isSomeSelected = selectedEnrollments.length > 0 && !isAllSelected;

  const handleSelectAll = (checked: boolean | 'indeterminate') => {
    if (checked === true) {
      onSelectionChange(allEnrollmentIds);
    } else {
      onSelectionChange([]);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {/* Loading skeleton */}
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-card border rounded-xl p-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-muted rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-1/3"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </div>
                <div className="space-x-2">
                  <div className="h-8 w-16 bg-muted rounded"></div>
                  <div className="h-8 w-8 bg-muted rounded"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (students.length === 0) {
    return (
      <StudentEmptyState 
        type="no-results"
        classGroupId={classGroupId}
        onClearFilters={onClearFilters}
      />
    );
  }

  return (
    <>
      {/* Mobile view */}
      <div className="lg:hidden space-y-4">
        {students.map((student) => (
          <StudentCard
            key={student.id}
            enrollment={student}
            getInitials={getInitials}
            classGroupId={classGroupId}
            onUnenrollStudent={onUnenrollStudent}
            onPauseEnrollment={onPauseEnrollment}
            onReactivateEnrollment={onReactivateEnrollment}
          />
        ))}
      </div>

      {/* Desktop view */}
      <div className="hidden lg:block">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={isAllSelected || (isSomeSelected ? 'indeterminate' : false)}
                  onCheckedChange={handleSelectAll}
                  aria-label="Selecionar todos os alunos"
                />
              </TableHead>
              <TableHead>Aluno</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Telefone</TableHead>
              <TableHead>Faixa</TableHead>
              <TableHead>Data da Matrícula</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {students.map((student) => (
              <TableRow 
                key={student.id}
                onClick={(e) => onRowSelection?.(student.id, e)}
                onKeyDown={(e) => {
                  if (e.key === ' ' || e.key === 'Enter') {
                    e.preventDefault();
                    onRowSelection?.(student.id, e as any);
                  }
                }}
                className={cn(
                  "cursor-pointer",
                  selectedEnrollments.includes(student.id) && "bg-muted/50 hover:bg-muted"
                )}
                tabIndex={0}
                aria-selected={selectedEnrollments.includes(student.id)}
              >
                <TableCell>
                  <Checkbox
                    checked={selectedEnrollments.includes(student.id)}
                    aria-label={`Selecionar ${student.user.full_name}`}
                  />
                </TableCell>
                <TableCell className="py-2.5">
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage src={student.user.avatar_url} alt={student.user.full_name} />
                      <AvatarFallback>{getInitials(student.user.full_name)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <Link
                        href={`/perfil/${student.user.id}`}
                        className="font-medium hover:underline focus:underline focus:outline-none"
                        aria-label={`Ver perfil completo de ${student.user.full_name}`}
                      >
                        {student.user.full_name}
                      </Link>
                      <div className="text-sm text-muted-foreground">{student.user.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  {student.is_paused && student.pause_info ? (
                    <Tooltip.Provider>
                      <Tooltip.Root>
                        <Tooltip.Trigger asChild>
                          <Badge variant="outline" className="border-amber-500 text-amber-500">
                            Pausado
                          </Badge>
                        </Tooltip.Trigger>
                        <Tooltip.Portal>
                          <Tooltip.Content 
                            className="bg-popover text-popover-foreground rounded-md px-3 py-1.5 text-sm shadow-md"
                            sideOffset={4}
                          >
                            Pausado em: {new Date(student.pause_info.paused_at).toLocaleDateString()}
                            <Tooltip.Arrow className="fill-popover" />
                          </Tooltip.Content>
                        </Tooltip.Portal>
                      </Tooltip.Root>
                    </Tooltip.Provider>
                  ) : (
                    <Badge variant="outline" className={cn('capitalize', statusConfig[student.status].className)}>
                      {statusConfig[student.status].label}
                    </Badge>
                  )}
                </TableCell>
                <TableCell>
                  {student.user.phone ? formatPhoneNumber(student.user.phone) : 'N/A'}
                </TableCell>
                <TableCell>
                  <StudentBeltRenderer
                    currentBelt={student.current_belt}
                    fallbackBelt={student.belt}
                    size="sm"
                  />
                </TableCell>
                <TableCell>
                  {new Date(student.enrollment_date).toLocaleDateString()}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onContact?.(student); }}>
                        Contatar
                      </DropdownMenuItem>
                      {student.is_paused ? (
                        <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onReactivateEnrollment?.(student.id, student.user.full_name); }}>
                          Reativar Matrícula
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onPauseEnrollment?.(student.id, student.user.full_name); }}>
                          Pausar Matrícula
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem 
                        className="text-destructive"
                        onClick={(e) => { e.stopPropagation(); onUnenrollStudent?.(student.id, student.user.full_name); }}
                      >
                        Cancelar Matrícula
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Results counter */}
      <footer className="flex justify-center pt-3 sm:pt-4 border-t border-border/50">
        <div 
          className="text-xs sm:text-sm text-muted-foreground bg-muted/30 px-3 py-1 rounded-full"
          role="status"
          aria-live="polite"
        >
          {students.length === 1 
            ? '1 aluno encontrado' 
            : `${students.length} alunos encontrados`
          }
        </div>
      </footer>
    </>
  );
} 