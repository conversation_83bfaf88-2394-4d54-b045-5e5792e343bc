// Shared types for student components

export interface BeltDetails {
  belt_color: string;
  degree: number;
  label?: string | null;
  stripe_color?: string | null;
  show_center_line?: boolean | null;
  center_line_color?: string | null;
}

export interface Student {
  id: string;
  user_id: string;
  user: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
    avatar_url?: string;
  };
  enrollment_date: string;
  status: 'active' | 'inactive' | 'suspended' | 'completed';
  belt?: string;
  current_belt?: BeltDetails;
  notes?: string;
  class_group_id: string;
  is_paused?: boolean;
  pause_info?: {
    id: string;
    paused_at: string;
    reason?: string;
    notes?: string;
  } | null;
}

export interface ClassGroup {
  id: string;
  name: string;
  description?: string;
  max_capacity?: number;
  location?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface StudentStats {
  total: number;
  active: number;
  inactive: number;
  suspended: number;
  completed?: number;
  paused?: number;
  active_including_paused?: number;
}

export type StatusFilter = 'all' | 'active' | 'inactive' | 'suspended' | 'completed' | 'paused';
export type SortField = 'name' | 'enrollment_date' | 'belt' | 'status';
export type SortOrder = 'asc' | 'desc';

export interface StudentFilters {
  status?: StatusFilter;
  search?: string;
  sort?: SortField;
  order?: SortOrder;
  page?: number;
  limit?: number;
}

export interface StudentCardActions {
  onStatusToggle?: (studentId: string, currentStatus: string) => Promise<void>;
  onEdit?: (student: Student) => void;
  onRemove?: (studentId: string) => void;
  onContact?: (student: Student) => void;
  onViewProfile?: (student: Student) => void;
  onViewHistory?: (student: Student) => void;
} 