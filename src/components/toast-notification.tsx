'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import { ToastAlert, ToastAlertProps } from '@/components/ui/toast-alert';
import { v4 as uuidv4 } from 'uuid';

// Tipo de toast com ID para rastreamento
type Toast = ToastAlertProps & { id: string };

// Contexto para gerenciar os toasts
type ToastContextType = {
  showToast: (toast: Omit<ToastAlertProps, 'onClose'>) => void;
  hideToast: (id: string) => void;
};

const ToastContext = createContext<ToastContextType | undefined>(undefined);

// Hook para usar o sistema de toasts
export function useToast() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast deve ser usado dentro de um ToastProvider');
  }
  return context;
}

// Provider para o sistema de toasts
export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([]);

  // Mostrar um novo toast
  const showToast = useCallback((toast: Omit<ToastAlertProps, 'onClose'>) => {
    const id = uuidv4();
    setToasts(prev => [...prev, { ...toast, id }]);
    
    // Se o toast tiver duração, configurar para removê-lo automaticamente
    if (toast.duration !== 0) {
      const duration = toast.duration || 5000;
      setTimeout(() => {
        hideToast(id);
      }, duration + 300); // Duração + tempo da animação
    }
  }, []);

  // Ocultar um toast específico
  const hideToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  return (
    <ToastContext.Provider value={{ showToast, hideToast }}>
      {children}
      
      {/* Renderizar todos os toasts ativos */}
      {toasts.map((toast) => (
        <ToastAlert
          key={toast.id}
          message={toast.message}
          variant={toast.variant}
          position={toast.position}
          duration={toast.duration}
          icon={toast.icon}
          onClose={() => hideToast(toast.id)}
        />
      ))}
    </ToastContext.Provider>
  );
}

// Componente para mostrar toasts de erro
export function ErrorToast({ 
  message, 
  duration,
  position = 'bottom-right'
}: { 
  message: string;
  duration?: number;
  position?: ToastAlertProps['position'];
}) {
  const { showToast } = useToast();
  
  React.useEffect(() => {
    showToast({
      message,
      variant: 'error',
      duration,
      position
    });
  }, [message, duration, position, showToast]);
  
  return null;
}

// Componente para mostrar alertas baseados em parâmetros de URL
export function AccessDeniedToast({ 
  errorType, 
  duration = 5000,
  position = 'bottom-right'
}: { 
  errorType: string;
  duration?: number;
  position?: ToastAlertProps['position'];
}) {
  const { showToast } = useToast();
  
  React.useEffect(() => {
    // Mapear tipos de erro para mensagens
    let message = "Você não tem permissão para acessar esta área.";
    
    if (errorType === 'acesso-negado-alunos') {
      message = "Apenas administradores podem acessar a área de gerenciamento de alunos.";
    } else if (errorType === 'acesso-negado') {
      message = "Você não tem permissão para acessar a área solicitada.";
    }
    
    showToast({
      message,
      variant: 'error',
      duration,
      position
    });
  }, [errorType, duration, position, showToast]);
  
  return null;
} 