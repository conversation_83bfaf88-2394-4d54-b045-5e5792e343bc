"use client"

import * as React from "react"
import { type DateRange } from "react-day-picker"
import { format } from "date-fns"
import { ptBR } from "date-fns/locale"
import { CalendarIcon } from "lucide-react"

import { Calendar } from "@/components/ui/calendar"
import { But<PERSON> } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"

interface DateRangePickerProps {
  dateRange?: DateRange
  onDateRangeChange?: (dateRange: DateRange | undefined) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  minDate?: Date
}

export default function DateRangePicker({ 
  dateRange,
  onDateRangeChange,
  placeholder = "Selecione as datas",
  className,
  disabled = false,
  minDate
}: DateRangePickerProps) {
  const [internalDateRange, setInternalDateRange] = React.useState<DateRange | undefined>(dateRange)

  const handleDateSelect = (newDateRange: DateRange | undefined) => {
    setInternalDateRange(newDateRange)
    onDateRangeChange?.(newDateRange)
  }

  const formatDateRange = (range: DateRange | undefined) => {
    if (!range?.from) {
      return placeholder
    }

    if (range.from && !range.to) {
      return format(range.from, "dd MMM yyyy", { locale: ptBR })
    }

    if (range.from && range.to) {
      return `${format(range.from, "dd MMM yyyy", { locale: ptBR })} - ${format(range.to, "dd MMM yyyy", { locale: ptBR })}`
    }

    return placeholder
  }

  const currentRange = dateRange || internalDateRange

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !currentRange && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {formatDateRange(currentRange)}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="range"
          defaultMonth={currentRange?.from}
          selected={currentRange}
          onSelect={handleDateSelect}
          numberOfMonths={2}
          disabled={(date) => minDate ? date < minDate : false}
          className="rounded-lg"
        />
      </PopoverContent>
    </Popover>
  )
}
