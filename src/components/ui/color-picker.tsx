'use client'

import * as React from 'react'
import { Check, ChevronsUpDown, X as LucideX } from 'lucide-react'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Input } from './input'

interface ColorPickerProps {
  value: string
  onChange: (value: string) => void
  /**
   * When true the picker is rendered as a compact circular swatch without the hex value text.
   * Useful for cases where we only need a small color preview (e.g. in table rows).
   */
  compact?: boolean
  /**
   * Permite remover/limpar a cor atual através de um botão X no popover.
   * Quando clicado envia uma string vazia para onChange.
   */
  allowClear?: boolean
  className?: string
}

export function ColorPicker({ value, onChange, compact = false, allowClear = false, className }: ColorPickerProps) {
  const [isOpen, setIsOpen] = React.useState(false)

  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = e.target.value
    React.startTransition(() => {
      onChange(newColor)
    })
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={isOpen}
          className={cn(
            compact ? 'w-8 h-8 p-0 justify-center' : 'w-full justify-between',
            className,
          )}
        >
          <div className={cn('flex items-center gap-2', compact && 'gap-0')}>            
            <div
              className={cn('relative rounded-full border', compact ? 'w-4 h-4' : 'w-4 h-4')}
              style={{ backgroundColor: value || 'transparent' }}
            >
              {(!value || value.trim() === '') && (
                <span className="absolute inset-0 flex items-center justify-center text-[14px] text-muted-foreground select-none">
                  <LucideX className="h-3.5 w-3.5 text-muted-foreground" />
                </span>
              )}
            </div>
            {!compact && value}
          </div>
          {!compact && (
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="relative w-auto p-0">
        {allowClear && (
          <Button
            type="button"
            size="icon"
            variant="ghost"
            className="absolute -top-3 -right-3 h-6 w-6 p-0 rounded-full border bg-background/90 shadow-sm opacity-0 transition-opacity hover:opacity-100 focus:opacity-100 group-hover:opacity-100"
            onClick={() => {
              // Clearing the color can be an expensive update depending on the parent form size.
              // Schedule it as a transition to keep the UI responsive.
              React.startTransition(() => {
                onChange('')
              })
              setIsOpen(false)
            }}
          >
            <span className="sr-only">Limpar cor</span>
            <LucideX className="h-3.5 w-3.5 text-muted-foreground" />
          </Button>
        )}
        <div className="p-2 space-y-2">
          <Input
            type="color"
            value={value}
            onChange={handleColorChange}
            className="w-full h-32 p-0 border-0 cursor-pointer"
          />
          <Input
            type="text"
            value={value}
            onChange={e => {
              const newColor = e.target.value
              React.startTransition(() => {
                onChange(newColor)
              })
            }}
            className="w-full text-xs"
          />
        </div>
      </PopoverContent>
    </Popover>
  )
} 