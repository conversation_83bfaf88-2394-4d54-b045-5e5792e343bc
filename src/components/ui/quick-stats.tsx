'use client';

import { motion } from 'framer-motion';
import { QuickStat } from '../../types/shared';
import { cn } from '@/lib/utils';

interface QuickStatsProps {
  stats: QuickStat[];
  className?: string;
  compact?: boolean;
  animated?: boolean;
}

interface QuickStatItemProps {
  stat: QuickStat;
  compact?: boolean;
  index?: number;
  animated?: boolean;
}

function QuickStatItem({ stat, compact = false, index = 0, animated = true }: QuickStatItemProps) {
  const content = (
    <div className={cn(
      "flex items-center gap-2 transition-all duration-200",
      stat.disabled && "opacity-60",
      stat.href && "hover:opacity-80 cursor-pointer",
      compact ? "gap-1" : "gap-2"
    )}>
      <div className={cn(stat.color, "flex-shrink-0")}>
        {stat.icon}
      </div>
      <div className={cn("text-sm", compact && "text-xs")}>
        <span className="font-medium text-slate-900 dark:text-gray-100">
          {stat.value}
        </span>
        <span className={cn(
          "ml-1",
          stat.disabled 
            ? "text-gray-500 dark:text-gray-500" 
            : "text-slate-600 dark:text-gray-400",
          compact && "text-xs"
        )}>
          {stat.label}
        </span>
      </div>
      
      {/* Trend indicator */}
      {stat.trend && (
        <div className={cn(
          "flex items-center gap-1 text-xs font-medium",
          stat.trend.isPositive 
            ? "text-green-600 dark:text-green-400" 
            : "text-red-600 dark:text-red-400"
        )}>
          <span>{stat.trend.isPositive ? '↗' : '↘'}</span>
          <span>{Math.abs(stat.trend.value)}%</span>
        </div>
      )}
    </div>
  );

  if (!animated) {
    return content;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.3, 
        delay: index * 0.1,
        ease: 'easeOut'
      }}
      whileHover={stat.href || stat.onClick ? { scale: 1.02 } : undefined}
      onClick={stat.onClick}
    >
      {content}
    </motion.div>
  );
}

export function QuickStats({ 
  stats, 
  className, 
  compact = false, 
  animated = true 
}: QuickStatsProps) {
  if (stats.length === 0) {
    return null;
  }

  const containerClasses = cn(
    "flex items-center",
    compact ? "gap-4" : "gap-6",
    className
  );

  const content = (
    <div className={containerClasses}>
      {stats.map((stat, index) => (
        <QuickStatItem
          key={stat.label}
          stat={stat}
          compact={compact}
          index={index}
          animated={animated}
        />
      ))}
    </div>
  );

  if (!animated) {
    return content;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={containerClasses}
    >
      {stats.map((stat, index) => (
        <QuickStatItem
          key={stat.label}
          stat={stat}
          compact={compact}
          index={index}
          animated={animated}
        />
      ))}
    </motion.div>
  );
}

// Export individual components for flexibility
export { QuickStatItem }; 