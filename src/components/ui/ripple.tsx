"use client"

import { useCallback, useEffect, useState, useRef } from "react"
import { cn } from "@/lib/utils"

interface RippleProps {
  color?: string;
  duration?: number;
}

interface RippleEffect {
  x: number;
  y: number;
  size: number;
  id: string;
}

export function Ripple({ 
  color = "rgba(255, 255, 255, 0.35)", 
  duration = 600 
}: RippleProps) {
  const [ripples, setRipples] = useState<RippleEffect[]>([])
  const [isClient, setIsClient] = useState(false)
  const idCounter = useRef(0)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (!isClient) return

    const timeouts = ripples.map((_, i) => {
      return setTimeout(() => {
        setRipples(prev => prev.filter((_, idx) => idx !== 0))
      }, duration)
    })

    return () => {
      timeouts.forEach(clearTimeout)
    }
  }, [ripples.length, duration, isClient])

  const onClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (!isClient) return

    const trigger = event.currentTarget
    const rect = trigger.getBoundingClientRect()
    const size = Math.max(trigger.clientWidth, trigger.clientHeight)
    
    // Calculando o centro do elemento
    const centerX = trigger.clientWidth / 2
    const centerY = trigger.clientHeight / 2
    
    // Calculando a posição do clique relativa ao elemento
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    
    // Calculando a distância do clique ao centro
    const dx = x - centerX
    const dy = y - centerY
    const distance = Math.sqrt(dx * dx + dy * dy)
    
    // Ajustando o tamanho do ripple baseado na distância
    const maxDistance = Math.sqrt(centerX * centerX + centerY * centerY)
    const scale = (distance / maxDistance) * 0.5 + 1
    const finalSize = size * scale

    // Use a stable counter instead of Date.now() + Math.random()
    idCounter.current += 1
    const id = `ripple-${idCounter.current}`

    setRipples(prev => [...prev, { x, y, size: finalSize, id }])
  }, [isClient])

  // Don't render anything on server to avoid hydration mismatch
  if (!isClient) {
    return (
      <div className="absolute inset-0 overflow-hidden pointer-events-none" />
    )
  }

  return (
    <div 
      className="absolute inset-0 overflow-hidden pointer-events-none"
      onClick={onClick}
    >
      {ripples.map(({ x, y, size, id }) => (
        <span
          key={id}
          className={cn(
            "absolute rounded-full -translate-x-1/2 -translate-y-1/2",
            "animate-ripple pointer-events-none"
          )}
          style={{
            left: x,
            top: y,
            width: size,
            height: size,
            backgroundColor: color,
          }}
        />
      ))}
    </div>
  )
} 