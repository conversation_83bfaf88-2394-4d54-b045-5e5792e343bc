'use client'

import { useTenantTheme } from '@/src/hooks/use-tenant-theme'
import { ButtonHTMLAttributes, forwardRef, useState } from 'react'
import { cn } from '@/src/lib/utils'

interface TenantBrandedButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline'
  size?: 'sm' | 'md' | 'lg'
}

const TenantBrandedButton = forwardRef<HTMLButtonElement, TenantBrandedButtonProps>(
  ({ className, variant = 'primary', size = 'md', children, ...props }, ref) => {
    const { primaryColor, secondaryColor } = useTenantTheme()
    const [isHovered, setIsHovered] = useState(false)
    
    const baseStyles = "inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
    
    const sizeStyles = {
      sm: "h-8 px-3 text-xs",
      md: "h-10 px-4 py-2",
      lg: "h-12 px-6 py-3 text-lg"
    }
    
    const getStyles = () => {
      switch (variant) {
        case 'primary':
          return {
            backgroundColor: isHovered && primaryColor 
              ? adjustBrightness(primaryColor, -10) 
              : primaryColor || '#000000',
            color: '#ffffff'
          }
        case 'secondary':
          return {
            backgroundColor: isHovered && secondaryColor 
              ? adjustBrightness(secondaryColor, -10) 
              : secondaryColor || '#6b7280',
            color: '#ffffff'
          }
        case 'outline':
          return {
            backgroundColor: isHovered ? (primaryColor || '#000000') : 'transparent',
            color: isHovered ? '#ffffff' : (primaryColor || '#000000'),
            borderColor: primaryColor || '#000000',
            borderWidth: '1px'
          }
        default:
          return {
            backgroundColor: primaryColor || '#000000',
            color: '#ffffff'
          }
      }
    }
    
    return (
      <button
        className={cn(baseStyles, sizeStyles[size], className)}
        style={getStyles()}
        ref={ref}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        {...props}
      >
        {children}
      </button>
    )
  }
)

function adjustBrightness(color: string, percent: number): string {
  const num = parseInt(color.replace('#', ''), 16);
  const amt = Math.round(2.55 * percent);
  const R = Math.max(Math.min((num >> 16) + amt, 255), 0);
  const G = Math.max(Math.min(((num >> 8) & 0x00FF) + amt, 255), 0);
  const B = Math.max(Math.min((num & 0x0000FF) + amt, 255), 0);
  
  return '#' + (1 << 24 | R << 16 | G << 8 | B).toString(16).slice(1);
}

TenantBrandedButton.displayName = 'TenantBrandedButton'

export { TenantBrandedButton } 