'use client'

import { HTMLAttributes, forwardRef } from 'react'
import { cn } from '@/src/lib/utils'
import { useTenantTheme } from '@/src/hooks/use-tenant-theme'
import { TenantBrandedButton } from './tenant-branded-button'

interface TenantBrandedCardProps extends HTMLAttributes<HTMLDivElement> {
  variant?: 'primary' | 'secondary' | 'outline'
  title: string
  description?: string
  showButton?: boolean
  buttonText?: string
  onButtonClick?: () => void
}

const TenantBrandedCard = forwardRef<HTMLDivElement, TenantBrandedCardProps>(
  ({ className, variant = 'primary', title, description, showButton = true, buttonText = 'Ação', onButtonClick, ...props }, ref) => {
    const { primaryColor, secondaryColor } = useTenantTheme()
    
    const baseStyles = "rounded-lg p-6 shadow-md"
    
    const getStyles = () => {
      switch (variant) {
        case 'primary':
          return {
            backgroundColor: primaryColor || '#000000',
            color: '#ffffff',
            borderLeft: `4px solid ${secondaryColor || '#6b7280'}`
          }
        case 'secondary':
          return {
            backgroundColor: secondaryColor || '#6b7280',
            color: '#ffffff',
            borderLeft: `4px solid ${primaryColor || '#000000'}`
          }
        case 'outline':
          return {
            backgroundColor: '#ffffff',
            color: '#333333',
            border: `1px solid ${primaryColor || '#000000'}`,
            borderLeft: `4px solid ${primaryColor || '#000000'}`
          }
        default:
          return {
            backgroundColor: primaryColor || '#000000',
            color: '#ffffff'
          }
      }
    }
    
    const getTitleColor = () => {
      if (variant === 'outline') {
        return primaryColor || '#000000'
      }
      return '#ffffff'
    }
    
    const getDescriptionColor = () => {
      if (variant === 'outline') {
        return '#555555'
      }
      return 'rgba(255, 255, 255, 0.9)'
    }
    
    const getButtonVariant = () => {
      if (variant === 'outline') {
        return 'primary'
      }
      if (variant === 'secondary') {
        return 'primary'
      }
      return 'secondary'
    }
    
    return (
      <div
        className={cn(baseStyles, className)}
        style={getStyles()}
        ref={ref}
        {...props}
      >
        <div className="flex flex-col gap-4">
          <h3 className="text-xl font-semibold" style={{ color: getTitleColor() }}>{title}</h3>
          
          {description && (
            <p style={{ color: getDescriptionColor() }}>{description}</p>
          )}
          
          {showButton && (
            <div className="mt-2">
              <TenantBrandedButton variant={getButtonVariant()} onClick={onButtonClick}>
                {buttonText}
              </TenantBrandedButton>
            </div>
          )}
        </div>
      </div>
    )
  }
)

TenantBrandedCard.displayName = 'TenantBrandedCard'

export { TenantBrandedCard } 