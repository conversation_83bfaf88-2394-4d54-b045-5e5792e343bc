import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

interface UserAvatarProps {
  src?: string | null;
  name: string;
  alt?: string;
  className?: string;
  fallbackClassName?: string;
  size?: "sm" | "md" | "lg" | "xl";
}

const sizeClasses = {
  sm: "h-8 w-8",
  md: "h-9 w-9", 
  lg: "h-10 w-10",
  xl: "h-14 w-14"
};

const textSizeClasses = {
  sm: "text-xs",
  md: "text-sm",
  lg: "text-sm", 
  xl: "text-lg"
};

export function UserAvatar({ 
  src, 
  name, 
  alt, 
  className, 
  fallbackClassName,
  size = "md" 
}: UserAvatarProps) {
  const getInitials = (fullName: string) => {
    return fullName
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const displayInitials = getInitials(name);
  const displayAlt = alt || name;

  return (
    <Avatar className={cn(sizeClasses[size], className)}>
      {src && (
        <AvatarImage 
          src={src} 
          alt={displayAlt}
          className="object-cover"
        />
      )}
      <AvatarFallback 
        className={cn(
          "bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold",
          textSizeClasses[size],
          fallbackClassName
        )}
      >
        {displayInitials}
      </AvatarFallback>
    </Avatar>
  );
} 