interface TenantConfig {
  baseDomain: string;
  isProduction: boolean;
  getFullDomain: (subdomain?: string) => string;
  cookieOptions: {
    primaryColorName: string;
    secondaryColorName: string;
    domainName: string;
    maxAge: number;
  };
  defaultColors: {
    primary: string;
    secondary: string;
  };
}

export const tenantConfig: TenantConfig = {
  baseDomain: process.env.NEXT_PUBLIC_BASE_DOMAIN || 'localhost:3000',
  isProduction: process.env.NODE_ENV === 'production',
  
  getFullDomain(subdomain?: string): string {
    if (!subdomain) return this.baseDomain;
    
    if (this.baseDomain === 'localhost:3000') {
      return `${subdomain}.${this.baseDomain}`;
    }
    
    return `${subdomain}.${this.baseDomain}`;
  },
  
  cookieOptions: {
    primaryColorName: 'tenant_primary_color',
    secondaryColorName: 'tenant_secondary_color',
    domainName: 'tenant_domain',
    maxAge: 60 * 60 * 24 * 30, // 30 dias
  },
  
  defaultColors: {
    primary: '#000000',
    secondary: '#6b7280',
  },
}; 