/**
 * Arquivo de chaves de cache centralizadas
 * 
 * Este arquivo centraliza todas as chaves de consulta usadas pelo React Query na aplicação,
 * garantindo consistência no cache e evitando duplicação de definições.
 */

export const CACHE_KEYS = {
  // Autenticação e Perfil
  USER_METADATA: ['user-metadata'],
  USER_PROFILE: ['user-profile'],
  VIEWED_USER_PROFILE: (userId: string) => ['viewed-user-profile', userId],
  USER_ADMIN_STATUS: ['user-admin-status'],
  USER_SESSION: ['user-session'],
  // CURRENT_USER: ['current-user'],
  
  // Responsáveis e Menores de Idade
  GUARDIAN: {
    INFO: (userId: string) => ['guardian-info', userId],
    STATUS: (userId: string) => ['guardian-status', userId],
    DISPLAY_INFO: (userId: string) => ['guardian-display-info', userId],
    MINOR_STATUS: (userId: string) => ['minor-status', userId],
    MINORS_WITHOUT_GUARDIAN: ['minors-without-guardian'],
    MINORS_WITH_GUARDIAN: ['minors-with-guardian']
  },
  
  // Sincronização de Metadados
  USER_METADATA_SYNC: {
    CONSISTENCY: (userId: string) => ['user-metadata-consistency', userId],
    SYNC_STATUS: (userId: string) => ['user-metadata-sync-status', userId],
    LAST_SYNC: (userId: string) => ['user-metadata-last-sync', userId]
  },
  
  // Permissões
  PERMISSION_CONTEXT: (userId?: string, targetId?: string) => 
    ['permission-context', userId, targetId],
  BASIC_PERMISSION_CONTEXT: (userId?: string) => 
    ['basic-permission-context', userId],
  PERMISSION_CHECK: (resource: string, action: string, userId?: string, targetId?: string) =>
    ['permission-check', resource, action, userId, targetId ?? 'none'],
  
  // Frequência (Attendance)
  ATTENDANCE: {
    STATS: (userId: string) => ['attendance-stats', userId],
    HISTORY: (userId: string) => ['attendance-history', userId]
  },
  
  // Aulas e Presença
  CLASSES: {
    LIST: (filters: Record<string, any>) => ['classes-list', filters],
    ATTENDANCE: (classId: string) => ['class-attendance', classId],
    STATS: ['classes-stats'],
    QR_CODE: (classId: string) => ['class-qr-code', classId],
  },
  
  // Presença - Página de Presença
  PRESENCE: {
    CLASSES_WITH_ATTENDANCE: (filters: Record<string, any>) => 
      ['presence-classes-with-attendance', filters],
    ATTENDANCE_STATS: ['presence-attendance-stats'],
    ATTENDANCE_BY_CLASS: (classId: string, filters: Record<string, any>) => 
      ['presence-attendance-by-class', classId, filters],
    ATTENDANCE_BY_STUDENT: (studentId: string, filters: Record<string, any>) => 
      ['presence-attendance-by-student', studentId, filters],
  },
  
  // Faixas (Belts)
  BELTS: {
    STATS: (userId: string) => ['belts-stats', userId],
    HISTORY: (userId: string) => ['belts-history', userId],
    REQUIREMENTS: (userId: string) => ['belts-requirements', userId],
    DETAILS: (userId: string, beltId: string) => ['belt-details', userId, beltId]
  },
  
  // Pagamentos
  PAYMENTS: {
    INFO: (userId: string) => ['payments-info', userId],
    HISTORY: (userId: string) => ['payments-history', userId],
    UPCOMING: (userId: string) => ['payments-upcoming', userId]
  },

  // Modalidades
  MODALITIES_LIST: (tenantId: string | null) => ['modalities-list', tenantId ?? 'none'],
  
  // Tenant
  TENANT: {
    THEME: (slug: string) => ['tenant-theme', slug],
    DATA: (slug: string) => ['tenant-data', slug],
    ID: (slug: string) => ['tenant-id', slug]
  },

  // Turmas (Class Groups)
  CLASS_GROUPS: {
    LIST: (filters: Record<string, any>) => ['class-groups-list', filters],
    DETAIL: (groupId: string) => ['class-group-detail', groupId],
    STATS: (groupId: string) => ['class-group-stats', groupId],
    CAPACITY: (groupId: string) => ['class-group-capacity', groupId],
    // Aulas da turma
    CLASSES: (groupId: string, filters: Record<string, any>) => 
      ['class-group-classes', groupId, filters],
    CLASS_STATS: (groupId: string) => 
      ['class-group-class-stats', groupId],
    INSTRUCTORS: (groupId: string) => 
      ['class-group-instructors', groupId],
    INFO: (groupId: string) => 
      ['class-group-info', groupId],
  },

  // Matrículas de Estudantes
  ENROLLMENT: {
    AVAILABLE_STUDENTS: (classGroupId: string, filters: Record<string, any>) => 
      ['enrollment-available-students', classGroupId, filters],
    CAPACITY_CHECK: (classGroupId: string) => 
      ['enrollment-capacity-check', classGroupId],
    CLASS_GROUP_ENROLLMENTS: (classGroupId: string, filters?: Record<string, any>) =>
      ['class-group-enrollments', classGroupId, filters || {}],
    ENROLLMENT_STATS: (classGroupId: string) =>
      ['enrollment-stats', classGroupId],
    PAUSE_STATUS: (enrollmentId: string) =>
      ['enrollment-pause-status', enrollmentId],
    // Detalhes de matrícula de um estudante específico
    STUDENT_DETAILS: (userId: string) =>
      ['student-enrollment-details', userId]
  },

  // Ensino (Instructor Teaching)
  INSTRUCTOR_TEACHING: {
    CLASS_GROUPS: (instructorId: string) =>
      ['instructor-class-groups', instructorId],
    STATS: (instructorId: string) =>
      ['instructor-teaching-stats', instructorId],
    STUDENTS: (instructorId: string) =>
      ['instructor-students', instructorId]
  },

  // Notificações
  NOTIFICATIONS: {
    LIST: (userId: string, filters?: Record<string, any>) =>
      ['notifications-list', userId, filters || {}],
    COUNT: (userId: string) =>
      ['notifications-count', userId],
    UNREAD_COUNT: (userId: string) =>
      ['notifications-unread-count', userId],
    DETAIL: (notificationId: string) =>
      ['notification-detail', notificationId],
    PREFERENCES: (userId: string) =>
      ['notification-preferences', userId]
  },

  // Templates de Notificação
  NOTIFICATION_TEMPLATES: {
    LIST: (tenantId: string, type?: string) =>
      ['notification-templates-list', tenantId, type || 'all'],
    DETAIL: (templateId: string) =>
      ['notification-template-detail', templateId],
    VARIABLES: (templateType: string) =>
      ['notification-template-variables', templateType],
    VALIDATION: (template: string, templateType: string) =>
      ['notification-template-validation', template, templateType],
    PREVIEW: (templateId: string, variables: Record<string, any>) =>
      ['notification-template-preview', templateId, variables],
    EXAMPLE_VARIABLES: (templateType: string) =>
      ['notification-template-example-variables', templateType],
    HISTORY: (templateId: string) =>
      ['notification-template-history', templateId]
  },

  // Configurações de Notificação
  NOTIFICATION_SETTINGS: {
    TENANT_CONFIG: (tenantId: string) =>
      ['notification-settings-tenant-config', tenantId],
    GLOBAL_VALIDATION: ['notification-settings-global-validation']
  }
};