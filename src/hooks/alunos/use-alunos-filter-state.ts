'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { FilterState } from '@/app/(dashboard)/alunos/types';

// Chave para o estado dos filtros no React Query
const FILTER_QUERY_KEY = ['alunosFilters'];

// Estado padrão dos filtros - atualizado para usar status centralizado
const defaultFilterState: FilterState = {
  search: '',
  status: ['active'], // Valor padrão mantido como 'active'
  belt: [],
  financialStatus: [],
  branch: [],
  enrollmentStatus: [],
  page: 1,
  limit: 10
};

// Valores válidos de status para validação
const VALID_STATUS_VALUES = ['active', 'inactive', 'suspended'] as const;
export type UserStatus = typeof VALID_STATUS_VALUES[number];

export function useAlunosFilterState(initialState?: Partial<FilterState>) {
  const queryClient = useQueryClient();
  
  // Buscar o estado atual dos filtros do cache do React Query
  const { data: filters = { ...defaultFilterState, ...initialState } } = useQuery({
    queryKey: FILTER_QUERY_KEY,
    queryFn: () =>
      (queryClient.getQueryData(FILTER_QUERY_KEY) as FilterState) ?? {
        ...defaultFilterState,
        ...initialState,
      },
    // Inicializar com o estado padrão e qualquer estado inicial fornecido
    initialData: { ...defaultFilterState, ...initialState },
    // Cache persistente para os filtros
    staleTime: Infinity,
    gcTime: 30 * 60 * 1000, // 30 minutos em cache
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false
  });

  // Função para validar e normalizar valores de status
  const normalizeStatusValues = (statusArray: string[] = []): string[] => {
    return statusArray.filter(status => 
      VALID_STATUS_VALUES.includes(status as UserStatus)
    );
  };

  // Função para atualizar filtros com validação de status
  const updateFilters = (newFilters: Partial<FilterState>) => {
    queryClient.setQueryData(FILTER_QUERY_KEY, (oldData: FilterState = filters) => {
      const updatedFilters = { ...oldData, ...newFilters };
      
      // Validar e normalizar valores de status se estiverem sendo atualizados
      if (newFilters.status) {
        updatedFilters.status = normalizeStatusValues(newFilters.status);
        // Se não há status válidos, usar o padrão
        if (updatedFilters.status.length === 0) {
          updatedFilters.status = ['active'];
        }
      }
      
      // Se estamos alterando qualquer filtro exceto a página, resetar para página 1
      // EXCETO quando apenas a página está sendo alterada
      const isOnlyPageChange = Object.keys(newFilters).length === 1 && 'page' in newFilters;
      
      if (!isOnlyPageChange && Object.keys(newFilters).some(key => key !== 'page')) {
        return { ...updatedFilters, page: 1 };
      }
      
      return updatedFilters;
    });
  };

  // Função para limpar todos os filtros
  const clearFilters = () => {
    queryClient.setQueryData(FILTER_QUERY_KEY, defaultFilterState);
  };

  // Função específica para atualizar a página
  const updatePage = (page: number) => {
    updateFilters({ page });
  };

  // Função específica para atualizar status com validação
  const updateStatus = (statusValues: string[]) => {
    const normalizedStatus = normalizeStatusValues(statusValues);
    updateFilters({ 
      status: normalizedStatus.length > 0 ? normalizedStatus : ['active'] 
    });
  };

  // Função para verificar se um status específico está selecionado
  const isStatusSelected = (statusValue: UserStatus): boolean => {
    return filters.status?.includes(statusValue) || false;
  };

  // Função para alternar um status específico
  const toggleStatus = (statusValue: UserStatus) => {
    const currentStatus = filters.status || [];
    const isSelected = currentStatus.includes(statusValue);
    
    if (isSelected) {
      // Remover o status se já estiver selecionado
      const newStatus = currentStatus.filter(s => s !== statusValue);
      // Se não sobrar nenhum status, manter pelo menos 'active'
      updateStatus(newStatus.length > 0 ? newStatus : ['active']);
    } else {
      // Adicionar o status se não estiver selecionado
      updateStatus([...currentStatus, statusValue]);
    }
  };

  return {
    filters,
    updateFilters,
    clearFilters,
    updatePage,
    updateStatus,
    isStatusSelected,
    toggleStatus,
    normalizeStatusValues,
    validStatusValues: VALID_STATUS_VALUES,
  };
}
