/**
 * Hook para formatação de números de telefone
 * Fornece funções para formatar números de telefone em diversos formatos
 */

/**
 * Formata um número de telefone para exibição
 * @param phone Número de telefone (com ou sem formatação)
 * @returns Número de telefone formatado
 */
export const formatPhoneNumber = (phone: string | null | undefined): string => {
  if (!phone) return '';
  
  // Certifica-se de que estamos lidando com uma string
  const phoneStr = String(phone);
  
  // Verifica se já tem o formato internacional
  const hasPlus = phoneStr.startsWith('+');
  
  // Remove tudo que não for dígito
  const numericPhone = phoneStr.replace(/\D/g, '');
  
  // Se não houver dígitos, retorna string vazia
  if (numericPhone.length === 0) return '';
  
  // Verifica se é um número internacional (tem + ou mais de 11 dígitos)
  if (hasPlus || numericPhone.length > 11) {
    
    let countryCode = '';
    let remainingDigits = '';
    
    // Detecta Brasil primeiro (DDI 55)
    if (numericPhone.startsWith('55') && numericPhone.length >= 11) {
      // Brasil: +55 XX XXXXX-XXXX ou +55 XX XXXX-XXXX
      countryCode = '55';
      remainingDigits = numericPhone.substring(2);
    } else if (numericPhone.startsWith('1') && numericPhone.length >= 11) {
      // América do Norte (EUA/Canadá): +1 XXX XXX XXXX
      countryCode = '1';
      remainingDigits = numericPhone.substring(1);
    } else if (numericPhone.startsWith('44') && numericPhone.length >= 12) {
      // Reino Unido: +44 XXXX XXXXXX
      countryCode = '44';
      remainingDigits = numericPhone.substring(2);
    } else if (numericPhone.length > 11) {
      // Código genérico para outros países (assume os 2 primeiros dígitos como código)
      countryCode = numericPhone.substring(0, 2);
      remainingDigits = numericPhone.substring(2);
    } else {
      return `+${numericPhone}`; // Para números incompletos, mostra só o que foi digitado
    }
    
    // Formatação para Brasil
    if (countryCode === '55' && remainingDigits.length >= 2) {
      const ddd = remainingDigits.substring(0, 2);
      const numero = remainingDigits.substring(2);
      
      if (numero.length === 9) {
        // Celular: +55 (XX) XXXXX-XXXX
        return `+${countryCode} (${ddd}) ${numero.substring(0, 5)}-${numero.substring(5)}`;
      } else if (numero.length === 8) {
        // Fixo: +55 (XX) XXXX-XXXX
        return `+${countryCode} (${ddd}) ${numero.substring(0, 4)}-${numero.substring(4)}`;
      } else if (numero.length > 0) {
        // Número parcial
        return `+${countryCode} (${ddd}) ${numero}`;
      }
      
      return `+${countryCode} (${ddd})`;
    }
    
    // Formatação genérica para outros países
    if (remainingDigits.length > 0) {
      // Tenta uma formatação básica com grupos de dígitos
      if (remainingDigits.length <= 4) {
        return `+${countryCode} ${remainingDigits}`;
      } else if (remainingDigits.length <= 8) {
        return `+${countryCode} ${remainingDigits.substring(0, 4)}-${remainingDigits.substring(4)}`;
      } else {
        return `+${countryCode} ${remainingDigits.substring(0, 4)}-${remainingDigits.substring(4, 8)}-${remainingDigits.substring(8)}`;
      }
    }
    
    return `+${countryCode}`;
  }
  
  // Formatos padrão para números brasileiros sem código do país
  if (numericPhone.length === 11) {
    // Celular com DDD: (99) 99999-9999
    return numericPhone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  } else if (numericPhone.length === 10) {
    // Fixo com DDD: (99) 9999-9999
    return numericPhone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  } else if (numericPhone.length === 9) {
    // Celular sem DDD: 99999-9999
    return numericPhone.replace(/(\d{5})(\d{4})/, '$1-$2');
  } else if (numericPhone.length === 8) {
    // Fixo sem DDD: 9999-9999
    return numericPhone.replace(/(\d{4})(\d{4})/, '$1-$2');
  } else if (numericPhone.length > 0) {
    // Para números parciais, retorna os dígitos sem formatação
    if (numericPhone.length <= 2) {
      return `(${numericPhone}`;
    } else if (numericPhone.length <= 6) {
      return `(${numericPhone.substring(0, 2)}) ${numericPhone.substring(2)}`;
    } else {
      return `(${numericPhone.substring(0, 2)}) ${numericPhone.substring(2, 6)}-${numericPhone.substring(6)}`;
    }
  }
  
  return phoneStr;
};

/**
 * Formata um número de telefone em tempo real durante digitação
 * @param value Número de telefone (parcial ou completo)
 * @returns Número formatado adequado para exibição durante digitação
 */
export const formatPhoneInput = (value: string): string => {
  if (!value) return '';

  // Permite digitar o '+' no início
  let input = value.trim();
  let hasPlus = input.startsWith('+');
  let digitsOnly = input.replace(/\D/g, '');

  // Detecta DDI se houver
  let countryCode = '';
  let rest = '';

  if (hasPlus) {
    // Extrai até 3 dígitos após o + como DDI
    const match = input.match(/^\+(\d{1,3})/);
    if (match) {
      countryCode = match[1];
      rest = digitsOnly.slice(countryCode.length);
    } else {
      // Só tem o +
      return '+';
    }
  } else {
    // Se não tem +, assume Brasil
    countryCode = '55';
    rest = digitsOnly;
  }

  // Máscara Brasil
  if (countryCode === '55') {
    if (rest.length <= 2) {
      return `${hasPlus ? '+' : ''}55 (${rest}`;
    }
    if (rest.length <= 6) {
      return `${hasPlus ? '+' : ''}55 (${rest.slice(0, 2)}) ${rest.slice(2)}`;
    }
    if (rest.length <= 10) {
      return `${hasPlus ? '+' : ''}55 (${rest.slice(0, 2)}) ${rest.slice(2, 6)}-${rest.slice(6)}`;
    }
    return `${hasPlus ? '+' : ''}55 (${rest.slice(0, 2)}) ${rest.slice(2, 7)}-${rest.slice(7, 11)}`;
  }

  // Máscara genérica internacional: +CC XXXX-XXXX... (agrupa de 4 em 4)
  let formatted = `${hasPlus ? '+' : ''}${countryCode}`;
  if (rest.length > 0) {
    formatted += ' ';
    for (let i = 0; i < rest.length; i += 4) {
      if (i > 0) formatted += '-';
      formatted += rest.slice(i, i + 4);
    }
  }
  return formatted;
};

/**
 * Formata campos de telefone em um objeto de dados
 * @param data Objeto com campos phone e/ou emergency_phone
 * @returns Objeto com campos formatados adicionados (formattedPhone e formattedEmergencyPhone)
 */
export const formatPhoneFields = (data: any) => {
  if (!data) return data;
  
  // Criar uma cópia profunda para não modificar o objeto original
  const formattedData = JSON.parse(JSON.stringify(data));
  
  // Formatação para o telefone principal
  if (formattedData.phone) {
    formattedData.formattedPhone = formatPhoneNumber(formattedData.phone);
    // Garantir que o valor formatado seja visível imediatamente no log
    console.log(`[PHONE] Telefone formatado: ${formattedData.phone} → ${formattedData.formattedPhone}`);
  }
  
  // Formatação para o telefone de emergência
  if (formattedData.emergency_phone) {
    formattedData.formattedEmergencyPhone = formatPhoneNumber(formattedData.emergency_phone);
    // Garantir que o valor formatado seja visível imediatamente no log
    console.log(`[PHONE] Telefone de emergência formatado: ${formattedData.emergency_phone} → ${formattedData.formattedEmergencyPhone}`);
  }
  
  return formattedData;
};

/**
 * Formata apenas o número nacional (sem DDI) durante a digitação
 * @param value Número nacional (sem DDI)
 * @param countryCode Código do país para determinar o formato (ex: '+55')
 * @returns Número nacional formatado
 */
export const formatNationalNumber = (value: string, countryCode: string = '+55'): string => {
  if (!value) return '';

  // Remove tudo que não for dígito
  const digitsOnly = value.replace(/\D/g, '');
  
  if (digitsOnly.length === 0) return '';

  // Formatação específica por país
  if (countryCode === '+55') {
    // Brasil: (XX) XXXXX-XXXX ou (XX) XXXX-XXXX
    if (digitsOnly.length <= 2) {
      return `(${digitsOnly}`;
    }
    if (digitsOnly.length <= 6) {
      return `(${digitsOnly.slice(0, 2)}) ${digitsOnly.slice(2)}`;
    }
    if (digitsOnly.length === 9) {
      // Celular sem DDD: XXXXX-XXXX
      return `${digitsOnly.slice(0, 5)}-${digitsOnly.slice(5)}`;
    }
    if (digitsOnly.length === 8) {
      // Fixo sem DDD: XXXX-XXXX
      return `${digitsOnly.slice(0, 4)}-${digitsOnly.slice(4)}`;
    }
    if (digitsOnly.length === 11) {
      // Celular com DDD: (XX) XXXXX-XXXX
      return `(${digitsOnly.slice(0, 2)}) ${digitsOnly.slice(2, 7)}-${digitsOnly.slice(7)}`;
    }
    if (digitsOnly.length === 10) {
      // Fixo com DDD: (XX) XXXX-XXXX
      return `(${digitsOnly.slice(0, 2)}) ${digitsOnly.slice(2, 6)}-${digitsOnly.slice(6)}`;
    }
    // Número parcial com DDD
    if (digitsOnly.length > 6) {
      return `(${digitsOnly.slice(0, 2)}) ${digitsOnly.slice(2, 6)}-${digitsOnly.slice(6)}`;
    }
  }
  
  // Formatação genérica para outros países (grupos de 4 dígitos)
  let formatted = '';
  for (let i = 0; i < digitsOnly.length; i += 4) {
    if (i > 0) formatted += '-';
    formatted += digitsOnly.slice(i, i + 4);
  }
  
  return formatted;
};

/**
 * Hook para formatação de números de telefone
 * @returns Objeto com funções de formatação
 */
export default function usePhoneFormat() {
  return {
    formatPhoneNumber,
    formatPhoneInput,
    formatPhoneFields,
    formatNationalNumber
  };
} 