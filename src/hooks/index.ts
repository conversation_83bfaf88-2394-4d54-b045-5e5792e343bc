/**
 * Exporta os hooks principais da aplicação para uso conveniente com importações
 */

export { default as usePhoneFormat, formatPhoneNumber, formatPhoneInput, formatPhoneFields } from './form/usePhoneFormat'

// Re-exportação dos hooks de usuário para conveniência
export { useUserStatus, useToggleUserStatus, useBulkUserStatus, type UserStatus } from './user/useUserStatus'

// Re-exportação dos hooks de filtros com validação de status
export { useAlunosFilterState } from './alunos/use-alunos-filter-state'
export { useInstructorsFilterState } from './instrutores/use-instructors-filter-state'

// Hooks específicos de estudantes (Fase 4 da migração)
export { useStudentStatus, useToggleStudentStatus, useBulkStudentStatus, useStudentStatusMapping } from './alunos/useStudentStatus'

// Hooks específicos de instrutores (Fase 4 da migração)
export { useInstructorStatus, useToggleInstructorStatus, useBulkInstructorStatus, useInstructorStatusMapping } from './instrutores/useInstructorStatus'

export * from './user'
export * from './tenant'

// Turmas (Class Groups)
export * from './turmas/use-class-groups'
// export * from './turmas/use-class-groups-mutations';
