"use client";

import { useEmailAvailability } from "@/hooks/use-email-availability";

/**
 * Hook específico para instrutores, usando o endpoint correto.
 */
export function useEmailAvailabilityInstructor(rawEmail: string) {
  return useEmailAvailability(rawEmail, {
    endpoint: "/api/instructors/email-availability",
    skipValues: ["@gmail.com"],
  });
}

// Mantém compatibilidade com imports existentes
export { useEmailAvailabilityInstructor as useEmailAvailability }; 