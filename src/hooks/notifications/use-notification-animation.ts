'use client';

import { useEffect, useState, useRef } from 'react';

interface UseNotificationAnimationOptions {
  count: number;
  loading: boolean;
}

interface UseNotificationAnimationReturn {
  hasNewNotification: boolean;
  shouldAnimate: boolean;
}

/**
 * Hook para gerenciar animações de notificação
 * Detecta quando há novas notificações e ativa animações temporárias
 */
export function useNotificationAnimation({
  count,
  loading
}: UseNotificationAnimationOptions): UseNotificationAnimationReturn {
  const [hasNewNotification, setHasNewNotification] = useState(false);
  const [shouldAnimate, setShouldAnimate] = useState(false);
  const previousCountRef = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  useEffect(() => {
    // Não fazer nada se ainda está carregando
    if (loading) return;

    const previousCount = previousCountRef.current;
    
    // Se o contador aumentou, temos uma nova notificação
    if (count > previousCount && previousCount > 0) {
      setHasNewNotification(true);
      setShouldAnimate(true);

      // Limpar timeout anterior se existir
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Parar a animação intensa após 5 segundos
      timeoutRef.current = setTimeout(() => {
        setHasNewNotification(false);
        setShouldAnimate(false);
      }, 5000);
    }

    // Atualizar a referência do contador anterior
    previousCountRef.current = count;

    // Se não há mais notificações, parar todas as animações
    if (count === 0) {
      setHasNewNotification(false);
      setShouldAnimate(false);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    }
  }, [count, loading]);

  // Cleanup do timeout quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    hasNewNotification,
    shouldAnimate: shouldAnimate || count > 0 // Sempre animar se há notificações
  };
}
