/**
 * Hook para contar notificações não lidas
 *
 * Nota: Os real-time updates são gerenciados pelo hook useNotifications para evitar
 * duplicação de subscriptions. Este hook apenas lê o cache do contador.
 */

'use client';

import { useQuery } from '@tanstack/react-query';
import { NotificationServiceClient } from '@/services/notifications/client/notification-service-client';
import { CACHE_KEYS } from '@/constants/cache-keys';

interface UseNotificationCountOptions {
  userId: string;
}

interface UseNotificationCountReturn {
  count: number;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

export function useNotificationCount({
  userId
}: UseNotificationCountOptions): UseNotificationCountReturn {

  // Query para buscar o contador de notificações não lidas
  const {
    data: count = 0,
    isLoading: loading,
    error,
    refetch
  } = useQuery({
    queryKey: CACHE_KEYS.NOTIFICATIONS.UNREAD_COUNT(userId),
    queryFn: async () => {
      if (!userId) return 0;

      const notificationService = new NotificationServiceClient();
      const response = await notificationService.getUnreadCount(userId);

      if (response.success && typeof response.data === 'number') {
        return response.data;
      } else {
        throw new Error(response.error || 'Erro ao carregar contador de notificações');
      }
    },
    enabled: !!userId,
    staleTime: 30 * 1000, // 30 segundos
    refetchOnWindowFocus: true,
    refetchOnMount: true
  });

  // Função para refresh manual
  const refresh = async () => {
    await refetch();
  };

  // Nota: A subscription real-time foi movida para useNotifications para evitar duplicação
  // O useNotifications agora é responsável por atualizar tanto a lista quanto o contador

  return {
    count,
    loading,
    error: error?.message || null,
    refresh
  };
}
