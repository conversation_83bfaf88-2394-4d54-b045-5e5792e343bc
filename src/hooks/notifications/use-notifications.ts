/**
 * Hook para gerenciar notificações do usuário
 * Inclui real-time updates via Supabase subscriptions integrado com React Query
 */

'use client';

import { useEffect, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/services/supabase/client';
import { NotificationServiceClient } from '@/services/notifications/client/notification-service-client';
import { CACHE_KEYS } from '@/constants/cache-keys';
import type {
  Notification,
  NotificationFilters,
  PaginatedNotifications,
  NotificationServiceResponse
} from '@/services/notifications/types/notification-types';

interface UseNotificationsOptions {
  userId: string;
  filters?: NotificationFilters;
  realTime?: boolean;
}

interface UseNotificationsReturn {
  notifications: Notification[];
  loading: boolean;
  error: string | null;
  total: number;
  hasMore: boolean;
  page: number;
  refresh: () => Promise<void>;
  loadMore: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  archive: (notificationId: string) => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
}

export function useNotifications({
  userId,
  filters,
  realTime = true
}: UseNotificationsOptions): UseNotificationsReturn {
  const queryClient = useQueryClient();

  // Estabilizar o objeto filters para evitar re-criações desnecessárias
  const stableFilters = useMemo(() => {
    return filters ? { ...filters } : {};
  }, [JSON.stringify(filters)]);

  // Query para buscar notificações
  const {
    data: queryData,
    isLoading: loading,
    error: queryError,
    refetch
  } = useQuery({
    queryKey: CACHE_KEYS.NOTIFICATIONS.LIST(userId, stableFilters),
    queryFn: async () => {
      if (!userId) return { data: [], total: 0, hasMore: false, page: 1 };

      const notificationService = new NotificationServiceClient();
      const response: NotificationServiceResponse<PaginatedNotifications> = await notificationService.getByUser(
        userId,
        { ...stableFilters, page: 1 }
      );

      if (response.success && response.data) {
        return {
          data: response.data.data,
          total: response.data.total,
          hasMore: response.data.hasMore,
          page: 1
        };
      } else {
        throw new Error(response.error || 'Erro ao carregar notificações');
      }
    },
    enabled: !!userId,
    staleTime: 30 * 1000, // 30 segundos
    refetchOnWindowFocus: true,
    refetchOnMount: true
  });

  // Extrair dados da query
  const notifications = queryData?.data || [];
  const total = queryData?.total || 0;
  const hasMore = queryData?.hasMore || false;
  const page = queryData?.page || 1;
  const error = queryError?.message || null;

  // Função para atualizar uma notificação específica no cache
  const updateNotificationInCache = (notificationId: string, updates: Partial<Notification>) => {
    queryClient.setQueryData(
      CACHE_KEYS.NOTIFICATIONS.LIST(userId, stableFilters),
      (oldData: any) => {
        if (!oldData) return oldData;

        return {
          ...oldData,
          data: oldData.data.map((notification: Notification) =>
            notification.id === notificationId
              ? { ...notification, ...updates }
              : notification
          )
        };
      }
    );
  };

  // Função para remover uma notificação da lista no cache
  const removeNotificationFromCache = (notificationId: string) => {
    queryClient.setQueryData(
      CACHE_KEYS.NOTIFICATIONS.LIST(userId, stableFilters),
      (oldData: any) => {
        if (!oldData) return oldData;

        return {
          ...oldData,
          data: oldData.data.filter((notification: Notification) => notification.id !== notificationId),
          total: oldData.total - 1
        };
      }
    );
  };

  // Mutation para marcar como lida
  const markAsReadMutation = useMutation({
    mutationFn: async (notificationId: string) => {
      console.log('📖 Marcando notificação como lida:', notificationId);
      const notificationService = new NotificationServiceClient();
      const response = await notificationService.markAsRead(notificationId, userId);

      if (!response.success) {
        throw new Error(response.error || 'Erro ao marcar como lida');
      }

      return response;
    },
    onSuccess: (_, notificationId) => {
      console.log('✅ Notificação marcada como lida com sucesso');

      // Verificar se os filtros atuais incluem apenas 'unread'
      const statusFilters = stableFilters.status;
      const isUnreadOnlyFilter = statusFilters &&
        statusFilters.length === 1 &&
        statusFilters[0] === 'unread';

      console.log('🔍 Filtros atuais:', statusFilters, 'isUnreadOnly:', isUnreadOnlyFilter);

      if (isUnreadOnlyFilter) {
        // Se o filtro é apenas para 'unread', remover a notificação da lista
        console.log('🗑️ Removendo notificação da lista (filtro unread only)');
        removeNotificationFromCache(notificationId);
      } else {
        // Caso contrário, apenas atualizar o status na lista
        console.log('🔄 Atualizando status da notificação na lista');
        updateNotificationInCache(notificationId, {
          status: 'read',
          read_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }

      // Invalidar contador de notificações não lidas
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.NOTIFICATIONS.UNREAD_COUNT(userId)
      });
    },
    onError: (error) => {
      console.error('❌ Erro ao marcar como lida:', error);
    }
  });

  const markAsRead = async (notificationId: string) => {
    await markAsReadMutation.mutateAsync(notificationId);
  };

  // Mutation para marcar todas como lidas
  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      const notificationService = new NotificationServiceClient();
      const response = await notificationService.markAllAsRead(userId);

      if (!response.success) {
        throw new Error(response.error || 'Erro ao marcar todas como lidas');
      }

      return response;
    },
    onSuccess: () => {
      // Verificar se os filtros atuais incluem apenas 'unread'
      const statusFilters = stableFilters.status;
      const isUnreadOnlyFilter = statusFilters &&
        statusFilters.length === 1 &&
        statusFilters[0] === 'unread';

      if (isUnreadOnlyFilter) {
        // Se o filtro é apenas para 'unread', remover todas as notificações 'unread' da lista
        queryClient.setQueryData(
          CACHE_KEYS.NOTIFICATIONS.LIST(userId, stableFilters),
          (oldData: any) => {
            if (!oldData) return oldData;

            const unreadNotifications = oldData.data.filter((n: Notification) => n.status === 'unread');
            const filteredNotifications = oldData.data.filter((notification: Notification) => notification.status !== 'unread');

            return {
              ...oldData,
              data: filteredNotifications,
              total: oldData.total - unreadNotifications.length
            };
          }
        );
      } else {
        // Caso contrário, apenas atualizar o status na lista
        const now = new Date().toISOString();
        queryClient.setQueryData(
          CACHE_KEYS.NOTIFICATIONS.LIST(userId, stableFilters),
          (oldData: any) => {
            if (!oldData) return oldData;

            return {
              ...oldData,
              data: oldData.data.map((notification: Notification) =>
                notification.status === 'unread'
                  ? { ...notification, status: 'read', read_at: now, updated_at: now }
                  : notification
              )
            };
          }
        );
      }

      // Invalidar contador de notificações não lidas
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.NOTIFICATIONS.UNREAD_COUNT(userId)
      });
    },
    onError: (error) => {
      console.error('❌ Erro ao marcar todas como lidas:', error);
    }
  });

  const markAllAsRead = async () => {
    await markAllAsReadMutation.mutateAsync();
  };

  // Mutation para arquivar notificação
  const archiveMutation = useMutation({
    mutationFn: async (notificationId: string) => {
      const notificationService = new NotificationServiceClient();
      const response = await notificationService.archive(notificationId, userId);

      if (!response.success) {
        throw new Error(response.error || 'Erro ao arquivar notificação');
      }

      return response;
    },
    onSuccess: (_, notificationId) => {
      updateNotificationInCache(notificationId, {
        status: 'archived',
        updated_at: new Date().toISOString()
      });
    },
    onError: (error) => {
      console.error('❌ Erro ao arquivar notificação:', error);
    }
  });

  const archive = async (notificationId: string) => {
    await archiveMutation.mutateAsync(notificationId);
  };

  // Mutation para deletar notificação
  const deleteMutation = useMutation({
    mutationFn: async (notificationId: string) => {
      const notificationService = new NotificationServiceClient();
      const response = await notificationService.delete(notificationId, userId);

      if (!response.success) {
        throw new Error(response.error || 'Erro ao deletar notificação');
      }

      return response;
    },
    onSuccess: (_, notificationId) => {
      removeNotificationFromCache(notificationId);
    },
    onError: (error) => {
      console.error('❌ Erro ao deletar notificação:', error);
    }
  });

  const deleteNotification = async (notificationId: string) => {
    await deleteMutation.mutateAsync(notificationId);
  };

  // Refresh
  const refresh = async () => {
    await refetch();
  };

  // Load more - por enquanto não implementado, pois precisaria de paginação mais complexa
  const loadMore = async () => {
    // TODO: Implementar paginação com React Query
    // Por enquanto, apenas refetch
    await refetch();
  };

  // Setup real-time subscription
  useEffect(() => {
    if (!realTime || !userId) return;

    const supabase = createClient();

    const subscription = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          console.log('🔔 Notification real-time event:', payload.eventType, payload);

          if (payload.eventType === 'INSERT') {
            const newNotification = payload.new as Notification;
            console.log('📥 Nova notificação:', newNotification);

            // Adicionar nova notificação ao cache da lista
            queryClient.setQueryData(
              CACHE_KEYS.NOTIFICATIONS.LIST(userId, stableFilters),
              (oldData: any) => {
                if (!oldData) return oldData;

                return {
                  ...oldData,
                  data: [newNotification, ...oldData.data],
                  total: oldData.total + 1
                };
              }
            );

            // Se a nova notificação é unread, atualizar o contador também
            if (newNotification.status === 'unread') {
              console.log('➕ Incrementando contador');
              queryClient.setQueryData(
                CACHE_KEYS.NOTIFICATIONS.UNREAD_COUNT(userId),
                (prev: number = 0) => {
                  const newCount = prev + 1;
                  console.log('📊 Contador atualizado:', prev, '->', newCount);
                  return newCount;
                }
              );
            }
          } else if (payload.eventType === 'UPDATE') {
            const oldNotification = payload.old as Notification;
            const updatedNotification = payload.new as Notification;
            console.log('🔄 Notificação atualizada:', {
              old: oldNotification,
              new: updatedNotification
            });

            // Verificar se a notificação atualizada ainda atende aos filtros
            const statusFilters = stableFilters.status;
            const shouldKeepNotification = !statusFilters || statusFilters.includes(updatedNotification.status);

            if (shouldKeepNotification) {
              updateNotificationInCache(updatedNotification.id, updatedNotification);
            } else {
              // Se não atende mais aos filtros, remover da lista
              removeNotificationFromCache(updatedNotification.id);
            }

            // Atualizar contador baseado na mudança de status
            if (oldNotification.status === 'unread' && updatedNotification.status !== 'unread') {
              console.log('➖ Decrementando contador (unread -> read)');
              queryClient.setQueryData(
                CACHE_KEYS.NOTIFICATIONS.UNREAD_COUNT(userId),
                (prev: number = 0) => {
                  const newCount = Math.max(0, prev - 1);
                  console.log('📊 Contador atualizado:', prev, '->', newCount);
                  return newCount;
                }
              );
            } else if (oldNotification.status !== 'unread' && updatedNotification.status === 'unread') {
              console.log('➕ Incrementando contador (read -> unread)');
              queryClient.setQueryData(
                CACHE_KEYS.NOTIFICATIONS.UNREAD_COUNT(userId),
                (prev: number = 0) => {
                  const newCount = prev + 1;
                  console.log('📊 Contador atualizado:', prev, '->', newCount);
                  return newCount;
                }
              );
            }
          } else if (payload.eventType === 'DELETE') {
            const deletedNotification = payload.old as Notification;
            console.log('🗑️ Notificação deletada:', deletedNotification);
            removeNotificationFromCache(deletedNotification.id);

            // Se a notificação deletada era unread, decrementar o contador
            if (deletedNotification.status === 'unread') {
              console.log('➖ Decrementando contador (delete unread)');
              queryClient.setQueryData(
                CACHE_KEYS.NOTIFICATIONS.UNREAD_COUNT(userId),
                (prev: number = 0) => {
                  const newCount = Math.max(0, prev - 1);
                  console.log('📊 Contador atualizado:', prev, '->', newCount);
                  return newCount;
                }
              );
            }
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [realTime, userId, stableFilters, queryClient]);

  return {
    notifications,
    loading,
    error,
    total,
    hasMore,
    page,
    refresh,
    loadMore,
    markAsRead,
    markAllAsRead,
    archive,
    deleteNotification
  };
}
