import { createTenantBrowserClient } from '@/services/supabase/client/tenant-client-browser';
import { type SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/services/supabase/types/database.types';
import { useEffect, useState } from 'react';

/**
 * Hook para usar o cliente Supabase com filtro de tenant no lado do cliente
 * Garante que os usuários só possam acessar dados do seu próprio tenant
 * @returns Cliente Supabase configurado com filtro de tenant
 */
export function useTenantSupabase() {
  const [client, setClient] = useState<SupabaseClient<Database> | null>(null);
  
  useEffect(() => {
    const supabase = createTenantBrowserClient();
    setClient(supabase);
  }, []);
  
  return client;
}

export default useTenantSupabase; 