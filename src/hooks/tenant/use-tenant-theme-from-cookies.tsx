'use client'

import { useEffect, useState } from 'react'
import Cookies from 'js-cookie'
import { useTenantTheme } from './use-tenant-theme'

export function useTenantThemeFromCookies() {
  const { primaryColor, secondaryColor, setPrimaryColor, setSecondaryColor } = useTenantTheme()
  const [isLoaded, setIsLoaded] = useState(false)
  
  useEffect(() => {
    function syncColorsFromCookies() {
      const primaryColorFromCookie = Cookies.get('tenant_primary_color')
      const secondaryColorFromCookie = Cookies.get('tenant_secondary_color')
      
      if (primaryColorFromCookie && primaryColorFromCookie !== primaryColor) {
        setPrimaryColor(primaryColorFromCookie)
      }
      
      if (secondaryColorFromCookie && secondaryColorFromCookie !== secondaryColor) {
        setSecondaryColor(secondaryColorFromCookie)
      }
      
      if (!isLoaded) {
        setIsLoaded(true)
      }
    }
    
    syncColorsFromCookies()
    
    const checkCookiesInterval = setInterval(syncColorsFromCookies, 1000)
    
    return () => {
      clearInterval(checkCookiesInterval)
    }
  }, [primaryColor, secondaryColor, setPrimaryColor, setSecondaryColor, isLoaded])
  
  return { isLoaded }
} 