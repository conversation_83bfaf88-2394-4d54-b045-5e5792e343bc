'use client';

import { useQuery, useQueryClient, keepPreviousData } from '@tanstack/react-query';
import { useState, useCallback, useMemo, useEffect } from 'react';
import { getClassGroups } from '@/app/(dashboard)/turmas/actions/class-group/get-class-groups';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { useRolePermissions } from '@/hooks/user/Permissions/use-role-permissions';
import { useUser } from '@/hooks/user/Auth/useUser';
import type { ClassGroupWithDetails, PaginatedResult } from '@/app/(dashboard)/aulas/types';

export interface ClassGroupFilters {
  search?: string;
  instructor_id?: string;
  branch_id?: string;
  category?: 'kids' | 'teens' | 'adults' | 'seniors';
  is_active?: boolean;
  has_availability?: boolean;
  min_belt_level?: number;
  max_belt_level?: number;
  start_date_from?: string;
  start_date_to?: string;
}

export interface ClassGroupPagination {
  page: number;
  limit: number;
  sort_by: string;
  sort_order: 'asc' | 'desc';
}

export function useClassGroups(
  initialFilters: Partial<ClassGroupFilters> = {},
  initialPagination: Partial<ClassGroupPagination> = {}
) {
  const queryClient = useQueryClient();
  const { user } = useUser();
  const { userRole, canAccessAllTurmas } = useRolePermissions();
  
  const [filters, setFilters] = useState<ClassGroupFilters>({
    search: '',
    ...initialFilters
  });
  
  const [pagination, setPagination] = useState<ClassGroupPagination>({
    page: 1,
    limit: 10,
    sort_by: 'name',
    sort_order: 'asc',
    ...initialPagination
  });

  // Aplicar filtro automático para instrutores (own_only)
  const finalFilters = useMemo(() => {
    const baseFilters = { ...filters };
    
    // Se o usuário é instrutor e não tem acesso a todas as turmas, filtrar apenas suas turmas
    if (userRole === 'instructor' && !canAccessAllTurmas && user?.id) {
      baseFilters.instructor_id = user.id;
    }
    
    return baseFilters;
  }, [filters, userRole, canAccessAllTurmas, user?.id]);

  // Memoizar os filtros para evitar re-renders desnecessários
  const memoizedParams = useMemo(() => ({
    ...finalFilters,
    ...pagination
  }), [finalFilters, pagination]);

  // Query para buscar turmas usando server action
  const {
    data: classGroupsData,
    isLoading: isInitialLoading,
    error,
    refetch,
    isFetching,
    isRefetching
  } = useQuery({
    queryKey: ['class-groups-list', memoizedParams],
    queryFn: async () => {
      const result = await getClassGroups(memoizedParams);

      if (!result.success) {
        const errorMessage = result.errors && typeof result.errors === 'object' && '_form' in result.errors 
          ? result.errors._form as string
          : 'Erro ao buscar turmas';
        throw new Error(errorMessage);
      }

      return result.data!;
    },
    staleTime: 0, // Always consider data stale to ensure fresh results
    gcTime: 1000 * 60 * 5, // Keep in cache for 5 minutes
    refetchOnWindowFocus: false,
    retry: 2,
    // Manter dados anteriores durante refetch para evitar loading states desnecessários
    placeholderData: keepPreviousData,
    // Só executar a query se tivermos as informações de role
    enabled: !!userRole
  });

  // Determinar se é loading inicial (primeira vez) ou fetching (busca/filtro)
  const isLoading = isInitialLoading && !classGroupsData;
  const isSearching = isFetching && !isInitialLoading;

  // Funções de controle de filtros
  const updateFilters = useCallback((newFilters: Partial<ClassGroupFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset para primeira página
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({ search: '' });
    setPagination(prev => ({ ...prev, page: 1 }));
  }, []);

  // Funções de controle de paginação
  const updatePagination = useCallback((newPagination: Partial<ClassGroupPagination>) => {
    setPagination(prev => ({ ...prev, ...newPagination }));
  }, []);

  const goToPage = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page }));
  }, []);

  const nextPage = useCallback(() => {
    if (classGroupsData?.pagination?.hasNext) {
      setPagination(prev => ({ ...prev, page: prev.page + 1 }));
    }
  }, [classGroupsData?.pagination?.hasNext]);

  const prevPage = useCallback(() => {
    if (classGroupsData?.pagination?.hasPrev) {
      setPagination(prev => ({ ...prev, page: prev.page - 1 }));
    }
  }, [classGroupsData?.pagination?.hasPrev]);

  // Estatísticas computadas
  const stats = useMemo(() => {
    const classGroups = classGroupsData?.data || [];
    
    return {
      total: classGroupsData?.pagination?.total || 0,
      active: classGroups.filter(group => group.is_active).length,
      inactive: classGroups.filter(group => !group.is_active).length,
      withAvailability: classGroups.filter(group => 
        !group.max_capacity || group.current_enrollment_count! < group.max_capacity
      ).length,
      full: classGroups.filter(group => 
        group.max_capacity && group.current_enrollment_count! >= group.max_capacity
      ).length
    };
  }, [classGroupsData]);

  // Verificar se há filtros ativos (excluir filtros automáticos)
  const hasActiveFilters = useMemo(() => {
    return !!(
      filters.search?.trim() || 
      (filters.instructor_id && canAccessAllTurmas) || // Só conta como filtro ativo se admin/poder remover
      filters.branch_id || 
      filters.category ||
      filters.is_active !== undefined ||
      filters.has_availability !== undefined ||
      filters.min_belt_level ||
      filters.max_belt_level ||
      filters.start_date_from ||
      filters.start_date_to
    );
  }, [filters, canAccessAllTurmas]);

  return {
    // Dados
    classGroups: classGroupsData?.data || [],
    pagination: classGroupsData?.pagination,
    
    // Estados
    isLoading, // Apenas true no carregamento inicial
    isFetching: isSearching, // True durante buscas/filtros
    error,
    
    // Filtros
    filters: finalFilters, // Retornar os filtros finais (com filtros automáticos aplicados)
    originalFilters: filters, // Filtros originais sem automáticos
    setFilters: updateFilters,
    clearFilters,
    hasActiveFilters,
    
    // Informações de permissão
    canAccessAllTurmas,
    userRole,
    
    // Paginação
    currentPage: pagination.page,
    pageSize: pagination.limit,
    sortBy: pagination.sort_by,
    sortOrder: pagination.sort_order,
    setPagination: updatePagination,
    goToPage,
    nextPage,
    prevPage,
    
    // Estatísticas
    stats,
    
    // Controles
    refetch
  };
} 