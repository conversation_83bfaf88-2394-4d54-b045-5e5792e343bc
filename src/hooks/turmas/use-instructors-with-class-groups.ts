'use client';

import { useQuery } from '@tanstack/react-query';
import { getInstructorsWithClassGroups } from '@/app/(dashboard)/turmas/actions/instructors';
import type { InstructorWithBelt } from '@/app/(dashboard)/turmas/actions/instructors';

/**
 * Hook para buscar instrutores que possuem turmas vinculadas
 * Utiliza React Query para cache e gerenciamento de estado
 */
export function useInstructorsWithClassGroups() {
  const query = useQuery({
    queryKey: ['instructors-with-class-groups'],
    queryFn: async () => {
      const result = await getInstructorsWithClassGroups();
      
      if (!result.success) {
        throw new Error(result.errors?._form || 'Erro ao carregar instrutores');
      }

      return result.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    refetchOnWindowFocus: false,
  });

  return query;
} 