'use client'

import {
  useState,
  useRef,
  useEffect,
  useCallback,
  type RefObject,
} from 'react'

export function useColumnResizing(
  tableRef: RefObject<HTMLTableElement | null>,
) {
  const [columnWidths, setColumnWidths] = useState<Record<number, number>>({})

  const isResizing = useRef<number | null>(null)
  const cursorStart = useRef<number>(0)
  const widthStart = useRef<number>(0)

  useEffect(() => {
    if (tableRef.current) {
      const ths = Array.from(
        tableRef.current.querySelectorAll<HTMLTableCellElement>('thead th'),
      )
      const initialWidths: Record<number, number> = {}
      ths.forEach((th, i) => {
        initialWidths[i] = th.offsetWidth
      })
      setColumnWidths(initialWidths)
    }
  }, [tableRef])

  const handleMouseDown = useCallback(
    (e: React.MouseEvent, index: number) => {
      e.preventDefault()
      isResizing.current = index

      cursorStart.current = e.clientX

      const th =
        tableRef.current?.querySelectorAll<HTMLTableCellElement>('thead th')[
          index
        ]
      widthStart.current = th?.offsetWidth || 0

      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    },
    [tableRef],
  )

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isResizing.current === null) return

    const newWidth = widthStart.current + (e.clientX - cursorStart.current)
    const minWidth = 50

    if (newWidth > minWidth) {
      setColumnWidths(prev => ({
        ...prev,
        [isResizing.current!]: newWidth,
      }))
    }
  }, [])

  const handleMouseUp = useCallback(() => {
    isResizing.current = null
    document.body.style.cursor = 'default'
    document.body.style.userSelect = 'auto'
  }, [])

  useEffect(() => {
    const onMouseMove = (e: MouseEvent) => handleMouseMove(e)
    const onMouseUp = () => handleMouseUp()

    window.addEventListener('mousemove', onMouseMove)
    window.addEventListener('mouseup', onMouseUp)

    return () => {
      window.removeEventListener('mousemove', onMouseMove)
      window.removeEventListener('mouseup', onMouseUp)
    }
  }, [handleMouseMove, handleMouseUp])

  return { columnWidths, handleMouseDown }
} 