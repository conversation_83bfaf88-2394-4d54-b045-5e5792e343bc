import { usePageTitle as usePageTitleContext } from '@/contexts/PageTitleContext';
import { ReactNode, useEffect } from 'react';

interface UsePageTitleProps {
  title?: string;
  subtitle?: string;
  icon?: ReactNode;
}

export function usePageTitle({ title, subtitle, icon }: UsePageTitleProps = {}) {
  const context = usePageTitleContext();

  useEffect(() => {
    if (title !== undefined) {
      context.setPageTitle(title);
    }
    
    if (subtitle !== undefined) {
      context.setPageSubtitle(subtitle);
    }
    
    if (icon !== undefined) {
      context.setPageIcon(icon);
    }

    // Cleanup quando o componente for desmontado
    return () => {
      if (title !== undefined) {
        context.setPageTitle(null);
      }
      if (subtitle !== undefined) {
        context.setPageSubtitle(null);
      }
      if (icon !== undefined) {
        context.setPageIcon(null);
      }
    };
  }, [title, subtitle, icon, context]);

  return {
    setPageTitle: context.setPageTitle,
    setPageSubtitle: context.setPageSubtitle,
    setPageIcon: context.setPageIcon,
    pageTitle: context.pageTitle,
    pageSubtitle: context.pageSubtitle,
    pageIcon: context.pageIcon
  };
} 