'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getCurrentUser, logoutUser } from '@/services/auth/actions/auth-actions';

interface AuthUser {
  id: string;
  email: string;
  [key: string]: any;
}

export function useAuth() {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function loadUser() {
      try {
        const userData = await getCurrentUser();
        setUser(userData as AuthUser);
      } catch (error) {
        console.error('Erro ao verificar autenticação:', error);
      } finally {
        setLoading(false);
      }
    }

    loadUser();
  }, []);

  const logout = async () => {
    try {
      await logoutUser();
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  const checkAuth = async (redirectIfNotAuth = false, redirectPath = '/login') => {
    try {
      const userData = await getCurrentUser();
      
      setUser(userData as AuthUser);
      
      if (!userData && redirectIfNotAuth) {
        router.push(redirectPath);
        return false;
      }
      
      return !!userData;
    } catch (error) {
      console.error('Erro ao verificar autenticação:', error);
      if (redirectIfNotAuth) {
        router.push(redirectPath);
      }
      return false;
    }
  };

  return {
    user,
    loading,
    isAuthenticated: !!user,
    checkAuth,
    logout
  };
} 