'use client';

import { useQuery } from '@tanstack/react-query';
import { User } from '@supabase/supabase-js';
import { CACHE_KEYS } from '@/constants/cache-keys';

interface UseUserResult {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
}

export function useUser(): UseUserResult {
  const { data, isLoading, error } = useQuery({
    queryKey: CACHE_KEYS.USER_SESSION,
    queryFn: async () => {
      try {
        // Importação dinâmica para evitar erros de Server Component
        const { getCurrentUser } = await import('@/services/auth/actions/auth-actions');
        return await getCurrentUser();
      } catch (error) {
        console.error('Erro ao obter usuário:', error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
  
  return {
    user: data || null,
    isLoading,
    error: error as Error | null,
  };
}