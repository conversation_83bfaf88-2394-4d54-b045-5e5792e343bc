import { useState, useEffect } from 'react';
import { createClient } from '@/services/supabase/client';
import { useUser } from '@/hooks/user/Auth/useUser';

type UserRole = 'admin' | 'instructor' | 'student';
type AccessLevel = 'full_access' | 'own_only' | 'teaching_only' | 'no_access';

interface RolePermissions {
  canAccessTurmas: boolean;
  canAccessAllTurmas: boolean;
  canAccessAulasLivres: boolean;
  canAccessPresenca: boolean;
  canAccessAllPresenca: boolean;
  userRole: UserRole | null;
  isLoading: boolean;
}

/**
 * Hook para verificar permissões baseadas em roles do usuário
 */
export function useRolePermissions(): RolePermissions {
  const { user } = useUser();
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchUserRole() {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        // Tentar obter role do JWT primeiro
        const roleFromJWT = user.app_metadata?.role;
        
        if (roleFromJWT) {
          setUserRole(roleFromJWT as UserRole);
          setIsLoading(false);
          return;
        }

        // Fallback: consultar o banco
        const supabase = createClient();
        const { data, error } = await supabase
          .from('users')
          .select('role')
          .eq('user_id', user.id)
          .single();

        if (error) {
          console.error('Erro ao buscar role do usuário:', error);
          setUserRole(null);
        } else {
          setUserRole(data.role as UserRole);
        }
      } catch (error) {
        console.error('Erro ao verificar role:', error);
        setUserRole(null);
      } finally {
        setIsLoading(false);
      }
    }

    fetchUserRole();
  }, [user]);

  // Calcular permissões baseadas na role
  const permissions = calculatePermissions(userRole);

  return {
    ...permissions,
    userRole,
    isLoading,
  };
}

/**
 * Calcula as permissões específicas baseadas na role do usuário
 */
function calculatePermissions(role: UserRole | null): Omit<RolePermissions, 'userRole' | 'isLoading'> {
  if (!role) {
    return {
      canAccessTurmas: false,
      canAccessAllTurmas: false,
      canAccessAulasLivres: false,
      canAccessPresenca: false,
      canAccessAllPresenca: false,
    };
  }

  switch (role) {
    case 'admin':
      return {
        canAccessTurmas: true,
        canAccessAllTurmas: true,      // Admins veem todas as turmas
        canAccessAulasLivres: true,    // Apenas admins têm acesso
        canAccessPresenca: true,
        canAccessAllPresenca: true,    // Admins veem todos os dados de presença
      };

    case 'instructor':
      return {
        canAccessTurmas: true,
        canAccessAllTurmas: false,     // Instrutores veem apenas suas turmas
        canAccessAulasLivres: false,   // Instrutores não têm acesso
        canAccessPresenca: true,
        canAccessAllPresenca: false,   // Instrutores veem apenas dados das aulas/turmas que ministram
      };

    case 'student':
      return {
        canAccessTurmas: false,        // Estudantes não têm acesso a turmas
        canAccessAllTurmas: false,
        canAccessAulasLivres: false,   // Estudantes não têm acesso
        canAccessPresenca: false,      // Estudantes não têm acesso à gestão de presença
        canAccessAllPresenca: false,
      };

    default:
      return {
        canAccessTurmas: false,
        canAccessAllTurmas: false,
        canAccessAulasLivres: false,
        canAccessPresenca: false,
        canAccessAllPresenca: false,
      };
  }
}

/**
 * Hook para verificar se o usuário pode acessar turmas específicas
 */
export function useInstructorTurmasAccess() {
  const { user } = useUser();
  const [instructorTurmas, setInstructorTurmas] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchInstructorTurmas() {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        const supabase = createClient();
        
        // Buscar turmas onde o usuário é instrutor
        const { data, error } = await supabase
          .from('class_groups')
          .select('id')
          .eq('instructor_id', user.id);

        if (error) {
          console.error('Erro ao buscar turmas do instrutor:', error);
          setInstructorTurmas([]);
        } else {
          setInstructorTurmas(data.map(turma => turma.id));
        }
      } catch (error) {
        console.error('Erro ao verificar turmas do instrutor:', error);
        setInstructorTurmas([]);
      } finally {
        setIsLoading(false);
      }
    }

    fetchInstructorTurmas();
  }, [user]);

  return {
    instructorTurmas,
    isLoading,
    canAccessTurma: (turmaId: string) => instructorTurmas.includes(turmaId),
  };
}

/**
 * Hook para verificar se o instrutor pode acessar dados de presença de uma aula específica
 */
export function useInstructorPresencaAccess() {
  const { user } = useUser();

  const canAccessPresencaData = async (classId: string): Promise<boolean> => {
    if (!user) return false;

    try {
      const supabase = createClient();

      // Verificar se o usuário é instrutor da aula específica
      const { data: classData, error: classError } = await supabase
        .from('classes')
        .select('instructor_id, class_group_id')
        .eq('id', classId)
        .single();

      if (classError || !classData) {
        console.error('Erro ao buscar dados da aula:', classError);
        return false;
      }

      // Se o instrutor ministra a aula diretamente
      if (classData.instructor_id === user.id) {
        return true;
      }

      // Se a aula pertence a uma turma que o instrutor ministra
      if (classData.class_group_id) {
        const { data: turmaData, error: turmaError } = await supabase
          .from('class_groups')
          .select('instructor_id')
          .eq('id', classData.class_group_id)
          .single();

        if (turmaError || !turmaData) {
          console.error('Erro ao buscar dados da turma:', turmaError);
          return false;
        }

        return turmaData.instructor_id === user.id;
      }

      return false;
    } catch (error) {
      console.error('Erro ao verificar acesso à presença:', error);
      return false;
    }
  };

  /**
   * Função para obter os IDs das turmas que o instrutor é responsável
   * Útil para construir filtros SQL eficientes
   */
  const getInstructorFilters = async (): Promise<{
    instructorId: string;
    classGroupIds: string[];
  } | null> => {
    if (!user) return null;

    try {
      const supabase = createClient();
      
      // Buscar turmas onde o usuário é instrutor responsável
      const { data: classGroups, error } = await supabase
        .from('class_groups')
        .select('id')
        .eq('instructor_id', user.id)
        .eq('status', 'active');

      if (error) {
        console.error('Erro ao buscar turmas do instrutor:', error);
        return {
          instructorId: user.id,
          classGroupIds: []
        };
      }

      return {
        instructorId: user.id,
        classGroupIds: classGroups.map(group => group.id)
      };
    } catch (error) {
      console.error('Erro ao obter filtros do instrutor:', error);
      return {
        instructorId: user.id,
        classGroupIds: []
      };
    }
  };

  return {
    canAccessPresencaData,
    getInstructorFilters,
  };
}

/**
 * Hook para processar filtros especiais que contêm _instructorFilter
 * Aplicado em queries que precisam filtrar por instrutor + turmas que ele é responsável
 */
export function useInstructorFilterProcessor() {
  const { user } = useUser();

  const processFilters = async (filters: any): Promise<any> => {
    if (!filters || !filters._instructorFilter) {
      return filters;
    }

    const { _instructorFilter, ...otherFilters } = filters;
    const { instructorId, classGroupIds } = _instructorFilter;

    // Se não há turmas, retornar apenas filtro por instrutor
    if (!classGroupIds || classGroupIds.length === 0) {
      return {
        ...otherFilters,
        instructor_id: instructorId
      };
    }

    // Se há turmas, retornar filtro OR para incluir aulas diretas e de turmas
    return {
      ...otherFilters,
      _or: [
        { instructor_id: instructorId },
        { class_group_id: { in: classGroupIds } }
      ]
    };
  };

  const buildSupabaseFilter = (supabaseQuery: any, filters: any): any => {
    if (!filters || !filters._instructorFilter) {
      return supabaseQuery;
    }

    const { instructorId, classGroupIds } = filters._instructorFilter;

    // Se não há turmas, usar filtro simples
    if (!classGroupIds || classGroupIds.length === 0) {
      return supabaseQuery.eq('instructor_id', instructorId);
    }

    // Se há turmas, usar filtro OR
    return supabaseQuery.or(`instructor_id.eq.${instructorId},class_group_id.in.(${classGroupIds.join(',')})`);
  };

  return {
    processFilters,
    buildSupabaseFilter,
  };
} 