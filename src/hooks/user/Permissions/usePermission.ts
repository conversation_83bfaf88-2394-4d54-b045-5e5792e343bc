'use client';

import { ResourceType, ActionType } from '@/services/permissions/types/permission-types';
import { useCachedPermissionCheck } from './useCachedPermissionCheck';
import { usePermissionCheck } from './usePermissionCheck';
import { useUserRole } from './useUserRole';

/**
 * Hook unificado para verificação de permissões
 * Combina a verificação baseada em cache (mais precisa) com a verificação local baseada em roles (mais rápida)
 * 
 * @param resource Recurso a ser verificado (ex: 'user', 'profile', 'branch')
 * @param action Ação a ser verificada (ex: 'view', 'edit', 'delete')
 * @param targetId ID do recurso alvo (opcional, para verificações que dependem do alvo)
 * @param checkMode Modo de verificação ('cached' para verificação completa com servidor, 'local' para verificação rápida local)
 * @returns Objeto com o resultado da verificação de permissão
 */
export function usePermission(
  resource: ResourceType,
  action: ActionType,
  targetId?: string,
  checkMode: 'cached' | 'local' = 'cached'
) {
  // Verifica se o alvo é o próprio usuário
  const { role, isAdmin } = useUserRole();
  
  // Obter verificação local baseada apenas na role
  const { isAllowed: isAllowedLocal, isLoading: isLoadingLocal } = usePermissionCheck(
    resource, 
    action, 
    targetId ? false : true
  );
  
  // Obter verificação completa via cache/servidor
  const { 
    isAllowed: isAllowedCache, 
    isLoading: isLoadingCache,
    permissionCheck
  } = useCachedPermissionCheck(resource, action, targetId);
  
  // Se solicitou modo local, usa apenas a verificação baseada em role
  if (checkMode === 'local') {
    return {
      isAllowed: isAllowedLocal,
      isLoading: isLoadingLocal,
      permissionCheck: { granted: isAllowedLocal }
    };
  }
  
  // Caso contrário, usa a verificação completa via cache/servidor
  return {
    isAllowed: isAllowedCache,
    isLoading: isLoadingCache,
    permissionCheck
  };
} 