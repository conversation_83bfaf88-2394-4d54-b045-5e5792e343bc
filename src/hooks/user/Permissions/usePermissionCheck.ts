'use client';

import { useEffect, useState, useMemo } from 'react';
import { useUserRole } from './useUserRole';
import { policyMap } from '@/services/permissions/policies/default-policies';
import { ResourceType, ActionType } from '@/services/permissions/types/permission-types';

export function usePermissionCheck(
  resource: ResourceType,
  action: ActionType,
  isSelf: boolean = false
) {
  const { role, isLoading } = useUserRole();
  
  const isAllowed = useMemo(() => {
    if (isLoading || !role) return false;
    
    const policy = policyMap[role];
    if (!policy) return false;
    
    const permission = policy.permissions.find(
      p => p.resource === resource && p.action === action
    );
    if (!permission) return false;
    
    const condition = permission.condition;
    switch (condition) {
      case 'any': return true;
      case 'self': return isSelf;
      case 'not_self': return !isSelf;
      default: return false;
    }
  }, [resource, action, role, isLoading, isSelf]);

  return {
    isAllowed,
    isLoading,
  };
} 