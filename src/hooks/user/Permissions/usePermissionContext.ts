'use client';

import { useQuery } from '@tanstack/react-query';
import { PermissionContext } from '@/services/permissions/types/permission-types';
import { useUserMetadata } from '@/hooks/user/Auth';
import { CACHE_KEYS } from '@/constants/cache-keys';

export function usePermissionContext(targetUserId?: string) {
  const { metadata, isLoading: isLoadingMetadata } = useUserMetadata();
  const currentUserId = metadata?.id;

  const {
    data: permissionContext,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: CACHE_KEYS.PERMISSION_CONTEXT(currentUserId, targetUserId),
    queryFn: async (): Promise<PermissionContext | null> => {
      if (!currentUserId) return null;
      
      const { getPermissionContext } = await import('@/services/permissions/contexts/permission-context');
      return getPermissionContext(currentUserId, targetUserId);
    },
    enabled: !!currentUserId,
    staleTime: 5 * 60 * 1000, 
    gcTime: 30 * 60 * 1000, 
  });

  return {
    permissionContext,
    isLoading: isLoading || isLoadingMetadata,
    error,
    refetch
  };
}

export function useBasicPermissionContext() {
  const { metadata, isLoading: isLoadingMetadata } = useUserMetadata();
  const currentUserId = metadata?.id;

  const {
    data: permissionContext,
    isLoading,
    error
  } = useQuery({
    queryKey: CACHE_KEYS.BASIC_PERMISSION_CONTEXT(currentUserId),
    queryFn: async (): Promise<PermissionContext | null> => {
      if (!currentUserId) return null;
      
      // Importar dinamicamente para evitar erros com Server Components
      const { getBasicPermissionContext } = await import('@/services/permissions/contexts/permission-context');
      return getBasicPermissionContext(currentUserId);
    },
    enabled: !!currentUserId,
    staleTime: 15 * 60 * 1000, // 15 minutos
    gcTime: 60 * 60 * 1000, // 1 hora
  });

  return {
    permissionContext,
    isLoading: isLoading || isLoadingMetadata,
    error
  };
} 