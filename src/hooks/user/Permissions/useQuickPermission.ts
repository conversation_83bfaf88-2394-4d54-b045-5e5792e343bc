'use client';

import { useUserRole } from './useUserRole';

/**
 * Hook para verificações rápidas e comuns de permissão
 * Baseado apenas nas regras de role, sem chamar o servidor
 * 
 * @returns Objeto com verificações comuns de permissão
 */
export function useQuickPermission() {
  const { 
    role, 
    isAdmin, 
    isTeacher, 
    isInstructor, 
    isStudent,
    hasAdminPermissions
  } = useUserRole();
  
  return {
    // Permissões administrativas
    canAccessDashboard: hasAdminPermissions,
    canManageUsers: isAdmin,
    canManageSystem: isAdmin,
    
    // Permissões de professor
    canGradeBelts: isAdmin || isTeacher,
    canManageClasses: isAdmin || isTeacher || isInstructor,
    canRegisterAttendance: hasAdminPermissions,
    
    // Permissões gerais
    canEditOwnProfile: true,
    
    // Informações de role
    role,
    isAdmin,
    isTeacher,
    isInstructor,
    isStudent,
    hasAdminPermissions
  };
} 