/**
 * Exportações centralizadas dos hooks de perfil do usuário
 * 
 * useUserProfile: Transforma metadados em perfil de usuário formatado
 */

export { useUserProfile } from './useUserProfile'
export { useUserAvatar } from './useUserAvatarContext'
export { useUpdateProfileCache } from './useUpdateProfileCache'
export { useQueryCacheManager } from './useQueryCacheManager'
export { useProfilePrefetch } from './useProfilePrefetch'
export { useFormatProfileData } from './useFormatProfileData'
export { useProfileWithGuardian } from './useProfileWithGuardian'