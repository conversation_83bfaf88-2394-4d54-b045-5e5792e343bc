import { useEffect, useState } from 'react';
import { formatPhoneFields } from '../../form/usePhoneFormat';

export function formatAddressFields(userData: any): any {
  if (!userData) return null;
  const formattedData = { ...userData };
  const addressFields = [
    'street',
    'street_number',
    'complement',
    'neighborhood',
    'city',
    'state',
    'postal_code'
  ];
  const hasAddressFields = addressFields.some(field => 
    formattedData[field] !== undefined && formattedData[field] !== null
  );
  if (!hasAddressFields && formattedData.address) {
    console.log('[PROFILE-FORMAT] Convertendo campo address legado para campos individuais');
    try {
      const addressParts = formattedData.address.split(/[\n,]+/).map((part: string) => part.trim());
      if (addressParts.length > 0) {
        const streetMatch = addressParts[0].match(/^(.*?)\s*(\d+)?$/);
        if (streetMatch) {
          formattedData.street = streetMatch[1].trim();
          if (streetMatch[2]) {
            formattedData.street += ` ${streetMatch[2]}`;
          }
        }
        if (addressParts.length > 1 && /^\d+[a-zA-Z]?$/.test(addressParts[1].trim())) {
          formattedData.street_number = addressParts[1].trim();
        }
        for (const part of addressParts) {
          if (part.includes('-') && !part.toLowerCase().includes('cep')) {
            const cityState = part.split('-').map((item: string) => item.trim());
            if (cityState.length >= 1) formattedData.city = cityState[0];
            if (cityState.length >= 2) formattedData.state = cityState[1];
          } else if (part.toLowerCase().includes('cep')) {
            const cepMatch = part.match(/(\d{5}-?\d{3})/);
            if (cepMatch) {
              formattedData.postal_code = cepMatch[1];
            }
          } else if (part.includes('Quadra') || part.includes('Bloco') || 
                     part.includes('Apto') || part.includes('Casa')) {
            formattedData.complement = part;
          } else if (!formattedData.neighborhood && 
                    !part.match(/^\d+[a-zA-Z]?$/) && 
                    !part.includes('-') && 
                    !part.toLowerCase().includes('cep') && 
                    part !== addressParts[0]) {
            formattedData.neighborhood = part;
          }
        }
      }
    } catch (error) {
      console.error('[PROFILE-FORMAT] Erro ao converter campo address:', error);
    }
  }
  addressFields.forEach(field => {
    if (formattedData[field] === undefined || formattedData[field] === null) {
      formattedData[field] = '';
    }
    if (typeof formattedData[field] !== 'string') {
      formattedData[field] = String(formattedData[field]);
    }
  });
  return formattedData;
}

export function useFormatProfileData(userData: any) {
  const [formattedData, setFormattedData] = useState<any>(null);
  const [isFormatting, setIsFormatting] = useState(true);
  useEffect(() => {
    if (!userData) {
      setFormattedData(null);
      setIsFormatting(false);
      return;
    }
    try {
      let formatted = formatPhoneFields(userData);
      formatted = formatAddressFields(formatted);
      console.log('[PROFILE-FORMAT] Dados formatados:', {
        originalPhone: userData.phone,
        formattedPhone: formatted.formattedPhone,
        originalEmergencyPhone: userData.emergency_phone,
        formattedEmergencyPhone: formatted.formattedEmergencyPhone,
        addressFields: {
          street: formatted.street,
          street_number: formatted.street_number,
          neighborhood: formatted.neighborhood,
          city: formatted.city,
          state: formatted.state
        }
      });
      setFormattedData(formatted);
    } catch (error) {
      console.error('[PROFILE-FORMAT] Erro ao formatar dados:', error);
      setFormattedData(userData);
    } finally {
      setIsFormatting(false);
    }
  }, [userData]);
  return {
    data: formattedData,
    isFormatting,
    refreshFormatting: () => {
      if (userData) {
        setIsFormatting(true);
        let formatted = formatPhoneFields(userData);
        formatted = formatAddressFields(formatted);
        setFormattedData(formatted);
        setIsFormatting(false);
      }
    }
  };
} 