'use client';

import { useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { prefetchUserProfileData } from '@/services/user/profile-prefetch';

/**
 * Hook que fornece uma função para realizar o prefetch de dados do perfil do usuário
 * Deve ser usado após o login bem-sucedido
 */
export function useProfilePrefetch() {
  const queryClient = useQueryClient();
  
  const prefetchUserProfile = useCallback(async (userId: string): Promise<boolean> => {
    if (!userId) {
      console.error('[useProfilePrefetch] ID de usuário não fornecido');
      return false;
    }
    
    try {
      return await prefetchUserProfileData(userId, queryClient);
    } catch (error) {
      console.error('[useProfilePrefetch] Erro ao prefetch do perfil:', error);
      return false;
    }
  }, [queryClient]);
  
  return {
    prefetchUserProfile
  };
} 