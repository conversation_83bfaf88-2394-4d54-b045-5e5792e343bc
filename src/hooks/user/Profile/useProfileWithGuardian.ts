import { useQuery, useQueryClient } from '@tanstack/react-query';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { loadUserProfileClient } from '@/services/user/profile-access-client';
import { useFormatProfileData } from './useFormatProfileData';
import { GuardianClientUtils } from '@/utils/guardian-utils';
import { UserWithGuardianInfo, MinorAccountDisplayInfo } from '@/src/types/guardian';

interface UseProfileWithGuardianReturn {
  data: any | null;
  formattedData: any | null;
  guardianDisplayInfo: MinorAccountDisplayInfo | null;
  isLoading: boolean;
  isFormatting: boolean;
  error: string | null;
  refetch: () => void;
  invalidate: () => void;
}

/**
 * Hook React Query otimizado para carregar perfil com dados de responsável
 * Combina carregamento de dados + formatação + informações de responsável
 */
export function useProfileWithGuardian(userId: string): UseProfileWithGuardianReturn {
  const queryClient = useQueryClient();

  // Query principal para carregar dados do perfil
  const {
    data: rawData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: CACHE_KEYS.VIEWED_USER_PROFILE(userId),
    queryFn: async () => {
      const result = await loadUserProfileClient(userId);
      if (result.error || !result.data) {
        throw new Error(result.error || 'Erro ao carregar perfil');
      }
      return result.data;
    },
    enabled: !!userId,
    staleTime: 3 * 60 * 1000, // 3 minutos
    gcTime: 15 * 60 * 1000, // 15 minutos
    retry: (failureCount, error) => {
      // Não fazer retry para erros de autenticação/autorização
      if (error?.message?.includes('unauthorized') || error?.message?.includes('403')) {
        return false;
      }
      return failureCount < 2;
    }
  });

  // Formatação dos dados usando hook existente
  const { data: formattedData, isFormatting } = useFormatProfileData(rawData);

  // Query derivada para informações de responsável
  const { data: guardianDisplayInfo } = useQuery({
    queryKey: [...CACHE_KEYS.GUARDIAN.DISPLAY_INFO(userId), 'profile-integration'],
    queryFn: () => {
      if (!formattedData) return null;
      
      // Verificar se temos dados suficientes para criar UserWithGuardianInfo
      const userWithGuardianInfo: UserWithGuardianInfo = {
        id: formattedData.id,
        first_name: formattedData.first_name,
        last_name: formattedData.last_name,
        email: formattedData.email,
        birth_date: formattedData.birth_date,
        is_minor: formattedData.is_minor || false,
        is_guardian_account: formattedData.is_guardian_account || false,
        managed_student_id: formattedData.managed_student_id,
        guardian_name: formattedData.guardian_name,
        guardian_email: formattedData.guardian_email,
        guardian_phone: formattedData.guardian_phone,
        guardian_relationship: formattedData.guardian_relationship,
        guardian_document: formattedData.guardian_document
      };

      return GuardianClientUtils.getMinorAccountDisplayInfo(userWithGuardianInfo);
    },
    enabled: !!formattedData,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 30 * 60 * 1000 // 30 minutos
  });

  // Função para invalidar cache relacionado
  const invalidate = () => {
    queryClient.invalidateQueries({ queryKey: CACHE_KEYS.VIEWED_USER_PROFILE(userId) });
    queryClient.invalidateQueries({ queryKey: CACHE_KEYS.GUARDIAN.INFO(userId) });
    queryClient.invalidateQueries({ queryKey: CACHE_KEYS.GUARDIAN.DISPLAY_INFO(userId) });
    queryClient.invalidateQueries({ queryKey: CACHE_KEYS.GUARDIAN.STATUS(userId) });
  };

  return {
    data: rawData,
    formattedData,
    guardianDisplayInfo: guardianDisplayInfo || null,
    isLoading,
    isFormatting,
    error: error?.message || null,
    refetch,
    invalidate
  };
}

/**
 * Hook para invalidar cache de perfil e responsável de forma coordenada
 */
export function useInvalidateProfileGuardianCache() {
  const queryClient = useQueryClient();

  return (userId: string) => {
    // Invalidar todas as queries relacionadas ao perfil e responsável
    queryClient.invalidateQueries({ queryKey: CACHE_KEYS.VIEWED_USER_PROFILE(userId) });
    queryClient.invalidateQueries({ queryKey: CACHE_KEYS.GUARDIAN.INFO(userId) });
    queryClient.invalidateQueries({ queryKey: CACHE_KEYS.GUARDIAN.DISPLAY_INFO(userId) });
    queryClient.invalidateQueries({ queryKey: CACHE_KEYS.GUARDIAN.STATUS(userId) });
    
    // Invalidar listas que podem incluir este usuário
    queryClient.invalidateQueries({ queryKey: CACHE_KEYS.GUARDIAN.MINORS_WITHOUT_GUARDIAN });
    queryClient.invalidateQueries({ queryKey: CACHE_KEYS.GUARDIAN.MINORS_WITH_GUARDIAN });
  };
} 