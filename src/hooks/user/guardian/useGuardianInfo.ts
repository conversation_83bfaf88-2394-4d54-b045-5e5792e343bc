import { useQuery } from '@tanstack/react-query';
import { 
  UserWithGuardianInfo, 
  MinorAccountDisplayInfo
} from '@/src/types/guardian';
import { GuardianClientUtils } from '@/utils/guardian-utils';
import { 
  getUserWithGuardianInfoAction,
  checkGuardianConfiguredAction,
  getMinorStatusAction
} from '@/app/(dashboard)/perfil/actions/guardian-actions';
import { CACHE_KEYS } from '@/constants/cache-keys';

/**
 * Hook para buscar e gerenciar informações de responsáveis usando React Query
 */
export function useGuardianInfo(userId: string) {
  const {
    data: user,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: CACHE_KEYS.GUARDIAN.INFO(userId),
    queryFn: async () => {
      const result = await getUserWithGuardianInfoAction(userId);
      if (!result.success) {
        throw new Error(result.error || 'Erro ao carregar dados do usuário');
      }
      return result.data;
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 30 * 60 * 1000, // 30 minutos
    retry: (failureCount, error) => {
      // Não fazer retry para erros de permissão
      if (error?.message?.includes('não autorizado') || error?.message?.includes('unauthorized')) {
        return false;
      }
      return failureCount < 3;
    }
  });

  // Query para informações de exibição (derivada dos dados do usuário)
  const {
    data: displayInfo,
    isLoading: isLoadingDisplayInfo
  } = useQuery({
    queryKey: CACHE_KEYS.GUARDIAN.DISPLAY_INFO(userId),
    queryFn: () => {
      if (!user) return null;
      return GuardianClientUtils.getMinorAccountDisplayInfo(user);
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000,
    gcTime: 30 * 60 * 1000
  });

  return {
    user,
    displayInfo,
    loading: isLoading || isLoadingDisplayInfo,
    error: error?.message || null,
    refetch
  };
}

/**
 * Hook para verificar se um usuário tem responsável configurado usando React Query
 */
export function useGuardianStatus(userId: string) {
  const {
    data: hasGuardian,
    isLoading,
    error
  } = useQuery({
    queryKey: CACHE_KEYS.GUARDIAN.STATUS(userId),
    queryFn: async () => {
      const result = await checkGuardianConfiguredAction(userId);
      if (!result.success) {
        throw new Error(result.error || 'Erro ao verificar status do responsável');
      }
      return result.hasGuardian || false;
    },
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutos (menos volátil)
    gcTime: 60 * 60 * 1000, // 1 hora
    retry: 2
  });

  return {
    hasGuardian,
    loading: isLoading,
    error: error?.message || null
  };
}

/**
 * Hook para verificar se um usuário é menor de idade usando React Query
 * Busca o status diretamente do banco de dados através da coluna is_minor
 * 
 * @param userId ID do usuário para verificar status de menor
 * @returns Objeto com isMinor, loading e error
 */
export function useMinorStatus(userId: string) {
  const {
    data,
    isLoading,
    error
  } = useQuery({
    queryKey: CACHE_KEYS.GUARDIAN.MINOR_STATUS(userId),
    queryFn: async () => {
      const result = await getMinorStatusAction(userId);
      if (!result.success) {
        throw new Error(result.error || 'Erro ao verificar status de menor de idade');
      }
      return result.data;
    },
    enabled: !!userId,
    staleTime: 24 * 60 * 60 * 1000, // 24 horas (status de menor não muda frequentemente)
    gcTime: 7 * 24 * 60 * 60 * 1000, // 7 dias
    retry: (failureCount, error) => {
      // Não fazer retry para erros de permissão
      if (error?.message?.includes('não autorizado') || error?.message?.includes('unauthorized')) {
        return false;
      }
      return failureCount < 2;
    }
  });

  return {
    isMinor: data?.isMinor || false,
    loading: isLoading,
    error: error?.message || null
  };
} 