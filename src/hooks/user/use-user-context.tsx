'use client';

import { 
  createContext, 
  useContext, 
  useState, 
  useEffect, 
  ReactNode,
  useCallback,
  useRef
} from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter, usePathname } from 'next/navigation';
import { 
  getUserProfileClient, 
} from '@/services/user/user-client';
import { UserProfile } from '@/services/user';
import { formatAddressFields } from '@/utils/address-utils';
import { CACHE_KEYS } from '@/constants/cache-keys';

// Chaves de consulta - centralizadas
// DEPRECATED: Use CACHE_KEYS.AUTH_USER_PROFILE instead
export const USER_PROFILE_QUERY_KEY = CACHE_KEYS.USER_METADATA
export const USER_ADMIN_STATUS_KEY = ['user-admin-status'];

// Tipos
interface UserContextType {
  // Dados do usuário
  profile: UserProfile | null;
  isLoading: boolean;
  error: Error | null;
  
  // Avatar específico
  avatarUrl: string | null;
  
  // Métodos
  updateAvatar: (newAvatarUrl: string) => void;
  refetchUserData: () => Promise<void>;
}

// Contexto
const UserContext = createContext<UserContextType | undefined>(undefined);

// Hook personalizado para usar o contexto
export function useUser() {
  const context = useContext(UserContext);
  
  if (!context) {
    return {
      profile: null,
      isLoading: false,
      error: null,
      avatarUrl: null,
      updateAvatar: () => {
        console.warn('Tentativa de atualizar avatar fora do contexto. Operação ignorada.');
      },
      refetchUserData: async () => {
        console.warn('Tentativa de atualizar dados do usuário fora do contexto. Operação ignorada.');
      }
    };
  }
  
  return context;
}

// Hooks específicos para casos de uso comuns
export function useUserAvatar() {
  const { avatarUrl, updateAvatar } = useUser();
  return { avatarUrl, updateAvatar };
}

export function useUserProfile() {
  const { profile, isLoading, error, refetchUserData } = useUser();
  
  return {
    profile,
    isLoading,
    isFetching: isLoading,
    error,
    refetch: refetchUserData
  };
}

// Hook para atualização do cache de perfil (migrado de useUpdateProfileCache)
export function useUpdateProfileCache() {
  const queryClient = useQueryClient();
  
  const updateAddressCache = useCallback((userId: string, addressData: any) => {
    if (!userId || !queryClient) return;
    try {
      // Verificar tanto a chave antiga quanto a nova para garantir compatibilidade
      let currentData = queryClient.getQueryData(USER_PROFILE_QUERY_KEY);
      
      if (!currentData) {
        console.warn('[CACHE] Dados de perfil não encontrados no cache para atualização de endereço');
        return;
      }
      
      const updatedData = { ...currentData } as any;
      const addressFields = [
        'street',
        'street_number',
        'complement',
        'neighborhood',
        'city',
        'state',
        'postal_code',
        'address'
      ];
      
      addressFields.forEach(field => {
        if (addressData[field] !== undefined) {
          updatedData[field] = addressData[field];
        }
      });
      
      const processedData = formatAddressFields(updatedData);
      
      // Atualizar o cache com os novos dados
      queryClient.setQueryData(USER_PROFILE_QUERY_KEY, processedData);
      
      // Invalidar a query para forçar atualização
      queryClient.invalidateQueries({
        queryKey: USER_PROFILE_QUERY_KEY
      });
    } catch (error) {
      console.error('[CACHE] Erro ao atualizar cache de endereço:', error);
    }
  }, [queryClient]);
  
  const invalidateProfileCache = useCallback((userId: string) => {
    if (!userId || !queryClient) return;
    
    // Invalidar o cache de metadados
    queryClient.invalidateQueries({
      queryKey: USER_PROFILE_QUERY_KEY
    });
    
    // Recarregar os dados
    setTimeout(() => {
      queryClient.refetchQueries({
        queryKey: USER_PROFILE_QUERY_KEY,
        type: 'active'
      });
    }, 100);
    
  }, [queryClient]);
  
  return {
    updateAddressCache,
    invalidateProfileCache
  };
}

// Hook para gerenciamento de cache de queries (migrado de useQueryCacheManager)
export function useQueryCacheManager() {
  const queryClient = useQueryClient();
  
  useEffect(() => {
    // Inicializar o serviço de cache se necessário
    import('@/services/cache').then(({ cacheService }) => {
      cacheService.initialize(queryClient);
    });
  }, [queryClient]);
  
  return {
    prefetchUserData: (userId: string, userData: any) => {
      import('@/services/cache').then(({ cacheService }) => {
        cacheService.prefetchUserTabsData(userId, userData);
      });
    },
    setQueryData: <T,>(queryKey: string[], data: T) => {
      queryClient.setQueryData(queryKey, data);
    },
    getQueryData: <T,>(queryKey: string[]): T | undefined => {
      return queryClient.getQueryData<T>(queryKey);
    },
    isQueryActive: (queryKey: string[]): boolean => {
      const queries = queryClient.getQueryCache().findAll({ 
        queryKey: queryKey as readonly unknown[] 
      });
      return queries.length > 0;
    }
  };
}

// Hook para prefetch de perfil de usuário (migrado de useProfilePrefetch)
export function useProfilePrefetch() {
  const queryClient = useQueryClient();
  
  const prefetchUserProfile = useCallback(async (userId: string): Promise<boolean> => {
    if (!userId) {
      console.error('[useProfilePrefetch] ID de usuário não fornecido');
      return false;
    }
    
    try {
      const { prefetchUserProfileData } = await import('@/services/user/profile-prefetch');
      return await prefetchUserProfileData(userId, queryClient);
    } catch (error) {
      console.error('[useProfilePrefetch] Erro ao prefetch do perfil:', error);
      return false;
    }
  }, [queryClient]);
  
  return {
    prefetchUserProfile
  };
}

// Provider
interface UserProviderProps {
  children: ReactNode;
  initialAvatarUrl?: string | null;
  initialProfile?: UserProfile | null;
}

export function UserProvider({ 
  children, 
  initialAvatarUrl = null,
  initialProfile = null 
}: UserProviderProps) {
  const [profile, setProfile] = useState<UserProfile | null>(initialProfile);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(initialAvatarUrl);
  const [isLoading, setIsLoading] = useState<boolean>(!initialProfile);
  const [error, setError] = useState<Error | null>(null);
  
  // Referência para rastrear se os dados já foram carregados
  const hasLoadedData = useRef<boolean>(!!initialProfile);
  
  const queryClient = useQueryClient();
  const router = useRouter();
  const pathname = usePathname();
  
  // Atualização do avatar com propagação para o cache
  const updateAvatar = (newAvatarUrl: string) => {
    setAvatarUrl(newAvatarUrl);
    
    // Atualizar o avatar no cache do React Query
    const cachedProfile = queryClient.getQueryData(USER_PROFILE_QUERY_KEY);
    if (cachedProfile) {
      const updatedProfile = {
        ...(cachedProfile as any),
        avatarUrl: newAvatarUrl
      };
      
      queryClient.setQueryData(USER_PROFILE_QUERY_KEY, updatedProfile);
      
      // Atualizar também o perfil local
      if (profile) {
        setProfile({
          ...profile,
          avatarUrl: newAvatarUrl
        });
      }
    }
  };
  
  // Função para buscar dados do usuário
  const fetchUserData = async () => {
    // Não carregue dados de usuário em páginas de autenticação
    const isAuthPage = pathname?.includes('/login') || 
                       pathname?.includes('/reset-password') || 
                       pathname?.includes('/auth/callback');
    
    if (isAuthPage) {
      setIsLoading(false);
      return; // Não carrega dados de usuário em páginas de autenticação
    }

    try {
      setIsLoading(true);

      // Verificar se já temos dados em cache
      const cachedData = queryClient.getQueryData(USER_PROFILE_QUERY_KEY);
      if (cachedData) {
        
        // Atualizar perfil e avatar do cache
        setProfile(cachedData as UserProfile);
        if ((cachedData as UserProfile).avatarUrl && !avatarUrl) {
          setAvatarUrl((cachedData as UserProfile).avatarUrl);
        }
        
        // Garantir que os dados são definidos no estado local
        setProfile(cachedData as UserProfile);
        hasLoadedData.current = true;
        setIsLoading(false);
        return;
      }

      // Buscar dados do usuário sem verificação prévia de sessão
      const { data, error } = await getUserProfileClient();
      
      if (error || !data) {
        console.error('[UserProvider] Erro ao buscar dados do usuário:', error);
        setIsLoading(false);
        return;
      }

      // Formatar dados de endereço se necessário
      const formattedData = formatAddressFields(data);

      // Atualizar perfil e avatar
      setProfile(formattedData);
      if (formattedData.avatarUrl && !avatarUrl) {
        setAvatarUrl(formattedData.avatarUrl);
      }
      
      // Pré-carregar no cache do React Query com persistência
      queryClient.setQueryData(USER_PROFILE_QUERY_KEY, formattedData);
      
      // Configurar staleTime para este queryKey
      queryClient.setDefaultOptions({
        queries: {
          staleTime: 1000 * 60 * 5 // 5 minutos
        }
      });
      
      // Marcar que os dados foram carregados
      hasLoadedData.current = true;
      
    } catch (err: any) {
      console.error('[UserProvider] Erro ao carregar dados do usuário:', err);
      setError(err);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para refetch manual dos dados
  const refetchUserData = async () => {
    // Invalidar o cache para garantir dados frescos
    queryClient.invalidateQueries({
      queryKey: USER_PROFILE_QUERY_KEY
    });
    
    await fetchUserData();
  };
  
  // Ouvir eventos de atualização de perfil
  useEffect(() => {
    const handleProfileUpdated = (event: Event) => {
      const customEvent = event as CustomEvent;
      const userId = customEvent.detail?.userId;
      const fields = customEvent.detail?.fields;
      
      // Verificar se é uma atualização para o usuário atual
      if (profile?.id && userId === profile.id) {
        // Se temos dados específicos, atualizar diretamente
        if (fields && Object.keys(fields).length > 0) {
          // Atualizar o perfil local
          setProfile(prevProfile => {
            if (!prevProfile) return prevProfile;
            
            const updatedProfile = { ...prevProfile };
            
            // Atualizar nome completo se temos fullName diretamente
            if (fields.fullName) {
              updatedProfile.fullName = fields.fullName;
              // Também atualizar first_name e last_name baseado no fullName
              const nameParts = fields.fullName.trim().split(' ');
              if (nameParts.length >= 1) {
                updatedProfile.firstName = nameParts[0];
                if (nameParts.length > 1) {
                  updatedProfile.lastName = nameParts.slice(1).join(' ');
                }
              }
            }
            // Atualizar nome completo se temos first_name ou last_name
            else if (fields.first_name || fields.last_name) {
              const firstName = fields.first_name || prevProfile.firstName;
              const lastName = fields.last_name || prevProfile.lastName;
              updatedProfile.fullName = `${firstName} ${lastName}`.trim();
              updatedProfile.firstName = firstName;
              updatedProfile.lastName = lastName;
            }
            
            // Atualizar outros campos diretamente
            Object.entries(fields).forEach(([key, value]) => {
              if (key !== 'first_name' && key !== 'last_name' && key !== 'fullName') {
                (updatedProfile as any)[key] = value;
              }
            });
            
            return updatedProfile;
          });
          
          // Atualizar também no cache do React Query
          const cachedData = queryClient.getQueryData(USER_PROFILE_QUERY_KEY);
          if (cachedData) {
            queryClient.setQueryData(USER_PROFILE_QUERY_KEY, (oldData: any) => {
              if (!oldData) return oldData;
              
              const updatedData = { ...oldData };
              
              // Mesmo tratamento para o nome completo
              if (fields.fullName) {
                updatedData.fullName = fields.fullName;
                // Também atualizar first_name e last_name baseado no fullName
                const nameParts = fields.fullName.trim().split(' ');
                if (nameParts.length >= 1) {
                  updatedData.firstName = nameParts[0];
                  if (nameParts.length > 1) {
                    updatedData.lastName = nameParts.slice(1).join(' ');
                  }
                }
              }
              else if (fields.first_name || fields.last_name) {
                const firstName = fields.first_name || oldData.firstName;
                const lastName = fields.last_name || oldData.lastName;
                updatedData.fullName = `${firstName} ${lastName}`.trim();
                updatedData.firstName = firstName;
                updatedData.lastName = lastName;
              }
              
              // Atualizar outros campos
              Object.entries(fields).forEach(([key, value]) => {
                if (key !== 'first_name' && key !== 'last_name' && key !== 'fullName') {
                  updatedData[key] = value;
                }
              });
              
              return updatedData;
            });
          }
        } else {
          // Se não temos dados específicos, recarregar todos os dados
          refetchUserData();
        }
      }
    };
    
    // Ouvir eventos de atualização de perfil
    window.addEventListener('profile:updated', handleProfileUpdated);
    
    return () => {
      window.removeEventListener('profile:updated', handleProfileUpdated);
    };
  }, [profile?.id, queryClient]);
  
  // Carregar dados iniciais e reagir a mudanças de rota
  useEffect(() => {
    // Se ainda não carregamos dados ou estamos em uma nova rota, buscar dados
    if (!hasLoadedData.current) {
      fetchUserData();
    } else {
      // Se já temos dados mas estamos em uma nova rota, verificar o cache
      const cachedData = queryClient.getQueryData(USER_PROFILE_QUERY_KEY);
      
      // Se temos dados em cache, usar esses dados
      if (cachedData) {
        setProfile(cachedData as UserProfile);
        if ((cachedData as UserProfile).avatarUrl) {
          setAvatarUrl((cachedData as UserProfile).avatarUrl);
        }
      } else {
        // Se não temos dados em cache, recarregar
        fetchUserData();
      }
    }
  }, [queryClient, pathname]);

  // Valor do contexto
  const contextValue = {
    profile,
    isLoading,
    error,
    avatarUrl,
    updateAvatar,
    refetchUserData
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
} 