'use client';

import { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';

interface SyncResult {
  success: boolean;
  synced: string[];
  errors: string[];
  skipped: string[];
}

interface ConsistencyCheck {
  consistent: boolean;
  differences: string[];
}

/**
 * Hook para gerenciar sincronização de metadados do usuário
 * Permite verificar consistência e forçar sincronização quando necessário
 */
export function useUserMetadataSync(userId: string) {
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();

  // Query para verificar consistência de metadados
  const consistencyQuery = useQuery({
    queryKey: ['user-metadata-consistency', userId],
    queryFn: async (): Promise<ConsistencyCheck> => {
      const response = await fetch(`/api/user/${userId}/metadata/consistency`, {
        method: 'GET',
      });
      
      if (!response.ok) {
        throw new Error('Falha ao verificar consistência dos metadados');
      }
      
      return response.json();
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchOnMount: false,
    refetchOnWindowFocus: false
  });

  /**
   * Força sincronização completa dos metadados do usuário
   */
  const forceSyncMetadata = async (): Promise<SyncResult> => {
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/user/${userId}/metadata/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'force_sync' })
      });
      
      if (!response.ok) {
        throw new Error('Falha ao sincronizar metadados');
      }
      
      const result: SyncResult = await response.json();
      
      // Invalidar cache após sincronização bem-sucedida
      if (result.success) {
        await queryClient.invalidateQueries({ 
          queryKey: ['user-metadata-consistency', userId] 
        });
        
        // Invalidar outros caches relacionados ao usuário
        await queryClient.invalidateQueries({ 
          queryKey: ['user', userId] 
        });
      }
      
      return result;
      
    } catch (error) {
      return {
        success: false,
        synced: [],
        errors: [error instanceof Error ? error.message : 'Erro desconhecido'],
        skipped: []
      };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Sincroniza campos específicos do usuário
   */
  const syncSpecificFields = async (
    fields: string[],
    direction: 'to-auth' | 'from-auth' = 'to-auth'
  ): Promise<SyncResult> => {
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/user/${userId}/metadata/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          action: 'sync_fields',
          fields,
          direction 
        })
      });
      
      if (!response.ok) {
        throw new Error('Falha ao sincronizar campos específicos');
      }
      
      const result: SyncResult = await response.json();
      
      // Invalidar cache após sincronização bem-sucedida
      if (result.success) {
        await queryClient.invalidateQueries({ 
          queryKey: ['user-metadata-consistency', userId] 
        });
      }
      
      return result;
      
    } catch (error) {
      return {
        success: false,
        synced: [],
        errors: [error instanceof Error ? error.message : 'Erro desconhecido'],
        skipped: []
      };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Força uma nova verificação de consistência
   */
  const recheckConsistency = async () => {
    await queryClient.invalidateQueries({ 
      queryKey: ['user-metadata-consistency', userId] 
    });
    
    return consistencyQuery.refetch();
  };

  return {
    // Estado
    isLoading: isLoading || consistencyQuery.isLoading,
    isChecking: consistencyQuery.isFetching,
    
    // Dados de consistência
    consistency: consistencyQuery.data,
    consistencyError: consistencyQuery.error,
    
    // Ações
    forceSyncMetadata,
    syncSpecificFields,
    recheckConsistency,
    
    // Estado da query
    isConsistencyLoading: consistencyQuery.isLoading,
    isConsistencyError: consistencyQuery.isError,
    
    // Indicadores úteis
    hasInconsistencies: consistencyQuery.data ? !consistencyQuery.data.consistent : false,
    inconsistencyCount: consistencyQuery.data?.differences.length || 0
  };
}

/**
 * Hook simplificado apenas para verificar se há inconsistências
 * Útil para componentes que só precisam saber se há problemas
 */
export function useMetadataConsistencyCheck(userId: string) {
  const { consistency, hasInconsistencies, isConsistencyLoading } = useUserMetadataSync(userId);
  
  return {
    hasInconsistencies,
    isLoading: isConsistencyLoading,
    differences: consistency?.differences || [],
    needsSync: hasInconsistencies
  };
} 