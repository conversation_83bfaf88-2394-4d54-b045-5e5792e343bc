export interface TimeElapsed {
  hours: number;
  minutes: number;
  seconds: number;
}

export function calculateTimeElapsed(fromDate: Date, toDate: Date = new Date()): TimeElapsed {
  const diffMs = toDate.getTime() - fromDate.getTime();
  
  return {
    hours: Math.floor(diffMs / (1000 * 60 * 60)),
    minutes: Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60)),
    seconds: Math.floor((diffMs % (1000 * 60)) / 1000)
  };
}

export function formatTimeElapsed(elapsed: TimeElapsed): string {
  const { hours, minutes, seconds } = elapsed;
  
  if (hours > 0) {
    return `${hours}h${minutes > 0 ? ` e ${minutes}min` : ''}`;
  }
  
  if (minutes > 0) {
    return `${minutes} minuto${minutes > 1 ? 's' : ''}`;
  }
  
  if (seconds > 0) {
    return `${seconds} segundo${seconds > 1 ? 's' : ''}`;
  }
  
  return 'poucos segundos';
}

export function createExpirationMessage(
  expirationDate: Date, 
  currentDate: Date = new Date()
): string {
  const elapsed = calculateTimeElapsed(expirationDate, currentDate);
  const timeElapsedText = formatTimeElapsed(elapsed);
  const formattedDate = expirationDate.toLocaleString('pt-BR');
  
  return `QR Code expirado há ${timeElapsedText}. Expirou em ${formattedDate}. Gere um novo QR Code.`;
} 