export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  conversationId?: string;
}

export interface ChatContext {
  userRole?: string;
  tenantId?: string;
  feature?: string;
}

export interface UsageStats {
  messagesUsed: number;
  messagesLimit: number;
  remainingMessages: number;
  canSendMessage: boolean;
  resetDate: string;
}

export interface ChatResponse {
  success: boolean;
  response?: string;
  conversationId?: string;
  timestamp?: string;
  error?: string;
  details?: any;
  usageStats?: UsageStats;
  code?: string; // Para códigos de erro específicos como 'DAILY_LIMIT_EXCEEDED'
}

export class ChatService {
  private static instance: ChatService;
  private baseUrl = '/api/ai/chat';

  static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  async sendMessage(
    message: string,
    conversationId?: string,
    context?: ChatContext
  ): Promise<ChatResponse> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          conversationId,
          context,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.error || 'Erro ao enviar mensagem',
          details: data.details,
        };
      }

      return data;
    } catch (error) {
      console.error('Erro no serviço de chat:', error);
      return {
        success: false,
        error: 'Erro de conexão. Verifique sua internet e tente novamente.',
      };
    }
  }

  /**
   * Converte uma resposta da API em uma mensagem de chat
   */
  createChatMessage(
    content: string,
    role: 'user' | 'assistant',
    conversationId?: string
  ): ChatMessage {
    return {
      id: this.generateMessageId(),
      content,
      role,
      timestamp: new Date(),
      conversationId,
    };
  }

  /**
   * Gera um ID único para a mensagem
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Consulta estatísticas de uso do chat
   */
  async getUsageStats(): Promise<{ success: boolean; usageStats?: UsageStats; error?: string }> {
    try {
      const response = await fetch('/api/ai/usage', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.error || 'Erro ao buscar estatísticas de uso',
        };
      }

      return {
        success: true,
        usageStats: data.usageStats,
      };
    } catch (error) {
      console.error('Erro ao buscar estatísticas de uso:', error);
      return {
        success: false,
        error: 'Erro de conexão ao buscar estatísticas',
      };
    }
  }

  /**
   * Extrai contexto da aplicação para enviar à IA
   */
  static extractContext(): ChatContext {
    // Tentar extrair informações do contexto atual
    const context: ChatContext = {};

    // Extrair feature da URL atual
    if (typeof window !== 'undefined') {
      const pathname = window.location.pathname;
      
      if (pathname.includes('/alunos')) {
        context.feature = 'Gestão de Alunos';
      } else if (pathname.includes('/instrutores')) {
        context.feature = 'Gestão de Instrutores';
      } else if (pathname.includes('/perfil')) {
        context.feature = 'Perfil do Usuário';
      } else if (pathname.includes('/dashboard')) {
        context.feature = 'Dashboard Principal';
      }
    }

    return context;
  }
}

export const chatService = ChatService.getInstance(); 