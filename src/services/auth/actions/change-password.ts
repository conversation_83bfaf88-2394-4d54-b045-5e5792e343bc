'use server';

import { z } from 'zod';
import { createClient } from '@/services/supabase/server';

/**
 * Schema de validação para troca de senha
 */
const changePasswordSchema = z
  .object({
    currentPassword: z
      .string({ required_error: 'Senha atual é obrigatória' })
      .min(8, 'Senha atual inválida'),
    newPassword: z
      .string({ required_error: 'Nova senha é obrigatória' })
      .min(6, 'A nova senha deve ter pelo menos 6 caracteres'),
    confirmPassword: z.string({ required_error: 'Confirme a nova senha' })
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    path: ['confirmPassword'],
    message: 'As senhas não coincidem'
  });

export type ChangePasswordData = z.infer<typeof changePasswordSchema>;

/**
 * Server action: altera a senha de um usuário autenticado.
 * 1. Valida o input com Zod
 * 2. Verifica se o usuário está autenticado
 * 3. Reautentica com a senha atual para garantir que é o próprio usuário
 * 4. Atualiza a senha usando `supabase.auth.updateUser`
 *
 * Retorna { success: boolean; errors?: Record<string, string | string[]> }
 */
export async function changePassword(data: ChangePasswordData) {
  // 1. Validação de entrada
  const result = changePasswordSchema.safeParse(data);
  if (!result.success) {
    return { success: false, errors: result.error.format() } as const;
  }

  try {
    // 2. Criar cliente Supabase autenticado pelo cookie
    const supabase = await createClient();

    // 2.1 Obter usuário atual
    const {
      data: { user },
      error: userError
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      } as const;
    }

    // 3. Reautenticar para garantir que a senha atual está correta
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email: user.email!,
      password: data.currentPassword
    });

    if (signInError) {
      return {
        success: false,
        errors: { currentPassword: 'Senha atual incorreta' }
      } as const;
    }

    // 4. Atualizar senha
    const { error: updateError } = await supabase.auth.updateUser({
      password: data.newPassword
    });

    if (updateError) {
      return {
        success: false,
        errors: { _form: updateError.message }
      } as const;
    }

    return { success: true } as const;
  } catch (error) {
    console.error('Erro ao alterar a senha:', error);
    return {
      success: false,
      errors: { _form: 'Erro inesperado ao alterar a senha' }
    } as const;
  }
} 