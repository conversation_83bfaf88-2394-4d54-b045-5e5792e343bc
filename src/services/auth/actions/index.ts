'use server';

import { headers } from "next/headers";
import { 
  signUpAction as signUpServiceAction,
  signInAction as signInServiceAction,
  forgotPasswordAction as forgotPasswordServiceAction,
  resetPasswordAction,
  signOutAction,
  getUserWelcomeData
} from "@/services/auth/server/auth-service";

export const signUpAction = async (formData: FormData) => {
  const headersList = await headers();
  const origin = headersList.get("origin") || "";
  
  const tenantIdFromForm = formData.get('tenant_id')?.toString() || null;
  
  const tenantId = tenantIdFromForm || headersList.get('x-tenant-id');
  
  return signUpServiceAction(formData, origin, tenantId);
};

export const signInAction = async (formData: FormData) => {
  const headersList = await headers();
  
  let host = headersList.get('host');
  

  if (!host) {
    host = formData.get('host')?.toString() || null;
    console.log("Host obtido do formData:", host);
  } else {
    console.log("Host obtido dos headers:", host);
  }
  
  return signInServiceAction(formData, host);
};

export const forgotPasswordAction = async (formData: FormData) => {
  const headersList = await headers();
  const origin = headersList.get("origin") || "";
  const remoteip = headersList.get("x-forwarded-for") || headersList.get("x-real-ip") || undefined;

  return forgotPasswordServiceAction(formData, origin, remoteip);
};

export { resetPasswordAction, signOutAction, getUserWelcomeData }; 