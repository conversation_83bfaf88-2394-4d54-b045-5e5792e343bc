'use client';

import { getUserWelcomeData } from '@/services/auth/actions/auth-actions';
import { createClient } from '@/services/supabase/client';

export const clientSignInAction = async (formData: FormData) => {
  // Realiza o fluxo de autenticação **no cliente** para que o IP e o
  // user-agent corretos (do usuário) sejam persistidos na tabela
  // auth.sessions no Supabase.
  try {
    const email = formData.get('email')?.toString() || '';
    const password = formData.get('password')?.toString() || '';
    const rememberMe = formData.get('remember_me') === 'true';

    if (!email || !password) {
      return {
        error: 'Campos obrigatórios não preenchidos',
        error_code: 'missing_fields',
      } as const;
    }

    // Utiliza o cliente de browser – desta forma a requisição parte do
    // navegador do usuário, garantindo que o Supabase registre o IP
    // correto na criação da sessão.
    const supabase = createClient();

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error || !data.session) {
      const mappedCode =
        error?.message === 'Invalid login credentials'
          ? 'invalid_credentials'
          : 'unknown_error';

      return {
        error: error?.message || 'Falha no login',
        error_code: mappedCode,
      } as const;
    }

    // Caso o utilizador NÃO marque "Lembrar-me" tornamos o cookie de sessão
    // não persistente (session cookie) removendo atributos de expiração.
    if (!rememberMe && typeof document !== 'undefined') {
      document.cookie
        .split(';')
        .map((c) => c.trim())
        .filter((c) => c.startsWith('sb-'))
        .forEach((cookieStr) => {
          const [name, value] = cookieStr.split('=');
          document.cookie = `${name}=${value}; path=/; SameSite=Lax`;
        });
    }

    // Persistimos alguns metadados em sessionStorage para manter o
    // comportamento anterior da aplicação.
    if (typeof sessionStorage !== 'undefined') {
      sessionStorage.setItem('last_login_time', Date.now().toString());
      if (data.user?.id) {
        sessionStorage.setItem('auth_user_id', data.user.id);
      }
      sessionStorage.setItem('is_fresh_login', 'true');
    }

    return { success: true, user: data.user } as const;
  } catch (error) {
    console.error('Erro no login:', error);
    return {
      error: 'Ocorreu um erro durante o login',
      error_code: 'unknown_error',
    } as const;
  }
};

export const clientSignOutAction = async (): Promise<{ success: boolean; error?: string }> => {
  try {
    // Limpar dados de sessão do cliente
    if (typeof sessionStorage !== 'undefined') {
      sessionStorage.removeItem('last_login_time');
      sessionStorage.removeItem('auth_user_id');
      sessionStorage.removeItem('is_fresh_login');
      // Limpar quaisquer outros dados relacionados à sessão
      sessionStorage.clear();
    }

    // Limpar localStorage se estiver sendo usado para armazenar dados de autenticação
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('supabase.auth.token');
      // Remover qualquer outro dado relacionado à autenticação
    }
    
    // Chamar a API para realizar o logout no servidor
    const response = await fetch('/api/auth/sign-out', {
      method: 'POST',
      headers: {
        'Cache-Control': 'no-cache, no-store'
      },
    });
    
    if (!response.ok) {
      const result = await response.json();
      console.error('Erro na resposta do servidor durante logout:', result);
      return {
        success: false,
        error: result.error || 'Falha ao realizar logout'
      };
    }
    
    // Redirecionar para a página de login após uma breve pausa
    // para permitir que os cookies sejam processados
    setTimeout(() => {
      window.location.href = '/login';
    }, 100);
    
    return { success: true };
    
  } catch (error) {
    console.error('Erro ao realizar logout:', error);
    return {
      success: false,
      error: 'Ocorreu um erro ao realizar logout'
    };
  }
};

export async function getClientUserWelcomeData() {
  try {
    console.log('Iniciando requisição para obter dados do usuário...');
    
    // Adicionar um pequeno delay para garantir que os cookies/localStorage
    // estejam configurados após o login
    await new Promise((resolve) => setTimeout(resolve, 500));
    
    // Implementar retry com backoff exponencial
    const maxRetries = 3;
    let retryCount = 0;
    let lastError = null;
    
    while (retryCount < maxRetries) {
      try {
        if (retryCount > 0) {
          // Esperar com backoff exponencial: 1s, 2s, 4s
          const delayMs = Math.pow(2, retryCount - 1) * 1000;
          console.log(`Tentativa ${retryCount + 1}/${maxRetries} após ${delayMs}ms...`);
          await new Promise(resolve => setTimeout(resolve, delayMs));
        }
        
        // Usar server action diretamente importada
        const userData = await getUserWelcomeData();

        console.log('[Client DEBUG] Raw userData from server:', userData);
        console.log('[Client DEBUG] userData.beltData type:', typeof userData.beltData);
        console.log('[Client DEBUG] userData.beltData value:', userData.beltData);
        console.log('[Client DEBUG] userData.beltLabel:', userData.beltLabel);
        console.log('[Client DEBUG] userData.belt:', userData.belt);
        console.log('[Client DEBUG] userData.stripes:', userData.stripes);

        // Tentar fazer parse do beltData se for string
        if (userData.beltData && typeof userData.beltData === 'string') {
          try {
            const parsed = JSON.parse(userData.beltData);
            console.log('[Client DEBUG] Parsed beltData:', parsed);
          } catch (e) {
            console.error('[Client DEBUG] Failed to parse beltData:', e);
          }
        }

        // Se detectar código de erro indicando cookies inválidos, limpar e tentar novamente
        if ((userData as any).error_code === 'invalid_cookies') {
          console.warn('Cookies de autenticação inválidos detectados. Limpando cookies e tentando novamente...');
          clearSupabaseAuthCookies();
          retryCount++;
          continue; // Tenta novamente
        }

        if ((userData as any).error) {
          console.warn('Aviso ao obter dados do usuário:', (userData as any).error);
        }

        return userData;
      } catch (err) {
        lastError = err;
        retryCount++;
        
        if (retryCount >= maxRetries) {
          console.error(`Todas as ${maxRetries} tentativas falharam.`);
          throw err;
        }
      }
    }
    
    throw lastError || new Error('Erro desconhecido ao obter dados do usuário');
    
  } catch (error) {
    console.error('Erro ao obter dados do usuário:', error);
    return {
      displayName: 'Usuário',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

export const clientForgotPasswordAction = async (
  email: string,
  redirectUrl?: string,
  hcaptchaToken?: string
) => {
  try {
    const requestBody: any = { email };

    if (redirectUrl) {
      requestBody.redirect_url = redirectUrl;
    }

    if (hcaptchaToken) {
      requestBody.hcaptchaToken = hcaptchaToken;
    }

    const response = await fetch('/api/auth/reset-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    const result = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: result.error || 'Falha ao solicitar redefinição de senha',
        error_code: result.error_code || 'unknown_error'
      };
    }

    return {
      success: true,
      ...result
    };
    
  } catch (error) {
    console.error('Erro ao solicitar redefinição de senha:', error);
    return {
      success: false,
      error: 'Ocorreu um erro ao solicitar a redefinição de senha',
      error_code: 'unknown_error'
    };
  }
};

export function clearSupabaseAuthCookies() {
  if (typeof document === 'undefined') return;

  // Remover cookies sb-*
  document.cookie
    .split(';')
    .map(c => c.trim())
    .filter(c => c.startsWith('sb-'))
    .forEach(cookieStr => {
      const name = cookieStr.split('=')[0];
      document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax`;
    });

  // Limpar entradas em localStorage relacionadas
  if (typeof localStorage !== 'undefined') {
    localStorage.removeItem('supabase.auth.token');
  }
} 