'use server';

import { createClient } from '@/services/supabase/server';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

/**
 * Server action para verificar autenticação no middleware
 * Retorna o objeto user se autenticado, null caso contrário
 */
export async function getUserForMiddleware(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  } catch (error) {
    console.error('Erro ao verificar autenticação no middleware:', error);
    return null;
  }
}

/**
 * Server action para verificar sessão no middleware e atualizar cookies
 * Essa função não expõe a API key do Supabase no cliente
 */
export async function checkSessionMiddleware(request: NextRequest): Promise<NextResponse> {
  // Criar uma resposta padrão que será modificada
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });
  
  try {
    // Verificar autenticação usando o Supabase no servidor
    const user = await getUserForMiddleware(request);
    
    // Log para debug
    if (process.env.NODE_ENV === 'development') {
      console.log('Auth Middleware Action - User authenticated:', !!user);
    }
    
    // Se o usuário estiver autenticado, obter a role diretamente do banco de dados
    // para garantir que estamos usando a informação mais recente
    if (user) {
      // Buscar dados do usuário diretamente do banco para garantir que temos a role atualizada
      const supabase = await createClient();
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('id', user.id)
        .single();
      
      // Adicionar informação do usuário como um header na resposta
      response.headers.set('x-auth-user-id', user.id);
      response.headers.set('x-auth-user-email', user.email || '');
      
      // Usar a role do banco de dados se disponível, caso contrário usar a do JWT
      if (!userError && userData) {
        response.headers.set('x-auth-user-role', userData.role || user.app_metadata?.role || '');
        
        // Log para debug de discrepâncias
        if (userData.role !== user.app_metadata?.role) {
          console.warn('Diferença detectada entre role no banco e no JWT:', {
            userId: user.id,
            dbRole: userData.role,
            jwtRole: user.app_metadata?.role
          });
        }
      } else {
        response.headers.set('x-auth-user-role', user.app_metadata?.role || '');
      }
    }
    
    return response;
  } catch (error) {
    console.error('Erro ao verificar sessão no middleware:', error);
    return response;
  }
} 