import { createClient } from "@/services/supabase/server";
import { createAdminClient } from "@/services/supabase/server";
import { redirect } from "next/navigation";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import type { ResponseCookie } from "next/dist/compiled/@edge-runtime/cookies";

export const requireAuth = async (redirectPath = "/login") => {
  const supabase = await createClient();
  
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect(redirectPath);
  }

  return { user };
};

/**
 * Limpa todos os cookies relacionados à autenticação do Supabase.
 * Esta função pode ser chamada por Server Actions.
 * @returns Um array com os nomes dos cookies que foram removidos
 */
export const clearAuthCookies = async (): Promise<string[]> => {
  const cookieStore = await cookies();
  const allCookies = cookieStore.getAll();
  
  // Identificar os cookies relacionados à autenticação do Supabase
  const supabaseCookies = allCookies.filter((cookie: ResponseCookie) => 
    cookie.name.startsWith('sb-') || 
    cookie.name === 'supabase-auth-token' ||
    cookie.name === 'access_token' || 
    cookie.name === 'refresh_token'
  );
  
  // Remover cada cookie
  for (const cookie of supabaseCookies) {
    cookieStore.delete(cookie.name);
  }
  
  return supabaseCookies.map((cookie: ResponseCookie) => cookie.name);
};

/**
 * Adiciona cabeçalhos para remover cookies de autenticação na resposta HTTP.
 * Esta função deve ser usada em conjunto com o NextResponse em Route Handlers.
 * @param response O objeto NextResponse a ser modificado
 * @returns O mesmo objeto NextResponse com os cabeçalhos de cookies atualizados
 */
export const addClearAuthCookiesToResponse = async (response: NextResponse): Promise<NextResponse> => {
  const cookieStore = await cookies();
  const allCookies = cookieStore.getAll();
  
  // Identificar os cookies relacionados à autenticação do Supabase
  const supabaseCookies = allCookies.filter((cookie: ResponseCookie) => 
    cookie.name.startsWith('sb-') || 
    cookie.name === 'supabase-auth-token' ||
    cookie.name === 'access_token' || 
    cookie.name === 'refresh_token'
  );
  
  // Remover cada cookie na resposta
  for (const cookie of supabaseCookies) {
    response.cookies.delete(cookie.name);
  }
  
  // Log para debug
  if (supabaseCookies.length > 0) {
    console.log(`Cookies de autenticação removidos: ${supabaseCookies.map((c: ResponseCookie) => c.name).join(', ')}`);
  }
  
  return response;
};

/**
 * Tipos para sincronização de metadados
 */
export interface UserMetadataSync {
  status?: string;
  role?: string;
  tenant_id?: string;
  branch_id?: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
}

export interface SyncResult {
  success: boolean;
  synced: string[];
  errors: string[];
  skipped: string[];
}

/**
 * Sincroniza o status do usuário da tabela users com o app_metadata do Supabase Auth
 * @param userId ID do usuário a ser sincronizado
 * @returns Promise<boolean> true se a sincronização foi bem-sucedida
 */
export const syncUserStatusWithMetadata = async (userId: string): Promise<boolean> => {
  try {
    // Usar apenas adminClient para evitar problemas com RLS durante o login
    const adminClient = await createAdminClient();
    
    // Buscar o status atual do usuário na tabela users usando adminClient
    // Isso evita problemas com RLS quando tenant_auth.tenant_id() ainda não está estabelecido no JWT
    const { data: userData, error: userError } = await adminClient
      .from('users')
      .select('status')
      .eq('id', userId)
      .single();
    
    if (userError || !userData) {
      console.error('Erro ao buscar status do usuário para sincronização:', userError);
      return false;
    }
    
    // Buscar os dados atuais do usuário no Auth
    const { data: authUser, error: authError } = await adminClient.auth.admin.getUserById(userId);
    
    if (authError || !authUser.user) {
      console.error('Erro ao buscar dados do usuário no Auth:', authError);
      return false;
    }
    
    // Verificar se o status já está sincronizado
    const currentMetadata = authUser.user.app_metadata || {};
    if (currentMetadata.status === userData.status) {
      console.log(`Status já sincronizado para usuário ${userId}: ${userData.status}`);
      return true;
    }
    
    // Sincronizar o status no app_metadata
    const { error: updateError } = await adminClient.auth.admin.updateUserById(userId, {
      app_metadata: {
        ...currentMetadata,
        status: userData.status
      }
    });
    
    if (updateError) {
      console.error('Erro ao sincronizar status no app_metadata:', updateError);
      return false;
    }
    
    console.log(`Status sincronizado para usuário ${userId}: ${userData.status}`);
    return true;
    
  } catch (error) {
    console.error('Erro na sincronização de status:', error);
    return false;
  }
};

/**
 * Sincroniza múltiplos campos de metadados do usuário
 * @param userId ID do usuário
 * @param fields Campos a serem sincronizados
 * @param direction Direção da sincronização: 'to-auth' (padrão) ou 'from-auth'
 * @returns Promise<SyncResult> Resultado detalhado da sincronização
 */
export const syncUserMetadata = async (
  userId: string,
  fields: (keyof UserMetadataSync)[],
  direction: 'to-auth' | 'from-auth' = 'to-auth'
): Promise<SyncResult> => {
  const result: SyncResult = {
    success: true,
    synced: [],
    errors: [],
    skipped: []
  };

  try {
    // Usar apenas adminClient para evitar problemas com RLS
    const adminClient = await createAdminClient();
    
    // Buscar dados do usuário na tabela users usando adminClient
    // Isso evita problemas com RLS quando tenant_auth.tenant_id() ainda não está estabelecido no JWT
    const { data: userData, error: userError } = await adminClient
      .from('users')
      .select('status, role, tenant_id, branch_id, first_name, last_name, avatar_url')
      .eq('id', userId)
      .single();
    
    if (userError || !userData) {
      result.success = false;
      result.errors.push(`Erro ao buscar dados do usuário: ${userError?.message || 'Usuário não encontrado'}`);
      return result;
    }
    
    // Buscar dados do usuário no Auth
    const { data: authUser, error: authError } = await adminClient.auth.admin.getUserById(userId);
    
    if (authError || !authUser.user) {
      result.success = false;
      result.errors.push(`Erro ao buscar dados do Auth: ${authError?.message || 'Usuário não encontrado'}`);
      return result;
    }
    
    const currentMetadata = authUser.user.app_metadata || {};
    const updatedMetadata = { ...currentMetadata };
    let hasChanges = false;
    
    if (direction === 'to-auth') {
      // Sincronizar da tabela users para app_metadata
      for (const field of fields) {
        const userValue = userData[field];
        const metadataValue = currentMetadata[field];
        
        if (userValue !== undefined && userValue !== metadataValue) {
          updatedMetadata[field] = userValue;
          hasChanges = true;
          result.synced.push(`${field}: ${metadataValue} → ${userValue}`);
        } else if (userValue === metadataValue) {
          result.skipped.push(`${field}: já sincronizado (${userValue})`);
        }
      }
      
      // Aplicar mudanças se houver
      if (hasChanges) {
        const { error: updateError } = await adminClient.auth.admin.updateUserById(userId, {
          app_metadata: updatedMetadata
        });
        
        if (updateError) {
          result.success = false;
          result.errors.push(`Erro ao atualizar app_metadata: ${updateError.message}`);
        }
      }
    } else {
      // Sincronizar do app_metadata para tabela users
      const userUpdates: Partial<UserMetadataSync> = {};
      
      for (const field of fields) {
        const metadataValue = currentMetadata[field];
        const userValue = userData[field];
        
        if (metadataValue !== undefined && metadataValue !== userValue) {
          userUpdates[field] = metadataValue;
          hasChanges = true;
          result.synced.push(`${field}: ${userValue} → ${metadataValue}`);
        } else if (metadataValue === userValue) {
          result.skipped.push(`${field}: já sincronizado (${metadataValue})`);
        }
      }
      
      // Aplicar mudanças se houver
      if (hasChanges && Object.keys(userUpdates).length > 0) {
        const { error: updateError } = await adminClient
          .from('users')
          .update(userUpdates)
          .eq('id', userId);
        
        if (updateError) {
          result.success = false;
          result.errors.push(`Erro ao atualizar tabela users: ${updateError.message}`);
        }
      }
    }
    
    console.log(`Sincronização de metadados para usuário ${userId}:`, {
      direction,
      synced: result.synced.length,
      skipped: result.skipped.length,
      errors: result.errors.length
    });
    
  } catch (error) {
    result.success = false;
    result.errors.push(`Erro na sincronização: ${error instanceof Error ? error.message : String(error)}`);
  }

  return result;
};

/**
 * Verifica a consistência entre os metadados do usuário na tabela users e no app_metadata
 * @param userId ID do usuário
 * @param fields Campos a serem verificados
 * @returns Promise<{ consistent: boolean, differences: string[] }> 
 */
export const checkUserMetadataConsistency = async (
  userId: string,
  fields: (keyof UserMetadataSync)[] = ['status', 'role', 'tenant_id']
): Promise<{ consistent: boolean, differences: string[] }> => {
  const differences: string[] = [];
  
  try {
    // Usar apenas adminClient para evitar problemas com RLS
    const adminClient = await createAdminClient();
    
    // Buscar dados das duas fontes usando adminClient para evitar problemas com RLS
    const [userResult, authResult] = await Promise.all([
      adminClient
        .from('users')
        .select('status, role, tenant_id, branch_id, first_name, last_name, avatar_url')
        .eq('id', userId)
        .single(),
      adminClient.auth.admin.getUserById(userId)
    ]);
    
    if (userResult.error || !userResult.data) {
      differences.push(`Erro ao buscar dados da tabela users: ${userResult.error?.message}`);
      return { consistent: false, differences };
    }
    
    if (authResult.error || !authResult.data.user) {
      differences.push(`Erro ao buscar dados do Auth: ${authResult.error?.message}`);
      return { consistent: false, differences };
    }
    
    const userData = userResult.data;
    const metadata = authResult.data.user.app_metadata || {};
    
    // Comparar campos
    for (const field of fields) {
      const userValue = userData[field];
      const metadataValue = metadata[field];
      
      if (userValue !== metadataValue) {
        differences.push(`${field}: users="${userValue}" vs metadata="${metadataValue}"`);
      }
    }
    
  } catch (error) {
    differences.push(`Erro na verificação: ${error instanceof Error ? error.message : String(error)}`);
  }
  
  return {
    consistent: differences.length === 0,
    differences
  };
};

/**
 * Força a sincronização completa de todos os metadados do usuário
 * Útil para corrigir inconsistências detectadas
 * @param userId ID do usuário
 * @returns Promise<SyncResult>
 */
export const forceFullUserMetadataSync = async (userId: string): Promise<SyncResult> => {
  console.log(`Iniciando sincronização completa para usuário ${userId}`);
  
  // Verificar consistência primeiro
  const consistency = await checkUserMetadataConsistency(userId);
  
  if (consistency.consistent) {
    return {
      success: true,
      synced: [],
      errors: [],
      skipped: ['Todos os campos já estão consistentes']
    };
  }
  
  console.log(`Inconsistências detectadas para usuário ${userId}:`, consistency.differences);
  
  // Sincronizar todos os campos
  return await syncUserMetadata(userId, ['status', 'role', 'tenant_id', 'branch_id', 'first_name', 'last_name', 'avatar_url']);
}; 