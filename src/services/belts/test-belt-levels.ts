'use server';

import { createAdminClient } from '@/services/supabase/server';

/**
 * Função de teste para verificar se o upsert de belt levels está funcionando corretamente
 */
export async function testBeltLevelsUpsert(
  tenantId: string,
  modalityId: string
): Promise<{ success: boolean; message: string; data?: any }> {
  try {
    console.log('🧪 Iniciando teste de upsert de belt levels...');
    
    const supabase = await createAdminClient();
    
    // Dados de teste
    const testLevels = [
      {
        tenant_id: tenantId,
        modality_id: modalityId,
        belt_color: '#FFFFFF',
        degree: 0,
        label: 'Faixa Branca - Teste',
        stripe_color: null,
        show_center_line: false,
        center_line_color: null,
        sort_order: 0,
      },
      {
        tenant_id: tenantId,
        modality_id: modalityId,
        belt_color: '#0000FF',
        degree: 0,
        label: 'Faixa Azul - Teste',
        stripe_color: '#FFFFFF',
        show_center_line: true,
        center_line_color: '#000000',
        sort_order: 1,
      }
    ];

    console.log('📋 Dados de teste:', testLevels);

    // 1. Limpar dados de teste anteriores
    console.log('🧹 Limpando dados de teste anteriores...');
    const { error: deleteError } = await supabase
      .from('belt_levels')
      .delete()
      .eq('tenant_id', tenantId)
      .eq('modality_id', modalityId)
      .like('label', '% - Teste');

    if (deleteError) {
      console.warn('⚠️ Erro ao limpar dados de teste:', deleteError);
    }

    // 2. Executar upsert via RPC
    console.log('🚀 Executando upsert via RPC...');
    const { data: rpcResult, error: rpcError } = await supabase.rpc('upsert_belt_levels_admin', {
      p_tenant_id: tenantId,
      p_modality_id: modalityId,
      p_levels: testLevels
    });

    if (rpcError) {
      console.error('❌ Erro na RPC:', rpcError);
      return {
        success: false,
        message: `Erro na RPC: ${rpcError.message}`,
        data: { rpcError }
      };
    }

    console.log('📊 Resultado da RPC:', rpcResult);

    // 3. Verificar se os dados foram persistidos
    console.log('🔍 Verificando persistência...');
    const { data: verificationData, error: verificationError } = await supabase
      .from('belt_levels')
      .select('*')
      .eq('tenant_id', tenantId)
      .eq('modality_id', modalityId)
      .like('label', '% - Teste')
      .order('sort_order');

    if (verificationError) {
      console.error('❌ Erro na verificação:', verificationError);
      return {
        success: false,
        message: `Erro na verificação: ${verificationError.message}`,
        data: { verificationError, rpcResult }
      };
    }

    console.log('✅ Dados encontrados:', verificationData?.length || 0);
    console.log('📋 Registros:', verificationData);

    // 4. Limpar dados de teste
    console.log('🧹 Limpando dados de teste...');
    await supabase
      .from('belt_levels')
      .delete()
      .eq('tenant_id', tenantId)
      .eq('modality_id', modalityId)
      .like('label', '% - Teste');

    const expectedCount = testLevels.length;
    const actualCount = verificationData?.length || 0;

    if (actualCount === expectedCount) {
      return {
        success: true,
        message: `✅ Teste passou! ${actualCount}/${expectedCount} registros persistidos corretamente.`,
        data: {
          rpcResult,
          verificationData,
          expectedCount,
          actualCount
        }
      };
    } else {
      return {
        success: false,
        message: `❌ Teste falhou! Esperado: ${expectedCount}, Encontrado: ${actualCount}`,
        data: {
          rpcResult,
          verificationData,
          expectedCount,
          actualCount
        }
      };
    }

  } catch (error) {
    console.error('💥 Erro no teste:', error);
    return {
      success: false,
      message: `Erro no teste: ${error}`,
      data: { error }
    };
  }
}

/**
 * Função para verificar o estado atual dos belt levels
 */
export async function checkBeltLevelsState(
  tenantId: string,
  modalityId: string
): Promise<{ success: boolean; message: string; data?: any }> {
  try {
    const supabase = await createAdminClient();
    
    const { data, error } = await supabase
      .from('belt_levels')
      .select('*')
      .eq('tenant_id', tenantId)
      .eq('modality_id', modalityId)
      .order('sort_order');

    if (error) {
      return {
        success: false,
        message: `Erro ao buscar belt levels: ${error.message}`,
        data: { error }
      };
    }

    return {
      success: true,
      message: `✅ Encontrados ${data?.length || 0} belt levels`,
      data: {
        count: data?.length || 0,
        levels: data
      }
    };

  } catch (error) {
    return {
      success: false,
      message: `Erro na verificação: ${error}`,
      data: { error }
    };
  }
}
