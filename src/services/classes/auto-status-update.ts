'use server';

import { logClassStatusChange } from '@/services/audit/class-status-audit';
import { 
  getAllClassesNeedingStatusUpdate,
  updateMultipleClassesStatus,
  type ClassForStatusUpdate 
} from './status-update-queries';
import { validateBatchStatusUpdate } from './status-validation';

/**
 * Resultado da atualização de status
 */
export interface StatusUpdateResult {
  success: boolean;
  totalProcessed: number;
  toOngoingUpdated: number;
  toCompletedUpdated: number;
  failed: number;
  errors: string[];
  executionTimeMs: number;
  timestamp: string;
}

/**
 * Calcula o novo status baseado no tempo atual
 * Reutiliza a lógica existente mas simplificada
 */
function calculateNewStatus(classItem: ClassForStatusUpdate): string {
  const now = new Date();
  const startTime = new Date(classItem.start_time);
  const endTime = new Date(classItem.end_time);
  
  // Apenas atualizar aulas que podem mudar de status automaticamente
  if (!['scheduled', 'ongoing'].includes(classItem.status)) {
    return classItem.status; // Manter status atual para cancelled, rescheduled, etc.
  }
  
  if (endTime <= now) {
    return 'completed';
  } else if (startTime <= now && now <= endTime) {
    return 'ongoing';
  } else {
    return 'scheduled';
  }
}

/**
 * Processa atualizações de status para um grupo de aulas do mesmo tenant
 */
async function processClassesForTenant(
  classes: ClassForStatusUpdate[], 
  newStatus: string,
  tenantId: string
): Promise<{ success: number; failed: number; auditLogs: number }> {
  if (classes.length === 0) {
    return { success: 0, failed: 0, auditLogs: 0 };
  }

  // Validar quais aulas podem ser atualizadas
  const classIds = classes.map(c => c.id);
  const validation = await validateBatchStatusUpdate(classIds, newStatus);
  
  if (validation.invalid.length > 0) {
    console.log(`⚠️  ${validation.invalid.length} aulas não podem ser atualizadas:`, 
      validation.invalid.map(v => `${v.classId}: ${v.reason}`).join(', '));
  }
  
  if (validation.valid.length === 0) {
    return { success: 0, failed: validation.invalid.length, auditLogs: 0 };
  }

  // Atualizar apenas aulas válidas
  const updateResult = await updateMultipleClassesStatus(validation.valid, newStatus, tenantId);
  
  // Registrar logs de auditoria para aulas atualizadas com sucesso
  let auditLogsCreated = 0;
  if (updateResult.success > 0) {
    // Pegar apenas as aulas que foram atualizadas com sucesso
    const updatedClasses = classes.filter(cls => validation.valid.includes(cls.id)).slice(0, updateResult.success);
    const auditPromises = updatedClasses.map(cls =>
      logClassStatusChange({
        class_id: cls.id,
        old_status: cls.status,
        new_status: newStatus,
        tenant_id: cls.tenant_id
      })
    );
    
    // Executar logs de auditoria em paralelo
    const auditResults = await Promise.allSettled(auditPromises);
    auditLogsCreated = auditResults.filter(result => 
      result.status === 'fulfilled' && result.value.success
    ).length;
  }
  
  return {
    success: updateResult.success,
    failed: updateResult.failed + validation.invalid.length,
    auditLogs: auditLogsCreated
  };
}

/**
 * Atualiza automaticamente o status de todas as aulas que precisam
 * Esta é a função principal chamada pelo cron job
 */
export async function updateClassesStatus(): Promise<StatusUpdateResult> {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();
  
  try {
    console.log('🔄 Iniciando atualização automática de status das aulas...');
    
    // Buscar todas as aulas que precisam ter status atualizado
    const { toOngoing, toCompleted } = await getAllClassesNeedingStatusUpdate();
    
    const totalToProcess = toOngoing.length + toCompleted.length;
    
    if (totalToProcess === 0) {
      const executionTime = Date.now() - startTime;
      console.log('✅ Nenhuma aula precisa ter status atualizado');
      
      return {
        success: true,
        totalProcessed: 0,
        toOngoingUpdated: 0,
        toCompletedUpdated: 0,
        failed: 0,
        errors: [],
        executionTimeMs: executionTime,
        timestamp
      };
    }
    
    console.log(`📊 Encontradas ${totalToProcess} aulas para atualizar:`);
    console.log(`  - Para 'ongoing': ${toOngoing.length}`);
    console.log(`  - Para 'completed': ${toCompleted.length}`);
    
    // Agrupar por tenant para processamento eficiente
    const ongoingByTenant = groupByTenant(toOngoing);
    const completedByTenant = groupByTenant(toCompleted);
    
    const errors: string[] = [];
    let totalOngoingUpdated = 0;
    let totalCompletedUpdated = 0;
    let totalFailed = 0;
    
    // Processar atualizações para 'ongoing'
    for (const [tenantId, classes] of Object.entries(ongoingByTenant)) {
      try {
        const result = await processClassesForTenant(classes, 'ongoing', tenantId);
        totalOngoingUpdated += result.success;
        totalFailed += result.failed;
        
        if (result.success > 0) {
          console.log(`✅ Tenant ${tenantId}: ${result.success} aulas → 'ongoing' (${result.auditLogs} logs)`);
        }
        if (result.failed > 0) {
          console.log(`❌ Tenant ${tenantId}: ${result.failed} aulas falharam → 'ongoing'`);
        }
      } catch (error) {
        const errorMsg = `Erro ao processar tenant ${tenantId} para 'ongoing': ${error}`;
        console.error(errorMsg);
        errors.push(errorMsg);
        totalFailed += classes.length;
      }
    }
    
    // Processar atualizações para 'completed'
    for (const [tenantId, classes] of Object.entries(completedByTenant)) {
      try {
        const result = await processClassesForTenant(classes, 'completed', tenantId);
        totalCompletedUpdated += result.success;
        totalFailed += result.failed;
        
        if (result.success > 0) {
          console.log(`✅ Tenant ${tenantId}: ${result.success} aulas → 'completed' (${result.auditLogs} logs)`);
        }
        if (result.failed > 0) {
          console.log(`❌ Tenant ${tenantId}: ${result.failed} aulas falharam → 'completed'`);
        }
      } catch (error) {
        const errorMsg = `Erro ao processar tenant ${tenantId} para 'completed': ${error}`;
        console.error(errorMsg);
        errors.push(errorMsg);
        totalFailed += classes.length;
      }
    }
    
    const executionTime = Date.now() - startTime;
    const totalUpdated = totalOngoingUpdated + totalCompletedUpdated;
    
    console.log(`🎯 Atualização concluída em ${executionTime}ms:`);
    console.log(`  - Total processadas: ${totalToProcess}`);
    console.log(`  - Atualizadas com sucesso: ${totalUpdated}`);
    console.log(`  - Falharam: ${totalFailed}`);
    
    return {
      success: errors.length === 0,
      totalProcessed: totalToProcess,
      toOngoingUpdated: totalOngoingUpdated,
      toCompletedUpdated: totalCompletedUpdated,
      failed: totalFailed,
      errors,
      executionTimeMs: executionTime,
      timestamp
    };
    
  } catch (error) {
    const executionTime = Date.now() - startTime;
    const errorMsg = `Erro crítico na atualização de status: ${error}`;
    console.error(errorMsg);
    
    return {
      success: false,
      totalProcessed: 0,
      toOngoingUpdated: 0,
      toCompletedUpdated: 0,
      failed: 0,
      errors: [errorMsg],
      executionTimeMs: executionTime,
      timestamp
    };
  }
}

/**
 * Agrupa aulas por tenant para processamento em lote
 */
function groupByTenant(classes: ClassForStatusUpdate[]): Record<string, ClassForStatusUpdate[]> {
  return classes.reduce((acc, cls) => {
    if (!acc[cls.tenant_id]) {
      acc[cls.tenant_id] = [];
    }
    acc[cls.tenant_id].push(cls);
    return acc;
  }, {} as Record<string, ClassForStatusUpdate[]>);
}

/**
 * Atualiza status de uma aula específica (para uso manual)
 */
export async function updateSingleClassStatus(
  classId: string, 
  tenantId: string
): Promise<{ success: boolean; message: string; oldStatus?: string; newStatus?: string }> {
  try {
    // Buscar dados da aula
    const { getAllClassesNeedingStatusUpdate } = await import('./status-update-queries');
    const allClasses = await getAllClassesNeedingStatusUpdate();
    
    // Encontrar a aula específica
    const allClassesList = [...allClasses.toOngoing, ...allClasses.toCompleted];
    const targetClass = allClassesList.find(c => c.id === classId && c.tenant_id === tenantId);
    
    if (!targetClass) {
      return { success: false, message: 'Aula não encontrada ou não precisa de atualização' };
    }
    
    // Calcular novo status
    const newStatus = calculateNewStatus(targetClass);
    
    if (newStatus === targetClass.status) {
      return { success: true, message: 'Status já está correto', oldStatus: targetClass.status, newStatus };
    }
    
    // Atualizar status
    const result = await processClassesForTenant([targetClass], newStatus, tenantId);
    
    if (result.success > 0) {
      return { 
        success: true, 
        message: 'Status atualizado com sucesso',
        oldStatus: targetClass.status,
        newStatus 
      };
    } else {
      return { success: false, message: 'Falha ao atualizar status' };
    }
    
  } catch (error) {
    console.error('Erro ao atualizar status da aula:', error);
    return { success: false, message: 'Erro interno' };
  }
} 