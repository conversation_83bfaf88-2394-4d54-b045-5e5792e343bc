'use server';

/**
 * Configuração de retry para diferentes tipos de operação
 */
interface RetryConfig {
  maxAttempts: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
  retryableErrors: string[];
}

/**
 * Configurações padrão de retry
 */
const DEFAULT_RETRY_CONFIGS: Record<string, RetryConfig> = {
  database: {
    maxAttempts: 3,
    baseDelayMs: 1000,
    maxDelayMs: 10000,
    backoffMultiplier: 2,
    retryableErrors: ['timeout', 'connection', 'network', 'temporary']
  },
  audit: {
    maxAttempts: 2,
    baseDelayMs: 500,
    maxDelayMs: 2000,
    backoffMultiplier: 1.5,
    retryableErrors: ['timeout', 'connection']
  },
  validation: {
    maxAttempts: 1, // Validações geralmente não devem ser retriadas
    baseDelayMs: 0,
    maxDelayMs: 0,
    backoffMultiplier: 1,
    retryableErrors: []
  }
};

/**
 * Resultado de uma operação com retry
 */
export interface RetryResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  attempts: number;
  totalRetryTimeMs: number;
  lastAttemptAt: string;
}

/**
 * Verifica se um erro é retriável baseado na configuração
 */
function isRetryableError(error: Error, config: RetryConfig): boolean {
  const errorMessage = error.message.toLowerCase();
  return config.retryableErrors.some(retryableError => 
    errorMessage.includes(retryableError.toLowerCase())
  );
}

/**
 * Calcula o delay para a próxima tentativa usando exponential backoff
 */
function calculateDelay(attempt: number, config: RetryConfig): number {
  const delay = config.baseDelayMs * Math.pow(config.backoffMultiplier, attempt - 1);
  return Math.min(delay, config.maxDelayMs);
}

/**
 * Executa uma operação com retry automático
 */
export async function executeWithRetry<T>(
  operation: () => Promise<T>,
  operationType: keyof typeof DEFAULT_RETRY_CONFIGS = 'database',
  customConfig?: Partial<RetryConfig>
): Promise<RetryResult<T>> {
  const config = { ...DEFAULT_RETRY_CONFIGS[operationType], ...customConfig };
  const startTime = Date.now();
  let lastError: Error | null = null;
  
  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      console.log(`🔄 Tentativa ${attempt}/${config.maxAttempts} para operação ${operationType}`);
      
      const result = await operation();
      
      const totalTime = Date.now() - startTime;
      console.log(`✅ Operação ${operationType} bem-sucedida na tentativa ${attempt} (${totalTime}ms)`);
      
      return {
        success: true,
        data: result,
        attempts: attempt,
        totalRetryTimeMs: totalTime,
        lastAttemptAt: new Date().toISOString()
      };
      
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      const totalTime = Date.now() - startTime;
      
      console.warn(`⚠️ Tentativa ${attempt}/${config.maxAttempts} falhou para ${operationType}: ${lastError.message}`);
      
      // Se é a última tentativa ou erro não é retriável, falhar
      if (attempt === config.maxAttempts || !isRetryableError(lastError, config)) {
        console.error(`❌ Operação ${operationType} falhou definitivamente após ${attempt} tentativas`);
        
        return {
          success: false,
          error: lastError.message,
          attempts: attempt,
          totalRetryTimeMs: totalTime,
          lastAttemptAt: new Date().toISOString()
        };
      }
      
      // Aguardar antes da próxima tentativa
      const delay = calculateDelay(attempt, config);
      if (delay > 0) {
        console.log(`⏳ Aguardando ${delay}ms antes da próxima tentativa...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  // Este código nunca deveria ser alcançado, mas está aqui para segurança
  const totalTime = Date.now() - startTime;
  return {
    success: false,
    error: lastError?.message || 'Erro desconhecido',
    attempts: config.maxAttempts,
    totalRetryTimeMs: totalTime,
    lastAttemptAt: new Date().toISOString()
  };
}

/**
 * Executa múltiplas operações em paralelo com retry individual
 */
export async function executeParallelWithRetry<T>(
  operations: Array<{ name: string; operation: () => Promise<T>; type?: keyof typeof DEFAULT_RETRY_CONFIGS }>,
  config?: { maxConcurrency?: number }
): Promise<Array<RetryResult<T> & { operationName: string }>> {
  const maxConcurrency = config?.maxConcurrency || 5;
  const results: Array<RetryResult<T> & { operationName: string }> = [];
  
  // Processar operações em lotes para controlar concorrência
  for (let i = 0; i < operations.length; i += maxConcurrency) {
    const batch = operations.slice(i, i + maxConcurrency);
    
    const batchPromises = batch.map(async ({ name, operation, type = 'database' }) => {
      const result = await executeWithRetry(operation, type);
      return { ...result, operationName: name };
    });
    
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
    
    console.log(`📊 Lote ${Math.floor(i / maxConcurrency) + 1} concluído: ${batchResults.length} operações`);
  }
  
  return results;
}

/**
 * Versão especializada para atualização de status de aulas com retry
 */
export async function updateClassStatusWithRetry(
  classIds: string[],
  newStatus: string,
  tenantId: string
): Promise<RetryResult<{ success: number; failed: number }>> {
  const { updateMultipleClassesStatus } = await import('./status-update-queries');
  
  return executeWithRetry(
    () => updateMultipleClassesStatus(classIds, newStatus, tenantId),
    'database',
    {
      maxAttempts: 3,
      retryableErrors: ['timeout', 'connection', 'lock', 'deadlock', 'temporary']
    }
  );
}

/**
 * Versão especializada para logs de auditoria com retry
 */
export async function createAuditLogWithRetry(
  logData: { class_id: string; old_status: string; new_status: string; tenant_id: string }
): Promise<RetryResult<{ success: boolean }>> {
  const { logClassStatusChange } = await import('@/services/audit/class-status-audit');
  
  return executeWithRetry(
    () => logClassStatusChange(logData),
    'audit',
    {
      maxAttempts: 2,
      retryableErrors: ['timeout', 'connection']
    }
  );
}

/**
 * Versão especializada para busca de aulas com retry
 */
export async function getClassesNeedingUpdateWithRetry(): Promise<RetryResult<{
  toOngoing: any[];
  toCompleted: any[];
}>> {
  const { getAllClassesNeedingStatusUpdate } = await import('./status-update-queries');
  
  return executeWithRetry(
    () => getAllClassesNeedingStatusUpdate(),
    'database',
    {
      maxAttempts: 2,
      retryableErrors: ['timeout', 'connection', 'read']
    }
  );
}

/**
 * Monitora e registra estatísticas de retry para análise
 */
export class RetryStatistics {
  private static stats: Record<string, {
    totalOperations: number;
    successfulOperations: number;
    failedOperations: number;
    totalRetries: number;
    avgRetries: number;
    avgRetryTime: number;
  }> = {};

  static recordResult<T>(operationType: string, result: RetryResult<T>) {
    if (!this.stats[operationType]) {
      this.stats[operationType] = {
        totalOperations: 0,
        successfulOperations: 0,
        failedOperations: 0,
        totalRetries: 0,
        avgRetries: 0,
        avgRetryTime: 0
      };
    }

    const stats = this.stats[operationType];
    stats.totalOperations++;
    
    if (result.success) {
      stats.successfulOperations++;
    } else {
      stats.failedOperations++;
    }
    
    stats.totalRetries += result.attempts - 1; // -1 porque a primeira tentativa não é retry
    stats.avgRetries = stats.totalRetries / stats.totalOperations;
    stats.avgRetryTime = (stats.avgRetryTime * (stats.totalOperations - 1) + result.totalRetryTimeMs) / stats.totalOperations;
  }

  static getStatistics() {
    return { ...this.stats };
  }

  static resetStatistics() {
    this.stats = {};
  }
} 