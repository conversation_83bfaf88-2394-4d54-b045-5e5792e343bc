'use server';

import { createAdminClient } from '@/services/supabase/server';

/**
 * Interface para aula que precisa ter status atualizado
 */
export interface ClassForStatusUpdate {
  id: string;
  status: string;
  start_time: string;
  end_time: string;
  tenant_id: string;
}

/**
 * Busca aulas 'ongoing' que já terminaram (devem virar 'completed')
 */
export async function getOngoingClassesThatEnded(): Promise<ClassForStatusUpdate[]> {
  const supabase = await createAdminClient();
  
  const { data, error } = await supabase
    .from('classes')
    .select('id, status, start_time, end_time, tenant_id')
    .eq('status', 'ongoing')
    .lte('end_time', new Date().toISOString())
    .is('deleted_at', null)
    .order('tenant_id', { ascending: true })
    .order('end_time', { ascending: true });

  if (error) {
    console.error('Erro ao buscar aulas ongoing que terminaram:', error);
    return [];
  }

  return data || [];
}

/**
 * Busca aulas 'scheduled' que já começaram (devem virar 'ongoing')
 */
export async function getScheduledClassesThatStarted(): Promise<ClassForStatusUpdate[]> {
  const supabase = await createAdminClient();
  
  const now = new Date().toISOString();
  
  const { data, error } = await supabase
    .from('classes')
    .select('id, status, start_time, end_time, tenant_id')
    .eq('status', 'scheduled')
    .lte('start_time', now)
    .gt('end_time', now) // Ainda não terminou
    .is('deleted_at', null)
    .order('tenant_id', { ascending: true })
    .order('start_time', { ascending: true });

  if (error) {
    console.error('Erro ao buscar aulas scheduled que começaram:', error);
    return [];
  }

  return data || [];
}

/**
 * Busca aulas 'scheduled' que já terminaram sem ter começado (devem virar 'completed')
 */
export async function getScheduledClassesThatEnded(): Promise<ClassForStatusUpdate[]> {
  const supabase = await createAdminClient();
  
  const { data, error } = await supabase
    .from('classes')
    .select('id, status, start_time, end_time, tenant_id')
    .eq('status', 'scheduled')
    .lte('end_time', new Date().toISOString())
    .is('deleted_at', null)
    .order('tenant_id', { ascending: true })
    .order('end_time', { ascending: true });

  if (error) {
    console.error('Erro ao buscar aulas scheduled que terminaram:', error);
    return [];
  }

  return data || [];
}

/**
 * Busca todas as aulas que precisam ter status atualizado
 * Otimizada para usar os índices criados
 */
export async function getAllClassesNeedingStatusUpdate(): Promise<{
  toOngoing: ClassForStatusUpdate[];
  toCompleted: ClassForStatusUpdate[];
}> {
  // Executar as três consultas em paralelo para melhor performance
  const [ongoingEnded, scheduledStarted, scheduledEnded] = await Promise.all([
    getOngoingClassesThatEnded(),
    getScheduledClassesThatStarted(), 
    getScheduledClassesThatEnded()
  ]);

  return {
    toOngoing: scheduledStarted,
    toCompleted: [...ongoingEnded, ...scheduledEnded]
  };
}

/**
 * Atualiza o status de uma aula específica
 */
export async function updateClassStatus(classId: string, newStatus: string, tenantId: string): Promise<boolean> {
  const supabase = await createAdminClient();
  
  const { error } = await supabase
    .from('classes')
    .update({ 
      status: newStatus,
      updated_at: new Date().toISOString()
    })
    .eq('id', classId)
    .eq('tenant_id', tenantId);

  if (error) {
    console.error(`Erro ao atualizar status da aula ${classId}:`, error);
    return false;
  }

  return true;
}

/**
 * Atualiza status de múltiplas aulas em lote (mais eficiente)
 */
export async function updateMultipleClassesStatus(
  classIds: string[], 
  newStatus: string, 
  tenantId: string
): Promise<{ success: number; failed: number }> {
  const supabase = await createAdminClient();
  
  const { data, error } = await supabase
    .from('classes')
    .update({ 
      status: newStatus,
      updated_at: new Date().toISOString()
    })
    .in('id', classIds)
    .eq('tenant_id', tenantId)
    .select('id');

  if (error) {
    console.error(`Erro ao atualizar status em lote para ${newStatus}:`, error);
    return { success: 0, failed: classIds.length };
  }

  const successCount = data?.length || 0;
  return { 
    success: successCount, 
    failed: classIds.length - successCount 
  };
}

/**
 * Estatísticas de performance do cron job
 */
export async function getStatusUpdateStats(tenantId?: string) {
  const supabase = await createAdminClient();
  
  let query = supabase
    .from('classes')
    .select('status, start_time, end_time')
    .is('deleted_at', null);
    
  if (tenantId) {
    query = query.eq('tenant_id', tenantId);
  }
  
  const { data, error } = await query;
  
  if (error) {
    console.error('Erro ao buscar estatísticas:', error);
    return null;
  }
  
  const now = new Date();
  let needsUpdate = 0;
  let upToDate = 0;
  
  data?.forEach((cls: any) => {
    const startTime = new Date(cls.start_time);
    const endTime = new Date(cls.end_time);
    
    // Calcular qual deveria ser o status
    let expectedStatus: string;
    if (endTime <= now) {
      expectedStatus = 'completed';
    } else if (startTime <= now && now <= endTime) {
      expectedStatus = 'ongoing';
    } else {
      expectedStatus = 'scheduled';
    }
    
    // Verificar se precisa atualizar (ignorar cancelled, rescheduled, etc)
    if (['scheduled', 'ongoing', 'completed'].includes(cls.status)) {
      if (cls.status === expectedStatus) {
        upToDate++;
      } else {
        needsUpdate++;
      }
    }
  });
  
  return {
    total: data?.length || 0,
    needsUpdate,
    upToDate,
    percentageUpToDate: data?.length ? Math.round((upToDate / data.length) * 100) : 100
  };
} 