'use server';

import { createAdminClient } from '@/services/supabase/server';

/**
 * Valida se uma aula pode ter seu status alterado automaticamente
 */
export async function validateClassForStatusUpdate(
  classId: string,
  newStatus: string,
  currentStatus: string
): Promise<{ canUpdate: boolean; reason?: string }> {
  
  // Regra 1: Aulas canceladas não devem ser alteradas automaticamente
  if (currentStatus === 'cancelled') {
    return {
      canUpdate: false,
      reason: 'Aulas canceladas não podem ter status alterado automaticamente'
    };
  }
  
  // Regra 2: Aulas reagendadas não devem ser alteradas automaticamente
  if (currentStatus === 'rescheduled') {
    return {
      canUpdate: false,
      reason: 'Aulas reagendadas não podem ter status alterado automaticamente'
    };
  }
  
  // Regra 3: Apenas aulas 'scheduled' e 'ongoing' podem ser alteradas automaticamente
  if (!['scheduled', 'ongoing'].includes(currentStatus)) {
    return {
      canUpdate: false,
      reason: `Status '${currentStatus}' não pode ser alterado automaticamente`
    };
  }
  
  // Regra 4: Validar transições válidas
  const validTransitions: Record<string, string[]> = {
    'scheduled': ['ongoing', 'completed'],
    'ongoing': ['completed']
  };
  
  if (!validTransitions[currentStatus]?.includes(newStatus)) {
    return {
      canUpdate: false,
      reason: `Transição inválida: '${currentStatus}' -> '${newStatus}'`
    };
  }
  
  return { canUpdate: true };
}

/**
 * Verifica se uma aula tem presenças registradas
 * Útil para futuras regras de negócio
 */
export async function checkClassHasAttendances(classId: string): Promise<{
  hasAttendances: boolean;
  attendanceCount: number;
}> {
  const supabase = await createAdminClient();
  
  const { data, error } = await supabase
    .from('attendances')
    .select('id', { count: 'exact' })
    .eq('class_id', classId)
    .limit(1);
  
  if (error) {
    console.error('Erro ao verificar presenças:', error);
    return { hasAttendances: false, attendanceCount: 0 };
  }
  
  return {
    hasAttendances: (data?.length || 0) > 0,
    attendanceCount: data?.length || 0
  };
}

/**
 * Aplica regras de negócio específicas para determinar se o status deve ser atualizado
 */
export async function shouldUpdateClassStatus(
  classId: string,
  currentStatus: string,
  proposedStatus: string
): Promise<{ shouldUpdate: boolean; reason?: string }> {
  
  // Validar se a aula pode ser atualizada
  const validation = await validateClassForStatusUpdate(classId, proposedStatus, currentStatus);
  if (!validation.canUpdate) {
    return {
      shouldUpdate: false,
      reason: validation.reason
    };
  }
  
  // Verificar se já está no status correto
  if (currentStatus === proposedStatus) {
    return {
      shouldUpdate: false,
      reason: 'Aula já está no status correto'
    };
  }
  
  // Para o caso especial mencionado: aulas sem presenças que terminaram continuam como concluídas
  if (proposedStatus === 'completed') {
    const { hasAttendances } = await checkClassHasAttendances(classId);
    
    // Esta lógica garante que aulas sem presenças ainda sejam marcadas como concluídas
    // Mantém a regra de negócio mencionada pelo usuário
    console.log(`📊 Aula ${classId}: ${hasAttendances ? 'com' : 'sem'} presenças → será marcada como '${proposedStatus}'`);
  }
  
  return { shouldUpdate: true };
}

/**
 * Valida múltiplas aulas para atualização em lote
 */
export async function validateBatchStatusUpdate(
  classIds: string[],
  newStatus: string
): Promise<{
  valid: string[];
  invalid: Array<{ classId: string; reason: string }>;
}> {
  const valid: string[] = [];
  const invalid: Array<{ classId: string; reason: string }> = [];
  
  // Buscar status atual das aulas
  const supabase = await createAdminClient();
  const { data: classes, error } = await supabase
    .from('classes')
    .select('id, status')
    .in('id', classIds);
  
  if (error || !classes) {
    return {
      valid: [],
      invalid: classIds.map(id => ({ classId: id, reason: 'Erro ao buscar dados da aula' }))
    };
  }
  
  // Validar cada aula
  for (const cls of classes) {
    const shouldUpdate = await shouldUpdateClassStatus(cls.id, cls.status, newStatus);
    
    if (shouldUpdate.shouldUpdate) {
      valid.push(cls.id);
    } else {
      invalid.push({
        classId: cls.id,
        reason: shouldUpdate.reason || 'Validação falhou'
      });
    }
  }
  
  // Identificar aulas não encontradas
  const foundIds = classes.map((c: any) => c.id);
  const notFound = classIds.filter(id => !foundIds.includes(id));
  for (const id of notFound) {
    invalid.push({
      classId: id,
      reason: 'Aula não encontrada'
    });
  }
  
  return { valid, invalid };
} 