import { createAdminClient } from '@/services/supabase/server';

interface Modality {
  id: string;
  slug: string;
  name: string;
  tenant_id: string;
  is_active: boolean;
}

// Interface for external API compatibility (keeping 'enabled' for backward compatibility)
interface ModalityResponse {
  id: string;
  slug: string;
  name: string;
  enabled: boolean;
}

/**
 * Lista todas as modalidades do tenant especificado.
 * @param tenantId ID do tenant atual
 */
export async function listModalities(tenantId: string): Promise<ModalityResponse[]> {
  const supabase = await createAdminClient();

  // Buscar modalidades diretamente da tabela unificada filtradas por tenant
  const { data: modalities, error } = await supabase
    .from('modalities')
    .select('id, slug, name, is_active')
    .eq('tenant_id', tenantId)
    .order('name', { ascending: true });

  if (error) {
    console.error('Erro ao buscar modalidades do tenant', error);
    throw error;
  }

  // Converter is_active para enabled para compatibilidade com API existente
  return (modalities ?? []).map((m) => ({
    id: m.id,
    slug: m.slug,
    name: m.name,
    enabled: m.is_active,
  }));
}

/**
 * Ativa ou desativa uma modalidade para determinado tenant.
 * Requer que o caller já tenha verificado permissões (admin).
 */
export async function toggleModality(
  tenantId: string,
  modalityId: string,
  enabled: boolean
): Promise<{ success: boolean } | { success: false; error: any }> {
  const supabase = await createAdminClient();

  // Atualizar diretamente o campo is_active na tabela modalities
  const { error } = await supabase
    .from('modalities')
    .update({ is_active: enabled })
    .eq('id', modalityId)
    .eq('tenant_id', tenantId);

  if (error) {
    console.error('Erro ao toggleModality', error);
    return { success: false, error };
  }

  return { success: true };
}

/**
 * Atualiza o nome de uma modalidade para um tenant específico
 * @param tenantId ID do tenant
 * @param modalityId ID da modalidade
 * @param name Novo nome da modalidade
 */
export async function updateModalityName(
  tenantId: string,
  modalityId: string,
  name: string
): Promise<{ success: boolean; error?: any }> {
  const supabase = await createAdminClient();

  const { error } = await supabase
    .from('modalities')
    .update({ name })
    .eq('id', modalityId)
    .eq('tenant_id', tenantId);

  if (error) {
    console.error('Erro ao atualizar nome da modalidade:', error);
    return { success: false, error };
  }

  return { success: true };
}

/**
 * Cria uma nova modalidade para um tenant específico
 * @param tenantId ID do tenant
 * @param slug Slug único da modalidade dentro do tenant
 * @param name Nome da modalidade
 * @param isActive Se a modalidade deve estar ativa (default: true)
 */
export async function createModality(
  tenantId: string,
  slug: string,
  name: string,
  isActive: boolean = true
): Promise<{ success: boolean; data?: Modality; error?: any }> {
  const supabase = await createAdminClient();

  const { data, error } = await supabase
    .from('modalities')
    .insert({
      tenant_id: tenantId,
      slug,
      name,
      is_active: isActive,
    })
    .select()
    .single();

  if (error) {
    console.error('Erro ao criar modalidade:', error);
    return { success: false, error };
  }

  return { success: true, data };
}

/**
 * Busca uma modalidade específica por slug e tenant
 * @param tenantId ID do tenant
 * @param slug Slug da modalidade
 */
export async function getModalityBySlug(
  tenantId: string,
  slug: string
): Promise<Modality | null> {
  const supabase = await createAdminClient();

  const { data, error } = await supabase
    .from('modalities')
    .select('*')
    .eq('tenant_id', tenantId)
    .eq('slug', slug)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // Não encontrado
      return null;
    }
    console.error('Erro ao buscar modalidade por slug:', error);
    throw error;
  }

  return data;
}

/**
 * Remove uma modalidade (soft delete - marca como inativa)
 * @param tenantId ID do tenant
 * @param modalityId ID da modalidade
 */
export async function deleteModality(
  tenantId: string,
  modalityId: string
): Promise<{ success: boolean; error?: any }> {
  const supabase = await createAdminClient();

  // Soft delete - marca como inativa
  const { error } = await supabase
    .from('modalities')
    .update({ is_active: false })
    .eq('id', modalityId)
    .eq('tenant_id', tenantId);

  if (error) {
    console.error('Erro ao deletar modalidade:', error);
    return { success: false, error };
  }

  return { success: true };
}

/**
 * Busca modalidades por IDs específicos
 * @param tenantId ID do tenant
 * @param modalityIds Array de IDs das modalidades
 */
export async function getModalitiesByIds(
  tenantId: string,
  modalityIds: string[]
): Promise<ModalityResponse[]> {
  if (!modalityIds || modalityIds.length === 0) {
    return [];
  }

  const supabase = await createAdminClient();

  const { data: modalities, error } = await supabase
    .from('modalities')
    .select('id, slug, name, is_active')
    .eq('tenant_id', tenantId)
    .in('id', modalityIds)
    .order('name', { ascending: true });

  if (error) {
    console.error('Erro ao buscar modalidades por IDs', error);
    throw error;
  }

  // Converter is_active para enabled para compatibilidade com API existente
  return (modalities ?? []).map((m) => ({
    id: m.id,
    slug: m.slug,
    name: m.name,
    enabled: m.is_active,
  }));
}

// Re-export from other modules
export * from './settings';