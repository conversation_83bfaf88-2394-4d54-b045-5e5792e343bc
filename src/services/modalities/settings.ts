import { createAdminClient } from '@/services/supabase/server';
import { createClient } from '@/services/supabase/server';

export interface ModalitySettings {
  tenant_id: string;
  modality_id: string;
  type?: string;
  level_rank_style?: string;
  secondary_color?: string;
  auto_assign_initial_rank: boolean;
  promotion_setting: string;
  promotion_fee?: number;
  require_sessions?: boolean;
  require_minimum_age?: boolean;
  created_at: string;
  updated_at: string;
}

export interface ModalitySettingsInput {
  type?: string;
  level_rank_style?: string;
  secondary_color?: string;
  auto_assign_initial_rank?: boolean;
  promotion_setting?: string;
  promotion_fee?: number;
  require_sessions?: boolean;
  require_minimum_age?: boolean;
}

/**
 * Valida se uma modalidade pertence ao tenant especificado
 * @param tenantId ID do tenant
 * @param modalityId ID da modalidade
 * @returns true se a modalidade pertence ao tenant, false caso contrário
 */
export async function validateModalityBelongsToTenant(
  tenantId: string,
  modalityId: string
): Promise<boolean> {
  console.log('🔍 validateModalityBelongsToTenant called with:', { tenantId, modalityId });
  
  const supabase = await createAdminClient();

  const { data, error } = await supabase
    .from('modalities')
    .select('id')
    .eq('tenant_id', tenantId)
    .eq('id', modalityId)
    .single();

  console.log('📋 Validation query result:', { data, error });

  if (error) {
    if (error.code === 'PGRST116') {
      // Não encontrado
      console.log('❌ Modalidade não encontrada na validação (PGRST116)');
      return false;
    }
    console.error('❌ Erro ao validar modalidade do tenant:', error);
    return false;
  }

  const isValid = !!data;
  console.log('✅ Validation result:', isValid);
  return isValid;
}

/**
 * Busca as configurações de uma modalidade para um tenant específico
 */
export async function getModalitySettings(
  tenantId: string,
  modalityId: string
): Promise<ModalitySettings | null> {
  console.log('🔍 getModalitySettings called with:', { tenantId, modalityId });
  
  // Validar se a modalidade pertence ao tenant
  const isValid = await validateModalityBelongsToTenant(tenantId, modalityId);
  if (!isValid) {
    console.warn(`Modalidade ${modalityId} não pertence ao tenant ${tenantId}`);
    return null;
  }

  // TEMPORÁRIO: Usando createAdminClient para debug (trocar depois)
  const supabase = await createAdminClient();

  console.log('🔍 Fazendo query para tenant_modality_settings...');
  
  // Usar uma query que funciona com RLS - usando uma função que define o tenant_id
  const { data: rpcResult, error } = await supabase
    .rpc('get_tenant_modality_settings', {
      p_tenant_id: tenantId,
      p_modality_id: modalityId
    });

  console.log('📋 RPC result:', { rpcResult, error });
  
  // A função RPC retorna JSON, então precisamos extrair os dados
  const data = rpcResult || null;

  if (error) {
    if (error.code === 'PGRST116') {
      // Não encontrado - retorna null
      console.log('❌ Configurações não encontradas (PGRST116)');
      return null;
    }
    console.error('❌ Erro ao buscar configurações da modalidade:', error);
    throw new Error('Falha ao buscar configurações da modalidade');
  }

  console.log('✅ Configurações encontradas:', data);
  return data;
}

/**
 * Atualiza ou cria as configurações de uma modalidade para um tenant
 */
export async function updateModalitySettings(
  tenantId: string,
  modalityId: string,
  settings: ModalitySettingsInput
): Promise<{ success: boolean; error?: any }> {
  // Validar se a modalidade pertence ao tenant
  const isValid = await validateModalityBelongsToTenant(tenantId, modalityId);
  if (!isValid) {
    console.error(`Tentativa de atualizar configurações de modalidade não autorizada: modalidade ${modalityId} não pertence ao tenant ${tenantId}`);
    return { 
      success: false, 
      error: { message: 'Modalidade não encontrada ou não autorizada' } 
    };
  }

  const supabase = await createAdminClient();

  // Chamada ao RPC definido no banco para garantir regras de negócio e RLS
  const { error } = await supabase.rpc('upsert_tenant_modality_settings', {
    p_tenant_id: tenantId,
    p_modality_id: modalityId,
    p_type: settings.type ?? null,
    p_level_rank_style: settings.level_rank_style ?? null,
    p_secondary_color: settings.secondary_color ?? null,
    p_auto_assign_initial_rank: settings.auto_assign_initial_rank ?? null,
    p_promotion_setting: settings.promotion_setting ?? null,
    p_promotion_fee: settings.promotion_fee ?? null,
    p_require_sessions: settings.require_sessions ?? null,
    p_require_minimum_age: settings.require_minimum_age ?? null,
  });

  if (error) {
    console.error('Erro ao atualizar configurações da modalidade (RPC):', error);
    return { success: false, error };
  }

  return { success: true };
}

/**
 * Lista todas as configurações de modalidades de um tenant
 */
export async function listModalitySettings(
  tenantId: string
): Promise<ModalitySettings[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('tenant_modality_settings')
    .select('*')
    .eq('tenant_id', tenantId)
    .order('created_at', { ascending: true });

  if (error) {
    console.error('Erro ao listar configurações das modalidades:', error);
    throw new Error('Falha ao listar configurações das modalidades');
  }

  return data || [];
} 