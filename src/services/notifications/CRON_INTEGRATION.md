# Integração das Cron Jobs com Sistema de Notificações

Este documento descreve como as cron jobs de pagamentos foram integradas com o sistema de notificações real da aplicação.

## Visão Geral

As duas principais cron jobs agora estão totalmente integradas com o sistema de notificações:

1. **Payment Reminders** (`/api/cron/payment-reminders`) - Lembretes de pagamento
2. **Overdue Notifications** (`/api/cron/overdue-notifications`) - Notificações de atraso

## Funcionalidades Implementadas

### 1. Lembretes de Pagamento

**Endpoint:** `POST /api/cron/payment-reminders`

**Tipos de lembretes:**
- **Upcoming** (3 dias antes): Lembrete preventivo
- **Due Today** (no dia): Lembrete urgente
- **Overdue** (após vencimento): Notificação de atraso

**Canais utilizados:**
- Notificação in-app (sempre)
- Email (quando permitido pelas configurações do usuário)

**Templates utilizados:**
- `payment_reminder_upcoming`
- `payment_reminder_due_today`
- `payment_reminder_overdue`

### 2. Notificações de Atraso com Escalação

**Endpoint:** `POST /api/cron/overdue-notifications`

**Níveis de escalação:**
- **Nível 1** (3 dias): Primeiro aviso
- **Nível 2** (7 dias): Aviso crítico
- **Nível 3** (15 dias): Último aviso

**Funcionalidades:**
- Notificações para estudantes com escalação automática
- Alertas para administradores sobre atrasos críticos (15+ dias)
- Cálculo automático do valor total em risco

**Templates utilizados:**
- `payment_overdue_level_1`
- `payment_overdue_level_2`
- `payment_overdue_level_3`
- `admin_overdue_alert`

## Arquitetura da Integração

### Componentes Principais

1. **NotificationDispatcher** - Coordena o envio por múltiplos canais
2. **NotificationService** - Gerencia notificações in-app no banco
3. **Templates** - Sistema de templates com variáveis dinâmicas
4. **Channels** - Suporte a email, in-app, WhatsApp (futuro)

### Fluxo de Execução

```
Cron Job → billing/notification-service → NotificationDispatcher → Channels
                                                ↓
                                        NotificationService (in-app)
                                                ↓
                                        Templates + Variables
```

## Templates Disponíveis

### Lembretes de Pagamento

- **payment_reminder_upcoming**: Para pagamentos que vencem em 3 dias
- **payment_reminder_due_today**: Para pagamentos que vencem hoje
- **payment_reminder_overdue**: Para pagamentos já em atraso

### Notificações de Atraso

- **payment_overdue_level_1**: Atraso de 3 dias
- **payment_overdue_level_2**: Atraso de 7 dias (crítico)
- **payment_overdue_level_3**: Atraso de 15 dias (último aviso)

### Alertas Administrativos

- **admin_overdue_alert**: Alerta para admins sobre atrasos críticos

## Variáveis de Template

Todas as notificações incluem as seguintes variáveis:

```typescript
{
  studentName: string,      // Nome completo do estudante
  amount: string,           // Valor formatado (ex: "150.00")
  dueDate: string,          // Data formatada (ex: "15/01/2024")
  planName: string,         // Nome do plano
  academyName: string,      // Nome da academia
  overdueDays?: number,     // Dias em atraso (para notificações de atraso)
  escalationLevel?: number, // Nível de escalação (1, 2, 3)
  reminderType?: string     // Tipo do lembrete (upcoming, due_today, overdue)
}
```

## Configuração e Execução

### Variáveis de Ambiente

```env
CRON_SECRET_KEY=sua_chave_secreta_aqui
```

### Execução Manual

```bash
# Lembretes de pagamento
curl -X POST http://localhost:3000/api/cron/payment-reminders \
  -H "Authorization: Bearer $CRON_SECRET_KEY"

# Notificações de atraso
curl -X POST http://localhost:3000/api/cron/overdue-notifications \
  -H "Authorization: Bearer $CRON_SECRET_KEY"
```

### Verificação de Status

```bash
# Status dos lembretes
curl -X GET http://localhost:3000/api/cron/payment-reminders \
  -H "Authorization: Bearer $CRON_SECRET_KEY"

# Status das notificações de atraso
curl -X GET http://localhost:3000/api/cron/overdue-notifications \
  -H "Authorization: Bearer $CRON_SECRET_KEY"
```

## Logs e Monitoramento

### Logs de Sucesso

```
✅ Lembrete <NAME_EMAIL> (upcoming)
✅ Notificação de atraso nível 2 <NAME_EMAIL>
✅ Alerta administrativo <NAME_EMAIL>
```

### Logs de Erro

```
❌ Erro ao enviar <NAME_EMAIL>: Canal email indisponível
❌ Erro ao processar notificação de <NAME_EMAIL>: Template não encontrado
```

## Próximos Passos

1. **Implementar busca real de nomes de planos e academias**
2. **Adicionar suporte a WhatsApp**
3. **Implementar templates personalizáveis por tenant**
4. **Adicionar métricas de entrega e abertura**
5. **Implementar retry automático para falhas**

## Exemplos de Uso

Veja o arquivo `examples/cron-integration-example.ts` para exemplos práticos de como usar e testar a integração.
