# Sistema de E-mail - Sprint 3

Este documento descreve a implementação do sistema de e-mail para notificações, parte do Sprint 3 do sistema de notificações.

## 📋 Funcionalidades Implementadas

### ✅ Abstração de Provedores
- Interface base `EmailProvider` para diferentes provedores de e-mail
- Implementação do provedor **Resend** (principal)
- Estrutura preparada para AWS SES (futuro)
- Validação e configuração automática

### ✅ Templates React Email
- Templates personalizáveis usando React Email
- Suporte a cores, logo e nome da academia
- Templates específicos:
  - **Lembrete de Pagamento** (`PaymentReminderTemplate`)
  - **Lembre<PERSON> de Aula** (`ClassReminderTemplate`)
- Template base reutilizável (`BaseEmailTemplate`)

### ✅ Configuração por Tenant
- Tabela `tenant_notification_settings` no banco
- Configurações específicas por academia:
  - Domínio de e-mail personalizado
  - Nome do remetente
  - Horários de silêncio
  - Tipos de notificação habilitados
- Integração com dados da academia (logo, cores)

### ✅ Sistema Multi-Canal
- Canal de e-mail integrado ao sistema existente
- Dispatcher para coordenar múltiplos canais
- Factory pattern para criação de canais
- Suporte a envio em lote

## 🏗️ Arquitetura

```
src/services/notifications/channels/email/
├── providers/
│   ├── email-provider.ts          # Interface base
│   └── resend-provider.ts         # Implementação Resend
├── templates/
│   ├── base-email-template.tsx    # Template base React Email
│   ├── payment-reminder-template.tsx
│   └── class-reminder-template.tsx
├── email-channel.ts               # Canal de e-mail
├── react-email-engine.ts          # Engine de renderização
└── notification-config-service.ts # Configurações por tenant
```

## 🚀 Como Usar

### 1. Configuração Inicial

Adicione as variáveis de ambiente necessárias:

```env
# Provedor de e-mail
EMAIL_PROVIDER=resend
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxx
RESEND_FROM_DOMAIN=meusaas.com
RESEND_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxxxxxxx
```

### 2. Enviar Lembrete de Pagamento

```typescript
import { NotificationDispatcher } from '@/services/notifications/channels/notification-dispatcher';

const dispatcher = new NotificationDispatcher();

await dispatcher.sendPaymentReminder({
  tenantId: 'academia-123',
  userId: 'aluno-456',
  studentName: 'João Silva',
  amount: 150.00,
  dueDate: '2024-02-15',
  planName: 'Plano Mensal - Jiu-Jitsu',
  channels: ['email', 'in_app']
});
```

### 3. Enviar Lembrete de Aula

```typescript
await dispatcher.sendClassReminder({
  tenantId: 'academia-123',
  userId: 'aluno-456',
  studentName: 'Maria Santos',
  className: 'Muay Thai - Iniciante',
  instructorName: 'Professor Carlos',
  classDate: '2024-02-10',
  classTime: '19:00',
  channels: ['email', 'in_app']
});
```

### 4. Configurar Academia

```typescript
import { NotificationConfigService } from '@/services/notifications/channels/email/notification-config-service';

const configService = new NotificationConfigService();

await configService.updateTenantSettings('academia-123', {
  emailFromName: 'Academia Champions',
  emailEnabled: true,
  notificationTypes: {
    payment: { email: true, whatsapp: false, in_app: true },
    class: { email: true, whatsapp: false, in_app: true }
  }
});
```

## 🎨 Personalização de Templates

### Cores e Logo da Academia

Os templates automaticamente usam:
- **Logo**: `tenants.logo_url`
- **Cor Primária**: `tenants.primary_color`
- **Cor Secundária**: `tenants.secondary_color`
- **Nome**: `tenants.name`

### Criando Novos Templates

1. Crie um novo arquivo em `templates/`:

```typescript
import { BaseEmailTemplate, EmailHeading, EmailText } from './base-email-template';

export function MeuNovoTemplate({ academyName, primaryColor, ... }) {
  return (
    <BaseEmailTemplate
      academyName={academyName}
      primaryColor={primaryColor}
    >
      <EmailHeading level={1}>Meu Título</EmailHeading>
      <EmailText>Meu conteúdo...</EmailText>
    </BaseEmailTemplate>
  );
}
```

2. Registre no `ReactEmailEngine`:

```typescript
case 'meu_novo_template':
  reactElement = MeuNovoTemplate({ ... });
  break;
```

## 🗄️ Banco de Dados

### Tabela `tenant_notification_settings`

```sql
CREATE TABLE tenant_notification_settings (
  tenant_id UUID PRIMARY KEY REFERENCES tenants(id),
  email_from_domain VARCHAR(100),
  email_from_name VARCHAR(100),
  email_enabled BOOLEAN DEFAULT true,
  whatsapp_enabled BOOLEAN DEFAULT true,
  notification_types JSONB DEFAULT '{}',
  quiet_hours_start TIME DEFAULT '22:00:00',
  quiet_hours_end TIME DEFAULT '08:00:00',
  timezone VARCHAR(50) DEFAULT 'America/Sao_Paulo',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE
);
```

### Configurações Padrão

Quando uma academia é criada, as configurações padrão são:
- E-mail habilitado
- Domínio: `{slug}@meusaas.com`
- Horário de silêncio: 22h às 8h
- Todos os tipos de notificação habilitados

## 🧪 Testes

Execute os exemplos para testar o sistema:

```typescript
import { executarExemplos } from '@/services/notifications/examples/email-usage-example';

// Em desenvolvimento
await executarExemplos();
```

## 🔧 Troubleshooting

### Erro: "Resend API Key inválida"
- Verifique se `RESEND_API_KEY` está configurada corretamente
- Confirme se a chave tem permissões de envio

### Erro: "Template não encontrado"
- Verifique se o `templateId` está registrado no `ReactEmailEngine`
- Confirme se o template existe na pasta `templates/`

### E-mails não chegam
- Verifique se o domínio está verificado no Resend
- Confirme se o e-mail do usuário está correto
- Verifique os logs do provedor

### Configurações de tenant não aplicadas
- Confirme se a academia tem configurações na tabela `tenant_notification_settings`
- Execute `createDefaultSettings()` se necessário

## 🔮 Próximos Passos

1. **AWS SES Provider**: Implementar provedor alternativo
2. **Webhooks**: Processar eventos de entrega do Resend
3. **Analytics**: Métricas de abertura e cliques
4. **Templates Visuais**: Editor de templates no admin
5. **A/B Testing**: Testar diferentes versões de templates

## 📚 Referências

- [Resend Documentation](https://resend.com/docs)
- [React Email Documentation](https://react.email/docs)
- [Supabase RLS](https://supabase.com/docs/guides/auth/row-level-security)
