/**
 * Interface base para provedores de e-mail
 * Define o contrato que todos os provedores de e-mail devem implementar
 */

export interface EmailData {
  to: string | string[];
  from: string;
  fromName?: string;
  subject: string;
  html: string;
  text?: string;
  replyTo?: string;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: EmailAttachment[];
  headers?: Record<string, string>;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface EmailAttachment {
  filename: string;
  content: string | Buffer;
  contentType?: string;
  encoding?: string;
  cid?: string; // Para imagens inline
}

export interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
  metadata?: Record<string, any>;
}

export interface BatchEmailResult {
  success: boolean;
  results: EmailResult[];
  totalSent: number;
  totalFailed: number;
  errors: string[];
}

export interface DomainVerification {
  domain: string;
  verified: boolean;
  dnsRecords?: {
    type: string;
    name: string;
    value: string;
    status: 'verified' | 'pending' | 'failed';
  }[];
  error?: string;
}

export interface EmailDeliveryStatus {
  messageId: string;
  status: 'pending' | 'sent' | 'delivered' | 'failed' | 'bounced' | 'complained' | 'opened' | 'clicked';
  timestamp: string;
  recipient: string;
  error?: string;
  events?: {
    type: string;
    timestamp: string;
    data?: Record<string, any>;
  }[];
}

/**
 * Interface base que todos os provedores de e-mail devem implementar
 */
export interface EmailProvider {
  /**
   * Envia um e-mail
   */
  send(email: EmailData): Promise<EmailResult>;

  /**
   * Envia múltiplos e-mails em lote
   */
  sendBatch(emails: EmailData[]): Promise<BatchEmailResult>;

  /**
   * Verifica o status de entrega de um e-mail
   */
  getDeliveryStatus(messageId: string): Promise<EmailDeliveryStatus>;

  /**
   * Verifica se um domínio está configurado corretamente
   */
  verifyDomain(domain: string): Promise<DomainVerification>;

  /**
   * Valida se o provedor está configurado corretamente
   */
  validateConfiguration(): Promise<boolean>;

  /**
   * Retorna o nome do provedor
   */
  getProviderName(): string;
}

/**
 * Classe base abstrata para provedores de e-mail
 */
export abstract class EmailProviderBase implements EmailProvider {
  protected providerName: string;

  constructor(providerName: string) {
    this.providerName = providerName;
  }

  abstract send(email: EmailData): Promise<EmailResult>;
  abstract sendBatch(emails: EmailData[]): Promise<BatchEmailResult>;
  abstract getDeliveryStatus(messageId: string): Promise<EmailDeliveryStatus>;
  abstract verifyDomain(domain: string): Promise<DomainVerification>;
  abstract validateConfiguration(): Promise<boolean>;

  getProviderName(): string {
    return this.providerName;
  }

  /**
   * Valida dados básicos do e-mail
   */
  protected validateEmailData(email: EmailData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!email.to || (Array.isArray(email.to) && email.to.length === 0)) {
      errors.push('Destinatário é obrigatório');
    }

    if (!email.from) {
      errors.push('Remetente é obrigatório');
    }

    if (!email.subject) {
      errors.push('Assunto é obrigatório');
    }

    if (!email.html && !email.text) {
      errors.push('Conteúdo HTML ou texto é obrigatório');
    }

    // Validar formato de e-mail básico
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    const recipients = Array.isArray(email.to) ? email.to : [email.to];
    for (const recipient of recipients) {
      if (!emailRegex.test(recipient)) {
        errors.push(`E-mail inválido: ${recipient}`);
      }
    }

    if (!emailRegex.test(email.from)) {
      errors.push(`E-mail do remetente inválido: ${email.from}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
