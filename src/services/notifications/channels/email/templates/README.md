# Templates de E-mail React Email

Este diretório contém todos os templates de e-mail da aplicação, convertidos para usar React Email para melhor compatibilidade, responsividade e manutenibilidade.

## 📁 Estrutura dos Arquivos

```
templates/
├── base-email-template.tsx          # Template base e componentes auxiliares
├── payment-reminder-template.tsx    # Lembrete de pagamento padrão
├── payment-due-soon-template.tsx    # Pagamento vence em 3 dias
├── payment-due-today-template.tsx   # Pagamento vence hoje
├── payment-overdue-template.tsx     # Pagamento em atraso (3 níveis)
├── welcome-template.tsx             # Boas-vindas para novos alunos
├── event-invitation-template.tsx    # Convites para eventos
├── admin-alert-template.tsx         # Alertas administrativos
├── class-reminder-template.tsx      # Lembrete de aulas
├── enrollment-payment-due-template.tsx # Vencimento de matrícula
├── expense-due-template.tsx         # Vencimento de despesas
├── new-enrollment-notification-template.tsx # Nova matrícula
├── system-default-template.tsx      # Template padrão do sistema
├── template-mapper.ts               # Utilitários de conversão
├── usage-examples.ts                # Exemplos de uso
├── index.ts                         # Exportações centralizadas
└── README.md                        # Esta documentação
```

## 🎨 Templates Disponíveis

### Templates de Pagamento

#### 1. **PaymentReminderTemplate** - Lembrete Padrão
- **Uso**: Lembretes gerais de pagamento
- **Tom**: Neutro e informativo
- **Props principais**: `studentName`, `amount`, `dueDate`, `planName`

#### 2. **PaymentDueSoonTemplate** - Vence em Breve
- **Uso**: Pagamentos que vencem em 3 dias
- **Tom**: Amigável e preventivo
- **Props principais**: `daysUntilDue` (padrão: 3)

#### 3. **PaymentDueTodayTemplate** - Vence Hoje
- **Uso**: Pagamentos que vencem no dia atual
- **Tom**: Urgência moderada
- **Props principais**: `gracePeriod`

#### 4. **PaymentOverdueTemplate** - Em Atraso
- **Uso**: Pagamentos em atraso com escalação
- **Tom**: Varia por nível (1: moderado, 2: alto, 3: crítico)
- **Props principais**: `overdueDays`, `overdueLevel`, `lateFee`, `suspensionWarning`

### Templates de Estudante

#### 5. **WelcomeTemplate** - Boas-vindas
- **Uso**: Novos alunos matriculados
- **Tom**: Caloroso e motivacional
- **Props principais**: `planName`, `startDate`, `enrollmentDate`, `instructorName`

#### 6. **ClassReminderTemplate** - Lembrete de Aula
- **Uso**: Aulas próximas
- **Tom**: Amigável e informativo
- **Props principais**: `className`, `classDate`, `classTime`, `instructorName`

### Templates de Eventos

#### 7. **EventInvitationTemplate** - Convite para Evento
- **Uso**: Eventos especiais da academia
- **Tom**: Empolgante e informativo
- **Props principais**: `eventName`, `eventDate`, `eventLocation`, `eventPrice`

### Templates Administrativos

#### 8. **AdminAlertTemplate** - Alertas Administrativos
- **Uso**: Notificações para gestores
- **Tom**: Profissional, varia por prioridade
- **Props principais**: `alertType`, `priority`, `overdueCount`, `totalAtRisk`

## 🚀 Como Usar

### Uso Básico

```typescript
import { render } from '@react-email/render';
import { PaymentReminderTemplate } from './templates';

const html = await render(PaymentReminderTemplate({
  academyName: 'Academia Guerreiros',
  studentName: 'João Silva',
  amount: 150.00,
  dueDate: '2024-01-15',
  planName: 'Plano Mensal'
}));
```

### Uso com Conversor Automático

```typescript
import { convertToReactEmailTemplate, renderEmailTemplate } from './templates';

// Dados vindos dos DEFAULT_TEMPLATES
const templateData = {
  academyName: 'Academia Guerreiros',
  studentName: 'João Silva',
  amount: '150.00',
  dueDate: '2024-01-15',
  planName: 'Plano Mensal',
  overdueDays: '5'
};

// Converte automaticamente
const { templateType, props } = convertToReactEmailTemplate(
  'payment',
  templateData,
  'payment_overdue_level_1'
);

// Ou renderiza diretamente
const html = await renderEmailTemplate('payment', templateData);
```

### Integração com Serviços

```typescript
import { ReactEmailService } from './templates/usage-examples';

const emailService = new ReactEmailService();

// Enviar lembrete de pagamento
await emailService.sendPaymentReminder(studentData, paymentData);

// Enviar boas-vindas
await emailService.sendWelcomeEmail(studentData, enrollmentData);
```

## 🎯 Mapeamento de Templates

O sistema mapeia automaticamente os templates HTML antigos para React Email:

| Template Antigo | React Email Template | Descrição |
|----------------|---------------------|-----------|
| `payment_reminder_basic` | `payment-reminder` | Lembrete padrão |
| `payment_reminder_soon` | `payment-due-soon` | Vence em 3 dias |
| `payment_reminder_today` | `payment-due-today` | Vence hoje |
| `payment_overdue_level_1` | `payment-overdue` | Atraso nível 1 |
| `payment_overdue_level_2` | `payment-overdue` | Atraso nível 2 |
| `payment_overdue_level_3` | `payment-overdue` | Atraso nível 3 |
| `enrollment_welcome` | `welcome` | Boas-vindas |
| `event_invitation` | `event-invitation` | Convite evento |
| `admin_alert_overdue` | `admin-alert` | Alerta admin |

## 🎨 Personalização

### Cores da Academia

Todos os templates suportam personalização de cores:

```typescript
const props = {
  // ... outras props
  primaryColor: '#007291',    // Cor principal da academia
  secondaryColor: '#004E89',  // Cor secundária
  academyLogo: 'https://...'  // Logo da academia
};
```

### Componentes Auxiliares

Use os componentes auxiliares para consistência:

```typescript
import { EmailHeading, EmailText, EmailButton, EmailDivider } from './base-email-template';

// Em seus templates customizados
<EmailHeading level={2} color={primaryColor}>
  Título da Seção
</EmailHeading>

<EmailText variant="small" color="#64748b">
  Texto explicativo
</EmailText>

<EmailButton href="https://..." primaryColor={primaryColor}>
  Botão de Ação
</EmailButton>
```

## 📊 Níveis de Atraso

O template `PaymentOverdueTemplate` suporta 3 níveis de escalação:

- **Nível 1** (1-7 dias): Tom moderado, cor laranja
- **Nível 2** (8-14 dias): Tom alto, cor vermelha
- **Nível 3** (15+ dias): Tom crítico, cor vermelha escura

## 🔧 Validação de Props

Use a função de validação para garantir que todas as props necessárias estão presentes:

```typescript
import { validateTemplateProps, getEmailTemplateMetadata } from './templates';

const isValid = validateTemplateProps('payment-reminder', props);
const metadata = getEmailTemplateMetadata('payment-reminder');
console.log('Props obrigatórias:', metadata.requiredProps);
```

## 📱 Responsividade

Todos os templates são responsivos e funcionam bem em:
- Clientes de desktop (Outlook, Thunderbird, etc.)
- Webmail (Gmail, Yahoo, Outlook.com)
- Mobile (iOS Mail, Android Gmail)

## 🧪 Testes

Veja `usage-examples.ts` para exemplos completos de como usar cada template com dados reais.

## 🔄 Migração dos Templates HTML

Para migrar templates HTML existentes:

1. Use `convertToReactEmailTemplate()` para conversão automática
2. Ajuste props específicas conforme necessário
3. Teste a renderização com `render()`
4. Valide em diferentes clientes de email

## 📝 Contribuindo

Ao criar novos templates:

1. Estenda `BaseEmailTemplate` para consistência
2. Use componentes auxiliares (`EmailHeading`, `EmailText`, etc.)
3. Defina interface TypeScript para as props
4. Adicione metadados em `EMAIL_TEMPLATE_METADATA`
5. Exporte no `index.ts`
6. Adicione exemplos em `usage-examples.ts`
