/**
 * Template base para e-mails personalizáveis por academia
 * Utiliza React Email para criar templates responsivos e personalizáveis
 */

import * as React from 'react';
import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Img,
  Heading,
  Text,
  Button,
  Hr,
  Font
} from '@react-email/components';

export interface BaseEmailTemplateProps {
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  children: React.ReactNode;
  previewText?: string;
}

export function BaseEmailTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  children,
  previewText
}: BaseEmailTemplateProps) {
  return (
    <Html lang="pt-BR">
      <Head>
        <Font
          fontFamily="Inter"
          fallbackFontFamily="Arial"
          webFont={{
            url: 'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiJ-Ek-_EeA.woff2',
            format: 'woff2',
          }}
          fontWeight={400}
          fontStyle="normal"
        />
        {previewText && (
          <meta name="description" content={previewText} />
        )}
      </Head>
      <Body style={bodyStyle}>
        <Container style={containerStyle}>
          {/* Header com logo da academia */}
          <Section style={headerStyle}>
            {academyLogo && (
              <Img
                src={academyLogo}
                alt={`Logo ${academyName}`}
                style={{
                  ...logoStyle,
                  maxHeight: '80px',
                  maxWidth: '200px',
                  width: 'auto',
                  height: 'auto'
                }}
              />
            )}
            <Heading style={{
              ...academyNameStyle,
              color: primaryColor
            }}>
              {academyName}
            </Heading>
          </Section>

          {/* Conteúdo principal */}
          <Section style={contentStyle}>
            {children}
          </Section>

          {/* Footer */}
          <Section style={{
            ...footerStyle,
            borderTop: `2px solid ${primaryColor}`
          }}>
            <Text style={footerTextStyle}>
              {academyName}
            </Text>
            <Text style={footerTextStyle}>
              Este e-mail foi enviado automaticamente. Por favor, não responda.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Estilos base
const bodyStyle = {
  backgroundColor: '#f6f9fc',
  fontFamily: 'Inter, Arial, sans-serif',
  margin: 0,
  padding: 0
};

const containerStyle = {
  maxWidth: '600px',
  margin: '0 auto',
  backgroundColor: '#ffffff',
  borderRadius: '8px',
  overflow: 'hidden',
  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
};

const headerStyle = {
  textAlign: 'center' as const,
  padding: '32px 24px 24px',
  backgroundColor: '#ffffff'
};

const logoStyle = {
  display: 'block',
  margin: '0 auto 16px'
};

const academyNameStyle = {
  fontSize: '24px',
  fontWeight: '600',
  margin: '0',
  lineHeight: '1.2'
};

const contentStyle = {
  padding: '0 24px 32px'
};

const footerStyle = {
  padding: '24px',
  textAlign: 'center' as const,
  backgroundColor: '#f8fafc'
};

const footerTextStyle = {
  fontSize: '12px',
  color: '#64748b',
  margin: '4px 0',
  lineHeight: '1.4'
};

// Componentes auxiliares para uso nos templates
export interface EmailButtonProps {
  href: string;
  children: React.ReactNode;
  primaryColor?: string;
}

export function EmailButton({ href, children, primaryColor = '#007291' }: EmailButtonProps) {
  return (
    <Button
      href={href}
      style={{
        backgroundColor: primaryColor,
        color: '#ffffff',
        padding: '12px 24px',
        borderRadius: '6px',
        textDecoration: 'none',
        fontWeight: '500',
        fontSize: '16px',
        border: 'none',
        cursor: 'pointer',
        display: 'inline-block'
      }}
    >
      {children}
    </Button>
  );
}

export interface EmailTextProps {
  children: React.ReactNode;
  variant?: 'body' | 'small' | 'large';
  color?: string;
  align?: 'left' | 'center' | 'right';
}

export function EmailText({ 
  children, 
  variant = 'body', 
  color = '#374151',
  align = 'left'
}: EmailTextProps) {
  const styles = {
    body: { fontSize: '16px', lineHeight: '1.6' },
    small: { fontSize: '14px', lineHeight: '1.5' },
    large: { fontSize: '18px', lineHeight: '1.6' }
  };

  return (
    <Text style={{
      ...styles[variant],
      color,
      textAlign: align,
      margin: '16px 0'
    }}>
      {children}
    </Text>
  );
}

export interface EmailHeadingProps {
  children: React.ReactNode;
  level?: 1 | 2 | 3;
  color?: string;
  align?: 'left' | 'center' | 'right';
}

export function EmailHeading({ 
  children, 
  level = 2, 
  color = '#1f2937',
  align = 'left'
}: EmailHeadingProps) {
  const styles = {
    1: { fontSize: '32px', fontWeight: '700' },
    2: { fontSize: '24px', fontWeight: '600' },
    3: { fontSize: '20px', fontWeight: '600' }
  };

  return (
    <Heading style={{
      ...styles[level],
      color,
      textAlign: align,
      margin: '24px 0 16px',
      lineHeight: '1.2'
    }}>
      {children}
    </Heading>
  );
}

export function EmailDivider({ color = '#e5e7eb' }: { color?: string }) {
  return <Hr style={{ borderColor: color, margin: '24px 0' }} />;
}
