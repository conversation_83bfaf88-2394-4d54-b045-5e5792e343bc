/**
 * Template de e-mail para convites de eventos
 * Tom empolgante e informativo
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface EventInvitationTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  
  // Dados do evento
  eventName: string;
  eventDescription: string;
  eventDate: string;
  eventTime?: string;
  eventLocation: string;
  eventDuration?: string;
  
  // Dados do destinatário
  studentName?: string;
  
  // Informações opcionais
  eventPrice?: number;
  currency?: string;
  registrationUrl?: string;
  registrationDeadline?: string;
  maxParticipants?: number;
  currentParticipants?: number;
  eventImage?: string;
  requirements?: string[];
  benefits?: string[];
}

export function EventInvitationTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  eventName,
  eventDescription,
  eventDate,
  eventTime,
  eventLocation,
  eventDuration,
  studentName,
  eventPrice,
  currency = 'BRL',
  registrationUrl,
  registrationDeadline,
  maxParticipants,
  currentParticipants,
  eventImage,
  requirements,
  benefits
}: EventInvitationTemplateProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('pt-BR', {
      weekday: 'long',
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    }).format(new Date(dateString));
  };

  const formatShortDate = (dateString: string) => {
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(new Date(dateString));
  };

  const eventDateFormatted = formatDate(eventDate);
  const registrationDeadlineFormatted = registrationDeadline ? formatShortDate(registrationDeadline) : null;

  const spotsRemaining = maxParticipants && currentParticipants 
    ? maxParticipants - currentParticipants 
    : null;

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`Convite especial: ${eventName} - ${academyName}`}
    >
      <EmailHeading level={1} color={primaryColor}>
        🎉 Convite Especial
      </EmailHeading>

      {studentName && (
        <EmailText>
          Olá <strong>{studentName}</strong>,
        </EmailText>
      )}

      <EmailText>
        Temos o prazer de convidá-lo(a) para um evento especial na {academyName}!
      </EmailText>

      {/* Imagem do evento */}
      {eventImage && (
        <div style={{ textAlign: 'center', margin: '24px 0' }}>
          <img 
            src={eventImage} 
            alt={eventName}
            style={{
              maxWidth: '100%',
              height: 'auto',
              borderRadius: '8px',
              border: `2px solid ${primaryColor}20`
            }}
          />
        </div>
      )}

      <EmailDivider color={primaryColor} />

      {/* Detalhes do evento */}
      <div style={{
        backgroundColor: '#f8fafc',
        padding: '24px',
        borderRadius: '8px',
        border: `1px solid ${primaryColor}20`
      }}>
        <EmailHeading level={2} color={primaryColor}>
          📅 {eventName}
        </EmailHeading>

        <EmailText>
          {eventDescription}
        </EmailText>

        <div style={{ marginTop: '20px' }}>
          <EmailText variant="small" color="#374151">
            <strong>📅 Data:</strong> {eventDateFormatted}
          </EmailText>
          {eventTime && (
            <EmailText variant="small" color="#374151">
              <strong>⏰ Horário:</strong> {eventTime}
            </EmailText>
          )}
          <EmailText variant="small" color="#374151">
            <strong>📍 Local:</strong> {eventLocation}
          </EmailText>
          {eventDuration && (
            <EmailText variant="small" color="#374151">
              <strong>⏱️ Duração:</strong> {eventDuration}
            </EmailText>
          )}
          {eventPrice !== undefined && (
            <EmailText variant="small" color="#374151">
              <strong>💰 Investimento:</strong> {eventPrice === 0 ? 'Gratuito' : formatCurrency(eventPrice)}
            </EmailText>
          )}
        </div>
      </div>

      {/* Vagas disponíveis */}
      {spotsRemaining !== null && (
        <div style={{
          backgroundColor: spotsRemaining <= 5 ? '#fef2f2' : '#f0f9ff',
          padding: '16px',
          borderRadius: '6px',
          border: `1px solid ${spotsRemaining <= 5 ? '#fecaca' : '#93c5fd'}`,
          margin: '20px 0'
        }}>
          <EmailText variant="small" color={spotsRemaining <= 5 ? '#dc2626' : '#1e40af'}>
            <strong>👥 Vagas:</strong> {spotsRemaining} vagas restantes de {maxParticipants} totais
            {spotsRemaining <= 5 && ' - Últimas vagas!'}
          </EmailText>
        </div>
      )}

      {/* Benefícios */}
      {benefits && benefits.length > 0 && (
        <>
          <EmailHeading level={3} color={primaryColor}>
            ✨ O que você vai ganhar:
          </EmailHeading>
          <div style={{ marginBottom: '20px' }}>
            {benefits.map((benefit, index) => (
              <EmailText key={index} variant="small">
                • {benefit}
              </EmailText>
            ))}
          </div>
        </>
      )}

      {/* Requisitos */}
      {requirements && requirements.length > 0 && (
        <>
          <EmailHeading level={3} color={primaryColor}>
            📋 Requisitos:
          </EmailHeading>
          <div style={{ marginBottom: '20px' }}>
            {requirements.map((requirement, index) => (
              <EmailText key={index} variant="small" color="#64748b">
                • {requirement}
              </EmailText>
            ))}
          </div>
        </>
      )}

      <EmailDivider />

      {/* Prazo de inscrição */}
      {registrationDeadlineFormatted && (
        <div style={{
          backgroundColor: '#fffbeb',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #fed7aa',
          marginBottom: '24px'
        }}>
          <EmailText variant="small" color="#d97706">
            ⏰ <strong>Prazo de Inscrição:</strong> até {registrationDeadlineFormatted}
          </EmailText>
        </div>
      )}

      {/* Botão de inscrição */}
      <div style={{ textAlign: 'center', margin: '32px 0' }}>
        {registrationUrl ? (
          <EmailButton href={registrationUrl} primaryColor={primaryColor}>
            Quero Participar!
          </EmailButton>
        ) : (
          <EmailButton href="#" primaryColor={primaryColor}>
            Confirmar Presença
          </EmailButton>
        )}
      </div>

      {/* Informações adicionais */}
      <EmailText variant="small" color="#64748b">
        <strong>Dúvidas?</strong> Entre em contato conosco para mais informações 
        sobre o evento ou processo de inscrição.
      </EmailText>

      <div style={{
        backgroundColor: '#f0fdf4',
        padding: '16px',
        borderRadius: '6px',
        border: '1px solid #bbf7d0',
        marginTop: '24px'
      }}>
        <EmailText variant="small" color="#166534">
          🚀 <strong>Não perca essa oportunidade!</strong> Eventos como este são 
          momentos únicos de aprendizado e evolução. Esperamos você lá!
        </EmailText>
      </div>
    </BaseEmailTemplate>
  );
}
