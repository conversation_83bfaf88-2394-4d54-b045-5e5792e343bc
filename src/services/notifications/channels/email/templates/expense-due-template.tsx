/**
 * Template de e-mail para vencimento de despesas da academia
 * Enviado apenas para o dono/administrador da academia
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface ExpenseDueTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  
  // Dados do administrador
  ownerName: string;
  
  // Dados da despesa
  expenseDescription: string;
  amount: number;
  dueDate: string;
  category: string;
  supplier?: string;
  invoiceNumber?: string;
  paymentMethod?: string;
  
  // Configurações opcionais
  currency?: string;
  lateFee?: number;
  isRecurring?: boolean;
  nextDueDate?: string;
  dashboardUrl?: string;

  // Status da despesa
  isOverdue?: boolean;
  daysOverdue?: number;
}

export function ExpenseDueTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  ownerName,
  expenseDescription,
  amount,
  dueDate,
  category,
  supplier,
  invoiceNumber,
  paymentMethod,
  currency = 'BRL',
  lateFee,
  isRecurring = false,
  nextDueDate,
  dashboardUrl,
  isOverdue: isOverdueProp,
  daysOverdue = 0
}: ExpenseDueTemplateProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    if (!dateString || dateString === 'undefined' || dateString === 'null') {
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(new Date());
    }

    // Se a data está no formato YYYY-MM-DD (apenas data), tratar como data local
    // para evitar problemas de timezone
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      const [year, month, day] = dateString.split('-').map(Number);
      const date = new Date(year, month - 1, day); // month é 0-indexed
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(date);
    }

    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(new Date());
    }

    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };

  // Usar a propriedade isOverdue passada ou calcular automaticamente
  const isOverdue = isOverdueProp !== undefined ? isOverdueProp : new Date(dueDate) < new Date();
  const dueDateFormatted = formatDate(dueDate);

  const getCategoryIcon = (category: string) => {
    const icons: { [key: string]: string } = {
      'aluguel': '🏢',
      'energia': '⚡',
      'agua': '💧',
      'internet': '🌐',
      'equipamentos': '🥋',
      'marketing': '📢',
      'seguros': '🛡️',
      'impostos': '📊',
      'manutencao': '🔧',
      'outros': '📋'
    };
    return icons[category.toLowerCase()] || '💰';
  };

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`Vencimento de despesa - ${expenseDescription} - ${formatCurrency(amount)}`}
    >
      <EmailHeading level={1} color={primaryColor}>
        {isOverdue ? '⚠️ Despesa em Atraso' : '💼 Vencimento de Despesa'}
      </EmailHeading>

      <EmailText>
        Olá <strong>{ownerName}</strong>,
      </EmailText>

      <EmailText>
        {isOverdue ? (
          <>
            Uma despesa da sua academia está em atraso{daysOverdue > 0 ? ` há ${daysOverdue} dias` : ''}. É importante regularizar
            esta situação para manter as operações da academia em dia e evitar
            possíveis complicações.
          </>
        ) : (
          <>
            Este é um lembrete sobre uma despesa que vence em breve. Mantenha
            as finanças da sua academia organizadas!
          </>
        )}
      </EmailText>

      <EmailDivider color={primaryColor} />

      {/* Detalhes da despesa */}
      <div style={{
        backgroundColor: '#f8fafc',
        padding: '24px',
        borderRadius: '8px',
        border: `1px solid ${primaryColor}20`
      }}>
        <EmailHeading level={3} color={primaryColor}>
          {getCategoryIcon(category)} Detalhes da Despesa
        </EmailHeading>

        <div style={{ marginBottom: '16px' }}>
          <EmailText variant="small" color="#64748b">
            <strong>Descrição:</strong> {expenseDescription}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Categoria:</strong> {category}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Valor:</strong> {formatCurrency(amount)}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Vencimento:</strong> {dueDateFormatted}
          </EmailText>
          {supplier && (
            <EmailText variant="small" color="#64748b">
              <strong>Fornecedor:</strong> {supplier}
            </EmailText>
          )}
          {invoiceNumber && (
            <EmailText variant="small" color="#64748b">
              <strong>Número da Fatura:</strong> {invoiceNumber}
            </EmailText>
          )}
          {paymentMethod && (
            <EmailText variant="small" color="#64748b">
              <strong>Forma de Pagamento:</strong> {paymentMethod}
            </EmailText>
          )}
          {isRecurring && (
            <EmailText variant="small" color="#64748b">
              <strong>Tipo:</strong> Despesa Recorrente
            </EmailText>
          )}
        </div>

        {isOverdue && lateFee && (
          <div style={{
            backgroundColor: '#fef2f2',
            padding: '16px',
            borderRadius: '6px',
            border: '1px solid #fecaca',
            marginBottom: '16px'
          }}>
            <EmailText variant="small" color="#dc2626">
              <strong>⚠️ Multa por Atraso:</strong> {formatCurrency(lateFee)}
            </EmailText>
            <EmailText variant="small" color="#dc2626">
              <strong>Total a Pagar:</strong> {formatCurrency(amount + lateFee)}
            </EmailText>
          </div>
        )}

        {isRecurring && nextDueDate && (
          <div style={{
            backgroundColor: '#f0f9ff',
            padding: '16px',
            borderRadius: '6px',
            border: '1px solid #bae6fd',
            marginBottom: '16px'
          }}>
            <EmailText variant="small" color="#0369a1">
              <strong>🔄 Próximo Vencimento:</strong> {formatDate(nextDueDate)}
            </EmailText>
          </div>
        )}
      </div>

      <EmailDivider />

      {/* Botão de ação */}
      <div style={{ textAlign: 'center', margin: '32px 0' }}>
        {dashboardUrl ? (
          <EmailButton href={dashboardUrl} primaryColor={primaryColor}>
            Ver no Dashboard
          </EmailButton>
        ) : (
          <EmailButton href="#" primaryColor={primaryColor}>
            Gerenciar Despesas
          </EmailButton>
        )}
      </div>

      {/* Dicas de gestão financeira */}
      <div style={{
        backgroundColor: '#f0fdf4',
        padding: '16px',
        borderRadius: '6px',
        border: '1px solid #bbf7d0',
        marginBottom: '24px'
      }}>
        <EmailText variant="small" color="#166534">
          <strong>💡 Dica de Gestão:</strong> Mantenha um controle rigoroso das despesas 
          para garantir a saúde financeira da sua academia. Considere automatizar 
          pagamentos recorrentes para evitar atrasos.
        </EmailText>
      </div>

      {/* Informações sobre fluxo de caixa */}
      <EmailText variant="small" color="#64748b">
        <strong>Gestão Financeira:</strong> Acompanhe regularmente o fluxo de caixa 
        da sua academia através do dashboard. Isso ajuda a manter as finanças 
        organizadas e a tomar decisões estratégicas.
      </EmailText>

      {isOverdue && (
        <div style={{
          backgroundColor: '#fffbeb',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #fed7aa',
          marginTop: '24px'
        }}>
          <EmailText variant="small" color="#d97706">
            <strong>⏰ Ação Urgente:</strong> Despesas em atraso podem afetar o 
            funcionamento da academia e gerar multas adicionais. Regularize 
            esta situação o quanto antes para manter as operações normais.
          </EmailText>
        </div>
      )}
    </BaseEmailTemplate>
  );
}
