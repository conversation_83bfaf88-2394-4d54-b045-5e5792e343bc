/**
 * Template de e-mail para notificar sobre nova matrícula de aluno
 * Enviado apenas para o dono/administrador da academia
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface NewEnrollmentNotificationTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  
  // Dados do administrador
  ownerName: string;
  
  // Dados do novo aluno
  studentName: string;
  studentEmail: string;
  studentPhone?: string;
  studentAge?: number;
  
  // Dados da matrícula
  planName: string;
  enrollmentDate: string;
  startDate: string;
  amount: number;
  paymentMethod?: string;
  paymentStatus: 'paid' | 'pending' | 'partial';
  
  // Configurações opcionais
  currency?: string;
  dashboardUrl?: string;
  studentProfileUrl?: string;
  totalStudents?: number;
  monthlyEnrollments?: number;
}

export function NewEnrollmentNotificationTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  ownerName,
  studentName,
  studentEmail,
  studentPhone,
  studentAge,
  planName,
  enrollmentDate,
  startDate,
  amount,
  paymentMethod,
  paymentStatus,
  currency = 'BRL',
  dashboardUrl,
  studentProfileUrl,
  totalStudents,
  monthlyEnrollments
}: NewEnrollmentNotificationTemplateProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(new Date(dateString));
  };

  const getPaymentStatusInfo = () => {
    switch (paymentStatus) {
      case 'paid':
        return { text: 'Pago', color: '#059669', bgColor: '#f0fdf4', borderColor: '#bbf7d0', icon: '✅' };
      case 'pending':
        return { text: 'Pendente', color: '#d97706', bgColor: '#fffbeb', borderColor: '#fed7aa', icon: '⏳' };
      case 'partial':
        return { text: 'Parcial', color: '#0369a1', bgColor: '#f0f9ff', borderColor: '#bae6fd', icon: '💰' };
      default:
        return { text: 'Desconhecido', color: '#64748b', bgColor: '#f8fafc', borderColor: '#e2e8f0', icon: '❓' };
    }
  };

  const paymentInfo = getPaymentStatusInfo();
  const enrollmentDateFormatted = formatDate(enrollmentDate);
  const startDateFormatted = formatDate(startDate);

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`Nova matrícula: ${studentName} - ${planName}`}
    >
      <EmailHeading level={1} color={primaryColor}>
        🎉 Nova Matrícula Realizada!
      </EmailHeading>

      <EmailText>
        Olá <strong>{ownerName}</strong>,
      </EmailText>

      <EmailText>
        Temos uma excelente notícia! Um novo aluno se matriculou na sua academia. 
        Confira os detalhes abaixo e dê as boas-vindas ao mais novo membro da família!
      </EmailText>

      <EmailDivider color={primaryColor} />

      {/* Informações do novo aluno */}
      <div style={{
        backgroundColor: '#f0fdf4',
        padding: '24px',
        borderRadius: '8px',
        border: `1px solid ${primaryColor}20`
      }}>
        <EmailHeading level={3} color={primaryColor}>
          👤 Dados do Novo Aluno
        </EmailHeading>

        <div style={{ marginBottom: '16px' }}>
          <EmailText variant="small" color="#64748b">
            <strong>Nome:</strong> {studentName}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>E-mail:</strong> {studentEmail}
          </EmailText>
          {studentPhone && (
            <EmailText variant="small" color="#64748b">
              <strong>Telefone:</strong> {studentPhone}
            </EmailText>
          )}
          {studentAge && (
            <EmailText variant="small" color="#64748b">
              <strong>Idade:</strong> {studentAge} anos
            </EmailText>
          )}
        </div>
      </div>

      <EmailDivider />

      {/* Detalhes da matrícula */}
      <div style={{
        backgroundColor: '#f8fafc',
        padding: '24px',
        borderRadius: '8px',
        border: `1px solid ${primaryColor}20`
      }}>
        <EmailHeading level={3} color={primaryColor}>
          📋 Detalhes da Matrícula
        </EmailHeading>

        <div style={{ marginBottom: '16px' }}>
          <EmailText variant="small" color="#64748b">
            <strong>Plano Escolhido:</strong> {planName}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Data da Matrícula:</strong> {enrollmentDateFormatted}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Início das Aulas:</strong> {startDateFormatted}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Valor:</strong> {formatCurrency(amount)}
          </EmailText>
          {paymentMethod && (
            <EmailText variant="small" color="#64748b">
              <strong>Forma de Pagamento:</strong> {paymentMethod}
            </EmailText>
          )}
        </div>

        {/* Status do pagamento */}
        <div style={{
          backgroundColor: paymentInfo.bgColor,
          padding: '16px',
          borderRadius: '6px',
          border: `1px solid ${paymentInfo.borderColor}`,
          marginBottom: '16px'
        }}>
          <EmailText variant="small" color={paymentInfo.color}>
            <strong>{paymentInfo.icon} Status do Pagamento:</strong> {paymentInfo.text}
          </EmailText>
        </div>
      </div>

      <EmailDivider />

      {/* Estatísticas rápidas */}
      {(totalStudents || monthlyEnrollments) && (
        <>
          <div style={{
            backgroundColor: '#eff6ff',
            padding: '16px',
            borderRadius: '6px',
            border: '1px solid #bfdbfe',
            marginBottom: '24px'
          }}>
            <EmailHeading level={3} color={primaryColor}>
              📊 Estatísticas da Academia
            </EmailHeading>
            
            <div style={{ display: 'flex', gap: '24px', flexWrap: 'wrap' }}>
              {totalStudents && (
                <div style={{ flex: '1', minWidth: '120px' }}>
                  <EmailText variant="small" color="#1d4ed8">
                    <strong>Total de Alunos:</strong> {totalStudents}
                  </EmailText>
                </div>
              )}
              {monthlyEnrollments && (
                <div style={{ flex: '1', minWidth: '120px' }}>
                  <EmailText variant="small" color="#1d4ed8">
                    <strong>Matrículas este Mês:</strong> {monthlyEnrollments}
                  </EmailText>
                </div>
              )}
            </div>
          </div>
          <EmailDivider />
        </>
      )}

      {/* Botões de ação */}
      <div style={{ textAlign: 'center', margin: '32px 0' }}>
        <div style={{ display: 'flex', gap: '16px', justifyContent: 'center', flexWrap: 'wrap' }}>
          {studentProfileUrl && (
            <EmailButton href={studentProfileUrl} primaryColor={primaryColor}>
              Ver Perfil do Aluno
            </EmailButton>
          )}
          {dashboardUrl && (
            <EmailButton href={dashboardUrl} primaryColor={secondaryColor}>
              Ir para Dashboard
            </EmailButton>
          )}
        </div>
      </div>

      {/* Próximos passos */}
      <div style={{
        backgroundColor: '#f0f9ff',
        padding: '16px',
        borderRadius: '6px',
        border: '1px solid #bae6fd',
        marginBottom: '24px'
      }}>
        <EmailText variant="small" color="#0369a1">
          <strong>📝 Próximos Passos:</strong>
          <br />• Entre em contato com o novo aluno para dar as boas-vindas
          <br />• Verifique se todos os documentos necessários foram enviados
          <br />• Confirme a presença na primeira aula
          {paymentStatus === 'pending' && <><br />• Acompanhe o status do pagamento</>}
        </EmailText>
      </div>

      {/* Mensagem motivacional */}
      <EmailText variant="small" color="#64748b">
        <strong>Parabéns!</strong> Cada nova matrícula é um sinal do crescimento 
        e sucesso da sua academia. Continue oferecendo um excelente serviço 
        para atrair ainda mais alunos!
      </EmailText>

      {paymentStatus === 'pending' && (
        <div style={{
          backgroundColor: '#fffbeb',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #fed7aa',
          marginTop: '24px'
        }}>
          <EmailText variant="small" color="#d97706">
            <strong>⚠️ Atenção:</strong> O pagamento desta matrícula ainda está pendente. 
            Acompanhe o status e entre em contato com o aluno se necessário.
          </EmailText>
        </div>
      )}
    </BaseEmailTemplate>
  );
}
