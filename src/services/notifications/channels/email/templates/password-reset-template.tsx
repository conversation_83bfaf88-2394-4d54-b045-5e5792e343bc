/**
 * Template React Email para redefinição de senha
 * Integrado com Supabase Auth e sistema multi-tenant
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface PasswordResetTemplateProps {
  // Dados da academia (multi-tenant)
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  
  // Dados do usuário
  userEmail: string;
  userName?: string;
  
  // Dados do Supabase Auth
  resetUrl: string;
  tokenHash: string;
  siteUrl: string;
  
  // Configurações opcionais
  expirationTime?: string;
  supportEmail?: string;
  supportPhone?: string;
}

export function PasswordResetTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  userEmail,
  userName,
  resetUrl,
  tokenHash,
  siteUrl,
  expirationTime = '1 hora',
  supportEmail,
  supportPhone
}: PasswordResetTemplateProps) {
  const previewText = `Redefinir senha - ${academyName}`;
  
  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={previewText}
    >
      {/* Saudação personalizada */}
      <EmailHeading level={1} color={primaryColor} align="center">
        Redefinir Senha
      </EmailHeading>
      
      <EmailText>
        {userName ? `Olá, ${userName}!` : 'Olá!'}
      </EmailText>
      
      <EmailText>
        Recebemos uma solicitação para redefinir a senha da sua conta em <strong>{academyName}</strong> 
        associada ao e-mail <strong>{userEmail}</strong>.
      </EmailText>
      
      <EmailText>
        Se você fez esta solicitação, clique no botão abaixo para criar uma nova senha:
      </EmailText>
      
      {/* Botão principal de reset */}
      <div style={{ textAlign: 'center', margin: '32px 0' }}>
        <EmailButton href={resetUrl} primaryColor={primaryColor}>
          Redefinir Minha Senha
        </EmailButton>
      </div>
      
      <EmailDivider />
      
      {/* Informações de segurança */}
      <EmailText variant="small" color="#6b7280">
        <strong>Informações importantes:</strong>
      </EmailText>
      
      <EmailText variant="small" color="#6b7280">
        • Este link é válido por {expirationTime}<br/>
        • O link só pode ser usado uma vez<br/>
        • Se você não solicitou esta redefinição, pode ignorar este e-mail com segurança
      </EmailText>
      
      <EmailDivider />
      
      {/* Link alternativo */}
      <EmailText variant="small" color="#6b7280">
        Se o botão não funcionar, copie e cole este link no seu navegador:
      </EmailText>
      
      <EmailText variant="small" color="#3b82f6" align="center">
        <a href={resetUrl} style={{ color: '#3b82f6', wordBreak: 'break-all' }}>
          {resetUrl}
        </a>
      </EmailText>
      
      <EmailDivider />
      
      {/* Informações de suporte */}
      {(supportEmail || supportPhone) && (
        <>
          <EmailText variant="small" color="#6b7280">
            <strong>Precisa de ajuda?</strong>
          </EmailText>
          
          <EmailText variant="small" color="#6b7280">
            Entre em contato conosco:
            {supportEmail && (
              <>
                <br/>📧 E-mail: <a href={`mailto:${supportEmail}`} style={{ color: primaryColor }}>{supportEmail}</a>
              </>
            )}
            {supportPhone && (
              <>
                <br/>📞 Telefone: <a href={`tel:${supportPhone}`} style={{ color: primaryColor }}>{supportPhone}</a>
              </>
            )}
          </EmailText>
        </>
      )}
      
      {/* Aviso de segurança */}
      <EmailDivider />
      
      <EmailText variant="small" color="#ef4444" align="center">
        <strong>⚠️ Nunca compartilhe este link com outras pessoas</strong>
      </EmailText>
      
      <EmailText variant="small" color="#6b7280" align="center">
        Por motivos de segurança, se você não solicitou esta redefinição de senha, 
        recomendamos que entre em contato conosco imediatamente.
      </EmailText>
    </BaseEmailTemplate>
  );
}

export default PasswordResetTemplate;
