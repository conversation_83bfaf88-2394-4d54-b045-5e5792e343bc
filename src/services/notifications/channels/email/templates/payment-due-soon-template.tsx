/**
 * Template de e-mail para pagamentos que vencem em breve (3 dias)
 * Tom amigável de lembrete preventivo
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface PaymentDueSoonTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  tenantSlug?: string;

  // Dados do estudante e pagamento
  studentName: string;
  amount: number;
  dueDate: string;
  planName: string;
  paymentMethod?: string;
  invoiceUrl?: string;
  paymentId?: string;

  // Configurações opcionais
  currency?: string;
  daysUntilDue?: number;
}

export function PaymentDueSoonTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  tenantSlug,
  studentName,
  amount,
  dueDate,
  planName,
  paymentMethod,
  invoiceUrl,
  paymentId,
  currency = 'BRL',
  daysUntilDue = 3
}: PaymentDueSoonTemplateProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    // Se a data está no formato YYYY-MM-DD (apenas data), tratar como data local
    // para evitar problemas de timezone
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      const [year, month, day] = dateString.split('-').map(Number);
      const date = new Date(year, month - 1, day); // month é 0-indexed
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(date);
    }

    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(new Date(dateString));
  };

  const dueDateFormatted = formatDate(dueDate);

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`Sua mensalidade vence em ${daysUntilDue} dias - ${planName}`}
    >
      <EmailHeading level={1} color={primaryColor}>
        📅 Lembrete Amigável
      </EmailHeading>

      <EmailText>
        Olá <strong>{studentName}</strong>,
      </EmailText>

      <EmailText>
        Esperamos que você esteja aproveitando seus treinos! Este é um lembrete 
        amigável de que sua mensalidade vence em <strong>{daysUntilDue} dias</strong>.
      </EmailText>

      <EmailDivider color={primaryColor} />

      {/* Detalhes do pagamento */}
      <div style={{
        backgroundColor: '#f0f9ff',
        padding: '24px',
        borderRadius: '8px',
        border: `1px solid ${primaryColor}20`
      }}>
        <EmailHeading level={3} color={primaryColor}>
          💳 Detalhes do Pagamento
        </EmailHeading>

        <div style={{ marginBottom: '16px' }}>
          <EmailText variant="small" color="#1e40af">
            <strong>Plano:</strong> {planName}
          </EmailText>
          <EmailText variant="small" color="#1e40af">
            <strong>Valor:</strong> {formatCurrency(amount)}
          </EmailText>
          <EmailText variant="small" color="#1e40af">
            <strong>Vencimento:</strong> {dueDateFormatted}
          </EmailText>
          {paymentMethod && (
            <EmailText variant="small" color="#1e40af">
              <strong>Forma de Pagamento:</strong> {paymentMethod}
            </EmailText>
          )}
        </div>

        <div style={{
          backgroundColor: '#dbeafe',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #93c5fd'
        }}>
          <EmailText variant="small" color="#1e40af">
            💡 <strong>Dica:</strong> Realize o pagamento com antecedência para garantir 
            que não haja interrupção em seus treinos!
          </EmailText>
        </div>
      </div>

      <EmailDivider />

      {/* Botão de ação */}
      <div style={{ textAlign: 'center', margin: '32px 0' }}>
        {paymentId && tenantSlug ? (
          <EmailButton href={`https://${tenantSlug}.${process.env.NEXT_PUBLIC_BASE_DOMAIN}/checkout/${paymentId}`} primaryColor={primaryColor}>
            Pagar Agora
          </EmailButton>
        ) : invoiceUrl ? (
          <EmailButton href={invoiceUrl} primaryColor={primaryColor}>
            Ver Fatura Completa
          </EmailButton>
        ) : (
          <EmailButton href="#" primaryColor={primaryColor}>
            Realizar Pagamento
          </EmailButton>
        )}
      </div>

      {/* Informações adicionais */}
      <EmailText variant="small" color="#64748b">
        <strong>Precisa de ajuda?</strong> Nossa equipe está sempre disponível 
        para esclarecer dúvidas sobre pagamentos ou qualquer outra questão.
      </EmailText>

      <EmailText variant="small" color="#64748b">
        Continue focado em seus objetivos! Estamos aqui para apoiar sua jornada. 💪
      </EmailText>
    </BaseEmailTemplate>
  );
}
