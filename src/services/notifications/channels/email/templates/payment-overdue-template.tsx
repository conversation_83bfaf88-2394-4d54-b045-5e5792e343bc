/**
 * Template de e-mail para pagamentos em atraso
 * Suporta diferentes níveis de urgência baseado nos dias de atraso
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface PaymentOverdueTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  tenantSlug?: string;
  
  // Dados do estudante e pagamento
  studentName: string;
  amount: number;
  dueDate: string;
  planName: string;
  paymentMethod?: string;
  invoiceUrl?: string;
  paymentId?: string;
  
  // Dados do atraso
  overdueDays: number;
  overdueLevel: 1 | 2 | 3; // 1: 3 dias, 2: 7 dias, 3: 15+ dias
  
  // Configurações opcionais
  currency?: string;
  lateFee?: number;
  suspensionWarning?: boolean;
}

export function PaymentOverdueTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  tenantSlug,
  studentName,
  amount,
  dueDate,
  planName,
  paymentMethod,
  invoiceUrl,
  paymentId,
  overdueDays,
  overdueLevel,
  currency = 'BRL',
  lateFee,
  suspensionWarning = false
}: PaymentOverdueTemplateProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    // Se a data está no formato YYYY-MM-DD (apenas data), tratar como data local
    // para evitar problemas de timezone
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      const [year, month, day] = dateString.split('-').map(Number);
      const date = new Date(year, month - 1, day); // month é 0-indexed
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(date);
    }

    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(new Date(dateString));
  };

  const dueDateFormatted = formatDate(dueDate);

  // Configurações baseadas no nível de atraso
  const levelConfig = {
    1: {
      title: '⚠️ Pagamento em Atraso',
      color: '#d97706',
      bgColor: '#fffbeb',
      borderColor: '#fed7aa',
      urgency: 'moderada'
    },
    2: {
      title: '🚨 Pagamento em Atraso Crítico',
      color: '#dc2626',
      bgColor: '#fef2f2',
      borderColor: '#fecaca',
      urgency: 'alta'
    },
    3: {
      title: '🚨 ÚLTIMO AVISO - Suspensão Iminente',
      color: '#991b1b',
      bgColor: '#fef2f2',
      borderColor: '#f87171',
      urgency: 'crítica'
    }
  };

  const config = levelConfig[overdueLevel];

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`ATRASO ${overdueDays} dias - ${planName} - ${formatCurrency(amount)}`}
    >
      <EmailHeading level={1} color={config.color}>
        {config.title}
      </EmailHeading>

      <EmailText>
        Olá <strong>{studentName}</strong>,
      </EmailText>

      <EmailText>
        {overdueLevel === 1 && (
          <>Sua mensalidade está em atraso há <strong>{overdueDays} dias</strong>. 
          Por favor, regularize sua situação o quanto antes para evitar complicações.</>
        )}
        {overdueLevel === 2 && (
          <>Sua mensalidade está em atraso há <strong>{overdueDays} dias</strong>. 
          Esta é uma situação crítica que requer ação imediata para evitar a suspensão das atividades.</>
        )}
        {overdueLevel === 3 && (
          <>Sua mensalidade está em atraso há <strong>{overdueDays} dias</strong>. 
          Este é o último aviso antes da suspensão das atividades. Ação URGENTE necessária.</>
        )}
      </EmailText>

      <EmailDivider color={config.color} />

      {/* Detalhes do pagamento */}
      <div style={{
        backgroundColor: config.bgColor,
        padding: '24px',
        borderRadius: '8px',
        border: `2px solid ${config.borderColor}`
      }}>
        <EmailHeading level={3} color={config.color}>
          📋 Detalhes do Pagamento
        </EmailHeading>

        <div style={{ marginBottom: '16px' }}>
          <EmailText variant="small" color={config.color}>
            <strong>Plano:</strong> {planName}
          </EmailText>
          <EmailText variant="small" color={config.color}>
            <strong>Valor Original:</strong> {formatCurrency(amount)}
          </EmailText>
          <EmailText variant="small" color={config.color}>
            <strong>Vencimento:</strong> {dueDateFormatted}
          </EmailText>
          <EmailText variant="small" color={config.color}>
            <strong>Dias em Atraso:</strong> {overdueDays} dias
          </EmailText>
          {paymentMethod && (
            <EmailText variant="small" color={config.color}>
              <strong>Forma de Pagamento:</strong> {paymentMethod}
            </EmailText>
          )}
        </div>

        {lateFee && (
          <div style={{
            backgroundColor: '#fee2e2',
            padding: '16px',
            borderRadius: '6px',
            border: '1px solid #fca5a5',
            marginBottom: '16px'
          }}>
            <EmailText variant="small" color="#dc2626">
              <strong>💰 Multa por Atraso:</strong> {formatCurrency(lateFee)}
            </EmailText>
            <EmailText variant="small" color="#dc2626">
              <strong>Total a Pagar:</strong> {formatCurrency(amount + lateFee)}
            </EmailText>
          </div>
        )}

        {suspensionWarning && (
          <div style={{
            backgroundColor: '#fef2f2',
            padding: '16px',
            borderRadius: '6px',
            border: '2px solid #dc2626'
          }}>
            <EmailText variant="small" color="#dc2626">
              ⛔ <strong>AVISO DE SUSPENSÃO:</strong> Suas atividades serão suspensas 
              se o pagamento não for realizado imediatamente.
            </EmailText>
          </div>
        )}
      </div>

      <EmailDivider />

      {/* Botão de ação urgente */}
      <div style={{ textAlign: 'center', margin: '32px 0' }}>
        {paymentId && tenantSlug ? (
          <EmailButton href={`https://${tenantSlug}.${process.env.NEXT_PUBLIC_BASE_DOMAIN}/checkout/${paymentId}`} primaryColor={config.color}>
            {overdueLevel === 3 ? 'PAGAR URGENTEMENTE' : 'Pagar Agora'}
          </EmailButton>
        ) : invoiceUrl ? (
          <EmailButton href={invoiceUrl} primaryColor={config.color}>
            {overdueLevel === 3 ? 'PAGAR URGENTEMENTE' : 'Regularizar Pagamento'}
          </EmailButton>
        ) : (
          <EmailButton href="#" primaryColor={config.color}>
            {overdueLevel === 3 ? 'PAGAR URGENTEMENTE' : 'Realizar Pagamento'}
          </EmailButton>
        )}
      </div>

      {/* Informações de contato */}
      <EmailText variant="small" color="#64748b">
        <strong>Precisa negociar?</strong> Entre em contato conosco imediatamente 
        para discutir opções de pagamento e evitar a suspensão das atividades.
      </EmailText>

      {overdueLevel === 3 && (
        <div style={{
          backgroundColor: '#fef2f2',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #fca5a5',
          marginTop: '24px'
        }}>
          <EmailText variant="small" color="#dc2626">
            <strong>⚠️ ÚLTIMA OPORTUNIDADE:</strong> Este é o último aviso antes da 
            suspensão definitiva. Contate-nos HOJE para resolver esta situação.
          </EmailText>
        </div>
      )}
    </BaseEmailTemplate>
  );
}
