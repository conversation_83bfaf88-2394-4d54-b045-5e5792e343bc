/**
 * Template de e-mail para lembrete de pagamento
 * Personalizável com dados da academia e do estudante
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface PaymentReminderTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  tenantSlug?: string;

  // Dados do estudante e pagamento
  studentName: string;
  amount: number;
  dueDate: string;
  planName: string;
  paymentMethod?: string;
  invoiceUrl?: string;
  paymentId?: string;

  // Configurações opcionais
  currency?: string;
  lateFee?: number;
  gracePeriod?: number;
}

export function PaymentReminderTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  tenantSlug,
  studentName,
  amount,
  dueDate,
  planName,
  paymentMethod,
  invoiceUrl,
  paymentId,
  currency = 'BRL',
  lateFee,
  gracePeriod
}: PaymentReminderTemplateProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    // Se a data está no formato YYYY-MM-DD (apenas data), tratar como data local
    // para evitar problemas de timezone
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      const [year, month, day] = dateString.split('-').map(Number);
      const date = new Date(year, month - 1, day); // month é 0-indexed
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(date);
    }

    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(new Date(dateString));
  };

  const isOverdue = new Date(dueDate) < new Date();
  const dueDateFormatted = formatDate(dueDate);

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`Lembrete de pagamento - ${planName} - ${formatCurrency(amount)}`}
    >
      <EmailHeading level={1} color={primaryColor}>
        {isOverdue ? '⚠️ Pagamento em Atraso' : '💳 Lembrete de Pagamento'}
      </EmailHeading>

      <EmailText>
        Olá <strong>{studentName}</strong>,
      </EmailText>

      <EmailText>
        {isOverdue ? (
          <>
            Identificamos que o pagamento da sua mensalidade está em atraso. 
            Por favor, regularize sua situação o quanto antes para continuar 
            aproveitando nossos treinos.
          </>
        ) : (
          <>
            Este é um lembrete amigável sobre o vencimento da sua mensalidade. 
            Mantenha seus treinos em dia!
          </>
        )}
      </EmailText>

      <EmailDivider color={primaryColor} />

      {/* Detalhes do pagamento */}
      <div style={{
        backgroundColor: '#f8fafc',
        padding: '24px',
        borderRadius: '8px',
        border: `1px solid ${primaryColor}20`
      }}>
        <EmailHeading level={3} color={primaryColor}>
          Detalhes do Pagamento
        </EmailHeading>

        <div style={{ marginBottom: '16px' }}>
          <EmailText variant="small" color="#64748b">
            <strong>Plano:</strong> {planName}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Valor:</strong> {formatCurrency(amount)}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Vencimento:</strong> {dueDateFormatted}
          </EmailText>
          {paymentMethod && (
            <EmailText variant="small" color="#64748b">
              <strong>Forma de Pagamento:</strong> {paymentMethod}
            </EmailText>
          )}
        </div>

        {isOverdue && lateFee && (
          <div style={{
            backgroundColor: '#fef2f2',
            padding: '16px',
            borderRadius: '6px',
            border: '1px solid #fecaca',
            marginBottom: '16px'
          }}>
            <EmailText variant="small" color="#dc2626">
              <strong>⚠️ Multa por Atraso:</strong> {formatCurrency(lateFee)}
            </EmailText>
            <EmailText variant="small" color="#dc2626">
              <strong>Total a Pagar:</strong> {formatCurrency(amount + lateFee)}
            </EmailText>
          </div>
        )}

        {!isOverdue && gracePeriod && (
          <EmailText variant="small" color="#059669">
            💡 <strong>Dica:</strong> Você tem {gracePeriod} dias de tolerância após o vencimento.
          </EmailText>
        )}
      </div>

      <EmailDivider />

      {/* Botão de ação */}
      <div style={{ textAlign: 'center', margin: '32px 0' }}>
        {paymentId && tenantSlug ? (
          <EmailButton href={`https://${tenantSlug}.${process.env.NEXT_PUBLIC_BASE_DOMAIN}/checkout/${paymentId}`} primaryColor={primaryColor}>
            Pagar Agora
          </EmailButton>
        ) : invoiceUrl ? (
          <EmailButton href={invoiceUrl} primaryColor={primaryColor}>
            Ver Fatura Completa
          </EmailButton>
        ) : (
          <EmailButton href="#" primaryColor={primaryColor}>
            Realizar Pagamento
          </EmailButton>
        )}
      </div>

      {/* Informações adicionais */}
      <EmailText variant="small" color="#64748b">
        <strong>Precisa de ajuda?</strong> Entre em contato conosco através dos nossos 
        canais de atendimento. Estamos aqui para ajudar!
      </EmailText>

      {isOverdue && (
        <div style={{
          backgroundColor: '#fffbeb',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #fed7aa',
          marginTop: '24px'
        }}>
          <EmailText variant="small" color="#d97706">
            <strong>⏰ Importante:</strong> Para evitar a suspensão das atividades, 
            regularize seu pagamento o quanto antes. Em caso de dificuldades, 
            entre em contato conosco para negociarmos uma solução.
          </EmailText>
        </div>
      )}
    </BaseEmailTemplate>
  );
}
