/**
 * Template de e-mail para notificações do sistema
 * Template simples e genérico para mensagens administrativas e de debug
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailDivider
} from './base-email-template';

export interface SystemDefaultTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  
  // Dados da notificação
  title: string;
  message: string;
  userName?: string;
  
  // Dados adicionais opcionais
  data?: Record<string, any>;
  isDebug?: boolean;
  timestamp?: string;
  environment?: string;
  
  // URLs opcionais
  actionUrl?: string;
  actionText?: string;
  dashboardUrl?: string;
}

export function SystemDefaultTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  title,
  message,
  userName,
  data,
  isDebug = false,
  timestamp,
  environment,
  actionUrl,
  actionText,
  dashboardUrl
}: SystemDefaultTemplateProps) {
  const getEmoji = () => {
    if (isDebug) return '🧪';
    if (title.toLowerCase().includes('erro')) return '⚠️';
    if (title.toLowerCase().includes('sucesso')) return '✅';
    if (title.toLowerCase().includes('lembrete')) return '📅';
    if (title.toLowerCase().includes('pagamento')) return '💳';
    return '📢';
  };

  const formatTimestamp = (ts?: string) => {
    if (!ts) return '';
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'America/Sao_Paulo'
    }).format(new Date(ts));
  };

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`${title} - ${academyName}`}
    >
      <EmailHeading level={1} color={primaryColor}>
        {getEmoji()} {title}
      </EmailHeading>

      {userName && (
        <EmailText>
          Olá <strong>{userName}</strong>,
        </EmailText>
      )}

      <EmailText>
        {message}
      </EmailText>

      {/* Informações de debug */}
      {isDebug && (
        <>
          <EmailDivider color={primaryColor} />
          
          <div style={{
            backgroundColor: '#fef3c7',
            padding: '16px',
            borderRadius: '8px',
            border: '1px solid #f59e0b',
            marginBottom: '16px'
          }}>
            <EmailHeading level={3} color="#d97706">
              🔧 Informações de Debug
            </EmailHeading>
            
            {timestamp && (
              <EmailText variant="small" color="#92400e">
                <strong>Timestamp:</strong> {formatTimestamp(timestamp)}
              </EmailText>
            )}
            
            {environment && (
              <EmailText variant="small" color="#92400e">
                <strong>Ambiente:</strong> {environment}
              </EmailText>
            )}
            
            {data && Object.keys(data).length > 0 && (
              <div style={{ marginTop: '12px' }}>
                <EmailText variant="small" color="#92400e">
                  <strong>Dados adicionais:</strong>
                </EmailText>
                <div style={{
                  backgroundColor: '#fffbeb',
                  padding: '12px',
                  borderRadius: '4px',
                  fontFamily: 'monospace',
                  fontSize: '12px',
                  color: '#92400e',
                  whiteSpace: 'pre-wrap',
                  overflow: 'auto'
                }}>
                  {JSON.stringify(data, null, 2)}
                </div>
              </div>
            )}
          </div>
        </>
      )}

      {/* Botão de ação */}
      {actionUrl && actionText && (
        <>
          <EmailDivider />
          
          <div style={{ textAlign: 'center', margin: '32px 0' }}>
            <a
              href={actionUrl}
              style={{
                backgroundColor: primaryColor,
                color: '#ffffff',
                padding: '12px 24px',
                borderRadius: '6px',
                textDecoration: 'none',
                fontWeight: '500',
                fontSize: '16px',
                display: 'inline-block'
              }}
            >
              {actionText}
            </a>
          </div>
        </>
      )}

      {/* Link para o dashboard */}
      {dashboardUrl && (
        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          <EmailText variant="small" color="#64748b">
            Acesse seu <a href={dashboardUrl} style={{ color: primaryColor }}>painel de controle</a> para mais informações.
          </EmailText>
        </div>
      )}

      {/* Rodapé informativo */}
      <EmailDivider />
      
      <EmailText variant="small" color="#64748b">
        Esta é uma notificação automática do sistema {academyName}. 
        {isDebug && ' Esta mensagem foi enviada em modo de debug e pode ser ignorada.'}
      </EmailText>
    </BaseEmailTemplate>
  );
}
