/**
 * Exemplos de uso do sistema de e-mail
 * Demonstra como usar o sistema de notificações por e-mail
 */

import { NotificationDispatcher, DispatchNotificationData } from '../channels/notification-dispatcher';
import { NotificationChannelFactory } from '../channels/notification-channel-factory';
import { NotificationConfigService } from '../channels/email/notification-config-service';

/**
 * Exemplo 1: Enviar lembrete de pagamento
 */
export async function exemploLembretePagemento() {
  try {
    const dispatcher = new NotificationDispatcher();

    const resultado = await dispatcher.sendPaymentReminder({
      tenantId: 'tenant-123',
      userId: 'user-456',
      studentName: '<PERSON>',
      amount: 150.00,
      dueDate: '2024-02-15',
      planName: 'Plano Mensal - Jiu-Jitsu',
      paymentMethod: 'PIX',
      invoiceUrl: 'https://app.academia.com/invoice/123',
      channels: ['email', 'in_app']
    });

    console.log('Resultado do envio:', resultado);

    if (resultado.success) {
      console.log('✅ Notificação enviada com sucesso!');
      console.log('ID da notificação:', resultado.notificationId);
      
      // Verificar resultados por canal
      Object.entries(resultado.channelResults).forEach(([canal, resultado]) => {
        if (resultado.success) {
          console.log(`✅ Canal ${canal}: Enviado (ID: ${resultado.messageId})`);
        } else {
          console.log(`❌ Canal ${canal}: Erro - ${resultado.error}`);
        }
      });
    } else {
      console.log('❌ Falha no envio:', resultado.errors);
    }

  } catch (error) {
    console.error('Erro no exemplo:', error);
  }
}

/**
 * Exemplo 2: Enviar lembrete de aula
 */
export async function exemploLembreteAula() {
  try {
    const dispatcher = new NotificationDispatcher();

    const resultado = await dispatcher.sendClassReminder({
      tenantId: 'tenant-123',
      userId: 'user-456',
      studentName: 'Maria Santos',
      className: 'Muay Thai - Iniciante',
      instructorName: 'Professor Carlos',
      classDate: '2024-02-10',
      classTime: '19:00',
      location: 'Sala 1',
      channels: ['email', 'in_app'],
      reminderType: 'today'
    });

    console.log('Lembrete de aula enviado:', resultado.success);

  } catch (error) {
    console.error('Erro no lembrete de aula:', error);
  }
}

/**
 * Exemplo 3: Configurar academia (tenant)
 */
export async function exemploConfigurarAcademia() {
  try {
    const configService = new NotificationConfigService();

    // Criar configurações padrão para uma nova academia
    await configService.createDefaultSettings('tenant-123');

    // Atualizar configurações específicas
    await configService.updateTenantSettings('tenant-123', {
      emailFromName: 'Academia Champions',
      emailEnabled: true,
      whatsappEnabled: false,
      notificationTypes: {
        payment: { email: true, whatsapp: false, in_app: true },
        class: { email: true, whatsapp: false, in_app: true },
        system: { email: false, whatsapp: false, in_app: true }
      },
      quietHoursStart: '22:00:00',
      quietHoursEnd: '07:00:00',
      timezone: 'America/Sao_Paulo'
    });

    console.log('✅ Academia configurada com sucesso!');

  } catch (error) {
    console.error('Erro ao configurar academia:', error);
  }
}

/**
 * Exemplo 4: Testar configuração de e-mail
 */
export async function exemploTestarEmail() {
  try {
    // Verificar se os canais estão configurados
    const canaisDisponiveis = NotificationChannelFactory.getAvailableChannels();
    
    console.log('Canais disponíveis:');
    canaisDisponiveis.forEach(canal => {
      console.log(`- ${canal.name}: ${canal.configured ? '✅' : '❌'} ${canal.description}`);
    });

    // Testar canal de e-mail
    const resultadoTeste = await NotificationChannelFactory.testChannel('email', {
      tenantId: 'tenant-123',
      userId: 'user-456',
      title: 'Teste de E-mail',
      message: 'Este é um e-mail de teste do sistema de notificações.'
    });

    if (resultadoTeste.success) {
      console.log('✅ Teste de e-mail bem-sucedido!');
      console.log('ID da mensagem:', resultadoTeste.messageId);
    } else {
      console.log('❌ Falha no teste de e-mail:', resultadoTeste.error);
    }

  } catch (error) {
    console.error('Erro no teste:', error);
  }
}

/**
 * Exemplo 5: Envio em lote
 */
export async function exemploEnvioLote() {
  try {
    const dispatcher = new NotificationDispatcher();

    const notificacoes: DispatchNotificationData[] = [
      {
        tenantId: 'tenant-123',
        userId: 'user-1',
        type: 'payment' as const,
        category: 'reminder' as const,
        title: 'Lembrete de Pagamento',
        message: 'Sua mensalidade vence amanhã.',
        channels: ['email', 'in_app'],
        variables: {
          studentName: 'João',
          amount: 150,
          dueDate: '2024-02-15',
          planName: 'Plano Mensal'
        }
      },
      {
        tenantId: 'tenant-123',
        userId: 'user-2',
        type: 'class' as const,
        category: 'reminder' as const,
        title: 'Lembrete de Aula',
        message: 'Você tem uma aula hoje.',
        channels: ['email', 'in_app'],
        variables: {
          studentName: 'Maria',
          className: 'Muay Thai',
          instructorName: 'Professor Carlos',
          classDate: '2024-02-10',
          classTime: '19:00'
        }
      }
    ];

    const resultados = await dispatcher.dispatchBatch(notificacoes);

    console.log(`Enviadas ${resultados.length} notificações em lote`);
    
    resultados.forEach((resultado, index) => {
      console.log(`Notificação ${index + 1}: ${resultado.success ? '✅' : '❌'}`);
      if (!resultado.success) {
        console.log('Erros:', resultado.errors);
      }
    });

  } catch (error) {
    console.error('Erro no envio em lote:', error);
  }
}

/**
 * Exemplo 6: Verificar status de entrega
 */
export async function exemploVerificarStatus() {
  try {
    const dispatcher = new NotificationDispatcher();

    // Primeiro, enviar uma notificação
    const resultado = await dispatcher.sendSystemNotification({
      tenantId: 'tenant-123',
      userId: 'user-456',
      title: 'Notificação de Teste',
      message: 'Testando verificação de status.',
      channels: ['email']
    });

    if (resultado.success && resultado.notificationId) {
      // Aguardar um pouco
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Verificar status
      const status = await dispatcher.getDeliveryStatus(resultado.notificationId, 'email');
      
      console.log('Status de entrega:', status);
    }

  } catch (error) {
    console.error('Erro ao verificar status:', error);
  }
}

/**
 * Exemplo 7: Validar configurações
 */
export async function exemploValidarConfiguracoes() {
  try {
    // Validar configurações globais
    const validacaoGlobal = NotificationConfigService.validateGlobalConfig();
    
    console.log('Configurações globais:');
    console.log('Válidas:', validacaoGlobal.isValid ? '✅' : '❌');
    
    if (validacaoGlobal.errors.length > 0) {
      console.log('Erros:', validacaoGlobal.errors);
    }

    // Validar canais específicos
    const validacaoCanais = await NotificationChannelFactory.validateChannels(['email', 'in_app']);
    
    console.log('Validação de canais:');
    Object.entries(validacaoCanais).forEach(([canal, valido]) => {
      console.log(`- ${canal}: ${valido ? '✅' : '❌'}`);
    });

  } catch (error) {
    console.error('Erro na validação:', error);
  }
}

// Função para executar todos os exemplos
export async function executarExemplos() {
  console.log('🚀 Iniciando exemplos do sistema de e-mail...\n');

  console.log('📧 Exemplo 1: Lembrete de Pagamento');
  await exemploLembretePagemento();
  console.log('\n');

  console.log('🥋 Exemplo 2: Lembrete de Aula');
  await exemploLembreteAula();
  console.log('\n');

  console.log('⚙️ Exemplo 3: Configurar Academia');
  await exemploConfigurarAcademia();
  console.log('\n');

  console.log('🧪 Exemplo 4: Testar E-mail');
  await exemploTestarEmail();
  console.log('\n');

  console.log('📦 Exemplo 5: Envio em Lote');
  await exemploEnvioLote();
  console.log('\n');

  console.log('📊 Exemplo 6: Verificar Status');
  await exemploVerificarStatus();
  console.log('\n');

  console.log('✅ Exemplo 7: Validar Configurações');
  await exemploValidarConfiguracoes();
  console.log('\n');

  console.log('🎉 Exemplos concluídos!');
}

// Para usar em desenvolvimento/teste:
// import { executarExemplos } from './email-usage-example';
// executarExemplos();
