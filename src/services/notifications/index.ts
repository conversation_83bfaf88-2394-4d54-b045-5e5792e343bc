/**
 * Exportações principais do sistema de notificações
 */

// Tipos
export * from './types/notification-types';
export * from './types/notification-schemas';

// Serviços
export { NotificationService } from './core/notification-service';
export { NotificationServiceClient } from './client/notification-service-client';
export { NotificationDispatcher } from './channels/notification-dispatcher';

// Serviços de Templates (apenas engines e validators, management via Server Actions)
export { TemplateEngine } from './templates/template-engine';
export { TemplateValidator } from './templates/template-validator';

// Hooks
export { useNotifications } from '../../hooks/notifications/use-notifications';
export { useNotificationCount } from '../../hooks/notifications/use-notification-count';
export { useNotificationPreferences } from '../../hooks/notifications/use-notification-preferences';