/**
 * Serviço de gerenciamento de templates de notificação
 * Implementa CRUD completo e funcionalidades avançadas
 */

import { createClient } from '@/services/supabase/server';
import { TemplateEngine } from './template-engine';
import { TemplateValidator } from './template-validator';
import type {
  NotificationTemplate,
  TemplateVariable,
  NotificationType,
  NotificationChannel,
  CreateTemplateData,
  UpdateTemplateData,
  TemplatePreviewData,
  TemplateValidationResult,
  NotificationServiceResponse
} from '../types/notification-types';
import {
  CreateTemplateSchema,
  UpdateTemplateSchema,
  TemplatePreviewSchema
} from '../types/notification-schemas';

export class TemplateManagementService {
  /**
   * Busca templates por tenant e tipo
   */
  async getTemplatesByTenant(
    tenantId: string, 
    type?: NotificationType
  ): Promise<NotificationServiceResponse<NotificationTemplate[]>> {
    try {
      const supabase = await createClient();
      
      let query = supabase
        .from('notification_templates')
        .select('*')
        .or(`tenant_id.eq.${tenantId},tenant_id.is.null`) // Templates do tenant ou globais
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (type) {
        query = query.eq('type', type);
      }

      const { data: templates, error } = await query;

      if (error) {
        console.error('Erro ao buscar templates:', error);
        return {
          success: false,
          error: `Erro ao buscar templates: ${error.message}`
        };
      }

      return {
        success: true,
        data: templates || []
      };

    } catch (error) {
      console.error('Erro no serviço de templates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Busca um template específico
   */
  async getTemplate(templateId: string): Promise<NotificationServiceResponse<NotificationTemplate>> {
    try {
      const supabase = await createClient();

      const { data: template, error } = await supabase
        .from('notification_templates')
        .select('*')
        .eq('id', templateId)
        .single();

      if (error) {
        console.error('Erro ao buscar template:', error);
        return {
          success: false,
          error: `Erro ao buscar template: ${error.message}`
        };
      }

      if (!template) {
        return {
          success: false,
          error: 'Template não encontrado'
        };
      }

      return {
        success: true,
        data: template
      };

    } catch (error) {
      console.error('Erro no serviço de templates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Cria um novo template
   */
  async createTemplate(
    tenantId: string, 
    templateData: CreateTemplateData
  ): Promise<NotificationServiceResponse<NotificationTemplate>> {
    try {
      // Validar dados de entrada
      const validatedData = CreateTemplateSchema.parse(templateData);

      // Validar template antes de criar
      const availableVariables = await this.getAvailableVariables(validatedData.type);
      if (!availableVariables.success) {
        return {
          success: false,
          error: availableVariables.error || 'Erro ao buscar variáveis disponíveis'
        };
      }

      const validation = await TemplateValidator.validateTemplate(
        validatedData.body_template,
        validatedData.type,
        availableVariables.data!
      );

      if (!validation.isValid) {
        return {
          success: false,
          error: `Template inválido: ${validation.errors.join(', ')}`
        };
      }

      const supabase = await createClient();

      const { data: template, error } = await supabase
        .from('notification_templates')
        .insert({
          tenant_id: tenantId,
          ...validatedData,
          variables: {}, // Será populado automaticamente
          is_active: true,
          is_default: false,
          version: 1
        })
        .select()
        .single();

      if (error) {
        console.error('Erro ao criar template:', error);
        return {
          success: false,
          error: `Erro ao criar template: ${error.message}`
        };
      }

      return {
        success: true,
        data: template,
        message: 'Template criado com sucesso'
      };

    } catch (error) {
      console.error('Erro no serviço de templates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Atualiza um template existente
   */
  async updateTemplate(
    templateId: string, 
    updates: UpdateTemplateData
  ): Promise<NotificationServiceResponse<NotificationTemplate>> {
    try {
      // Validar dados de entrada
      const validatedUpdates = UpdateTemplateSchema.parse(updates);

      // Buscar template atual para validação
      const currentTemplate = await this.getTemplate(templateId);
      if (!currentTemplate.success) {
        return currentTemplate;
      }

      // Se está atualizando o body_template, validar
      if (validatedUpdates.body_template) {
        const availableVariables = await this.getAvailableVariables(currentTemplate.data!.type);
        if (!availableVariables.success) {
          return {
            success: false,
            error: availableVariables.error || 'Erro ao buscar variáveis disponíveis'
          };
        }

        const validation = await TemplateValidator.validateTemplate(
          validatedUpdates.body_template,
          currentTemplate.data!.type,
          availableVariables.data!
        );

        if (!validation.isValid) {
          return {
            success: false,
            error: `Template inválido: ${validation.errors.join(', ')}`
          };
        }
      }

      const supabase = await createClient();

      const { data: template, error } = await supabase
        .from('notification_templates')
        .update({
          ...validatedUpdates,
          updated_at: new Date().toISOString(),
          version: currentTemplate.data!.version + 1
        })
        .eq('id', templateId)
        .select()
        .single();

      if (error) {
        console.error('Erro ao atualizar template:', error);
        return {
          success: false,
          error: `Erro ao atualizar template: ${error.message}`
        };
      }

      return {
        success: true,
        data: template,
        message: 'Template atualizado com sucesso'
      };

    } catch (error) {
      console.error('Erro no serviço de templates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Deleta um template (soft delete)
   */
  async deleteTemplate(templateId: string): Promise<NotificationServiceResponse<void>> {
    try {
      const supabase = await createClient();

      const { error } = await supabase
        .from('notification_templates')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', templateId);

      if (error) {
        console.error('Erro ao deletar template:', error);
        return {
          success: false,
          error: `Erro ao deletar template: ${error.message}`
        };
      }

      return {
        success: true,
        message: 'Template deletado com sucesso'
      };

    } catch (error) {
      console.error('Erro no serviço de templates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Duplica um template existente
   */
  async duplicateTemplate(
    templateId: string, 
    newName: string
  ): Promise<NotificationServiceResponse<NotificationTemplate>> {
    try {
      // Buscar template original
      const originalTemplate = await this.getTemplate(templateId);
      if (!originalTemplate.success) {
        return originalTemplate;
      }

      const template = originalTemplate.data!;

      // Criar novo template baseado no original
      const newTemplateData: CreateTemplateData = {
        type: template.type,
        channel: template.channel,
        name: newName,
        subject_template: template.subject_template,
        body_template: template.body_template,
        parent_template_id: template.id
      };

      return await this.createTemplate(template.tenant_id!, newTemplateData);

    } catch (error) {
      console.error('Erro no serviço de templates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Busca variáveis disponíveis para um tipo de template
   */
  async getAvailableVariables(
    templateType: NotificationType
  ): Promise<NotificationServiceResponse<TemplateVariable[]>> {
    try {
      const supabase = await createClient();

      const { data: variables, error } = await supabase
        .from('template_variables')
        .select('*')
        .eq('template_type', templateType)
        .order('category', { ascending: true })
        .order('variable_name', { ascending: true });

      if (error) {
        console.error('Erro ao buscar variáveis:', error);
        return {
          success: false,
          error: `Erro ao buscar variáveis: ${error.message}`
        };
      }

      return {
        success: true,
        data: variables || []
      };

    } catch (error) {
      console.error('Erro no serviço de templates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Valida um template
   */
  async validateTemplate(
    template: string,
    templateType: NotificationType
  ): Promise<NotificationServiceResponse<TemplateValidationResult>> {
    try {
      const availableVariables = await this.getAvailableVariables(templateType);
      if (!availableVariables.success) {
        return {
          success: false,
          error: availableVariables.error
        };
      }

      const validation = await TemplateValidator.validateTemplate(
        template,
        templateType,
        availableVariables.data!
      );

      return {
        success: true,
        data: validation
      };

    } catch (error) {
      console.error('Erro no serviço de templates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Gera preview de um template
   */
  async previewTemplate(
    templateId: string,
    variables: Record<string, any>
  ): Promise<NotificationServiceResponse<TemplatePreviewData>> {
    try {
      // Validar dados de entrada
      TemplatePreviewSchema.parse({
        template_id: templateId,
        variables
      });

      // Buscar template
      const templateResult = await this.getTemplate(templateId);
      if (!templateResult.success) {
        return {
          success: false,
          error: templateResult.error
        };
      }

      const template = templateResult.data!;

      // Buscar dados da academia se tenant_id estiver disponível
      let academyData = {
        name: 'Academia Exemplo',
        logo_url: '',
        primary_color: '#333333',
        secondary_color: '#666666',
        slug: 'academia-exemplo'
      };

      if (template.tenant_id) {
        const supabase = await createClient();
        const { data: tenant } = await supabase
          .from('tenants')
          .select('name, logo_url, primary_color, secondary_color, slug')
          .eq('id', template.tenant_id)
          .single();

        if (tenant) {
          academyData = {
            name: tenant.name,
            logo_url: tenant.logo_url || '',
            primary_color: tenant.primary_color || '#333333',
            secondary_color: tenant.secondary_color || '#666666',
            slug: tenant.slug
          };
        }
      }

      // Renderizar template
      const subjectResult = template.subject_template
        ? TemplateEngine.renderWithAcademyData(template.subject_template, variables, academyData)
        : null;

      const bodyResult = TemplateEngine.renderWithAcademyData(
        template.body_template,
        variables,
        academyData
      );

      const previewData: TemplatePreviewData = {
        template,
        variables: { ...variables, ...academyData },
        rendered_subject: subjectResult?.rendered,
        rendered_body: bodyResult.rendered
      };

      return {
        success: true,
        data: previewData
      };

    } catch (error) {
      console.error('Erro no serviço de templates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Reseta template para versão padrão
   */
  async resetToDefault(templateId: string): Promise<NotificationServiceResponse<NotificationTemplate>> {
    try {
      // Buscar template atual
      const currentTemplate = await this.getTemplate(templateId);
      if (!currentTemplate.success) {
        return currentTemplate;
      }

      const template = currentTemplate.data!;

      // Verificar se tem template pai (padrão)
      if (!template.parent_template_id) {
        return {
          success: false,
          error: 'Template não possui versão padrão para reset'
        };
      }

      // Buscar template padrão
      const defaultTemplate = await this.getTemplate(template.parent_template_id);
      if (!defaultTemplate.success) {
        return {
          success: false,
          error: 'Template padrão não encontrado'
        };
      }

      // Atualizar com dados do template padrão
      const updates: UpdateTemplateData = {
        subject_template: defaultTemplate.data!.subject_template,
        body_template: defaultTemplate.data!.body_template
      };

      return await this.updateTemplate(templateId, updates);

    } catch (error) {
      console.error('Erro no serviço de templates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Busca histórico de versões de um template
   */
  async getTemplateHistory(templateId: string): Promise<NotificationServiceResponse<NotificationTemplate[]>> {
    try {
      const supabase = await createClient();

      // Por enquanto, retorna apenas o template atual
      // Em uma implementação futura, poderia ter uma tabela de histórico
      const { data: template, error } = await supabase
        .from('notification_templates')
        .select('*')
        .eq('id', templateId);

      if (error) {
        console.error('Erro ao buscar histórico:', error);
        return {
          success: false,
          error: `Erro ao buscar histórico: ${error.message}`
        };
      }

      return {
        success: true,
        data: template || []
      };

    } catch (error) {
      console.error('Erro no serviço de templates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Cria variáveis de exemplo para um tipo de template
   */
  async createExampleVariables(templateType: NotificationType): Promise<NotificationServiceResponse<Record<string, any>>> {
    try {
      const availableVariables = await this.getAvailableVariables(templateType);
      if (!availableVariables.success) {
        return availableVariables;
      }

      const examples = TemplateEngine.createExampleVariables(availableVariables.data!);

      return {
        success: true,
        data: examples
      };

    } catch (error) {
      console.error('Erro no serviço de templates:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }
}
