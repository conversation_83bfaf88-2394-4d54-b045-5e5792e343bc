/**
 * Validador de templates de notificação
 * Verifica sintaxe, variáveis obrigatórias e compatibilidade
 */

import { TemplateEngine } from './template-engine';
import type { 
  TemplateVariable, 
  NotificationType, 
  TemplateValidationResult 
} from '../types/notification-types';

export class TemplateValidator {
  /**
   * Valida um template completo
   */
  static async validateTemplate(
    template: string,
    templateType: NotificationType,
    availableVariables: TemplateVariable[]
  ): Promise<TemplateValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const missingVariables: string[] = [];
    const unusedVariables: string[] = [];

    // 1. Validar sintaxe básica
    const syntaxValidation = TemplateEngine.validateSyntax(template);
    if (!syntaxValidation.isValid) {
      errors.push(...syntaxValidation.errors);
    }

    // 2. Extrair variáveis usadas no template
    const usedVariables = TemplateEngine.extractVariables(template);

    // 3. Criar mapa de variáveis disponíveis
    const availableVariableMap = new Map<string, TemplateVariable>();
    for (const variable of availableVariables) {
      availableVariableMap.set(variable.variable_key, variable);
    }

    // 4. Verificar variáveis obrigatórias
    const requiredVariables = availableVariables.filter(v => v.is_required);
    for (const requiredVar of requiredVariables) {
      if (!usedVariables.includes(requiredVar.variable_key)) {
        missingVariables.push(requiredVar.variable_key);
        errors.push(`Variável obrigatória não utilizada: {{${requiredVar.variable_key}}}`);
      }
    }

    // 5. Verificar variáveis não reconhecidas
    for (const usedVar of usedVariables) {
      if (!availableVariableMap.has(usedVar)) {
        errors.push(`Variável não reconhecida: {{${usedVar}}}. Verifique se está disponível para o tipo '${templateType}'.`);
      }
    }

    // 6. Identificar variáveis disponíveis não utilizadas
    for (const availableVar of availableVariables) {
      if (!usedVariables.includes(availableVar.variable_key) && !availableVar.is_required) {
        unusedVariables.push(availableVar.variable_key);
      }
    }

    // 7. Gerar warnings para boas práticas
    if (unusedVariables.length > 0) {
      warnings.push(`Variáveis disponíveis não utilizadas: ${unusedVariables.map(v => `{{${v}}}`).join(', ')}`);
    }

    if (template.length < 10) {
      warnings.push('Template muito curto. Considere adicionar mais conteúdo.');
    }

    if (template.length > 5000) {
      warnings.push('Template muito longo. Considere dividir em seções menores.');
    }

    // 8. Verificar se há variáveis da academia sendo usadas
    const academyVariables = ['academyName', 'academyLogo', 'primaryColor', 'secondaryColor'];
    const hasAcademyVariables = usedVariables.some(v => academyVariables.includes(v));
    
    if (!hasAcademyVariables) {
      warnings.push('Considere incluir variáveis da academia como {{academyName}} para personalização.');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      missingVariables,
      unusedVariables
    };
  }

  /**
   * Valida apenas a sintaxe do template
   */
  static validateSyntax(template: string): { isValid: boolean; errors: string[] } {
    return TemplateEngine.validateSyntax(template);
  }

  /**
   * Verifica se um template é compatível com um tipo de notificação
   */
  static isCompatibleWithType(
    template: string,
    templateType: NotificationType,
    availableVariables: TemplateVariable[]
  ): boolean {
    const usedVariables = TemplateEngine.extractVariables(template);
    const typeVariables = availableVariables
      .filter(v => v.template_type === templateType)
      .map(v => v.variable_key);

    // Verificar se todas as variáveis usadas estão disponíveis para o tipo
    return usedVariables.every(usedVar => 
      typeVariables.includes(usedVar) || 
      this.isAcademyVariable(usedVar)
    );
  }

  /**
   * Verifica se uma variável é uma variável da academia (sempre disponível)
   */
  private static isAcademyVariable(variableName: string): boolean {
    const academyVariables = [
      'academyName',
      'academyLogo', 
      'primaryColor',
      'secondaryColor',
      'academySlug'
    ];
    
    return academyVariables.includes(variableName);
  }

  /**
   * Sugere melhorias para o template
   */
  static suggestImprovements(
    template: string,
    templateType: NotificationType,
    availableVariables: TemplateVariable[]
  ): string[] {
    const suggestions: string[] = [];
    const usedVariables = TemplateEngine.extractVariables(template);

    // Sugerir uso de variáveis importantes não utilizadas
    const importantVariables = availableVariables.filter(v => 
      v.template_type === templateType && 
      v.is_required === false &&
      !usedVariables.includes(v.variable_key)
    );

    if (templateType === 'payment') {
      const hasAmount = usedVariables.includes('amount');
      const hasDueDate = usedVariables.includes('dueDate');
      
      if (!hasAmount) {
        suggestions.push('Considere incluir o valor da mensalidade com {{amount}}');
      }
      
      if (!hasDueDate) {
        suggestions.push('Considere incluir a data de vencimento com {{dueDate}}');
      }
    }

    if (templateType === 'class') {
      const hasInstructor = usedVariables.includes('instructorName');
      const hasDateTime = usedVariables.includes('classDate') || usedVariables.includes('classTime');
      
      if (!hasInstructor) {
        suggestions.push('Considere incluir o nome do instrutor com {{instructorName}}');
      }
      
      if (!hasDateTime) {
        suggestions.push('Considere incluir data/horário da aula com {{classDate}} e {{classTime}}');
      }
    }

    // Sugerir uso de variáveis da academia
    if (!usedVariables.includes('academyName')) {
      suggestions.push('Adicione {{academyName}} para personalizar com o nome da academia');
    }

    // Sugerir formatação
    if (!template.includes('\n') && template.length > 100) {
      suggestions.push('Considere quebrar o texto em parágrafos para melhor legibilidade');
    }

    return suggestions;
  }

  /**
   * Verifica se o template tem conteúdo mínimo necessário
   */
  static hasMinimumContent(template: string): { isValid: boolean; message?: string } {
    const cleanTemplate = template.trim();
    
    if (cleanTemplate.length === 0) {
      return { isValid: false, message: 'Template não pode estar vazio' };
    }

    if (cleanTemplate.length < 5) {
      return { isValid: false, message: 'Template deve ter pelo menos 5 caracteres' };
    }

    // Verificar se tem pelo menos uma variável ou texto significativo
    const hasVariables = TemplateEngine.extractVariables(template).length > 0;
    const hasText = cleanTemplate.replace(/\{\{[^}]+\}\}/g, '').trim().length > 0;

    if (!hasVariables && !hasText) {
      return { isValid: false, message: 'Template deve conter texto ou variáveis' };
    }

    return { isValid: true };
  }

  /**
   * Analisa a complexidade do template
   */
  static analyzeComplexity(template: string): {
    score: number; // 1-10 (1 = muito simples, 10 = muito complexo)
    factors: string[];
  } {
    const factors: string[] = [];
    let score = 1;

    const variableCount = TemplateEngine.extractVariables(template).length;
    const characterCount = template.length;
    const lineCount = template.split('\n').length;

    // Fatores que aumentam complexidade
    if (variableCount > 5) {
      score += 2;
      factors.push(`Muitas variáveis (${variableCount})`);
    } else if (variableCount > 2) {
      score += 1;
      factors.push(`Várias variáveis (${variableCount})`);
    }

    if (characterCount > 1000) {
      score += 2;
      factors.push('Template muito longo');
    } else if (characterCount > 500) {
      score += 1;
      factors.push('Template longo');
    }

    if (lineCount > 20) {
      score += 1;
      factors.push('Muitas linhas');
    }

    // Verificar variáveis aninhadas
    const nestedVariables = TemplateEngine.extractVariables(template)
      .filter(v => v.includes('.'));
    
    if (nestedVariables.length > 0) {
      score += 1;
      factors.push('Variáveis aninhadas');
    }

    return {
      score: Math.min(score, 10),
      factors
    };
  }
}
