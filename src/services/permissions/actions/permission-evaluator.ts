import { 
  ActionType, 
  ConditionalPermission, 
  PermissionCheckResult, 
  PermissionContext, 
  PermissionCondition,
  ResourceType
} from '../types/permission-types';
import { PermissionRepository } from '../repository/permission-repository';

/**
 * Classe responsável por avaliar permissões
 */
export class PermissionEvaluator {
  private repository: PermissionRepository;
  
  constructor(repository: PermissionRepository) {
    this.repository = repository;
  }
  
  /**
   * <PERSON>lia se o usuário tem permissão para realizar determinada ação
   */
  evaluate(
    context: PermissionContext,
    resource: ResourceType,
    action: ActionType
  ): PermissionCheckResult {
    const permission = this.repository.hasPermission(
      context.currentUserRole,
      resource,
      action
    );
    
    if (!permission) {
      return {
        granted: false,
        reason: `O papel ${context.currentUserRole} não tem permissão para ${action} em ${resource}`
      };
    }
    
    const conditionResult = this.evaluateCondition(permission, context);
    
    return conditionResult;
  }
  
  /**
   * <PERSON><PERSON> as condições específicas da permissão
   */
  private evaluateCondition(
    permission: ConditionalPermission,
    context: PermissionContext
  ): PermissionCheckResult {
    const condition = permission.condition;
    
    switch (condition) {
      case 'any':
        return { granted: true };
        
      case 'self':
        if (!context.targetUserId || context.currentUserId === context.targetUserId) {
          return { granted: true };
        }
        return {
          granted: false,
          reason: 'Esta operação só é permitida para o próprio usuário'
        };
        
      case 'not_self':
        if (context.targetUserId && context.currentUserId !== context.targetUserId) {
          return { granted: true };
        }
        return {
          granted: false,
          reason: 'Esta operação não é permitida no próprio usuário'
        };
        
      case 'same_tenant':
        if (!context.targetTenantId || context.currentUserTenantId === context.targetTenantId) {
          return { granted: true };
        }
        return {
          granted: false,
          reason: 'Esta operação só é permitida dentro do mesmo tenant'
        };
        
      case 'students_only':
        if (context.targetUserRole === 'student') {
          return { granted: true };
        }
        return {
          granted: false,
          reason: 'Esta operação só é permitida para recursos de estudantes'
        };
        
      case 'self_or_students':
        if (!context.targetUserId || context.currentUserId === context.targetUserId) {
          return { granted: true };
        }
        if (context.targetUserRole === 'student') {
          return { granted: true };
        }
        return {
          granted: false,
          reason: 'Esta operação só é permitida para o próprio usuário ou recursos de estudantes'
        };
        
      case 'others_only':
        if (context.targetUserId && context.currentUserId !== context.targetUserId) {
          return { granted: true };
        }
        return {
          granted: false,
          reason: 'Esta operação só é permitida para recursos de outros usuários'
        };
        
      default:
        return {
          granted: false,
          reason: `Condição de permissão desconhecida: ${condition}`
        };
    }
  }
  
  /**
   * Método utilitário para verificar se o usuário atual pode executar uma ação
   * específica em um determinado usuário alvo
   */
  async canPerformUserAction(
    currentUserId: string,
    targetUserId: string | undefined,
    resource: ResourceType,
    action: ActionType,
    context?: PermissionContext
  ): Promise<PermissionCheckResult> {
    if (!context) {
      return {
        granted: false,
        reason: 'Contexto de permissão não disponível'
      };
    }
    
    if (
      resource === 'user' && 
      action === 'deactivate' && 
      targetUserId === currentUserId
    ) {
      return {
        granted: false,
        reason: 'Não é possível desativar a própria conta'
      };
    }
    
    return this.evaluate(context, resource, action);
  }
} 