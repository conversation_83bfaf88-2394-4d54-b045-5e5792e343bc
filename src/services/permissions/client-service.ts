'use client';

import { ActionType, PermissionCheckResult, PermissionContext, ResourceType } from './types/permission-types';
import { PermissionRepository } from './repository/permission-repository';
import { PermissionEvaluator } from './actions/permission-evaluator';
import { PermissionContextClientResolver } from './contexts/permission-context-client';
import { QueryClient } from '@tanstack/react-query';

/**
 * Serviço cliente de permissões, para verificações no navegador
 */
export class PermissionClientService {
  private repository: PermissionRepository;
  private evaluator: PermissionEvaluator;
  private contextResolver: PermissionContextClientResolver;
  private queryClient: QueryClient | null = null;
  
  constructor() {
    this.repository = new PermissionRepository();
    this.evaluator = new PermissionEvaluator(this.repository);
    this.contextResolver = new PermissionContextClientResolver();
  }
  
  /**
   * Configura o queryClient para uso no cache
   */
  setQueryClient(queryClient: QueryClient) {
    this.queryClient = queryClient;
  }
  
  /**
   * Verifica se o usuário atual pode realizar uma ação em um recurso
   * Tenta usar o cache se disponível
   * 
   * @param currentUserId ID do usuário atual
   * @param resource Tipo de recurso
   * @param action Tipo de ação
   * @param targetUserId ID do usuário alvo (opcional)
   * @returns Resultado da verificação
   */
  async hasPermission(
    currentUserId: string,
    resource: ResourceType,
    action: ActionType,
    targetUserId?: string
  ): Promise<PermissionCheckResult> {
    try {
      // Primeiro verificar se já temos o resultado desta verificação em cache
      if (this.queryClient) {
        const cachedResult = this.queryClient.getQueryData<PermissionCheckResult>(
          ['permission-check', resource, action, currentUserId, targetUserId ?? 'none']
        );
        
        if (cachedResult) {
          return cachedResult;
        }
      }
      
      // Tentar usar o contexto do cache se o queryClient estiver disponível
      let context: PermissionContext | null = null;
      
      if (this.queryClient) {
        // Primeiro, tentar obter um contexto básico que deve estar em cache
        const cachedContext = this.queryClient.getQueryData<PermissionContext | null>(
          ['basic-permission-context', currentUserId]
        );
        
        if (cachedContext) {
          context = cachedContext;
          console.log(`Usando contexto de permissão em cache para user ${currentUserId}`);
        } 
        // Se não tiver contexto básico mas tiver um targetUserId, verificar por contexto específico
        else if (targetUserId) {
          const cachedTargetContext = this.queryClient.getQueryData<PermissionContext | null>(
            ['permission-context', currentUserId, targetUserId]
          );
          
          if (cachedTargetContext) {
            context = cachedTargetContext;
            console.log(`Usando contexto específico em cache para ${currentUserId}->${targetUserId}`);
          }
        }
      }
      
      // Se não há contexto em cache, buscar normalmente
      if (!context) {
        console.log(`Contexto de permissão não encontrado em cache para ${currentUserId}, buscando do servidor`);
        context = await this.contextResolver.getContext(currentUserId, targetUserId);
        
        // Armazenar o novo contexto em cache
        if (context && this.queryClient) {
          const cacheKey = targetUserId 
            ? ['permission-context', currentUserId, targetUserId]
            : ['basic-permission-context', currentUserId];
            
          this.queryClient.setQueryData(cacheKey, context);
          console.log(`Contexto de permissão armazenado em cache: ${cacheKey.join('/')}`);
        }
      }
      
      if (!context) {
        return { 
          granted: false, 
          reason: 'Não foi possível obter o contexto de permissão' 
        };
      }
      
      const result = this.evaluator.evaluate(context, resource, action);
      
      // Se temos o queryClient, armazenar o resultado em cache
      if (this.queryClient) {
        this.queryClient.setQueryData(
          ['permission-check', resource, action, currentUserId, targetUserId ?? 'none'],
          result
        );
      }
      
      return result;
    } catch (error) {
      console.error('Erro ao verificar permissão:', error);
      return { 
        granted: false, 
        reason: 'Erro interno ao verificar permissão' 
      };
    }
  }
  
  /**
   * Verifica se o usuário atual pode realizar uma ação em um usuário alvo
   * Usa o cache quando possível
   * 
   * @param currentUserId ID do usuário atual 
   * @param targetUserId ID do usuário alvo
   * @param action Ação a ser verificada
   * @returns Resultado da verificação
   */
  async canManageUser(
    currentUserId: string,
    targetUserId: string | undefined,
    action: ActionType
  ): Promise<PermissionCheckResult> {
    try {
      let context: PermissionContext | null = null;
      
      // Tentar obter do cache
      if (this.queryClient) {
        const cachedContext = this.queryClient.getQueryData<PermissionContext | null>(
          ['permission-context', currentUserId, targetUserId]
        );
        
        if (cachedContext) {
          context = cachedContext;
        }
      }
      
      // Se não há contexto em cache, buscar normalmente
      if (!context) {
        context = await this.contextResolver.getContext(currentUserId, targetUserId);
      }
      
      if (!context) {
        return { 
          granted: false, 
          reason: 'Não foi possível obter o contexto de permissão' 
        };
      }
      
      const result = this.evaluator.canPerformUserAction(
        currentUserId, 
        targetUserId, 
        'user',
        action,
        context
      );
      
      return result;
    } catch (error) {
      console.error('Erro ao verificar permissão de usuário:', error);
      return { 
        granted: false, 
        reason: 'Erro interno ao verificar permissão' 
      };
    }
  }
  
  /**
   * Pré-carrega o contexto de permissão para o usuário atual
   * e armazena em cache
   * 
   * @param currentUserId ID do usuário atual
   * @returns Contexto de permissão ou null em caso de erro
   */
  async prefetchPermissionContext(currentUserId: string): Promise<PermissionContext | null> {
    if (!this.queryClient) {
      console.warn('QueryClient não disponível para prefetch de permissões');
      return null;
    }
    
    try {
      // Primeiro, verificar se já existe em cache
      const existingContext = this.queryClient.getQueryData<PermissionContext | null>(
        ['basic-permission-context', currentUserId]
      );
      
      if (existingContext) {
        console.log('Context já em cache, usando dados existentes:', currentUserId);
        return existingContext;
      }
      
      // Buscar contexto do servidor
      console.log('Prefetch: buscando contexto de permissões para:', currentUserId);
      const context = await this.contextResolver.getBasicContext(currentUserId);
      
      if (context) {
        // Armazenar no cache com staleTime e gcTime
        console.log('Prefetch: armazenando contexto de permissões em cache para:', currentUserId);
        this.queryClient.setQueryData(['basic-permission-context', currentUserId], context);
        
        // Também armazenar usando setQueryData para garantir acesso imediato
        this.queryClient.setQueryData(['basic-permission-context', currentUserId], context);
      } else {
        console.warn('Prefetch: contexto de permissões nulo para:', currentUserId);
      }
      
      return context;
    } catch (error) {
      console.error('Erro ao pré-carregar contexto de permissões:', error);
      return null;
    }
  }
  
  /**
   * Invalida todo o cache de permissões para forçar recarregamento
   */
  invalidatePermissionCache() {
    if (!this.queryClient) return;
    
    this.queryClient.invalidateQueries({
      queryKey: ['permission-context'],
    });
    
    this.queryClient.invalidateQueries({
      queryKey: ['basic-permission-context'],
    });
    
    this.queryClient.invalidateQueries({
      queryKey: ['permission-check'],
    });
  }
}

let permissionClientServiceInstance: PermissionClientService | null = null;

/**
 * Obtém uma instância do serviço cliente de permissões
 */
export function getPermissionClientService(): PermissionClientService {
  if (!permissionClientServiceInstance) {
    permissionClientServiceInstance = new PermissionClientService();
  }
  
  return permissionClientServiceInstance;
} 