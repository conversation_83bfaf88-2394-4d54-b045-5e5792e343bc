'use client';

import { createClient } from '@/services/supabase/client';
import { PermissionContext, UserRole } from '../types/permission-types';
import { getBasicPermissionContext, getPermissionContext } from './permission-context';

/**
 * Classe responsável por obter o contexto necessário para avaliação de permissões no cliente
 */
export class PermissionContextClientResolver {
  /**
   * Obtém o contexto completo para avaliação de permissões
   */
  async getContext(currentUserId: string, targetUserId?: string): Promise<PermissionContext | null> {
    try {
      // Usando o server action em vez de acessar diretamente o Supabase
      return await getPermissionContext(currentUserId, targetUserId);
    } catch (error) {
      console.error('Erro ao resolver contexto de permissão:', error);
      return null;
    }
  }
  
  /**
   * Versão mais leve que utiliza apenas os IDs para verificação
   * Usado em casos onde o contexto completo não é necessário
   */
  async getBasicContext(currentUserId: string): Promise<PermissionContext | null> {
    try {
      // Usando o server action em vez de acessar diretamente o Supabase
      return await getBasicPermissionContext(currentUserId);
    } catch (error) {
      console.error('Erro ao resolver contexto básico de permissão:', error);
      return null;
    }
  }
} 