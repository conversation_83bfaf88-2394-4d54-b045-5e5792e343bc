import { 
  EditCondition, 
  FieldPermission, 
  FieldPermissionConfig, 
  FieldPermissionFactory,
  ProfileField,
  RoleBasedFieldPermission
} from '../types/field-permission-types';
import { UserRole } from '../types/permission-types';

export class DefaultFieldPermissionFactory implements FieldPermissionFactory {
  createPermission(field: ProfileField, config: FieldPermissionConfig): FieldPermission | RoleBasedFieldPermission {
    const { roles, editConditions, adminCanEditOthers = true } = config;
    
    if (Array.isArray(editConditions)) {
      return {
        field,
        roles,
        editConditions: this.handleAdminPermissions(roles, editConditions, adminCanEditOthers)
      };
    }
    
    return {
      field,
      roles,
      getEditConditions: (role: UserRole) => {
        if (adminCanEditOthers && role === 'admin' && !this.canRoleEditOthers(role, editConditions as Record<string, EditCondition[]>)) {
          return ['self', 'others'];
        }
        
        const conditions = (editConditions as Record<string, EditCondition[]>)[role] || ['self'];
        return conditions;
      }
    };
  }
  
  private canRoleEditOthers(role: UserRole, editConditionMap: Record<string, EditCondition[]>): boolean {
    const conditions = editConditionMap[role] || [];
    return conditions.includes('others');
  }
  
  private handleAdminPermissions(
    roles: UserRole[], 
    conditions: EditCondition[], 
    adminCanEditOthers: boolean
  ): EditCondition[] {
    // Se admin está na lista de papéis e não pode editar outros, retornar como está
    if (!adminCanEditOthers || !roles.includes('admin')) {
      return conditions;
    }
    
    // Se admin deve poder editar outros e 'others' não está nas condições, adicionar
    if (!conditions.includes('others')) {
      return [...conditions, 'others'];
    }
    
    return conditions;
  }
} 