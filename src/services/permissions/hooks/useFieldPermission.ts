'use client';

import { useCallback } from 'react';
import { fieldPermissionMap } from '../policies/field-permissions';
import { ProfileField } from '../types/field-permission-types';
import { usePermission } from './use-permission';
import { useSession } from '@/hooks/user/Auth/useSession';
import { UserRole } from '@/src/hooks/user/Permissions/useUserRole';
import { FieldPermissionChecker } from '../strategies/field-permission-checker';
import { FieldPermissionContext } from '../types/field-permission-types';

/**
 * Hook para verificar permissões de edição em campos específicos
 */
export function useFieldPermission() {
  const { isAllowed } = usePermission('profile', 'edit');
  const { session } = useSession();
  const currentUserRole = session?.user?.role;
  const currentUserId = session?.user?.id;
  const currentTenantId = (session?.user as any)?.tenantId;

  /**
   * Verifica se o usuário atual pode editar um campo específico
   * 
   * @param field Campo a ser verificado
   * @param targetUserId ID do usuário alvo (dono do perfil)
   * @returns Se o usuário atual pode editar o campo
   */
  const canEditField = useCallback((field: ProfileField, targetUserId?: string, targetTenantId?: string): boolean => {
    // Primeiro verifica permissão básica para editar perfil
    if (!isAllowed) {
      return false;
    }

    // Se não há usuário logado, não tem permissão
    if (!currentUserRole || !currentUserId) {
      return false;
    }

    // Recuperar permissões para o campo
    const fieldPermissions = fieldPermissionMap.get(field) || [];
    
    // Se não há permissões definidas para o campo, não tem permissão
    if (fieldPermissions.length === 0) {
      return false;
    }

    // Criar contexto de permissão
    const context: FieldPermissionContext = {
      currentUserId,
      currentUserRole: currentUserRole as UserRole,
      targetUserId: targetUserId || currentUserId,
      currentTenantId,
      targetTenantId: targetTenantId || currentTenantId
    };

    // Usar a estratégia de verificação de permissão
    const permissionChecker = new FieldPermissionChecker(fieldPermissions);
    return permissionChecker.canEdit(field, context);
    
  }, [isAllowed, currentUserRole, currentUserId, currentTenantId]);

  return { canEditField };
} 