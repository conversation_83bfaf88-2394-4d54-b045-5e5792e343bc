import { PermissionPolicy } from '../types/permission-types';

/**
 * Políticas de permissão para administradores
 */
export const adminPolicy: PermissionPolicy = {
  role: 'admin',
  permissions: [
    // Gerenciamento de usuários
    { resource: 'user', action: 'view', condition: 'same_tenant' },
    { resource: 'user', action: 'create', condition: 'same_tenant' },
    { resource: 'user', action: 'edit', condition: 'same_tenant' },
    { resource: 'user', action: 'delete', condition: 'not_self' },
    { resource: 'user', action: 'activate', condition: 'same_tenant' },
    { resource: 'user', action: 'deactivate', condition: 'not_self' },
    { resource: 'user', action: 'list', condition: 'same_tenant' },
    { resource: 'user', action: 'import', condition: 'same_tenant' },
    { resource: 'user', action: 'export', condition: 'same_tenant' },
    
    // Gerenciamento de alunos
    { resource: 'students', action: 'view', condition: 'same_tenant' },
    { resource: 'students', action: 'create', condition: 'same_tenant' },
    { resource: 'students', action: 'edit', condition: 'same_tenant' },
    { resource: 'students', action: 'delete', condition: 'same_tenant' },
    { resource: 'students', action: 'list', condition: 'same_tenant' },
    { resource: 'students', action: 'import', condition: 'same_tenant' },
    { resource: 'students', action: 'export', condition: 'same_tenant' },
    
    // Perfis
    { resource: 'profile', action: 'view', condition: 'same_tenant' },
    { resource: 'profile', action: 'edit', condition: 'same_tenant' },
    
    // Presenças
    { resource: 'attendance', action: 'view', condition: 'same_tenant' },
    { resource: 'attendance', action: 'create', condition: 'same_tenant' },
    { resource: 'attendance', action: 'edit', condition: 'same_tenant' },
    { resource: 'attendance', action: 'delete', condition: 'same_tenant' },
    { resource: 'attendance', action: 'list', condition: 'same_tenant' },
    { resource: 'attendance', action: 'export', condition: 'same_tenant' },
    
    // Pagamentos
    { resource: 'payment', action: 'view', condition: 'same_tenant' },
    { resource: 'payment', action: 'create', condition: 'same_tenant' },
    { resource: 'payment', action: 'edit', condition: 'same_tenant' },
    { resource: 'payment', action: 'delete', condition: 'same_tenant' },
    { resource: 'payment', action: 'list', condition: 'same_tenant' },
    { resource: 'payment', action: 'export', condition: 'same_tenant' },
    
    // Aulas
    { resource: 'class', action: 'view', condition: 'same_tenant' },
    { resource: 'class', action: 'create', condition: 'same_tenant' },
    { resource: 'class', action: 'edit', condition: 'same_tenant' },
    { resource: 'class', action: 'delete', condition: 'same_tenant' },
    { resource: 'class', action: 'list', condition: 'same_tenant' },
    
    // Faixas
    { resource: 'belt', action: 'view', condition: 'same_tenant' },
    { resource: 'belt', action: 'create', condition: 'same_tenant' },
    { resource: 'belt', action: 'edit', condition: 'same_tenant' },
    { resource: 'belt', action: 'delete', condition: 'same_tenant' },
    { resource: 'belt', action: 'list', condition: 'same_tenant' },
    
    // Relatórios
    { resource: 'report', action: 'view', condition: 'same_tenant' },
    { resource: 'report', action: 'create', condition: 'same_tenant' },
    { resource: 'report', action: 'export', condition: 'same_tenant' },
    
    // Tenant
    { resource: 'tenant', action: 'view', condition: 'same_tenant' },
    { resource: 'tenant', action: 'edit', condition: 'same_tenant' },
  ],
};

/**
 * Políticas de permissão para instrutores
 */
export const instructorPolicy: PermissionPolicy = {
  role: 'instructor',
  permissions: [
    // Gerenciamento de usuários
    { resource: 'user', action: 'view', condition: 'students_only' },
    { resource: 'user', action: 'list', condition: 'students_only' },
    
    // Perfis
    { resource: 'profile', action: 'view', condition: 'self_or_students' },
    { resource: 'profile', action: 'edit', condition: 'self' },
    
    // Presenças
    { resource: 'attendance', action: 'view', condition: 'same_tenant' },
    { resource: 'attendance', action: 'create', condition: 'same_tenant' },
    { resource: 'attendance', action: 'edit', condition: 'same_tenant' },
    { resource: 'attendance', action: 'list', condition: 'same_tenant' },
    
    // Aulas
    { resource: 'class', action: 'view', condition: 'same_tenant' },
    { resource: 'class', action: 'create', condition: 'same_tenant' },
    { resource: 'class', action: 'edit', condition: 'same_tenant' },
    { resource: 'class', action: 'list', condition: 'same_tenant' },
    
    // Faixas
    { resource: 'belt', action: 'view', condition: 'same_tenant' },
    { resource: 'belt', action: 'create', condition: 'same_tenant' },
    { resource: 'belt', action: 'edit', condition: 'same_tenant' },
  ],
};

/**
 * Políticas de permissão para professores
 */
export const teacherPolicy: PermissionPolicy = {
  role: 'teacher',
  permissions: [
    // Gerenciamento de usuários
    { resource: 'user', action: 'view', condition: 'students_only' },
    { resource: 'user', action: 'list', condition: 'students_only' },
    
    // Perfis
    { resource: 'profile', action: 'view', condition: 'self_or_students' },
    { resource: 'profile', action: 'edit', condition: 'self' },
    
    // Presenças
    { resource: 'attendance', action: 'view', condition: 'same_tenant' },
    { resource: 'attendance', action: 'create', condition: 'same_tenant' },
    { resource: 'attendance', action: 'edit', condition: 'same_tenant' },
    { resource: 'attendance', action: 'list', condition: 'same_tenant' },
    
    // Aulas
    { resource: 'class', action: 'view', condition: 'same_tenant' },
    { resource: 'class', action: 'create', condition: 'same_tenant' },
    { resource: 'class', action: 'edit', condition: 'same_tenant' },
    { resource: 'class', action: 'list', condition: 'same_tenant' },
    
    // Faixas
    { resource: 'belt', action: 'view', condition: 'same_tenant' },
    { resource: 'belt', action: 'create', condition: 'same_tenant' },
    { resource: 'belt', action: 'edit', condition: 'same_tenant' },
  ],
};

/**
 * Políticas de permissão para estudantes
 */
export const studentPolicy: PermissionPolicy = {
  role: 'student',
  permissions: [
    // Gerenciamento de usuários
    { resource: 'user', action: 'view', condition: 'self' },
    
    // Perfis
    { resource: 'profile', action: 'view', condition: 'self' },
    { resource: 'profile', action: 'edit', condition: 'self' },
    
    // Presenças
    { resource: 'attendance', action: 'view', condition: 'self' },
    
    // Pagamentos
    { resource: 'payment', action: 'view', condition: 'self' },
    
    // Aulas
    { resource: 'class', action: 'view', condition: 'same_tenant' },
    { resource: 'class', action: 'list', condition: 'same_tenant' },
    
    // Faixas
    { resource: 'belt', action: 'view', condition: 'self' },
  ],
};

/**
 * Mapa de todas as políticas disponíveis
 */
export const policyMap: Record<string, PermissionPolicy> = {
  admin: adminPolicy,
  instructor: instructorPolicy,
  teacher: teacherPolicy,
  student: studentPolicy,
}; 