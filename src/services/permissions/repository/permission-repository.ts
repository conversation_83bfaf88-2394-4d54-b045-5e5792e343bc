import { ConditionalPermission, PermissionPolicy, UserRole } from '../types/permission-types';
import { policyMap } from '../policies/default-policies';

/**
 * Repositório para acesso e gerenciamento de políticas de permissão
 */
export class PermissionRepository {
  private policies: Record<string, PermissionPolicy>;
  
  constructor() {
    this.policies = { ...policyMap };
  }
  
  /**
   * Obtém a política completa para um determinado papel
   */
  getPolicyForRole(role: UserRole): PermissionPolicy | null {
    return this.policies[role] || null;
  }
  
  /**
   * Obtém todas as permissões para um determinado papel
   */
  getPermissionsForRole(role: UserRole): ConditionalPermission[] {
    const policy = this.getPolicyForRole(role);
    return policy ? policy.permissions : [];
  }
  
  /**
   * Verifica se uma permissão específica existe para um papel
   */
  hasPermission(
    role: UserRole, 
    resource: string, 
    action: string
  ): ConditionalPermission | null {
    const permissions = this.getPermissionsForRole(role);
    
    const permission = permissions.find(
      p => p.resource === resource && p.action === action
    );
    
    return permission || null;
  }
  
  /**
   * Adiciona uma nova política para um papel (sobrescreve se já existir)
   */
  setPolicy(policy: PermissionPolicy): void {
    this.policies[policy.role] = policy;
  }
  
  /**
   * Adiciona ou atualiza uma permissão específica para um papel
   */
  setPermission(
    role: UserRole, 
    permission: ConditionalPermission
  ): void {
    const policy = this.getPolicyForRole(role);
    
    if (!policy) {
      this.setPolicy({
        role,
        permissions: [permission]
      });
      return;
    }
    
    const index = policy.permissions.findIndex(
      p => p.resource === permission.resource && p.action === permission.action
    );
    
    if (index >= 0) {
      policy.permissions[index] = permission;
    } else {
      policy.permissions.push(permission);
    }
  }
  
  /**
   * Remove uma permissão específica de um papel
   */
  removePermission(
    role: UserRole, 
    resource: string, 
    action: string
  ): boolean {
    const policy = this.getPolicyForRole(role);
    
    if (!policy) {
      return false;
    }
    
    const index = policy.permissions.findIndex(
      p => p.resource === resource && p.action === action
    );
    
    if (index >= 0) {
      policy.permissions.splice(index, 1);
      return true;
    }
    
    return false;
  }
  
  /**
   * Obtém todas as políticas 
   */
  getAllPolicies(): Record<string, PermissionPolicy> {
    return { ...this.policies };
  }
  
  /**
   * Reseta todas as políticas para os valores padrão
   */
  resetToDefaults(): void {
    this.policies = { ...policyMap };
  }
} 