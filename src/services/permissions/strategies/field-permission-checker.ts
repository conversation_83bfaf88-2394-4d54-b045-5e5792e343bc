import { 
  EditCondition,
  FieldPermission, 
  FieldPermissionContext, 
  IFieldPermissionStrategy, 
  ProfileField,
  RoleBasedFieldPermission 
} from '../types/field-permission-types';
import { UserRole } from '../types/permission-types';

export class FieldPermissionChecker implements IFieldPermissionStrategy {
  private permissions: Array<FieldPermission | RoleBasedFieldPermission>;
  
  constructor(permissions: Array<FieldPermission | RoleBasedFieldPermission>) {
    this.permissions = permissions;
  }
  
  canEdit(field: ProfileField, context: FieldPermissionContext): boolean {
    const { currentUserId, currentUserRole, targetUserId } = context;
    
    // 1. Encontrar todas as permissões para o campo específico
    const fieldPermissions = this.permissions.filter(p => p.field === field);
    
    // 2. Encontrar permissões que se aplicam ao papel do usuário
    const rolePermissions = fieldPermissions.filter(p => p.roles.includes(currentUserRole));
    
    // Se não houver permissões para este papel, negar acesso
    if (rolePermissions.length === 0) {
      return false;
    }
    
    // 3. Verificar condições de edição para cada permissão
    return rolePermissions.some(permission => {
      // Obter condições de edição com base no tipo de permissão
      const conditions = this.getEditConditions(permission, currentUserRole);
      
      // 4. Verificar se as condições de edição permitem a operação
      return this.checkEditConditions(conditions, context);
    });
  }
  
  private getEditConditions(
    permission: FieldPermission | RoleBasedFieldPermission, 
    role: UserRole
  ): EditCondition[] {
    // Verificar se é uma permissão baseada em papel
    if ('getEditConditions' in permission) {
      return permission.getEditConditions(role);
    }
    
    // Caso contrário, usar editConditions padrão
    return permission.editConditions;
  }
  
  private checkEditConditions(
    conditions: EditCondition[], 
    context: FieldPermissionContext
  ): boolean {
    const { currentUserId, targetUserId, currentTenantId, targetTenantId } = context;
    
    // Verificar cada condição
    return conditions.some(condition => {
      switch(condition) {
        case 'self':
          return currentUserId === targetUserId;
        case 'others':
          return currentUserId !== targetUserId;
        case 'same_tenant':
          return currentTenantId === targetTenantId;
        default:
          return false;
      }
    });
  }
} 