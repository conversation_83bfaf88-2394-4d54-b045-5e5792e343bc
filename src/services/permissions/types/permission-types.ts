/**
 * Tipos básicos para o sistema de permissões
 */

// Papéis de usuário disponíveis no sistema
export type UserRole = 'admin' | 'teacher' | 'instructor' | 'student';

// Recursos do sistema que podem ter permissões
export type ResourceType = 
  | 'user'
  | 'profile' 
  | 'attendance' 
  | 'payment' 
  | 'class' 
  | 'belt' 
  | 'report'
  | 'tenant'
  | 'students'
  | 'instructors';  // Adicionado para gerenciamento de alunos

// Ações que podem ser realizadas em recursos
export type ActionType = 
  | 'view' 
  | 'create' 
  | 'edit' 
  | 'delete' 
  | 'activate' 
  | 'deactivate'
  | 'list'
  | 'export'
  | 'import';

// Permissão para uma ação específica em um recurso
export interface Permission {
  resource: ResourceType;
  action: ActionType;
}

// Condição que pode ser aplicada a uma permissão
export type PermissionCondition = 
  | 'any'          // Sem restrições
  | 'self'         // Apenas para o próprio usuário
  | 'same_tenant'  // Apenas no mesmo tenant
  | 'not_self'     // Qualquer usuário exceto o próprio
  | 'students_only' // Apenas estudantes
  | 'self_or_students' // Próprio usuário OU estudantes apenas
  | 'others_only'; // Apenas outros perfis

// Permissão com condição
export interface ConditionalPermission extends Permission {
  condition: PermissionCondition;
}

// Política de permissões para um papel específico
export interface PermissionPolicy {
  role: UserRole;
  permissions: ConditionalPermission[];
}

// Contexto usado para avaliar permissões
export interface PermissionContext {
  currentUserId: string;
  currentUserRole: UserRole;
  currentUserTenantId: string;
  targetUserId?: string;
  targetUserRole?: UserRole;
  targetTenantId?: string;
}

// Resultado de uma verificação de permissão
export interface PermissionCheckResult {
  granted: boolean;
  reason?: string;
} 