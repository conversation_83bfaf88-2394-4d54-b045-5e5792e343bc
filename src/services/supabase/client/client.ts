import { createBrowserClient } from "@supabase/ssr";
import type { SupabaseClient } from "@supabase/supabase-js";
import type { CookieOptions } from "@supabase/ssr";

// Utilizar um singleton para evitar múltiplas instâncias do cliente Supabase
let supabaseClientInstance: SupabaseClient | null = null;
// Flag para controlar se o listener de autenticação já foi configurado
let authListenerConfigured = false;

// Usamos uma abordagem mais direta para o desenvolvimento local com cookies
export const createClient = () => {
  // Retornar a instância existente se já tiver sido criada
  if (supabaseClientInstance) {
    return supabaseClientInstance;
  }

  try {    
    // Para o cliente, precisamos validar as variáveis de ambiente manualmente
    // pois a configuração centralizada pode não estar disponível no cliente
    const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    if (!url || !anonKey) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Variáveis de ambiente do Supabase não encontradas no cliente:', {
          hasUrl: !!url,
          hasAnonKey: !!anonKey,
          nodeEnv: process.env.NODE_ENV,
          userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
        });
      }
      throw new Error('Configuração do Supabase não encontrada. Verifique as variáveis de ambiente NEXT_PUBLIC_SUPABASE_URL e NEXT_PUBLIC_SUPABASE_ANON_KEY.');
    }

    const client = createBrowserClient(
      url,
      anonKey,
      {
        auth: {
          persistSession: true,
          autoRefreshToken: true,
          detectSessionInUrl: true,
          flowType: 'pkce',
        },
        cookies: {
          getAll: () => {
            if (typeof document === 'undefined') return [];
            
            return document.cookie.split('; ').reduce((cookies, cookie) => {
              const [name, value] = cookie.split('=');
              if (name && value) {
                cookies.push({ name, value });
              }
              return cookies;
            }, [] as { name: string; value: string }[]);
          },
          setAll: (cookiesToSet: { name: string; value: string; options?: CookieOptions }[]) => {
            if (typeof document === 'undefined') return;
            
            cookiesToSet.forEach(({ name, value, options }) => {
              let cookie = `${name}=${value}`;
              
              if (options) {
                if (options.expires) {
                  cookie += `; expires=${options.expires.toUTCString()}`;
                }
                if (options.path) {
                  cookie += `; path=${options.path}`;
                }
                if (options.domain) {
                  cookie += `; domain=${options.domain}`;
                }
                if (options.sameSite) {
                  cookie += `; samesite=${options.sameSite}`;
                }
                if (options.secure) {
                  cookie += '; secure';
                }
              }
              
              document.cookie = cookie;
            });
          }
        }
      }
    );
    
    client.auth.getSession().then(({ data, error }) => {
      if (error) {
        console.error('Erro ao verificar sessão durante criação do cliente:', error.message);
      } else {
                    
        if (!data.session && typeof localStorage !== 'undefined') {
          const hasAuthTokens = Object.keys(localStorage).some(key => 
            key.startsWith('sb-') && (key.includes('auth') || key.includes('session'))
          );
          
          if (hasAuthTokens) {
            client.auth.refreshSession().then(({ data: refreshData, error: refreshError }) => {
              if (refreshError) {
                console.error('Falha ao recuperar sessão:', refreshError.message);
              }
            });
          }
        }
      }
    }).catch(err => {
      console.error('Exceção ao verificar sessão durante criação do cliente:', err);
    });
    
    if (!authListenerConfigured) {
      client.auth.onAuthStateChange((event, session) => {
        
        if (event === 'SIGNED_IN' && session) {
          
          if (typeof sessionStorage !== 'undefined') {
            sessionStorage.setItem('auth_event_signed_in', 'true');
            sessionStorage.setItem('auth_event_time', Date.now().toString());
            sessionStorage.setItem('auth_event_user', session.user.id);
          }
        }
      });
      
      // Marcar que o listener já foi configurado
      authListenerConfigured = true;
    }
    
    // Armazenar a instância para uso futuro
    supabaseClientInstance = client;
    
    return client;
  } catch (error) {
    console.error('Erro crítico ao criar cliente Supabase:', error);
    // Re-throw o erro para que seja capturado por error boundaries
    throw error;
  }
}; 