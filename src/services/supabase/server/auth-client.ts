import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import { getSupabaseConfig } from "@/config/supabase";

// Cliente Supabase específico para operações de autenticação
// que retorna também os cookies para serem definidos na resposta HTTP
export const createAuthClient = async () => {
  const cookieStore = await cookies();
  const pendingCookies: { name: string; value: string; options?: any }[] = [];
  
  // Log de todos os cookies disponíveis para debug
  const allCookies = cookieStore.getAll();
  console.log('Auth Client - Cookies disponíveis:', allCookies.map(c => `${c.name}`).join(', '));
  
  const supabaseCookies = allCookies.filter(c => 
    c.name.startsWith('sb-') || 
    c.name === 'supabase-auth-token'
  );
  
  if (supabaseCookies.length > 0) {
    console.log('Auth Client - Cookies Supabase encontrados:', 
                supabaseCookies.map(c => c.name).join(', '));
  } else {
    console.warn('Auth Client - Nenhum cookie Supabase encontrado');
  }

  const { url, anonKey } = getSupabaseConfig();
  
  const supabase = createServerClient(
    url,
    anonKey,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              console.log(`Auth Client - Cookie capturado para definir: ${name}`);
              pendingCookies.push({ name, value, options });
            });
          } catch (error) {
            console.error('Erro ao processar cookies no cliente de autenticação:', error);
          }
        },
      },
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: false,
      },
    },
  );

  return {
    supabase,
    applySessionCookiesToResponse: (response: NextResponse) => {
      if (pendingCookies.length === 0) {
        console.warn('Auth Client - Não há cookies pendentes para aplicar à resposta');
        return response;
      }

      console.log(`Auth Client - Aplicando ${pendingCookies.length} cookies à resposta HTTP`);
      
      const host = process.env.HOST || 'localhost';
      console.log(`Auth Client - Usando host para determinar domínio do cookie: ${host}`);
      
      for (const { name, value, options } of pendingCookies) {
        const cookieOptions = { ...options };
        
        cookieOptions.path = '/';
        cookieOptions.sameSite = 'lax';
        cookieOptions.secure = process.env.NODE_ENV === 'production';

        // Lógica de domínio do cookie
        // Para localhost e 127.0.0.1, não defina o atributo de domínio.
        // Para outros domínios, use o host.
        if (host !== 'localhost' && !host.startsWith('127.0.0.1')) {
          // Em produção, se você tiver um domínio customizado, por exemplo app.example.com
          // você pode querer definir cookieOptions.domain = '.example.com' para compartilhar
          // cookies entre subdomínios. Para este exemplo, vamos usar o host diretamente.
          cookieOptions.domain = host;
        } else {
          // Para localhost, omitir o atributo de domínio é mais seguro e funciona melhor.
          delete cookieOptions.domain;
        }
        
        // Para cookies de autenticação do Supabase, configurações especiais
        if (name.startsWith('sb-') || name === 'supabase-auth-token') {
          // Cookies de sessão devem ser HttpOnly para segurança
          cookieOptions.httpOnly = true; 
          
          // O Supabase gerencia a expiração dos tokens, maxAge pode ser consistente.
          // Se o SDK do Supabase definir um maxAge, ele será usado. Caso contrário,
          // pode-se definir um padrão razoável ou deixar como cookie de sessão.
          if (!cookieOptions.maxAge) {
            // Exemplo: cookie de sessão (expira quando o navegador fecha) ou um maxAge longo
            // Se o Supabase lida com refresh tokens, o maxAge do cookie de acesso pode ser menor.
            // Para este exemplo, vamos deixar o Supabase SSR decidir o maxAge ou usar um padrão de 7 dias.
             cookieOptions.maxAge = cookieOptions.maxAge || 7 * 24 * 60 * 60; // 7 dias como fallback
          }
        }

        console.log(`Auth Client - Detalhes para definir o cookie ${name}:`, {
          value: value ? '[PRESENT]' : '[EMPTY]', // Não logar o valor do cookie
          domain: cookieOptions.domain,
          httpOnly: cookieOptions.httpOnly,
          maxAge: cookieOptions.maxAge,
          path: cookieOptions.path,
          sameSite: cookieOptions.sameSite,
          secure: cookieOptions.secure
        });
        
        try {
          console.log(`Auth Client - Definindo cookie na resposta: ${name}`);
          response.cookies.set(name, value, cookieOptions);
        } catch (error) {
          console.error(`Auth Client - Erro ao definir cookie ${name}:`, error);
        }
      }

      return response;
    },
  };
}; 