import { TenantExtractionContext } from './types';
import { TENANT_CONFIG } from './tenant-config';
import { TenantUtils } from './tenant-utils';

export class TenantExtractorClient {
  private readonly defaultTenant: string;

  constructor(defaultTenant = TENANT_CONFIG.defaultTenant) {
    this.defaultTenant = defaultTenant;
  }

  /**
   * Extrai o slug do tenant no ambiente cliente
   */
  extractTenantSlug(): string {
    if (typeof window === 'undefined') {
      return this.defaultTenant;
    }

    const context = this.buildExtractionContext();
    const slug = TenantUtils.extractTenantSlug(context);
    
    return slug || this.defaultTenant;
  }

  /**
   * Constrói o contexto de extração do cliente
   */
  private buildExtractionContext(): TenantExtractionContext {
    const location = window.location;
    
    return {
      hostname: location.hostname,
      pathname: location.pathname,
      headers: this.extractHeaders(),
      cookies: this.extractCookies()
    };
  }

  /**
   * Extrai headers relevantes (simulado no cliente)
   */
  private extractHeaders(): Record<string, string> {
    const headers: Record<string, string> = {};
    
    // No cliente, podemos simular alguns headers baseados na URL
    if (window.location.hostname) {
      headers['host'] = window.location.hostname;
    }
    
    // Verificar se existe algum meta tag com informações de tenant
    const tenantMeta = document.querySelector('meta[name="x-tenant-slug"]');
    if (tenantMeta) {
      headers['x-tenant-slug'] = tenantMeta.getAttribute('content') || '';
    }
    
    return headers;
  }

  /**
   * Extrai cookies relevantes
   */
  private extractCookies(): Record<string, string> {
    const cookies: Record<string, string> = {};
    
    if (typeof document !== 'undefined') {
      document.cookie.split(';').forEach(cookie => {
        const [name, value] = cookie.trim().split('=');
        if (name && value) {
          cookies[name] = decodeURIComponent(value);
        }
      });
    }
    
    return cookies;
  }

  /**
   * Verifica se o slug atual é válido
   */
  isValidCurrentSlug(): boolean {
    const slug = this.extractTenantSlug();
    return TenantUtils.isValidSlug(slug);
  }

  /**
   * Obtém estatísticas do cache (para debug)
   */
  getCacheStats() {
    // Importação dinâmica para evitar problemas de SSR
    return import('./tenant-cache').then(({ tenantCache }) => 
      tenantCache.getStats()
    );
  }
} 