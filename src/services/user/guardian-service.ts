import { createClient } from '@/services/supabase/server';
import { cache } from 'react';
import { 
  GuardianInfoInput, 
  UpdateGuardianInput,
  SetupManagedAccountInput,
  guardianWithCPFValidationSchema,
  minorAccountInfoSchema 
} from './schemas/guardian-schemas';
import { 
  GuardianInfo, 
  MinorAccountInfo, 
  UserWithGuardianInfo,
  MinorAccountDisplayInfo,
  calculateIsMinor 
} from '@/src/types/guardian';

/**
 * Serviço para gerenciar responsáveis e contas de menores de idade
 */
class GuardianService {
  /**
   * Busca informações completas de um usuário incluindo dados de responsável
   */
  async getUserWithGuardianInfo(userId: string): Promise<UserWithGuardianInfo | null> {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('users')
      .select(`
        id,
        first_name,
        last_name,
        email,
        is_minor,
        is_guardian_account,
        managed_student_id,
        guardian_name,
        guardian_email,
        guardian_phone,
        guardian_relationship,
        guardian_document,
        students!students_user_id_fkey!inner(birth_date)
      `)
      .eq('id', userId)
      .single();

    if (error || !data) {
      console.error('Erro ao buscar dados do usuário:', error);
      return null;
    }

    return {
      id: data.id,
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email,
      birth_date: data.students?.[0]?.birth_date || null,
      is_minor: data.is_minor || false,
      is_guardian_account: data.is_guardian_account || false,
      managed_student_id: data.managed_student_id,
      guardian_name: data.guardian_name,
      guardian_email: data.guardian_email,
      guardian_phone: data.guardian_phone,
      guardian_relationship: data.guardian_relationship,
      guardian_document: data.guardian_document
    };
  }

  /**
   * Busca o status de menor de idade diretamente do banco de dados
   */
  async getMinorStatus(userId: string): Promise<boolean> {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('users')
      .select('is_minor')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Erro ao buscar status de menor de idade:', error);
      throw new Error('Erro ao verificar status de menor de idade');
    }

    return data?.is_minor || false;
  }

  /**
   * Atualiza dados do responsável para um usuário menor de idade
   */
  async updateGuardianInfo(input: UpdateGuardianInput): Promise<{ success: boolean; error?: string }> {
    try {
      // Validar entrada
      const validatedData = guardianWithCPFValidationSchema.parse({
        guardian_name: input.guardian_name,
        guardian_email: input.guardian_email,
        guardian_phone: input.guardian_phone,
        guardian_relationship: input.guardian_relationship,
        guardian_document: input.guardian_document
      });

      const supabase = await createClient();

      // Verificar se o usuário é menor de idade
      const user = await this.getUserWithGuardianInfo(input.userId);
      if (!user?.is_minor) {
        return { success: false, error: 'Usuário não é menor de idade' };
      }

      // Atualizar dados do responsável
      const { error } = await supabase
        .from('users')
        .update({
          guardian_name: validatedData.guardian_name,
          guardian_email: validatedData.guardian_email,
          guardian_phone: validatedData.guardian_phone,
          guardian_relationship: validatedData.guardian_relationship,
          guardian_document: validatedData.guardian_document
        })
        .eq('id', input.userId);

      if (error) {
        console.error('Erro ao atualizar dados do responsável:', error);
        return { success: false, error: 'Erro ao atualizar dados do responsável' };
      }

      return { success: true };
    } catch (error) {
      console.error('Erro na validação dos dados:', error);
      return { success: false, error: 'Dados inválidos fornecidos' };
    }
  }

  /**
   * Configura uma conta para ser administrada por um responsável
   */
  async setupManagedAccount(input: SetupManagedAccountInput): Promise<{ success: boolean; error?: string }> {
    try {
      // Validar entrada
      const validatedData = guardianWithCPFValidationSchema.parse(input.guardianData);
      
      const supabase = await createClient();

      // Verificar se o usuário menor existe e é realmente menor de idade
      const minorUser = await this.getUserWithGuardianInfo(input.minorUserId);
      if (!minorUser) {
        return { success: false, error: 'Usuário menor não encontrado' };
      }

      if (!minorUser.is_minor) {
        return { success: false, error: 'Usuário não é menor de idade' };
      }

      // Atualizar dados do menor com informações do responsável
      const { error } = await supabase
        .from('users')
        .update({
          guardian_name: validatedData.guardian_name,
          guardian_email: validatedData.guardian_email,
          guardian_phone: validatedData.guardian_phone,
          guardian_relationship: validatedData.guardian_relationship,
          guardian_document: validatedData.guardian_document
        })
        .eq('id', input.minorUserId);

      if (error) {
        console.error('Erro ao configurar conta administrada:', error);
        return { success: false, error: 'Erro ao configurar conta administrada' };
      }

      return { success: true };
    } catch (error) {
      console.error('Erro na validação dos dados:', error);
      return { success: false, error: 'Dados inválidos fornecidos' };
    }
  }

  /**
   * Verifica se um usuário tem responsável configurado
   */
  async hasGuardianConfigured(userId: string): Promise<boolean> {
    const user = await this.getUserWithGuardianInfo(userId);
    return !!(
      user?.guardian_name &&
      user?.guardian_email &&
      user?.guardian_phone &&
      user?.guardian_relationship &&
      user?.guardian_document
    );
  }

  /**
   * Lista todos os estudantes menores de idade que precisam de responsável
   */
  async getMinorsWithoutGuardian(): Promise<UserWithGuardianInfo[]> {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('users')
      .select(`
        id,
        first_name,
        last_name,
        email,
        is_minor,
        is_guardian_account,
        managed_student_id,
        guardian_name,
        guardian_email,
        guardian_phone,
        guardian_relationship,
        guardian_document,
        students!students_user_id_fkey!inner(birth_date)
      `)
      .eq('is_minor', true)
      .is('guardian_name', null);

    if (error) {
      console.error('Erro ao buscar menores sem responsável:', error);
      return [];
    }

    return data.map(user => ({
      id: user.id,
      first_name: user.first_name,
      last_name: user.last_name,
      email: user.email,
      birth_date: user.students?.[0]?.birth_date || null,
      is_minor: user.is_minor || false,
      is_guardian_account: user.is_guardian_account || false,
      managed_student_id: user.managed_student_id,
      guardian_name: user.guardian_name,
      guardian_email: user.guardian_email,
      guardian_phone: user.guardian_phone,
      guardian_relationship: user.guardian_relationship,
      guardian_document: user.guardian_document
    }));
  }

  /**
   * Lista todos os estudantes menores de idade que já têm responsável configurado
   */
  async getMinorsWithGuardian(): Promise<UserWithGuardianInfo[]> {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('users')
      .select(`
        id,
        first_name,
        last_name,
        email,
        is_minor,
        is_guardian_account,
        managed_student_id,
        guardian_name,
        guardian_email,
        guardian_phone,
        guardian_relationship,
        guardian_document,
        students!students_user_id_fkey!inner(birth_date)
      `)
      .eq('is_minor', true)
      .not('guardian_name', 'is', null);

    if (error) {
      console.error('Erro ao buscar menores com responsável:', error);
      return [];
    }

    return data.map(user => ({
      id: user.id,
      first_name: user.first_name,
      last_name: user.last_name,
      email: user.email,
      birth_date: user.students?.[0]?.birth_date || null,
      is_minor: user.is_minor || false,
      is_guardian_account: user.is_guardian_account || false,
      managed_student_id: user.managed_student_id,
      guardian_name: user.guardian_name,
      guardian_email: user.guardian_email,
      guardian_phone: user.guardian_phone,
      guardian_relationship: user.guardian_relationship,
      guardian_document: user.guardian_document
    }));
  }

  /**
   * Obtém informações de exibição para conta de menor
   */
  getMinorAccountDisplayInfo(user: UserWithGuardianInfo): MinorAccountDisplayInfo {
    const hasGuardian = !!(
      user.guardian_name &&
      user.guardian_email &&
      user.guardian_phone &&
      user.guardian_relationship &&
      user.guardian_document
    );

    return {
      isMinor: user.is_minor,
      hasGuardian,
      guardianName: user.guardian_name || undefined,
      guardianRelationship: user.guardian_relationship || undefined,
      requiresGuardianConsent: user.is_minor && !hasGuardian
    };
  }

  /**
   * Valida se um usuário pode realizar ações em nome de um menor
   */
  async canActAsGuardian(guardianUserId: string, minorUserId: string): Promise<boolean> {
    const supabase = await createClient();
    
    // Verificar se o responsável está configurado para este menor
    const { data, error } = await supabase
      .from('users')
      .select('guardian_email')
      .eq('id', minorUserId)
      .single();

    if (error || !data) return false;

    // Verificar se o e-mail do responsável coincide com o usuário atual
    const { data: guardianData, error: guardianError } = await supabase
      .from('users')
      .select('email')
      .eq('id', guardianUserId)
      .single();

    if (guardianError || !guardianData) return false;

    return data.guardian_email === guardianData.email;
  }

  /**
   * Remove dados do responsável (quando o menor completar 18 anos)
   */
  async removeGuardianData(userId: string): Promise<{ success: boolean; error?: string }> {
    const supabase = await createClient();

    const { error } = await supabase
      .from('users')
      .update({
        guardian_name: null,
        guardian_email: null,
        guardian_phone: null,
        guardian_relationship: null,
        guardian_document: null,
        is_minor: false
      })
      .eq('id', userId);

    if (error) {
      console.error('Erro ao remover dados do responsável:', error);
      return { success: false, error: 'Erro ao remover dados do responsável' };
    }

    return { success: true };
  }
}

// Cache da instância do serviço
export const guardianService = new GuardianService();

// Funções com cache para uso em Server Components
export const getUserWithGuardianInfo = cache(guardianService.getUserWithGuardianInfo.bind(guardianService));
export const hasGuardianConfigured = cache(guardianService.hasGuardianConfigured.bind(guardianService));
export const getMinorsWithoutGuardian = cache(guardianService.getMinorsWithoutGuardian.bind(guardianService));
export const getMinorsWithGuardian = cache(guardianService.getMinorsWithGuardian.bind(guardianService)); 