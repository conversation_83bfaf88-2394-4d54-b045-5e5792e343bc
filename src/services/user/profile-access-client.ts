interface StudentBelt {
  id?: string;
  belt_level_id?: string;
  color: string;
  degree: number;
  stripeColor?: string | null;
  showCenterLine?: boolean | null;
  centerLineColor?: string | null;
  label?: string | null;
  sortOrder?: number | null;
  awardedAt?: string | null;
}

/**
 * Verifica se o usuário tem acesso ao perfil solicitado
 * 
 * @param requestedUserId ID do usuário cujo perfil está sendo acessado
 * @returns boolean indicando se o acesso é permitido
 */
export async function checkProfileAccess(requestedUserId: string): Promise<boolean> {
  try {
    const response = await fetch(`/api/permissions/check?resource=profile&action=view&targetId=${requestedUserId}`);
    const data = await response.json();
    
    // A API pode retornar allowed ou granted como chave, verificar ambos
    return data.allowed === true || data.granted === true;
  } catch (error) {
    console.error('Erro ao verificar permissões de acesso ao perfil:', error);
    return false;
  }
}

/**
 * Carrega os dados do perfil de um usuário específico
 * 
 * @param userId ID do usuário cujo perfil será carregado
 * @returns Dados do perfil do usuário
 */
export async function loadUserProfileClient(userId: string) {
  try {
    console.log(`[PERFIL] Buscando dados do perfil ${userId} da API`);
    
    // Sempre buscar dados frescos da API
    const response = await fetch(`/api/user/${userId}/profile`, {
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache'
      }
    });
    
    if (!response.ok) {
      const error = await response.text();
      console.error('Erro ao carregar perfil:', error);
      return { data: null, error: 'Erro ao carregar perfil' };
    }
    
    const data = await response.json();
    console.log(`[PERFIL] Dados buscados da API`, data);
    
    return { data, error: null };
  } catch (error) {
    console.error('Erro ao carregar perfil do usuário:', error);
    return { data: null, error: 'Erro ao carregar perfil' };
  }
} 