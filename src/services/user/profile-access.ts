import { redirect } from 'next/navigation';
import { createClient } from '@/services/supabase/server';
import { validate as uuidValidate } from 'uuid';
import { getPermissionService } from '@/services/permissions/service';
import { getCurrentUser, requireAuth } from '@/services/auth/actions/auth-actions';

// Interface para a faixa atual do estudante
interface StudentBelt {
  id?: string;
  belt_level_id: string;
  color: string;
  degree: number;
  stripeColor?: string | null;
  showCenterLine?: boolean | null;
  centerLineColor?: string | null;
  label?: string | null;
  sortOrder?: number | null;
  awardedAt?: string | null;
}

// Interface para os dados de perfil do usuário
interface UserProfileData {
  id: string;
  email: string;
  first_name: string;
  last_name: string | null;
  fullName: string;
  role: string;
  created_at: string;
  joinDate: string | null;
  avatar_url: string | null;
  phone: string | null;
  metadata: any;
  
  // Campos que podem ser extraídos dos metadados ou das novas colunas
  address: string | null;
  emergency_contact: string | null;
  emergency_phone: string | null;
  
  // Campos específicos de estudante
  studentId?: string;
  registrationNumber?: string;
  status?: string;
  financialStatus?: string;
  birthDate?: string;
  gender?: string;
  
  // Campos de relacionamento com emergência
  emergency_contact_relationship?: string;
  
  // Informações de saúde
  healthNotes?: string;
  allergies?: string;
  medicalConditions?: string;
  medications?: string;
  
  // Código de check-in
  check_in_code?: string;
  
  // Informações de pagamento
  paymentStatus?: string;
  lastPaymentDate?: string | null;
  nextPaymentDue?: string | null;
  
  // Dados de presença
  attendanceRate?: number;
  lastAttendanceDate?: string | null;
  
  // Faixa atual
  currentBeltId?: string;
  currentBelt?: StudentBelt;
}

/**
 * Middleware para proteger o acesso às páginas de perfil
 * Usa o novo sistema de permissões
 * 
 * @param requestedUserId ID do usuário cujo perfil está sendo acessado
 * @returns Objeto de redirecionamento se o acesso for negado, null se permitido
 */
export async function protectProfileAccess(requestedUserId: string) {
  if (!uuidValidate(requestedUserId)) {
    console.error('ID de usuário inválido:', requestedUserId);
    return redirect('/home?erro=perfil-invalido');
  }
  
  // Obter o usuário autenticado usando auth-actions
  const { user } = await requireAuth('/login');
  
  // Usar o novo sistema de permissões
  const permissionService = getPermissionService();
  
  return permissionService.protectRoute(
    user.id,
    'profile',
    'view',
    requestedUserId,
    '/home?erro=acesso-negado'
  );
}

/**
 * Carrega os dados do perfil de um usuário específico
 * 
 * @param userId ID do usuário cujo perfil será carregado
 * @returns Dados do perfil do usuário
 */
export async function loadUserProfile(userId: string) {
  try {
    console.log('[DEBUG] Iniciando carregamento do perfil:', userId);
    const supabase = await createClient();
    
    // Função auxiliar para capitalizar primeira letra
    const capitalize = (text: string): string => {
      if (!text) return '';
      return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
    };
    
    // Buscar dados básicos do usuário
    const { data, error } = await supabase
      .from('users')
      .select(`
        id, 
        email, 
        first_name, 
        last_name, 
        role, 
        created_at, 
        avatar_url,
        phone,
        metadata,
        status
      `)
      .eq('id', userId)
      .single();
    
    if (error) {
      console.error('Erro ao carregar perfil do usuário:', error);
      return { error: 'Erro ao carregar perfil' };
    }
    
    // Extrair metadados adicionais (se existirem)
    const metadata = data.metadata || {};
    
    // Formatar o nome completo
    const fullName = `${data.first_name || ''}${data.last_name ? ' ' + data.last_name : ''}`;
    
    // Formatar a data de entrada
    const joinDate = data.created_at 
      ? new Date(data.created_at).toLocaleDateString('pt-BR') 
      : null;

    // Preparar o objeto de dados básicos
    let userData: UserProfileData = {
      ...data,
      fullName,
      joinDate,
      // Manter compatibilidade com o código existente 
      // que usa esses campos de metadata
      address: metadata.address || null,
      emergency_contact: metadata.emergency_contact || null,
      emergency_phone: metadata.emergency_phone || null
    };

    // Se o usuário for um estudante, buscar informações adicionais
    if (data.role === 'student') {
      console.log('[DEBUG] Usuário é estudante, buscando dados adicionais');
      
      // Buscar detalhes do estudante
      const { data: studentData, error: studentError } = await supabase
        .from('students')
        .select(`
          id,
          registration_number,
          financial_status,
          birth_date,
          emergency_contact_name,
          emergency_contact_phone,
          emergency_contact_relationship,
          health_notes,
          allergies,
          medical_conditions,
          medications,
          gender,
          street,
          street_number,
          complement,
          neighborhood,
          city,
          state,
          postal_code,
          check_in_code,
          current_belt_id,
          payment_status,
          last_payment_date,
          next_payment_due,
          attendance_rate,
          last_attendance_date
        `)
        .eq('user_id', userId)
        .single();
      
      if (studentError) {
        console.error('[DEBUG] Erro ao buscar dados do estudante:', studentError);
      } else {
        console.log('[DEBUG] Dados do estudante recuperados com sucesso:', studentData ? 'Sim' : 'Não');
      }
      
      if (!studentError && studentData) {
        // Formatar endereço completo
        const formattedAddress = studentData.street ? 
          `${studentData.street}${studentData.street_number ? ', ' + studentData.street_number : ''}${studentData.complement ? ', ' + studentData.complement : ''}
          ${studentData.neighborhood ? studentData.neighborhood : ''}
          ${studentData.city ? studentData.city + ' - ' + studentData.state : ''}
          ${studentData.postal_code ? 'CEP: ' + studentData.postal_code : ''}`.trim() : 
          metadata.address || null;

        userData = {
          ...userData,
          studentId: studentData.id,
          registrationNumber: studentData.registration_number,
          status: data.status || 'active',
          financialStatus: studentData.financial_status,
          birthDate: studentData.birth_date,
          gender: studentData.gender,
          emergency_contact: studentData.emergency_contact_name || userData.emergency_contact,
          emergency_phone: studentData.emergency_contact_phone || userData.emergency_phone,
          emergency_contact_relationship: studentData.emergency_contact_relationship,
          // Informações de saúde
          healthNotes: studentData.health_notes,
          allergies: studentData.allergies,
          medicalConditions: studentData.medical_conditions,
          medications: studentData.medications,
          // Endereço formatado
          address: formattedAddress,
          // Código de check-in
          check_in_code: studentData.check_in_code,
          // Informações de pagamento
          paymentStatus: studentData.payment_status,
          lastPaymentDate: studentData.last_payment_date ? 
            new Date(studentData.last_payment_date).toLocaleDateString('pt-BR') : null,
          nextPaymentDue: studentData.next_payment_due ? 
            new Date(studentData.next_payment_due).toLocaleDateString('pt-BR') : null,
          // Dados de presença
          attendanceRate: studentData.attendance_rate,
          lastAttendanceDate: studentData.last_attendance_date ? 
            new Date(studentData.last_attendance_date).toLocaleDateString('pt-BR') : null,
          // Referência à faixa atual
          currentBeltId: studentData.current_belt_id
        };
        
        console.log('[DEBUG] Check-in code:', studentData.check_in_code);
        console.log('[DEBUG] Current belt ID:', studentData.current_belt_id);
        
        console.log('[DEBUG] Obtendo detalhes da faixa atual via RPC');
        const { data: beltDetails, error: beltRpcError } = await supabase.rpc('get_student_current_belt_details', { student_id_param: studentData.id });

        if (beltRpcError) {
          console.error('[DEBUG] Erro ao obter faixa via RPC:', beltRpcError);
        }

        if (beltDetails && beltDetails.length > 0) {
          const belt = beltDetails[0];
          userData.currentBelt = {
            belt_level_id: belt.belt_level_id,
            color: belt.belt_color,
            degree: belt.degree,
            stripeColor: belt.stripe_color,
            showCenterLine: belt.show_center_line,
            centerLineColor: belt.center_line_color,
            label: belt.label,
            sortOrder: belt.sort_order,
          };
        }
        
        if (userData.attendanceRate === undefined || userData.attendanceRate === null) {
          console.log('[DEBUG] Calculando taxa de presença via RPC');
          const { data: attendanceData, error: attendanceError } = await supabase
            .rpc('get_student_attendance_rate', { student_id_param: studentData.id });
          
          if (attendanceError) {
            console.error('[DEBUG] Erro ao calcular taxa de presença:', attendanceError);
          }
          
          if (!attendanceError && attendanceData) {
            console.log('[DEBUG] Taxa de presença calculada:', attendanceData);
            userData.attendanceRate = attendanceData;
          }
        }
        
        if (!userData.lastAttendanceDate) {
          console.log('[DEBUG] Buscando última data de presença');
          try {
            // Verifica primeiro na tabela student_attendance (parece ser a tabela atual)
            const { data: studentAttendanceData, error: studentAttendanceError } = await supabase
              .from('student_attendance')
              .select('check_in')
              .eq('student_id', studentData.id)
              .order('check_in', { ascending: false })
              .limit(1);
            
            if (!studentAttendanceError && studentAttendanceData && studentAttendanceData.length > 0) {
              console.log('[DEBUG] Última data de presença encontrada em student_attendance:', studentAttendanceData[0].check_in);
              userData.lastAttendanceDate = new Date(studentAttendanceData[0].check_in).toLocaleDateString('pt-BR');
            } else {
              // Se não encontrou na primeira tabela, tenta na tabela attendance
              const { data: attendanceData, error: attendanceError } = await supabase
                .from('attendance')
                .select('checked_in_at')
                .eq('student_id', studentData.id)
                .order('checked_in_at', { ascending: false })
                .limit(1);
              
              if (attendanceError) {
                console.error('[DEBUG] Erro ao buscar última data de presença em attendance:', 
                  typeof attendanceError === 'object' ? JSON.stringify(attendanceError) : attendanceError);
              }
              
              if (!attendanceError && attendanceData && attendanceData.length > 0) {
                console.log('[DEBUG] Última data de presença encontrada em attendance:', attendanceData[0].checked_in_at);
                userData.lastAttendanceDate = new Date(attendanceData[0].checked_in_at).toLocaleDateString('pt-BR');
              } else {
                console.log('[DEBUG] Nenhum registro de presença encontrado para o estudante');
              }
            }
          } catch (error) {
            console.error('[DEBUG] Erro inesperado ao buscar última data de presença:', 
              error instanceof Error ? error.message : typeof error === 'object' ? JSON.stringify(error) : String(error));
          }
        }
        
        if (!userData.paymentStatus) {
          console.log('[DEBUG] Buscando informações de pagamento');
          const { data: paymentData, error: paymentError } = await supabase
            .from('payments')
            .select(`
              status,
              payment_date,
              due_date
            `)
            .eq('student_id', studentData.id)
            .order('due_date', { ascending: true })
            .limit(2);
          
          if (paymentError) {
            console.error('[DEBUG] Erro ao buscar informações de pagamento:', paymentError);
          }
          
          if (!paymentError && paymentData && paymentData.length > 0) {
            console.log('[DEBUG] Informações de pagamento recuperadas:', paymentData.length);
            const lastPayment = paymentData[0];
            userData.paymentStatus = capitalize(lastPayment.status);
            
            if (lastPayment.payment_date) {
              userData.lastPaymentDate = new Date(lastPayment.payment_date).toLocaleDateString('pt-BR');
            }
            
            if (paymentData.length > 1) {
              const nextPayment = paymentData[1];
              userData.nextPaymentDue = new Date(nextPayment.due_date).toLocaleDateString('pt-BR');
            } else if (lastPayment.status !== 'paid' && lastPayment.due_date) {
              userData.nextPaymentDue = new Date(lastPayment.due_date).toLocaleDateString('pt-BR');
            }
          }
        }

        // Log para debug dos campos médicos em studentData
        console.log('[DEBUG] Dados médicos de studentData:', {
          health_notes: studentData.health_notes,
          allergies: studentData.allergies,
          medical_conditions: studentData.medical_conditions,
          medications: studentData.medications
        });
        
        // Log para debug dos campos médicos em userData
        console.log('[DEBUG] Dados médicos em userData:', {
          healthNotes: userData.healthNotes,
          allergies: userData.allergies,
          medicalConditions: userData.medicalConditions,
          medications: userData.medications
        });
      }
    }

    console.log('[DEBUG] Dados do perfil montados com sucesso. Dados principais:', {
      id: userData.id,
      name: userData.fullName,
      role: userData.role,
      checkInCode: userData.check_in_code
    });
    
    return { data: userData };
  } catch (error) {
    console.error('[DEBUG] Erro ao carregar perfil:', error);
    return { error: 'Erro ao carregar dados do perfil' };
  }
} 