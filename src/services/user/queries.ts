'use server'

import { createClient } from '@/services/supabase/server'

export async function getUsersWithStatus() {
  const supabase = await createClient()

  const { data, error } = await supabase.from('users').select('id, email, full_name, first_name, last_name, role, status, created_at, branch_id').order('created_at', { ascending: false })

  if (error) throw error
  return data
}

export async function getStudentsWithUserStatus() {
  const supabase = await createClient()

  const { data, error } = await supabase
    .from('students')
    .select(
      `
      *,
      user:users!students_user_id_fkey(id, email, full_name, first_name, last_name, status, role)
    `
    )
    .order('created_at', { ascending: false })

  if (error) throw error
  return data
}

export async function getInstructorsWithUserStatus() {
  const supabase = await createClient()

  const { data, error } = await supabase
    .from('instructors')
    .select(
      `
      *,
      user:users!instructors_user_id_fkey(id, email, full_name, first_name, last_name, status, role)
    `
    )
    .order('created_at', { ascending: false })

  if (error) throw error
  return data
}

export async function getUsersByStatus(status: string | string[]) {
  const supabase = await createClient()

  let query = supabase.from('users').select('id, email, full_name, first_name, last_name, role, status, created_at')

  if (Array.isArray(status)) {
    query = query.in('status', status)
  } else {
    query = query.eq('status', status)
  }

  const { data, error } = await query.order('created_at', { ascending: false })

  if (error) throw error
  return data
}

export async function getActiveUsers() {
  return getUsersByStatus('active')
}

export async function getInactiveUsers() {
  return getUsersByStatus('inactive')
}

export async function getSuspendedUsers() {
  return getUsersByStatus('suspended')
}

export async function getUserById(userId: string) {
  const supabase = await createClient()

  const { data, error } = await supabase.from('users').select('*').eq('id', userId).single()

  if (error) throw error
  return data
}

export async function updateUserBasicInfo(
  userId: string,
  updates: {
    first_name?: string
    last_name?: string
    full_name?: string
    email?: string
    phone?: string
  }
) {
  const supabase = await createClient()

  const { data, error } = await supabase
    .from('users')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', userId)
    .select()
    .single()

  if (error) throw error
  return data
}
