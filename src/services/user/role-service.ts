import { createClient } from '@/services/supabase/server';

export type UserRole = 'admin' | 'teacher' | 'student';

interface UserRoleData {
  userId: string;
  role: UserRole;
}

class RoleValidator {
  isAdmin(role?: string | null): boolean {
    return role === 'admin';
  }
  
  isTeacher(role?: string | null): boolean {
    return role === 'teacher';
  }
  
  isStudent(role?: string | null): boolean {
    return role === 'student';
  }
  
  getHomeRoute(role?: string | null): string {
    if (this.isAdmin(role)) return '/dashboard';
    return '/home';
  }
}

export async function getUserRole(userId: string): Promise<UserRoleData | { error: string }> {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Erro ao buscar role do usuário:', error);
      return { error: error.message };
    }

    if (!data || !data.role) {
      return { error: 'Role do usuário não encontrada' };
    }

    return {
      userId,
      role: data.role as UserRole
    };
  } catch (error: any) {
    console.error('Erro inesperado ao buscar role do usuário:', error);
    return { error: error.message || 'Erro inesperado ao buscar role do usuário' };
  }
}

export async function getRedirectPathAfterLogin(userId: string): Promise<string> {
  const roleValidator = new RoleValidator();
  const roleData = await getUserRole(userId);
  
  if ('error' in roleData) {
    // Se houver erro, redireciona para home por padrão
    return '/home';
  }
  
  return roleValidator.getHomeRoute(roleData.role);
}

export const roleValidator = new RoleValidator(); 