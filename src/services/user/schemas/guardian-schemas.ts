import { z } from 'zod';
import { validatePhoneNumber } from '@/utils/phone-utils';

/**
 * Schema para validação de dados do responsável/tutor legal
 */
export const guardianInfoSchema = z.object({
  guardian_name: z
    .string()
    .min(2, 'Nome do responsável deve ter pelo menos 2 caracteres')
    .max(100, 'Nome do responsável não pode exceder 100 caracteres')
    .regex(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras e espaços'),
  
  guardian_email: z
    .string()
    .email('E-mail do responsável deve ser um e-mail válido')
    .max(255, 'E-mail não pode exceder 255 caracteres'),
  
  guardian_phone: z
    .string()
    .min(1, 'Telefone do responsável é obrigatório')
    .refine((phone) => {
      if (!phone) return false;
      return validatePhoneNumber(phone, 'BR');
    }, 'Telefone deve ser um número válido com DDI'),
  
  guardian_relationship: z
    .enum([
      'pai',
      'mae', 
      'avo',
      'avo_materna',
      'avo_paterna',
      'tio',
      'tia',
      'responsavel_legal',
      'outro'
    ], {
      errorMap: () => ({ message: 'Selecione uma relação válida com o menor' })
    }),
  
  guardian_document: z
    .string()
    .min(11, 'Documento deve ter pelo menos 11 caracteres')
    .max(14, 'Documento não pode exceder 14 caracteres')
    .regex(/^\d{3}\.\d{3}\.\d{3}-\d{2}$|^\d{11}$|^\d{2}\.\d{3}\.\d{3}-\d{1}$/, 
      'Documento deve ser um CPF válido (000.000.000-00)')
});

/**
 * Schema para validação de dados opcionais do responsável
 */
export const optionalGuardianInfoSchema = z.object({
  guardian_name: z.string().optional().nullable(),
  guardian_email: z.string().email().optional().nullable(),
  guardian_phone: z.string().optional().nullable(),
  guardian_relationship: z.string().optional().nullable(),
  guardian_document: z.string().optional().nullable()
});

/**
 * Schema para informações de conta de menor
 */
export const minorAccountInfoSchema = z.object({
  is_minor: z.boolean().default(false),
  is_guardian_account: z.boolean().default(false),
  managed_student_id: z.string().uuid().optional().nullable()
});

/**
 * Schema para atualização de dados do responsável
 */
export const updateGuardianSchema = z.object({
  userId: z.string().uuid('ID do usuário deve ser um UUID válido'),
  ...guardianInfoSchema.shape
});

/**
 * Schema para configurar conta administrada
 */
export const setupManagedAccountSchema = z.object({
  minorUserId: z.string().uuid('ID do menor deve ser um UUID válido'),
  guardianData: guardianInfoSchema
});

/**
 * Schema para validação de permissão de responsável
 */
export const guardianPermissionSchema = z.object({
  guardianUserId: z.string().uuid(),
  minorUserId: z.string().uuid(),
  action: z.enum(['view', 'edit', 'delete', 'enroll', 'graduate'])
});

/**
 * Schema para verificação de menor de idade
 */
export const minorValidationSchema = z.object({
  birth_date: z.string().datetime().or(z.date()),
  requires_guardian_data: z.boolean().optional().default(true)
});

/**
 * Funções de validação customizadas
 */

export function validateCPF(cpf: string): boolean {
  // Remove pontos e traços
  const cleanCPF = cpf.replace(/[^\d]/g, '');
  
  // Verifica se tem 11 dígitos
  if (cleanCPF.length !== 11) return false;
  
  // Verifica se todos os dígitos são iguais
  if (/^(\d)\1{10}$/.test(cleanCPF)) return false;
  
  // Valida dígitos verificadores
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (10 - i);
  }
  let digit = 11 - (sum % 11);
  if (digit === 10 || digit === 11) digit = 0;
  if (digit !== parseInt(cleanCPF.charAt(9))) return false;
  
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (11 - i);
  }
  digit = 11 - (sum % 11);
  if (digit === 10 || digit === 11) digit = 0;
  if (digit !== parseInt(cleanCPF.charAt(10))) return false;
  
  return true;
}

/**
 * Schema com validação de CPF mais rigorosa
 */
export const guardianWithCPFValidationSchema = guardianInfoSchema.refine(
  (data) => {
    if (data.guardian_document) {
      return validateCPF(data.guardian_document);
    }
    return true;
  },
  {
    message: 'CPF do responsável é inválido',
    path: ['guardian_document']
  }
);

/**
 * Schema para busca de responsáveis
 */
export const guardianSearchSchema = z.object({
  search: z.string().optional(),
  minorId: z.string().uuid().optional(),
  relationship: z.string().optional()
});

export type GuardianInfoInput = z.infer<typeof guardianInfoSchema>;
export type MinorAccountInfoInput = z.infer<typeof minorAccountInfoSchema>;
export type UpdateGuardianInput = z.infer<typeof updateGuardianSchema>;
export type SetupManagedAccountInput = z.infer<typeof setupManagedAccountSchema>;
export type GuardianPermissionInput = z.infer<typeof guardianPermissionSchema>;
export type GuardianSearchInput = z.infer<typeof guardianSearchSchema>; 