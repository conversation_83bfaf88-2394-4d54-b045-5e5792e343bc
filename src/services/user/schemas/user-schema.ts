import { z } from 'zod';

export const userStatusSchema = z.enum(['active', 'inactive', 'suspended'], {
  message: 'Status deve ser active, inactive ou suspended'
});

export const updateUserStatusSchema = z.object({
  userId: z.string().uuid('ID de usuário inválido'),
  status: userStatusSchema,
});

export const userFilterStatusSchema = z.array(userStatusSchema).optional();

export type UserStatus = z.infer<typeof userStatusSchema>;
export type UpdateUserStatusData = z.infer<typeof updateUserStatusSchema>; 