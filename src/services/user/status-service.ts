'use server';

import { createAdminClient } from '@/services/supabase/server';
import { updateUserStatusSchema } from './schemas/user-schema';

export async function updateUserStatus(data: unknown) {
  const result = updateUserStatusSchema.safeParse(data);
  if (!result.success) {
    return { success: false, errors: result.error.format() };
  }

  const { userId, status } = result.data;
  const supabase = await createAdminClient();

  try {
    // Atualizar status em public.users
    const { error: updateError } = await supabase
      .from('users')
      .update({ status })
      .eq('id', userId);

    if (updateError) throw updateError;

    // Sincronizar com auth.users metadata
    await syncUserStatusToAuth(userId, status);

    return { success: true };
  } catch (error) {
    console.error('Erro ao atualizar status do usuário:', error);
    return {
      success: false,
      errors: { _form: 'Erro ao atualizar status do usuário' }
    };
  }
}

export async function syncUserStatusToAuth(userId: string, status: string) {
  const supabase = await createAdminClient();
  
  try {
    // Primeiro, buscar o usuário para obter o app_metadata atual
    // Isso evita sobrescrever outros campos do app_metadata
    const { data: user, error: getUserError } = await supabase.auth.admin.getUserById(userId);
    
    if (getUserError) throw getUserError;
    if (!user.user) throw new Error(`Usuário não encontrado: ${userId}`);
    
    // Fazer merge do app_metadata existente com o novo status
    // Preserva todos os campos existentes e atualiza apenas o status
    const { error } = await supabase.auth.admin.updateUserById(userId, {
      app_metadata: { 
        ...user.user.app_metadata,
        status 
      }
    });

    if (error) throw error;
  } catch (error) {
    console.error('Erro ao sincronizar status no auth.users:', error);
    throw error;
  }
}

export async function getUserStatus(userId: string) {
  const supabase = await createAdminClient();
  
  try {
    const { data, error } = await supabase
      .from('users')
      .select('status')
      .eq('id', userId)
      .single();

    if (error) throw error;
    
    return { success: true, data: data.status };
  } catch (error) {
    console.error('Erro ao buscar status do usuário:', error);
    return {
      success: false,
      errors: { _form: 'Erro ao buscar status do usuário' }
    };
  }
}

export async function validateUserActiveStatus(userId: string): Promise<boolean> {
  const result = await getUserStatus(userId);
  return result.success && result.data === 'active';
} 