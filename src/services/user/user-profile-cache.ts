import { unstable_cache } from 'next/cache';
import { createClient } from '@/services/supabase/client';

// Constante para indicar que o cache foi desabilitado
const PROFILE_CACHE_DISABLED = true;

interface StudentBelt {
  id?: string;
  belt_level_id?: string;
  color: string;
  degree: number;
  stripeColor?: string | null;
  showCenterLine?: boolean | null;
  centerLineColor?: string | null;
  label?: string | null;
  sortOrder?: number | null;
  awardedAt?: string | null;
  instructor?: string;
}

interface UserProfileData {
  id: string;
  email: string;
  first_name: string;
  last_name: string | null;
  fullName: string;
  role: string;
  created_at: string;
  joinDate: string | null;
  avatar_url: string | null;
  phone: string | null;
  metadata: any;
  
  address: string | null;
  emergency_contact: string | null;
  emergency_phone: string | null;
  
  studentId?: string;
  registrationNumber?: string;
  status?: string;
  financialStatus?: string;
  birthDate?: string;
  gender?: string;
  
  emergency_contact_relationship?: string;
  
  healthNotes?: string;
  allergies?: string;
  medicalConditions?: string;
  medications?: string;
  
  check_in_code?: string;
  
  paymentStatus?: string;
  lastPaymentDate?: string;
  nextPaymentDue?: string;
  lastPaymentAmount?: string;
  nextPaymentDate?: string;
  
  attendanceRate?: number;
  lastAttendanceDate?: string;
  
  currentBeltId?: string;
  currentBelt?: StudentBelt;
}

interface ProfileResponse {
  data: UserProfileData | null;
  error: string | null;
}

export const USER_PROFILE_TAG = 'user-profile';
export const getUserProfileTag = (userId: string) => `user-profile-${userId}`;

// Função de cache desabilitada: sempre retorna null para forçar carregamento da API
export const loadUserProfileCache = unstable_cache(
  async (userId: string): Promise<ProfileResponse> => {
    if (PROFILE_CACHE_DISABLED) {
      console.log('[PROFILE_CACHE] Cache de perfil desativado, retornando null para forçar fetch da API');
      return { data: null, error: null };
    }
    
    // O código abaixo nunca será executado enquanto PROFILE_CACHE_DISABLED for true
    try {
      const supabase = createClient();
      
      const capitalize = (text: string): string => {
        if (!text) return '';
        return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
      };
      
      const { data, error } = await supabase
        .from('users')
        .select(`
          id, 
          email, 
          first_name, 
          last_name, 
          role, 
          created_at, 
          avatar_url,
          phone,
          metadata,
          status
        `)
        .eq('id', userId)
        .single();
      
      if (error) {
        console.error('[API] Erro ao carregar perfil do usuário:', error);
        return { data: null, error: 'Erro ao carregar perfil' };
      }
      
      const metadata = data.metadata || {};
      
      const fullName = `${data.first_name || ''}${data.last_name ? ' ' + data.last_name : ''}`;
      
      const joinDate = data.created_at 
        ? new Date(data.created_at).toLocaleDateString('pt-BR') 
        : null;

      let userData: UserProfileData = {
        ...data,
        fullName,
        joinDate,
        address: metadata.address || null,
        emergency_contact: metadata.emergency_contact || null,
        emergency_phone: metadata.emergency_phone || null
      };

      if (data.role === 'student') {
        const [studentData, beltsData, paymentsData] = await Promise.all([
          supabase.from('students')
            .select(`
              id,
              registration_number,
              financial_status,
              birth_date,
              emergency_contact_name,
              emergency_contact_phone,
              emergency_contact_relationship,
              health_notes,
              allergies,
              medical_conditions,
              medications,
              gender,
              street,
              city,
              state,
              zip,
              country
            `)
            .eq('user_id', userId)
            .single()
            .then(({ data, error }) => {
              if (error) {
                console.warn('[API] Erro ao buscar dados de estudante:', error);
                return null;
              }
              return data;
            }),
            
          // Busca a última graduação do aluno, juntando com belt_levels para obter detalhes da faixa
          (async () => {
            const { data: studentRow } = await supabase
              .from('students')
              .select('id')
              .eq('user_id', userId)
              .single();

            if (!studentRow) return null;

            const { data, error } = await supabase
              .from('student_belts')
              .select(`
                id,
                belt_level_id,
                awarded_at,
                awarded_by,
                belt_levels:belt_level_id (
                  id,
                  belt_color,
                  degree,
                  stripe_color,
                  show_center_line,
                  center_line_color,
                  label,
                  sort_order,
                  modalities:modality_id (
                    name
                  )
                ),
                users:awarded_by (
                  first_name,
                  last_name,
                  full_name
                )
              `)
              .eq('student_id', studentRow.id)
              .order('awarded_at', { ascending: false })
              .limit(1)
              .single();

            if (error) return null;
            return data;
          })(),
            
          supabase.from('student_payments')
            .select(`
              id,
              amount,
              status,
              payment_date,
              due_date
            `)
            .eq('student_id', (await supabase.from('students').select('id').eq('user_id', userId).single()).data?.id)
            .order('payment_date', { ascending: false })
            .limit(1)
            .single()
            .then(({ data, error }) => {
              if (error) return null;
              return data;
            })
        ]);
        
        if (studentData) {
          // Priorize os campos da tabela students para contatos de emergência
          // Mas use os campos do metadata como fallback quando necessário
          userData = {
            ...userData,
            studentId: studentData.id,
            registrationNumber: studentData.registration_number,
            status: capitalize(data.status || 'active'),
            financialStatus: capitalize(studentData.financial_status || ''),
            birthDate: studentData.birth_date,
            gender: studentData.gender,
            
            // Priorize os campos emergency_contact_name/phone da tabela students
            emergency_contact: studentData.emergency_contact_name || metadata.emergency_contact,
            emergency_phone: studentData.emergency_contact_phone || metadata.emergency_phone,
            emergency_contact_relationship: studentData.emergency_contact_relationship || metadata.emergency_contact_relationship,
            
            healthNotes: studentData.health_notes || metadata.healthNotes,
            allergies: studentData.allergies || metadata.allergies,
            medicalConditions: studentData.medical_conditions || metadata.medicalConditions,
            medications: studentData.medications || metadata.medications
          };
          
          // Log para debug dos campos de contato de emergência
          console.log('[PROFILE_CACHE] Dados de contato de emergência:', {
            emergency_contact: userData.emergency_contact,
            emergency_phone: userData.emergency_phone,
            emergency_contact_relationship: userData.emergency_contact_relationship,
            
            // Valores originais
            students_emergency_contact_name: studentData.emergency_contact_name,
            students_emergency_contact_phone: studentData.emergency_contact_phone,
            students_emergency_contact_relationship: studentData.emergency_contact_relationship,
            metadata_emergency_contact: metadata.emergency_contact,
            metadata_emergency_phone: metadata.emergency_phone,
            metadata_emergency_contact_relationship: metadata.emergency_contact_relationship
          });
          
          // Log para debug dos campos médicos
          console.log('[PROFILE_CACHE] Dados médicos carregados da tabela students:', {
            health_notes: studentData.health_notes,
            allergies: studentData.allergies,
            medical_conditions: studentData.medical_conditions,
            medications: studentData.medications
          });
          
          // Log para debug dos campos médicos no userData final
          console.log('[PROFILE_CACHE] Dados médicos no userData final:', {
            healthNotes: userData.healthNotes,
            allergies: userData.allergies,
            medicalConditions: userData.medicalConditions,
            medications: userData.medications
          });
          
          // Log para debug dos campos médicos no metadata
          console.log('[PROFILE_CACHE] Dados médicos no metadata:', {
            healthNotes: metadata.healthNotes,
            allergies: metadata.allergies,
            medicalConditions: metadata.medicalConditions,
            medications: metadata.medications
          });
          
          if (studentData.street || studentData.city) {
            const addressParts = [];
            if (studentData.street) addressParts.push(studentData.street);
            if (studentData.city) {
              if (studentData.state) {
                addressParts.push(`${studentData.city} - ${studentData.state}`);
              } else {
                addressParts.push(studentData.city);
              }
            }
            if (studentData.zip) addressParts.push(`CEP: ${studentData.zip}`);
            if (studentData.country) addressParts.push(studentData.country);
            
            userData.address = addressParts.join('\n');
          }
          
          if (beltsData && beltsData.belt_levels) {
            const level = (beltsData as any).belt_levels as any;

            const formattedAwardDate = beltsData.awarded_at 
              ? new Date(beltsData.awarded_at).toLocaleDateString('pt-BR') 
              : null;

            const instructorData = (beltsData as any).users;
            const modalityData = level?.modalities;

            const studentBeltId = beltsData.id;
            const beltLevelId = level?.id ?? beltsData.belt_level_id;

            // Garantir que o ID principal da graduação atual seja o registro de student_belts
            userData.currentBeltId = studentBeltId ?? beltLevelId;

            userData.currentBelt = {
              id: studentBeltId ?? beltLevelId,
              beltLevelId,
              color: level?.belt_color,
              degree: level?.degree || 0,
              stripeColor: level?.stripe_color ?? null,
              showCenterLine: level?.show_center_line ?? null,
              centerLineColor: level?.center_line_color ?? null,
              label: level?.label ?? null,
              sortOrder: level?.sort_order ?? null,
              awardedAt: formattedAwardDate,
              instructor: instructorData ? `Professor ${instructorData.full_name || `${instructorData.first_name} ${instructorData.last_name}`.trim()}` : null,
              modality_name: modalityData?.name || 'Modalidade',
              belt_label: level?.label || 'Faixa'
            } as any;
          }
          
          if (paymentsData) {
            userData.lastPaymentAmount = new Intl.NumberFormat('pt-BR', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            }).format(paymentsData.amount / 100);
            
            userData.lastPaymentDate = paymentsData.payment_date;
            userData.nextPaymentDate = paymentsData.due_date;
            userData.paymentStatus = paymentsData.status;
          }
          
          try {
            const fetchAttendance = async () => {
              const now = new Date();
              const threeMonthsAgo = new Date();
              threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
              
              const { data: attendanceData, error: attendanceError } = await supabase
                .from('student_attendance')
                .select('check_in')
                .eq('student_id', studentData.id)
                .gte('check_in', threeMonthsAgo.toISOString())
                .lte('check_in', now.toISOString())
                .order('check_in', { ascending: false });
              
              if (!attendanceError && attendanceData && attendanceData.length > 0) {
                userData.lastAttendanceDate = new Date(attendanceData[0].check_in).toLocaleDateString('pt-BR');
              }
            };
            
            fetchAttendance();
          } catch (attendanceError) {
            console.warn('[API] Erro ao buscar dados de presença:', attendanceError);
          }
        }
      }
      
      return { data: userData, error: null };
      
    } catch (error: any) {
      console.error('[PROFILE_CACHE] Erro ao carregar perfil do cache:', error);
      return { data: null, error: 'Erro ao carregar perfil' };
    }
  },
  ['profile-data'],
  { revalidate: 60 } // Reduzir para 1 minuto para minimizar problemas
);

/**
 * Função para invalidar o cache de perfil de usuário
 * Modificada para não fazer nada quando o cache está desativado
 */
export async function invalidateUserProfileCache(userId: string): Promise<void> {
  if (PROFILE_CACHE_DISABLED) {
    console.log('[PROFILE_CACHE] Cache de perfil desativado, ignorando invalidação');
    return;
  }
  
  // O código abaixo nunca será executado enquanto PROFILE_CACHE_DISABLED for true
  console.log('[PROFILE_CACHE] Tentando invalidar cache de perfil:', userId);
}

/**
 * Função para limpar todo o cache de perfil
 * Modificada para não fazer nada quando o cache está desativado
 */
export async function clearAllProfileCache(): Promise<void> {
  if (PROFILE_CACHE_DISABLED) {
    console.log('[PROFILE_CACHE] Cache de perfil desativado, ignorando limpeza global');
    return;
  }
  
  // O código abaixo nunca será executado enquanto PROFILE_CACHE_DISABLED for true
  console.log('[PROFILE_CACHE] Tentando limpar todo o cache de perfil');
}