import { createClient, createAdminClient } from '@/services/supabase/server';
import { getPermissionService } from '@/services/permissions/service';
import { getCurrentUser, requireAuth } from '@/services/auth/actions/auth-actions';

function handlePermissionError(error: any) {
  console.error("Erro de permissão:", error);
  
  if (error.code === "PGRST301" || error.message?.includes("permission")) {
    return "Você não tem permissão para acessar estes dados";
  }
  
  return error.message || "Ocorreu um erro inesperado";
}

/**
 * Protege uma rota de dashboard administrativo
 * Verifica se o usuário tem permissão para acessar a rota
 */
export async function protectDashboardRoute() {
  const { user } = await requireAuth('/login');
  
  const permissionService = getPermissionService();
  
  return permissionService.protectRoute(
    user.id,
    'user',
    'list',
    undefined,
    '/home?erro=acesso-negado'
  );
}

export async function loadUsers(tenantId: string) {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from("users")
      .select("id, name, email, role, active, created_at")
      .eq("tenant_id", tenantId)
      .order("name");

    if (error) {
      console.error("Erro ao carregar usuários:", error);
      return { error: handlePermissionError(error) };
    }

    return { data };
  } catch (error: any) {
    console.error("Erro inesperado ao carregar usuários:", error);
    return { error: handlePermissionError(error) };
  }
}

export async function loadBranches(tenantId: string) {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from("branches")
      .select("id, name")
      .eq("tenant_id", tenantId)
      .order("name");

    if (error) {
      console.error("Erro ao carregar filiais:", error);
      return { error: handlePermissionError(error) };
    }

    return { data };
  } catch (error: any) {
    console.error("Erro inesperado ao carregar filiais:", error);
    return { error: handlePermissionError(error) };
  }
}

export async function deleteUser(email: string, requesterId: string) {
  try {
    const supabase = await createClient();
    
    const { data: requester, error: requesterError } = await supabase
      .from("users")
      .select("role")
      .eq("id", requesterId)
      .single();

    if (requesterError || !requester) {
      return { error: "User not allowed" };
    }

    if (requester.role !== "admin") {
      return { error: "User not allowed" };
    }

    // Buscar o ID do usuário pelo email
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("id, role")
      .eq("email", email)
      .single();

    if (userError || !userData) {
      return { error: "Usuário não encontrado" };
    }

    // Se o usuário for estudante, remover da tabela de estudantes
    if (userData.role === "student") {
      const { error: studentError } = await supabase
        .from("students")
        .delete()
        .eq("user_id", userData.id);

      if (studentError) {
        console.error("Erro ao excluir dados do estudante:", studentError);
        return { error: `Erro ao excluir dados do estudante: ${studentError.message}` };
      }
    }

    // Excluir usuário da tabela de usuários
    const { error: deleteError } = await supabase
      .from("users")
      .delete()
      .eq("id", userData.id);

    if (deleteError) {
      console.error("Erro ao excluir usuário:", deleteError);
      return { error: `Erro ao excluir usuário: ${deleteError.message}` };
    }

    // Excluir usuário da autenticação do Supabase usando o admin client
    const adminClient = await createAdminClient();
    console.log("Excluindo usuário com admin client:", email);
    
    const { error: authError } = await adminClient.auth.admin.deleteUser(userData.id);

    if (authError) {
      console.error("Erro ao excluir usuário da autenticação:", authError);
      return { error: `Erro ao excluir usuário da autenticação: ${authError.message}` };
    }

    return { success: true };
  } catch (error: any) {
    console.error("Erro inesperado ao excluir usuário:", error);
    return { error: error.message || "Erro inesperado ao excluir usuário" };
  }
}

// Função para testar acesso à API
export async function testApiAccess(userId: string) {
  try {
    const supabase = await createClient();
    
    // Definir interface para resultado de teste
    interface TestResult {
      success: boolean;
      error: string | null;
      data: any;
    }
    
    // Definir tipo do objeto de resultados
    const results: Record<string, TestResult> = {};
    
    // Teste 1: Verificar se consegue ler da tabela de usuários
    try {
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("id")
        .limit(1);
        
      results["readUsers"] = {
        success: !userError,
        error: userError ? userError.message : null,
        data: userData ? "Dados obtidos com sucesso" : null
      };
    } catch (error: any) {
      results["readUsers"] = {
        success: false,
        error: error.message,
        data: null
      };
    }
    
    // Teste 2: Verificar se consegue ler o próprio usuário
    try {
      const { data: selfData, error: selfError } = await supabase
        .from("users")
        .select("id, role")
        .eq("id", userId)
        .single();
        
      results["readSelf"] = {
        success: !selfError,
        error: selfError ? selfError.message : null,
        data: selfData || null
      };
    } catch (error: any) {
      results["readSelf"] = {
        success: false,
        error: error.message,
        data: null
      };
    }
    
    // Teste 3: Verificar se consegue usar a API de autenticação
    try {
      const user = await getCurrentUser();
        
      results["auth"] = {
        success: !!user,
        error: !user ? "Usuário não autenticado" : null,
        data: user ? {
          id: user.id,
          email: user.email,
          role: user.app_metadata?.role
        } : null
      };
    } catch (error: any) {
      results["auth"] = {
        success: false,
        error: error.message,
        data: null
      };
    }
    
    return { data: results };
  } catch (error: any) {
    console.error("Erro ao executar testes de API:", error);
    return { error: error.message || "Erro ao executar testes de API" };
  }
}

// Função para buscar o perfil do usuário atual (incluindo avatar e nome)
export async function getUserProfile() {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return { error: "Usuário não autenticado" };
    }
    
    const userId = user.id;
    const supabase = await createClient();
    
    // Buscar dados completos do usuário do banco de dados
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("id, first_name, last_name, email, role, avatar_url, avatar_storage_path")
      .eq("id", userId)
      .single();
    
    if (userError) {
      console.error("Erro ao buscar dados do usuário:", userError);
      return { error: handlePermissionError(userError) };
    }
    
    // Preparar o objeto de retorno com dados formatados
    const fullName = `${userData.first_name}${userData.last_name ? ' ' + userData.last_name : ''}`;
    
    return { 
      data: {
        id: userData.id,
        email: userData.email,
        firstName: userData.first_name,
        lastName: userData.last_name,
        fullName,
        role: userData.role,
        avatarUrl: userData.avatar_url,
        avatarStoragePath: userData.avatar_storage_path
      } 
    };
  } catch (error: any) {
    console.error("Erro inesperado ao buscar perfil do usuário:", error);
    return { error: error.message || "Erro inesperado ao buscar perfil do usuário" };
  }
} 