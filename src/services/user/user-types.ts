/**
 * Tipos compartilhados para os serviços de usuário
 * Podem ser importados tanto por componentes client quanto server
 */

export type UserStatus = 'active' | 'inactive' | 'suspended';

export interface UserProfile {
  id: string
  email: string
  firstName: string
  lastName: string | null
  fullName: string
  role: string
  status: UserStatus
  avatarUrl: string | null
  avatarStoragePath: string | null
  branchId?: string
  tenantId?: string
} 