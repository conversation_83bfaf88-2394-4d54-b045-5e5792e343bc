export interface RankPresetRank {
  /** Nome do nível/graduação */
  name: string;
  /** Ordem ou grau (0 = faixa pura, 1..6 = graus/listras) */
  degree: number;
  /** Cor principal da faixa (hex, rgb ou nome) */
  belt_color: string;
  /** Cor das listras do container de grau */
  stripe_color: string;
  /** Exibe a linha horizontal central */
  show_center_line?: boolean;
  /** Cor da linha horizontal central */
  center_line_color?: string;
  /** Aulas necessárias para promoção (opcional) */
  sessions?: number;
  /** Idade mínima para promoção (opcional) */
  minimum_age?: number;
}

export interface RankPreset {
  /** Rótulo exibido no seletor de estilo de graduação */
  label: string;
  /** Lista de graduações pertencentes a este estilo */
  ranks: RankPresetRank[];
}

/**
 * Presets prontos de graduações.
 * 
 * Adicione ou modifique as entradas conforme necessário.
 */
export const rankPresets: Record<string, RankPreset> = {
  "Jiu-Jitsu Brasileiro Adulto": {
    label: "Jiu-Jitsu Brasileiro Adulto",
    ranks: [
      {
        name: "Faixa Branca",
        degree: 0,
        belt_color: "#FFFFFF",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Branca - 1º Grau",
        degree: 1,
        belt_color: "#FFFFFF",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Branca - 2º Grau",
        degree: 2,
        belt_color: "#FFFFFF",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Branca - 3º Grau",
        degree: 3,
        belt_color: "#FFFFFF",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Branca - 4º Grau",
        degree: 4,
        belt_color: "#FFFFFF",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Azul",
        degree: 0,
        belt_color: "#2563EB",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Azul - 1º Grau",
        degree: 1,
        belt_color: "#2563EB",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Azul - 2º Grau",
        degree: 2,
        belt_color: "#2563EB",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Azul - 3º Grau",
        degree: 3,
        belt_color: "#2563EB",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Azul - 4º Grau",
        degree: 4,
        belt_color: "#2563EB",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Roxa",
        degree: 0,
        belt_color: "#7C3AED",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Roxa - 1º Grau",
        degree: 1,
        belt_color: "#7C3AED",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Roxa - 2º Grau",
        degree: 2,
        belt_color: "#7C3AED",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Roxa - 3º Grau",
        degree: 3,
        belt_color: "#7C3AED",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Roxa - 4º Grau",
        degree: 4,
        belt_color: "#7C3AED",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Marrom",
        degree: 0,
        belt_color: "#92400E",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Marrom - 1º Grau",
        degree: 1,
        belt_color: "#92400E",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Marrom - 2º Grau",
        degree: 2,
        belt_color: "#92400E",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Marrom - 3º Grau",
        degree: 3,
        belt_color: "#92400E",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Marrom - 4º Grau",
        degree: 4,
        belt_color: "#92400E",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Preta",
        degree: 0,
        belt_color: "#000000",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Preta - 1º Grau",
        degree: 1,
        belt_color: "#000000",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Preta - 2º Grau",
        degree: 2,
        belt_color: "#000000",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Preta - 3º Grau",
        degree: 3,
        belt_color: "#000000",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Preta - 4º Grau",
        degree: 4,
        belt_color: "#000000",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Preta - 5º Grau",
        degree: 5,
        belt_color: "#000000",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Preta - 6º Grau",
        degree: 6,
        belt_color: "#000000",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#000000",
      },
    ],
  },
  "Jiu-Jitsu Brasileiro Infantil": {
    label: "Jiu-Jitsu Brasileiro Infantil",
    ranks: [
      {
        name: "Faixa Branca",
        degree: 0,
        belt_color: "#FFFFFF",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Branca - 1º Grau",
        degree: 1,
        belt_color: "#FFFFFF",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Branca - 2º Grau",
        degree: 2,
        belt_color: "#FFFFFF",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Branca - 3º Grau",
        degree: 3,
        belt_color: "#FFFFFF",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Branca - 4º Grau",
        degree: 4,
        belt_color: "#FFFFFF",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Branca - 5º Grau",
        degree: 1,
        belt_color: "#FFFFFF",
        stripe_color: "#FF0000",
      },
      {
        name: "Faixa Cinza / Branco",
        degree: 0,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#FFFFFF",
      },
      {
        name: "Faixa Cinza / Branco - 1º Grau",
        degree: 1,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#FFFFFF",
      },
      {
        name: "Faixa Cinza / Branco - 2º Grau",
        degree: 2,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#FFFFFF",
      },
      {
        name: "Faixa Cinza / Branco - 3º Grau",
        degree: 3,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#FFFFFF",
      },
      {
        name: "Faixa Cinza / Branco - 4º Grau",
        degree: 4,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#FFFFFF",
      },
      {
        name: "Faixa Cinza / Branco - 5º Grau",
        degree: 1,
        belt_color: "#909090",
        stripe_color: "#FF0000",
        show_center_line: true,
        center_line_color: "#FFFFFF",
      },
      {
        name: "Faixa Cinza",
        degree: 0,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Cinza - 1º Grau",
        degree: 1,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Cinza - 2º Grau",
        degree: 2,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Cinza - 3º Grau",
        degree: 3,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Cinza - 4º Grau",
        degree: 4,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
      },
      {
        name: "Faixa Cinza - 5º Grau",
        degree: 1,
        belt_color: "#909090",
        stripe_color: "#FF0000",
      },
      {
        name: "Faixa Cinza - 6º Grau",
        degree: 2,
        belt_color: "#909090",
        stripe_color: "#FF0000",
      },
      {
        name: "Faixa Cinza - 7º Grau",
        degree: 3,
        belt_color: "#909090",
        stripe_color: "#FF0000",
      },
      {
        name: "Faixa Cinza - 8º Grau",
        degree: 4,
        belt_color: "#909090",
        stripe_color: "#FF0000",
      },
      {
        name: "Faixa Cinza - 9º Grau",
        degree: 1,
        belt_color: "#909090",
        stripe_color: "#FAFF00",
      },
      {
        name: "Faixa Cinza - 10º Grau",
        degree: 2,
        belt_color: "#909090",
        stripe_color: "#FAFF00",
      },
      {
        name: "Faixa Cinza - 11º Grau",
        degree: 3,
        belt_color: "#909090",
        stripe_color: "#FAFF00",
      },
      {
        name: "Faixa Cinza / Preto",
        degree: 0,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Cinza / Preto - 1º Grau",
        degree: 1,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Cinza / Preto - 2º Grau",
        degree: 2,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Cinza / Preto - 3º Grau",
        degree: 3,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Cinza / Preto - 4º Grau",
        degree: 4,
        belt_color: "#909090",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Cinza / Preto - 5º Grau",
        degree: 1,
        belt_color: "#909090",
        stripe_color: "#FF0000",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Cinza / Preto - 6º Grau",
        degree: 2,
        belt_color: "#909090",
        stripe_color: "#FF0000",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Cinza / Preto - 7º Grau",
        degree: 3,
        belt_color: "#909090",
        stripe_color: "#FF0000",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Cinza / Preto - 8º Grau",
        degree: 4,
        belt_color: "#909090",
        stripe_color: "#FF0000",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Cinza / Preto - 9º Grau",
        degree: 1,
        belt_color: "#909090",
        stripe_color: "#FAFF00",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Cinza / Preto - 10º Grau",
        degree: 2,
        belt_color: "#909090",
        stripe_color: "#FF0000",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Cinza / Preto - 11º Grau",
        degree: 3,
        belt_color: "#909090",
        stripe_color: "#FF0000",
        show_center_line: true,
        center_line_color: "#000000",
      },
      {
        name: "Faixa Amarelo / Branco",
        degree: 0,
        belt_color: "#FAFF00",
        stripe_color: "#FFFFFF",
        show_center_line: true,
        center_line_color: "#FFFFFF",
      },
    ],
  },
};

/**
 * Retorna todos os rótulos disponíveis para o seletor.
 */
export const presetLabels = Object.keys(rankPresets);

/**
 * Obtém a lista de graduações de um preset pelo seu rótulo.
 */
export function getRanksByPreset(label: string) {
  return rankPresets[label]?.ranks;
} 