import { ReactNode } from 'react';

// Interface para estatísticas rápidas que pode ser reutilizada em diferentes componentes
export interface QuickStat {
  label: string;
  value: number | string;
  icon: ReactNode;
  color: string;
  disabled?: boolean;
  trend?: {
    value: number;
    isPositive: boolean;
    data?: number[];
  };
  href?: string;
  onClick?: () => void;
}

// Interface para estatísticas com gráficos (para componentes mais complexos)
export interface StatCard extends QuickStat {
  subtitle?: string;
  maxValue?: number;
  chartType?: 'radial' | 'bar' | 'line' | 'progress';
  description?: string;
  prefix?: string;
  suffix?: string;
}

// Interface para opções de filtro
export interface FilterOption {
  id: string;
  label: string;
  icon: ReactNode;
  count?: number;
  active?: boolean;
  disabled?: boolean;
}

// Interface para ações rápidas
export interface QuickAction {
  id: string;
  label: string;
  icon: ReactNode;
  href?: string;
  onClick?: () => void;
  primary?: boolean;
  variant?: 'default' | 'outline' | 'ghost' | 'destructive';
  requiresConfirmation?: boolean;
  disabled?: boolean;
} 