/**
 * Utilitários para formatação de dados.
 */

import { formatPhoneNumberDisplay } from '@/utils/phone-utils';

/**
 * Formata o nome completo a partir de nome e sobrenome.
 * @param firstName Primeiro nome
 * @param lastName Sobrenome (opcional)
 * @returns Nome completo formatado
 */
export function formatFullName(firstName?: string | null, lastName?: string | null): string {
  const first = firstName || '';
  const last = lastName || '';
  
  if (!first && !last) return '';
  if (!last) return first;
  if (!first) return last;
  
  return `${first} ${last}`;
}

/**
 * Formata telefone de responsável para exibição
 * @param phone Telefone com DDI (ex: +5511999998888)
 * @returns Telefone formatado para exibição (ex: +55 (11) 99999-8888)
 */
export function formatGuardianPhone(phone?: string | null): string {
  if (!phone) return '';
  return formatPhoneNumberDisplay(phone, 'BR', 'international');
}

/**
 * Formata tipos de contrato para exibição amigável
 * @param contractType Tipo de contrato (código)
 * @returns Nome formatado do tipo de contrato
 */
export function formatContractType(contractType?: string | null): string {
  if (!contractType) return '';
  
  const contractTypes: Record<string, string> = {
    'clt': 'CLT - Carteira Assinada',
    'pj': 'PJ - Pessoa Jurídica',
    'autonomo': 'Autônomo',
    'parceria': 'Parceria/Associação',
    'associacao': 'Sócio da Academia',
    'freelancer': 'Freelancer',
    'terceirizado': 'Terceirizado'
  };
  
  const value = contractType.toLowerCase();
  return contractTypes[value] || contractType;
}

/**
 * Formata modelos de pagamento para exibição amigável
 * @param paymentModel Modelo de pagamento (código)
 * @returns Nome formatado do modelo de pagamento
 */
export function formatPaymentModel(paymentModel?: string | null): string {
  if (!paymentModel) return '';
  
  const paymentModels: Record<string, string> = {
    'hora_aula': 'Por Hora/Aula',
    'salario_mensal': 'Salário Mensal',
    'comissao': 'Comissão por Resultado',
    'participacao_lucros': 'Participação nos Lucros',
    'fixed': 'Fixo',
    'hourly': 'Por Hora',
    'commission': 'Comissão',
    'monthly': 'Mensal',
    'weekly': 'Semanal',
    'daily': 'Diário'
  };
  
  const value = paymentModel.toLowerCase();
  return paymentModels[value] || paymentModel;
}

/**
 * Formata gênero para exibição amigável
 * @param gender Gênero (código)
 * @returns Nome formatado do gênero
 */
export function formatGender(gender?: string | null): string {
  if (!gender) return '';
  
  const genders: Record<string, string> = {
    'masculine': 'Masculino',
    'masculino': 'Masculino',
    'feminine': 'Feminino',
    'feminino': 'Feminino',
    'other': 'Outro',
    'outro': 'Outro',
    'prefiro_nao_informar': 'Prefiro não informar'
  };
  
  const value = gender.toLowerCase();
  return genders[value] || gender;
}

export const formatCurrency = (value: number | null) => {
  if (value === null) return 'N/A'
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value)
}

/**
 * Formata percentuais
 * @param value Valor numérico
 * @returns Valor formatado como percentual
 */
export function formatPercentage(value: number | string): string {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue)) return '';
  
  return `${numValue}%`;
} 