/**
 * Utilitários para formatação de datas e tempo.
 */

import { toZonedTime } from 'date-fns-tz';

/**
 * Converte uma string de data para um objeto Date local.
 * Útil para datas do banco no formato YYYY-MM-DD.
 * @param dateString String de data no formato YYYY-MM-DD
 * @returns Objeto Date local ou null se inválido
 */
export function parseLocalDate(dateString: string): Date | null {
  if (!dateString || typeof dateString !== 'string') return null;
  
  // Se é uma string no formato YYYY-MM-DD (data simples sem hora)
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    // Criar data local para evitar problemas de timezone
    const [year, month, day] = dateString.split('-').map(Number);
    return new Date(year, month - 1, day);
  }
  
  // Para outros formatos, usar conversão normal
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * Formata uma data para exibição em formato brasileiro.
 * @param dateString Data como string ou objeto Date
 * @returns Data formatada (ex: "15/03/2024")
 */
export function formatDate(dateString: string | Date): string {
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  return date.toLocaleDateString('pt-BR');
}

/**
 * Formata um horário para exibição em formato brasileiro.
 * @param dateString Data/hora como string ou objeto Date
 * @returns Horário formatado (ex: "14:30")
 */
export function formatTime(dateString: string | Date): string {
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
}

/**
 * Formata uma data e hora completa para exibição em formato brasileiro.
 * @param dateString Data/hora como string ou objeto Date
 * @returns Data e hora formatadas (ex: "15/03/2024 14:30")
 */
export function formatDateTime(dateString: string | Date): string {
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  return `${formatDate(date)} ${formatTime(date)}`;
}

/**
 * Formata uma data considerando o timezone de Brasília.
 * Útil para datas que vêm do banco como strings simples (YYYY-MM-DD).
 * @param dateString Data como string ou objeto Date
 * @returns Data formatada no timezone brasileiro (ex: "15/03/2024")
 */
export function formatDateBrazil(dateString: string | Date): string {
  if (!dateString) return '';
  
  let date: Date;
  
  if (typeof dateString === 'string') {
    // Se é uma string no formato YYYY-MM-DD (data simples sem hora)
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      // Criar data local para evitar problemas de timezone
      const [year, month, day] = dateString.split('-').map(Number);
      date = new Date(year, month - 1, day);
    } else {
      // Para strings com hora/timezone, usar conversão normal
      date = new Date(dateString);
      // Corrigir para UTC-3 manualmente se for UTC meia-noite
      if (date.getUTCHours() === 0 && date.getUTCMinutes() === 0 && date.getUTCSeconds() === 0) {
        date = new Date(date.getTime() + 3 * 60 * 60 * 1000);
      }
    }
  } else {
    date = dateString;
  }
  
  return date.toLocaleDateString('pt-BR');
}

/**
 * Formata um horário considerando o timezone de Brasília.
 * @param dateString Data/hora como string ou objeto Date
 * @returns Horário formatado no timezone brasileiro (ex: "14:30")
 */
export function formatTimeBrazil(dateString: string | Date): string {
  if (!dateString) return '';
  
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  return date.toLocaleTimeString('pt-BR', { 
    hour: '2-digit', 
    minute: '2-digit',
    timeZone: 'America/Sao_Paulo'
  });
}

/**
 * Formata uma data e hora completa considerando o timezone de Brasília.
 * @param dateString Data/hora como string ou objeto Date
 * @returns Data e hora formatadas no timezone brasileiro (ex: "15/03/2024 14:30")
 */
export function formatDateTimeBrazil(dateString: string | Date): string {
  if (!dateString) return '';
  
  // Para campos que são apenas data (como start_date, end_date), usar formatação de data local
  if (typeof dateString === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    return formatDateBrazil(dateString);
  }
  
  // Para campos com horário, formatar completo
  return `${formatDateBrazil(dateString)} ${formatTimeBrazil(dateString)}`;
} 