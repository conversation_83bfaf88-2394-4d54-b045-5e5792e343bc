/**
 * Utilitários específicos para gestão de responsáveis/tutores legais
 */

import { formatPhoneNumberDisplay, validatePhoneNumber, formatPhoneNumber } from '@/utils/phone-utils';
import { 
  GUARDIAN_RELATIONSHIPS, 
  UserWithGuardianInfo, 
  MinorAccountDisplayInfo 
} from '@/src/types/guardian';

/**
 * Classe utilitária para operações client-side relacionadas a responsáveis
 */
export class GuardianClientUtils {
  /**
   * Extrai informações de exibição para contas de menores de idade
   * @param user Dados do usuário com informações de responsável
   * @returns Informações formatadas para exibição
   */
  static getMinorAccountDisplayInfo(user: UserWithGuardianInfo): MinorAccountDisplayInfo {
    // Usa o valor is_minor do banco de dados em vez de calcular
    const isMinor = user.is_minor || false;
    const hasGuardian = !!(
      user.guardian_name && 
      user.guardian_email && 
      user.guardian_phone && 
      user.guardian_relationship
    );

    return {
      isMinor,
      hasGuardian,
      guardianName: user.guardian_name || undefined,
      guardianRelationship: user.guardian_relationship 
        ? formatGuardianRelationship(user.guardian_relationship)
        : undefined,
      requiresGuardianConsent: isMinor && !hasGuardian
    };
  }

  /**
   * Verifica se um usuário menor de idade tem responsável configurado adequadamente
   * @param user Dados do usuário
   * @returns true se menor tem responsável configurado ou se não é menor
   */
  static isMinorAccountProperlyConfigured(user: UserWithGuardianInfo): boolean {
    // Usa o valor is_minor do banco de dados em vez de calcular
    const isMinor = user.is_minor || false;
    
    if (!isMinor) return true;
    
    return isGuardianDataComplete({
      guardian_name: user.guardian_name,
      guardian_email: user.guardian_email,
      guardian_phone: user.guardian_phone,
      guardian_relationship: user.guardian_relationship,
      guardian_document: user.guardian_document
    });
  }
}

/**
 * Formata telefone de responsável para exibição
 * @param phone Telefone com DDI (ex: +*************)
 * @returns Telefone formatado para exibição (ex: +55 (11) 99999-8888)
 */
export function formatGuardianPhone(phone?: string | null): string {
  if (!phone) return '';
  return formatPhoneNumberDisplay(phone, 'BR', 'international');
}

/**
 * Formata telefone de responsável para formato nacional brasileiro
 * @param phone Telefone com DDI (ex: +*************)
 * @returns Telefone formatado nacional (ex: (11) 99999-8888)
 */
export function formatGuardianPhoneNational(phone?: string | null): string {
  if (!phone) return '';
  return formatPhoneNumberDisplay(phone, 'BR', 'national');
}

/**
 * Valida se um telefone de responsável está em formato válido
 * @param phone Telefone com DDI
 * @returns true se válido, false caso contrário
 */
export function validateGuardianPhone(phone?: string | null): boolean {
  if (!phone) return false;
  return validatePhoneNumber(phone, 'BR');
}

/**
 * Formata o relacionamento do responsável para exibição
 * @param relationship Código do relacionamento
 * @returns Nome formatado do relacionamento
 */
export function formatGuardianRelationship(relationship?: string | null): string {
  if (!relationship) return '';
  return GUARDIAN_RELATIONSHIPS[relationship as keyof typeof GUARDIAN_RELATIONSHIPS] || relationship;
}

/**
 * Formata CPF para exibição
 * @param cpf CPF com ou sem formatação
 * @returns CPF formatado (000.000.000-00)
 */
export function formatGuardianCPF(cpf?: string | null): string {
  if (!cpf) return '';
  
  const digitsOnly = cpf.replace(/\D/g, '');
  
  if (digitsOnly.length !== 11) return cpf;
  
  return digitsOnly.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
}

/**
 * Verifica se um responsável possui todos os dados obrigatórios
 * @param guardian Dados do responsável
 * @returns true se todos os dados obrigatórios estão preenchidos
 */
export function isGuardianDataComplete(guardian: {
  guardian_name?: string | null;
  guardian_email?: string | null;
  guardian_phone?: string | null;
  guardian_relationship?: string | null;
  guardian_document?: string | null;
}): boolean {
  return !!(
    guardian.guardian_name &&
    guardian.guardian_email &&
    guardian.guardian_phone &&
    guardian.guardian_relationship &&
    guardian.guardian_document &&
    validateGuardianPhone(guardian.guardian_phone)
  );
}

/**
 * Sanitiza dados do responsável removendo formatação desnecessária
 * @param guardian Dados do responsável
 * @returns Dados sanitizados
 */
export function sanitizeGuardianData(guardian: {
  guardian_name?: string;
  guardian_email?: string;
  guardian_phone?: string;
  guardian_relationship?: string;
  guardian_document?: string;
}) {
  return {
    ...guardian,
    guardian_name: guardian.guardian_name?.trim(),
    guardian_email: guardian.guardian_email?.toLowerCase().trim(),
    guardian_phone: guardian.guardian_phone?.trim(),
    guardian_relationship: guardian.guardian_relationship?.trim(),
    guardian_document: guardian.guardian_document?.replace(/\D/g, '').trim()
  };
} 