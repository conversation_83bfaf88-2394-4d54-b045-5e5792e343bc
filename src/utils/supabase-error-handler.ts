/**
 * Utilitários para tratamento de erros do Supabase
 * 
 * Este arquivo centraliza o tratamento de erros relacionados ao Supabase,
 * fornecendo mensagens consistentes e logs detalhados para debugging.
 */

interface SupabaseErrorDetails {
  isConfigurationError: boolean;
  isNetworkError: boolean;
  isAuthError: boolean;
  isPermissionError: boolean;
  userMessage: string;
  debugInfo: Record<string, any>;
}

export class SupabaseErrorHandler {
  /**
   * Analisa um erro do Supabase e retorna informações estruturadas
   */
  static analyzeError(error: any, context?: string): SupabaseErrorDetails {
    const errorMessage = error?.message || error?.toString() || 'Erro desconhecido';
    const errorCode = error?.code || error?.status;
    
    // Detectar tipo de erro
    const isConfigurationError = this.isConfigurationError(errorMessage);
    const isNetworkError = this.isNetworkError(errorMessage, errorCode);
    const isAuthError = this.isAuthError(errorMessage, errorCode);
    const isPermissionError = this.isPermissionError(errorMessage, errorCode);
    
    // Gerar mensagem amigável para o usuário
    const userMessage = this.generateUserMessage(
      isConfigurationError,
      isNetworkError,
      isAuthError,
      isPermissionError,
      errorMessage
    );
    
    // Informações para debugging
    const debugInfo = {
      originalError: errorMessage,
      errorCode,
      context: context || 'unknown',
      timestamp: new Date().toISOString(),
      nodeEnv: process.env.NODE_ENV,
      // Adicionar stack trace apenas em desenvolvimento
      ...(process.env.NODE_ENV === 'development' && { 
        stack: error?.stack?.substring(0, 500) // Limitar tamanho do stack
      })
    };
    
    return {
      isConfigurationError,
      isNetworkError,
      isAuthError,
      isPermissionError,
      userMessage,
      debugInfo
    };
  }
  
  /**
   * Loga um erro do Supabase com informações contextuais
   */
  static logError(error: any, context: string, additionalInfo?: Record<string, any>): void {
    const analysis = this.analyzeError(error, context);
    
    const logData = {
      ...analysis.debugInfo,
      ...additionalInfo,
      classification: {
        configuration: analysis.isConfigurationError,
        network: analysis.isNetworkError,
        auth: analysis.isAuthError,
        permission: analysis.isPermissionError
      }
    };
    
    if (analysis.isConfigurationError) {
      console.error('🔥 ERRO DE CONFIGURAÇÃO DO SUPABASE:', logData);
    } else if (analysis.isNetworkError) {
      console.error('🌐 ERRO DE REDE DO SUPABASE:', logData);
    } else if (analysis.isAuthError) {
      console.error('🔐 ERRO DE AUTENTICAÇÃO DO SUPABASE:', logData);
    } else if (analysis.isPermissionError) {
      console.error('🚫 ERRO DE PERMISSÃO DO SUPABASE:', logData);
    } else {
      console.error('❌ ERRO DO SUPABASE:', logData);
    }
  }
  
  /**
   * Retorna uma resposta padronizada para Server Actions
   */
  static createServerActionErrorResponse(
    error: any, 
    context: string,
    fallbackMessage = 'Ocorreu um erro inesperado'
  ): { success: false; error: string; details?: any } {
    const analysis = this.analyzeError(error, context);
    
    // Log detalhado para o servidor
    this.logError(error, context);
    
    const response: { success: false; error: string; details?: any } = {
      success: false,
      error: analysis.userMessage || fallbackMessage
    };
    
    // Incluir detalhes apenas em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      response.details = analysis.debugInfo;
    }
    
    return response;
  }
  
  /**
   * Detecta se é um erro de configuração
   */
  private static isConfigurationError(errorMessage: string): boolean {
    const configPatterns = [
      'API key',
      'project.*URL.*required',
      'configuração.*supabase',
      'NEXT_PUBLIC_SUPABASE',
      'SUPABASE_SERVICE_ROLE',
      'client.*required',
      'invalid.*api.*key'
    ];
    
    return configPatterns.some(pattern => 
      new RegExp(pattern, 'i').test(errorMessage)
    );
  }
  
  /**
   * Detecta se é um erro de rede
   */
  private static isNetworkError(errorMessage: string, errorCode?: any): boolean {
    const networkCodes = [500, 502, 503, 504, 'ECONNREFUSED', 'ENOTFOUND', 'ETIMEDOUT'];
    const networkPatterns = [
      'fetch.*failed',
      'network.*error',
      'connection.*refused',
      'timeout',
      'unreachable',
      'dns.*resolution'
    ];
    
    return networkCodes.includes(errorCode) ||
           networkPatterns.some(pattern => 
             new RegExp(pattern, 'i').test(errorMessage)
           );
  }
  
  /**
   * Detecta se é um erro de autenticação
   */
  private static isAuthError(errorMessage: string, errorCode?: any): boolean {
    const authCodes = [401, 403];
    const authPatterns = [
      'unauthorized',
      'invalid.*token',
      'session.*expired',
      'authentication.*failed',
      'access.*denied',
      'jwt.*expired'
    ];
    
    return authCodes.includes(errorCode) ||
           authPatterns.some(pattern => 
             new RegExp(pattern, 'i').test(errorMessage)
           );
  }
  
  /**
   * Detecta se é um erro de permissão
   */
  private static isPermissionError(errorMessage: string, errorCode?: any): boolean {
    const permissionPatterns = [
      'permission.*denied',
      'insufficient.*privileges',
      'forbidden',
      'row.*level.*security',
      'policy.*violation'
    ];
    
    return errorCode === 403 ||
           permissionPatterns.some(pattern => 
             new RegExp(pattern, 'i').test(errorMessage)
           );
  }
  
  /**
   * Gera mensagem amigável para o usuário
   */
  private static generateUserMessage(
    isConfig: boolean,
    isNetwork: boolean,
    isAuth: boolean,
    isPermission: boolean,
    originalMessage: string
  ): string {
    if (isConfig) {
      return 'Sistema temporariamente indisponível. Nossa equipe foi notificada.';
    }
    
    if (isNetwork) {
      return 'Problema de conexão. Verifique sua internet e tente novamente.';
    }
    
    if (isAuth) {
      return 'Sua sessão expirou. Faça login novamente.';
    }
    
    if (isPermission) {
      return 'Você não tem permissão para realizar esta ação.';
    }
    
    // Para outros erros, usar uma mensagem genérica
    return 'Ocorreu um erro inesperado. Tente novamente em alguns instantes.';
  }
}

/**
 * Hook para uso em componentes React
 */
export function useSupabaseErrorHandler() {
  const handleError = (error: any, context: string, additionalInfo?: Record<string, any>) => {
    return SupabaseErrorHandler.analyzeError(error, context);
  };
  
  const logError = (error: any, context: string, additionalInfo?: Record<string, any>) => {
    SupabaseErrorHandler.logError(error, context, additionalInfo);
  };
  
  return { handleError, logError };
} 