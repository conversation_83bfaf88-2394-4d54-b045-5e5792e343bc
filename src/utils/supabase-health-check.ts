/**
 * Sistema de verificação de saúde do Supabase
 * 
 * Este arquivo fornece utilitários para verificar se a configuração
 * e conexão com o Supabase estão funcionando corretamente.
 */

import { isSupabaseConfigured, isSupabaseServiceRoleConfigured, diagnoseSupabaseConfig } from '@/config/supabase';
import { SupabaseErrorHandler } from './supabase-error-handler';

interface HealthCheckResult {
  isHealthy: boolean;
  timestamp: string;
  checks: {
    configuration: HealthCheckItem;
    connectivity: HealthCheckItem;
    authentication: HealthCheckItem;
    database: HealthCheckItem;
  };
  summary: {
    total: number;
    passed: number;
    failed: number;
    warnings: number;
  };
}

interface HealthCheckItem {
  status: 'pass' | 'fail' | 'warn' | 'skip';
  message: string;
  details?: any;
  duration?: number;
}

export class SupabaseHealthChecker {
  /**
   * Executa verificação completa de saúde do Supabase
   */
  static async performHealthCheck(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    console.log('🔍 Iniciando verificação de saúde do Supabase...');
    
    const checks = {
      configuration: await this.checkConfiguration(),
      connectivity: await this.checkConnectivity(),
      authentication: await this.checkAuthentication(),
      database: await this.checkDatabaseAccess(),
    };
    
    const checkResults = Object.values(checks);
    const passed = checkResults.filter(c => c.status === 'pass').length;
    const failed = checkResults.filter(c => c.status === 'fail').length;
    const warnings = checkResults.filter(c => c.status === 'warn').length;
    const total = checkResults.length;
    
    const isHealthy = failed === 0 && passed > 0;
    
    const result: HealthCheckResult = {
      isHealthy,
      timestamp: new Date().toISOString(),
      checks,
      summary: {
        total,
        passed,
        failed,
        warnings
      }
    };
    
    const duration = Date.now() - startTime;
    console.log(`🔍 Verificação de saúde concluída em ${duration}ms:`, {
      healthy: isHealthy,
      summary: result.summary
    });
    
    return result;
  }
  
  /**
   * Executa verificação rápida (apenas configuração)
   */
  static quickHealthCheck(): { isHealthy: boolean; message: string } {
    try {
      const diagnostic = diagnoseSupabaseConfig();
      return {
        isHealthy: diagnostic.isConfigured,
        message: diagnostic.isConfigured 
          ? 'Configuração OK' 
          : 'Configuração incompleta'
      };
    } catch (error) {
      return {
        isHealthy: false,
        message: `Erro de configuração: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }
  
  /**
   * Verifica a configuração do Supabase
   */
  private static async checkConfiguration(): Promise<HealthCheckItem> {
    const startTime = Date.now();
    
    try {
      const diagnostic = diagnoseSupabaseConfig();
      
      if (!diagnostic.isConfigured) {
        return {
          status: 'fail',
          message: 'Configuração do Supabase incompleta',
          details: diagnostic,
          duration: Date.now() - startTime
        };
      }
      
      if (diagnostic.urlFormat !== 'valid') {
        return {
          status: 'fail',
          message: 'URL do Supabase inválida',
          details: diagnostic,
          duration: Date.now() - startTime
        };
      }
      
      const hasServiceRole = isSupabaseServiceRoleConfigured();
      
      return {
        status: 'pass',
        message: 'Configuração válida',
        details: {
          ...diagnostic,
          hasServiceRole
        },
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        status: 'fail',
        message: 'Erro ao verificar configuração',
        details: { error: error instanceof Error ? error.message : String(error) },
        duration: Date.now() - startTime
      };
    }
  }
  
  /**
   * Verifica conectividade básica com o Supabase
   */
  private static async checkConnectivity(): Promise<HealthCheckItem> {
    const startTime = Date.now();
    
    if (!isSupabaseConfigured()) {
      return {
        status: 'skip',
        message: 'Pulado devido à configuração inválida',
        duration: Date.now() - startTime
      };
    }
    
    try {
      const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
      
      if (!url) {
        return {
          status: 'fail',
          message: 'URL não disponível para teste',
          duration: Date.now() - startTime
        };
      }
      
      // Verificar se consegue fazer uma requisição básica
      const testUrl = `${url}/rest/v1/`;
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5s timeout
      
      const response = await fetch(testUrl, {
        method: 'HEAD',
        signal: controller.signal,
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
        }
      });
      
      clearTimeout(timeoutId);
      
      return {
        status: response.ok || response.status === 401 ? 'pass' : 'warn', // 401 é esperado sem auth
        message: `Conectividade OK (${response.status})`,
        details: {
          status: response.status,
          statusText: response.statusText
        },
        duration: Date.now() - startTime
      };
    } catch (error) {
      const analysis = SupabaseErrorHandler.analyzeError(error, 'connectivity-check');
      
      return {
        status: analysis.isNetworkError ? 'fail' : 'warn',
        message: 'Erro de conectividade',
        details: analysis.debugInfo,
        duration: Date.now() - startTime
      };
    }
  }
  
  /**
   * Verifica capacidade de autenticação
   */
  private static async checkAuthentication(): Promise<HealthCheckItem> {
    const startTime = Date.now();
    
    if (!isSupabaseConfigured()) {
      return {
        status: 'skip',
        message: 'Pulado devido à configuração inválida',
        duration: Date.now() - startTime
      };
    }
    
    try {
      // Este é um teste simplificado que verifica se podemos criar um cliente
      const { createClient } = await import('@/services/supabase/server/server');
      const client = await createClient();
      
      if (!client) {
        return {
          status: 'fail',
          message: 'Falha ao criar cliente Supabase',
          duration: Date.now() - startTime
        };
      }
      
      return {
        status: 'pass',
        message: 'Cliente de autenticação criado com sucesso',
        duration: Date.now() - startTime
      };
    } catch (error) {
      const analysis = SupabaseErrorHandler.analyzeError(error, 'auth-check');
      
      return {
        status: 'fail',
        message: 'Erro ao verificar autenticação',
        details: analysis.debugInfo,
        duration: Date.now() - startTime
      };
    }
  }
  
  /**
   * Verifica acesso básico ao banco de dados
   */
  private static async checkDatabaseAccess(): Promise<HealthCheckItem> {
    const startTime = Date.now();
    
    if (!isSupabaseConfigured()) {
      return {
        status: 'skip',
        message: 'Pulado devido à configuração inválida',
        duration: Date.now() - startTime
      };
    }
    
    try {
      // Teste básico de acesso ao banco
      const { createClient } = await import('@/services/supabase/server/server');
      const client = await createClient();
      
      // Tentar uma consulta simples
      const { error } = await client
        .from('tenants')
        .select('id')
        .limit(1);
      
      if (error && error.code !== 'PGRST116') { // PGRST116 = nenhum resultado, que é ok
        return {
          status: 'warn',
          message: 'Acesso limitado ao banco de dados',
          details: {
            error: error.message,
            code: error.code
          },
          duration: Date.now() - startTime
        };
      }
      
      return {
        status: 'pass',
        message: 'Acesso ao banco de dados OK',
        duration: Date.now() - startTime
      };
    } catch (error) {
      const analysis = SupabaseErrorHandler.analyzeError(error, 'database-check');
      
      return {
        status: 'fail',
        message: 'Erro ao acessar banco de dados',
        details: analysis.debugInfo,
        duration: Date.now() - startTime
      };
    }
  }
}

/**
 * Hook para uso em componentes React (desenvolvimento)
 */
export function useSupabaseHealth() {
  const checkHealth = async () => {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('Health check disponível apenas em desenvolvimento');
      return null;
    }
    
    return await SupabaseHealthChecker.performHealthCheck();
  };
  
  const quickCheck = () => {
    return SupabaseHealthChecker.quickHealthCheck();
  };
  
  return { checkHealth, quickCheck };
}
