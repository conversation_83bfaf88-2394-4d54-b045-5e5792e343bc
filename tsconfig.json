{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "tsBuildInfoFile": ".next/cache/tsbuildinfo.json", "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/src/*": ["./src/*"], "@/app/*": ["./src/app/*"], "@/app/(dashboard)/*": ["./src/app/(dashboard)/*"], "@/components/*": ["./src/components/*"], "@/utils/*": ["./src/utils/*"], "@/hooks/*": ["./src/hooks/*"], "@/lib/*": ["./src/lib/*"], "@/services/*": ["./src/services/*"], "@/config/*": ["./src/config/*"], "@/schemas": ["./src/schemas/index.ts"], "@/schemas/*": ["./src/schemas/*"], "@/types/*": ["./src/types/"], "@/constants/*": ["./src/constants/*"], "@/store/*": ["./src/store/*"], "@/contexts/*": ["./src/contexts/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}